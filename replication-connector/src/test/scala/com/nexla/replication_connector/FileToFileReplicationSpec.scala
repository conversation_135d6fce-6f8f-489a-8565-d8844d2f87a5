package com.nexla.replication_connector

import akka.actor.{ActorSystem, Scheduler}
import akka.stream.Materializer
import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import com.nexla.common.ConnectionType
import com.nexla.connector.config.FlowType
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.context.Context.RetryConfig
import com.nexla.replication_connector.flow_logs.FlowLogsSender
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.metrics.MetricsSender
import com.nexla.replication_connector.pipeline.ReplicationPipeline
import com.nexla.replication_connector.state.State
import com.nexla.replication_connector.utils.{Fixtures, LocalFileSystemConnectorService, ShutdownActorSystem}
import org.apache.commons.io.FileUtils
import org.mockito.Mockito.mock
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import java.io.File
import java.nio.file.{Files, Paths}
import java.util
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.duration.DurationInt
import scala.jdk.CollectionConverters.collectionAsScalaIterableConverter
import scala.util.Random

class FileToFileReplicationSpec extends AnyFunSuite with Matchers with ShutdownActorSystem {
  implicit val system: ActorSystem = ActorSystem()
  private implicit val ec: ExecutionContextExecutor = system.dispatcher
  private implicit val mat: Materializer = Materializer(system)
  private implicit val sch: Scheduler = system.scheduler

  private val javaListingClient = mock(classOf[JavaListingClient])
  private val mockFileVault: FileVaultClient = mock(classOf[FileVaultClient])
  private val mockMsgProducer: ReplicationMessageProducer = mock(classOf[ReplicationMessageProducer])

  private val props = Fixtures.stubProps
  private val probeFactory = new ProbeFactory {
    override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
      if (sourceType.equals(ConnectionType.FTP)) {
        new LocalFileSystemConnectorService
      } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
        new LocalFileSystemConnectorService
      } else {
        throw new Exception(s"Unsupported type $sourceType")
      }
    }
  }

  private def createDataSource(sourceId: Int, sourceRootPath: String) = {
    val dataSource = new DataSource()
    dataSource.setId(sourceId)
    dataSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    dataSource.setSourceConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", sourceRootPath)
      }
    })
    dataSource
  }

  private def createDataSink(sinkId: Int, destinationRootPath: String) = {
    val dataSink = new DataSink()
    dataSink.setId(sinkId)
    dataSink.setConnectionType(ConnectionType.FTP)
    dataSink.setSinkConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", destinationRootPath)
      }
    })
    val ds = new DataSet
    ds.setId(1)
    dataSink.setDataSet(ds)
    dataSink
  }

  private def compareDirs(dir1: String, dir2: String): Boolean = {
    val files1 = FileUtils.listFiles(new File(dir1), null, true).asScala.toList
    val files2 = FileUtils.listFiles(new File(dir2), null, true).asScala.toList

    if (files1.length != files2.length) false
    else {
      files1.sortBy(_.getAbsolutePath).zip(files2.sortBy(_.getAbsolutePath)).forall { case (f1, f2) =>
        FileUtils.contentEquals(f1, f2)
      }
    }
  }

  val flowLogsSender = new FlowLogsSender(mockMsgProducer)(ec)
  val metricsSender = new MetricsSender(mockMsgProducer)(ec)

  test("should replicate files") {
    val sourceRootPath = getClass.getResource("/test_data/input1").getPath
    val destinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString

    val state = new State
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val dataSource = createDataSource(Random.nextInt(), sourceRootPath)
    val dataSink = createDataSink(Random.nextInt(), destinationRootPath)
    val ctx = Context(runId, s"task-$flowId", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))

    val srcFile1Path = Paths.get(sourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(sourceRootPath, "/file2.csv").toString
    val expectedDstFile1Path = Paths.get(destinationRootPath, "/file1.csv").toString
    val expectedDstFile2Path = Paths.get(destinationRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)

    val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)
    val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, mockMsgProducer,
      props, probeFactory, flowLogsSender, metricsSender)
    pip.startPipeline(new PipelineKiller).futureValue(Timeout(70.seconds))

    val areFiles11Equal = FileUtils.contentEquals(new File(expectedDstFile1Path), new File(srcFile1Path))
    val areFiles21Equal = FileUtils.contentEquals(new File(expectedDstFile2Path), new File(srcFile2Path))
    val areDirsEqual = compareDirs(sourceRootPath, destinationRootPath)
    Files.exists(Paths.get(expectedDstFile1Path)) shouldBe true
    Files.exists(Paths.get(expectedDstFile2Path)) shouldBe true
    areFiles11Equal shouldBe true
    areFiles21Equal shouldBe true
    areDirsEqual shouldBe true

  }

  test("should replicate nested directory") {
    val sourceRootPath = getClass.getResource("/test_data/input2").getPath
    val destinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString

    val state = new State
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val dataSource = createDataSource(Random.nextInt(), sourceRootPath)
    val dataSink = createDataSink(Random.nextInt(), destinationRootPath)
    val ctx = Context(runId, s"task-$flowId", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))

    val srcFiles = List(
      Paths.get(sourceRootPath, "/dir1/dir2/dir3/file.csv").toString,
      Paths.get(sourceRootPath, "/dir1/dir2/dir5/file.csv").toString,
      Paths.get(sourceRootPath, "/dir1/dir2/file.csv").toString,
      Paths.get(sourceRootPath, "/dir1/dir2/file2.csv").toString,
      Paths.get(sourceRootPath, "/file.csv").toString,
    )
    val dstFiles = List(
      Paths.get(destinationRootPath, "/dir1/dir2/dir3/file.csv").toString,
      Paths.get(destinationRootPath, "/dir1/dir2/dir5/file.csv").toString,
      Paths.get(destinationRootPath, "/dir1/dir2/file.csv").toString,
      Paths.get(destinationRootPath, "/dir1/dir2/file2.csv").toString,
      Paths.get(destinationRootPath, "/file.csv").toString,
    )

    val listingAppClient = Fixtures.mockListing(dataSource.getId, srcFiles)
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)

    val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)
    val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, mockMsgProducer,
      props, probeFactory, flowLogsSender, metricsSender)
    pip.startPipeline(new PipelineKiller).futureValue(Timeout(70.seconds))

    srcFiles.zip(dstFiles).foreach { case (srcFile, dstFile) =>
      Files.exists(Paths.get(srcFile)) shouldBe true
      Files.exists(Paths.get(dstFile)) shouldBe true

      val areFilesEqual = FileUtils.contentEquals(new File(srcFile), new File(dstFile))
      areFilesEqual shouldEqual true
    }
    val areDirsEqual = compareDirs(sourceRootPath, destinationRootPath)
    areDirsEqual shouldBe true
  }
}
