package com.nexla.replication_connector.legacy_tests.config

import com.nexla.admin.client.DataSource
import com.nexla.common.{AppType, NexlaConstants}
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.vault.ConfigEnv
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.utils.Fixtures
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.AppUtils
import org.mockito.Mockito
import org.scalatest.TagAnnotation
import org.scalatest.concurrent.TimeLimits
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import java.time.Duration
import java.util

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class FileReadRetryConfigTest extends AnyFunSuite with TimeLimits with AppUtils {
  def readFileSourceConfig(dataSource: DataSource): FileSourceConnectorConfig = {
    val props: AppProps = {
      val c = loadConfig(AppType.REPLICATION_CONNECTOR, new util.HashMap[String, String](), Some(new ConfigEnv.LocalEnv()))
      val spyC = Mockito.spy(c)
      Mockito.doAnswer(_ => "").when(spyC).getString("nexla.creds.enc")
      Mockito.doAnswer(_ => "").when(spyC).getString("nexla.creds.enc.iv")
      Mockito.doAnswer(_ => 123).when(spyC).getInt("FLOW_ID")
      Mockito.doAnswer(_ => 345L).when(spyC).getLong("RUN_ID")
      new AppProps(spyC)
    }
    val enricher = new ConfigEnricher {}
    new FileSourceConnectorConfig(enricher.fullDataSourceConfig(props.config, dataSource, props.enrichSourceParams, FileSourceConnectorConfig.configDef()))
  }

  test("should read fileReadRetryConfig from source config") {
    val sourceCfg = new util.HashMap[String, Object]()
    sourceCfg.put(NexlaConstants.FILE_SOURCE_RETRY_ATTEMPTS_CONFIG_PATH, Integer.valueOf(123))
    sourceCfg.put(NexlaConstants.FILE_SOURCE_RETRY_MIN_BACKOFF_MS_CONFIG_PATH, Integer.valueOf(456))
    sourceCfg.put(NexlaConstants.FILE_SOURCE_RETRY_MAX_BACKOFF_MS_CONFIG_PATH, Integer.valueOf(789))

    val dataSource = Fixtures.adminDataSource(1, sourceCfg)
    val fileSourceConfig = readFileSourceConfig(dataSource)
    val retryConfig = fileSourceConfig.fileReadRetryConfig.get()

    retryConfig.nrOfAttempts shouldEqual 123
    retryConfig.minBackoff shouldEqual Duration.ofMillis(456)
    retryConfig.maxBackoff shouldEqual Duration.ofMillis(789)
  }

  test("should read fileReadRetryConfig from application.conf if not specified in source config") {
    val dataSource = Fixtures.adminDataSource(1, new util.HashMap[String, Object]())
    val fileSourceConfig = readFileSourceConfig(dataSource)
    val retryConfig = fileSourceConfig.fileReadRetryConfig.get()

    retryConfig.nrOfAttempts shouldEqual 3
    retryConfig.minBackoff shouldEqual Duration.ofMillis(1)
    retryConfig.maxBackoff shouldEqual Duration.ofMillis(2)
  }


}