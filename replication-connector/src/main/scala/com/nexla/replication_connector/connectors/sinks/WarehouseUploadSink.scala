package com.nexla.replication_connector.connectors.sinks


import akka.actor.ActorSystem
import akka.stream.Materializer
import com.bazaarvoice.jolt.JsonUtils
import com.google.common.base.Suppliers
import com.google.common.collect.Maps
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials
import com.nexla.common.ResourceType.SINK
import com.nexla.common.logging.NexlaLogger
import com.nexla.common.metrics.RecordMetric
import com.nexla.common.parse.FilePropertiesDetector
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.{INSERT, UPSERT}
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat._
import com.nexla.connector.config.jdbc.{JdbcSinkConnectorConfig, WarehouseCopyFileFormat}
import com.nexla.parser.DelimitedTextParser
import com.nexla.parser.DelimitedTextParser.DEFAULT_OPTIONS
import com.nexla.probe.sql.SchemaUtils.selectSchema
import com.nexla.probe.sql.SqlConnectorService
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.connectors.sinks.ReplicationSinkConnector.{SinkUploadError, SinkUploadResult, SinkUploadSuccess}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.pipeline.SourceDownloader.LocallyReplicatedFile
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import connect.jdbc.sink.dialect.{DbDialect, DialectRegistry}
import connect.jdbc.sink.dialect.DialectFeature.{COPY_INSERT, COPY_UPSERT}
import connect.jdbc.sink.dialect.copy.{ReplicationContext, SinkCopyOperation}
import org.apache.commons.io.FilenameUtils
import org.slf4j.LoggerFactory

import java.io.{File, FileInputStream, InputStream}
import java.sql.Connection
import java.util
import java.util.Optional
import java.util.zip.GZIPInputStream
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class WarehouseUploadSink(
                           pipelineContext: Context,
                           adminApiClient: AdminApiClient,
                           props: AppProps,
                         )
                         (implicit val ec: ExecutionContext,
                          val system: ActorSystem,
                          val mat: Materializer)
  extends ReplicationSinkConnector
    with StrictNexlaLogging
    with WithLogging
    with ConfigEnricher {
  override val sendsItsOwnMetrics: Boolean = false

  private val resultConfig: util.Map[String, String] = fullDataSinkConfig(props.config, pipelineContext.sink, props.enrichSinkParams, JdbcSinkConnectorConfig.configDef())
  enrichWithDataCredentials(adminApiClient, resultConfig)
  val config: JdbcSinkConnectorConfig = new JdbcSinkConnectorConfig(resultConfig)
  private val dbDialect: DbDialect = DialectRegistry.getInstance().fromConnectionString(config.authConfig)

  private val probeService = new SqlConnectorService(props.config.getStore, null)
  probeService.initLogger(SINK, config.sinkId, Optional.empty())

  private val connProvider: java.util.function.Supplier[Connection] = () => probeService.getConnection(config.authConfig)

  private val schema = Suppliers.memoize(() => selectSchema(connProvider, dbDialect, config))
  private val copyMode = config.copyAllowed &&
    (((config.insertMode eq UPSERT) && dbDialect.getFeatures.contains(COPY_UPSERT)) ||
      ((config.insertMode eq INSERT) && dbDialect.getFeatures.contains(COPY_INSERT)))

  if (!copyMode) {
    throw new IllegalStateException()
  }

  private val chm = Maps.newConcurrentMap[String, SinkCopyOperation]()
  private val nexlaLogger = new NexlaLogger(LoggerFactory.getLogger(this.getClass))

  def stop() = {

    logger.info(s"sink task stop")
    chm.asScala.values.foreach(_.close(connProvider))
  }


  def uploadFile(localFile: LocallyReplicatedFile): Future[SinkUploadResult] = {
    val result = Future {

      val copyOperation = chm.computeIfAbsent("key", _ => {
        val fileFormat = localFile
          .fileConfig
          .getOrElse(config.fileFormat.asScala.getOrElse(detectFileFormat(localFile.file)))

        dbDialect
          .newSinkCopyOperation(config)
          .init(schema.get, config, adminApiClient, fileFormat, nexlaLogger)
      })

      val forceColumns = fileCsvColumns(localFile.file, copyOperation.getFileFormat).map(_.asJava).asJava

      copyOperation.uploadFile(localFile.file)
      val replicationContext = new ReplicationContext(forceColumns, new RecordMetric)
      copyOperation.uploadFiles(connProvider, config.insertMode, _ => (), localFile.file, Optional.of(replicationContext))
      replicationContext
    }
    result.map { replicationContext =>
      val metric = replicationContext.getReplicateMetric
      SinkUploadSuccess(
        listedId = localFile.listedId,
        file = localFile.file,
        recordCount = metric.getSentRecordsTotal.get(),
        errorCount = metric.getErrorRecords.get(),
        byteSize = metric.getSentBytesTotal.get(),
        displayName = config.table,
      )
    }.recover { case ex =>
      SinkUploadError(
        listedId = localFile.listedId,
        file = localFile.file,
        recordCount = localFile.recordCount,
        errorCount = 1,
        byteSize = localFile.byteSize,
        displayName = config.table,
        ex = ex,
      )
    }
  }

  private def detectFileFormat(file: File) = {
    val extension = FilenameUtils.getExtension(file.getName)

    extension match {
      case "gz" =>
        val is = new GZIPInputStream(new FileInputStream(file))
        val source = scala.io.Source.fromInputStream(is)
        val isJson = source
          .getLines()
          .take(1)
          .toList
          .headOption
          .exists(line => Try(JsonUtils.jsonToMap(line)).isSuccess)
        source.close()
        is.close()

        if (isJson) {
          JSON_GZIP
        } else {
          val inputStream = new GZIPInputStream(new FileInputStream(file))
          val sample = FilePropertiesDetector.readSample(inputStream)
          val detector = new FilePropertiesDetector(sample, 50)
          val detect = detector.detectTextDelimiter()
          detect.delimiter.toString match {
            case ";" => CSV_GZIP
            case "," => CSV_COMMA_GZIP
            case "|" => CSV_BAR_GZIP
          }
        }
      case _ =>
        val is = new FileInputStream(file)
        val source = scala.io.Source.fromInputStream(is)
        val isJson = source
          .getLines()
          .take(1)
          .toList
          .headOption
          .exists(line => Try(JsonUtils.jsonToMap(line)).isSuccess)
        source.close()
        is.close()

        if (isJson) {
          JSON
        } else {
          val inputStream = new FileInputStream(file)
          val sample = FilePropertiesDetector.readSample(inputStream)
          val detector = new FilePropertiesDetector(sample, 50)
          val detect = detector.detectTextDelimiter()
          detect.delimiter.toString match {
            case ";" => CSV
            case "," => CSV_COMMA
            case "|" => CSV_BAR
          }
        }
    }
  }

  def fileCsvColumns(file: File, fileFormat: WarehouseCopyFileFormat) = {
    for {
      mc <- config.mappingConfig.asScala
      fileColumns <- readFileColumns(file, fileFormat)
    } yield {
      fileColumns.map(col => {
        val mapping = mc.getMapping.get(col)
        if (mapping == null) {
          val errorMsg = s"Mapping for $col is not found. Mapping is [${mc.getMapping}]"
          val ex = new Exception(errorMsg)
          logger.error(s"Mapping columns failed", ex)
          throw ex
        }
        mapping.keySet().asScala.head
      })
    }
  }

  private def readFileColumns(file: File, fileFormat: WarehouseCopyFileFormat) = {
    val fileStream = new FileInputStream(file)

    def tryReadFormat(fileFormat: WarehouseCopyFileFormat): Option[List[String]] = {
      fileFormat.format match {
        case WarehouseCopyFileFormat.Format.CSV_FORMAT if fileFormat.compression == Compression.NONE =>
          readHeaders(fileFormat, fileStream)

        case WarehouseCopyFileFormat.Format.CSV_FORMAT if fileFormat.compression == Compression.GZIP =>
          readHeaders(fileFormat, new GZIPInputStream(fileStream))

        case _ => None
      }
    }

    tryReadFormat(fileFormat)

    //    @tailrec
    //    def findFormat(formats: List[WarehouseCopyFileFormat]): Option[List[String]] = {
    //      formats match {
    //        case elem :: rest =>
    //          val result = Try(tryReadFormat(elem)).toOption.flatten
    //          if (result.nonEmpty) {
    //            result
    //          } else {
    //            findFormat(rest)
    //          }
    //        case Nil => None
    //      }
    //    }

    //    detectByFormat
    //      .orElse {
    //        val detectParserIS = new FileInputStream(file)
    //        val detection = detectParser(empty(), detectParserIS, Maps.newHashMap(), Maps.newHashMap(), file.getAbsolutePath, null, logger.underlying)
    //        detectParserIS.close()
    //
    //        detection.parser.flatMap { parser =>
    //          parser.parseMessages(fileStream)
    //            .toList
    //            .asScala
    //            .map(_.asScala)
    //            .headOption
    //            .flatten
    //            .map(x => x.getRawMessage.keySet().asScala.toList)
    //        }
    //      }
  }

  private def readHeaders(fileFormat: WarehouseCopyFileFormat, is: InputStream) = {
    val parser = new DelimitedTextParser()
    parser.options(DEFAULT_OPTIONS.get(fileFormat.delimiter.charAt(0)))
    Option {
      parser.readMessagesAndSchema(is)
        .getColumns
        .asScala
        .toList
    } filter { columns =>
      columns.forall(xx => xx.matches("^[A-Za-z_][A-Za-z0-9_]*$"))
    }
  }

}
