package com.nexla.replication_connector.connectors.sources

import akka.stream.scaladsl.Source
import com.google.common.collect.Maps
import com.nexla.admin.client.config.SourceConfigUtils
import com.nexla.admin.client.{AdminApiClient, DataCredentials, DataSource}
import com.nexla.admin.config.ConfigUtils.{enrichWithCredentialsStore, enrichWithDataCredentials}
import com.nexla.common.ConnectionType
import com.nexla.common.ConnectionType.{DROPBOX, SNOWFLAKE}
import com.nexla.common.NexlaConstants._
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.FlowType
import com.nexla.connector.config.big_query.BigQuerySinkConnectorConfig
import com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix
import com.nexla.connector.config.file.DirScanningMode.DIRECTORIES
import com.nexla.connector.config.file.DropBoxAuthConfig.ACCESS_TOKEN
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat._
import com.nexla.connector.config.jdbc.{JdbcSinkConnectorConfig, JdbcSourceConnectorConfig, WarehouseCopyFileFormat}
import com.nexla.file.service.LocalConnectorService
import com.nexla.probe.s3.S3ConnectorService
import com.nexla.probe.sql.SqlConnectorService
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.pipeline.metadata.{FileToReplicate, RemoteReplicationMetadata}
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import connect.jdbc.sink.dialect.{DbDialect, DialectRegistry}
import connect.jdbc.source.{BulkTableQuerier, SqlStatementCreator}
import connect.jdbc.util.WarehouseUtils
import org.apache.commons.lang3.StringUtils

import java.net.URI
import java.nio.file.{Files, Paths}
import java.util
import java.util.Optional
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.{ExecutionContext, Future}

class WarehouseUnloadSource(pipelineContext: Context, adminApiClient: AdminApiClient, props: AppProps)(implicit ec: ExecutionContext) extends ReplicationSourceConnector
  with StrictNexlaLogging
  with ConfigEnricher
  with WithLogging {

  lazy val dialectRegistry: DialectRegistry = DialectRegistry.getInstance()

  def prepareReplicationSourceRemote(): Future[RemoteReplicationMetadata] = Future {

    val mode = sourceConfig.query
      .asScala
      .map(_ => BulkTableQuerier.QueryMode.QUERY)
      .getOrElse(BulkTableQuerier.QueryMode.TABLE)

    val sourceDialect = dialectRegistry.fromConnectionString(sourceConfig.authConfig)
    val unloadFormat = unloadFileFormat(sourceConfig, sourceDialect)
    val tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig)
    val copyOperation = sourceDialect.newSourceCopyOperation(sourceConfig, unloadFormat, logger.underlying, true, tempStorage, FlowType.REPLICATION)

    val probeService = new SqlConnectorService(props.config.getStore, adminApiClient)
    val connection = probeService.getConnection(sourceConfig.authConfig)


    try {

      val querier = new BulkTableQuerier(
        sourceDialect,
        sourceConfig,
        false,
        Integer.MAX_VALUE,
        mode,
        sourceConfig.authConfig.schemaName,
        sourceConfig.table,
        sourceConfig.query,
        Optional.empty())

      val stmtCreator = new SqlStatementCreator()
      querier.initStatementCreator(connection, stmtCreator)
      val query = stmtCreator.getStatementSql

      val copyOperationS3Dest = copyOperation.destinationPath(sourceConfig.sourceId, pipelineContext.runId)
      copyOperation.executeCopyCommand(connection, query, copyOperationS3Dest)
      //      connection.commit()

      if (sourceConfig.directUnload && sourceConfig.authConfig.dbType == SNOWFLAKE) {
        listUnloadedFilesDirect(unloadFormat, query, copyOperationS3Dest)
      } else {
        listUnloadedFilesS3(sourceConfig, unloadFormat, query)
      }

    } finally {
      copyOperation.close()
      connection.close()
    }

  }

  private def listUnloadedFilesDirect(unloadFormat: WarehouseCopyFileFormat, query: String, tempFolder: String) = {
    val unloadFiles = Files.list(Paths.get(tempFolder))
      .iterator()
      .asScala
      .toList
      .map(_.toFile)

    val files = Source.single {
      unloadFiles.map { file =>
        val relativePath = StringUtils.removeStart(file.getAbsolutePath, tempFolder)
        FileToReplicate(None, relativePath, Some(unloadFormat), Some(file), query)
      }
    }

    RemoteReplicationMetadata(files, dummyConfig, new LocalConnectorService)
  }

  // todo remove it when refactor to inplace download on Unload Source level
  private def dummyConfig = {
    val config = new util.HashMap[String, String]
    config.put(SOURCE_ID, "1234")
    config.put(CREDS_ENC, "1")
    config.put(CREDS_ENC_IV, "1")
    config.put(CREDENTIALS_DECRYPT_KEY, "1")
    config.put(DEPTH, "1")
    config.put(ACCESS_TOKEN, "test_token")
    config.put(SOURCE_TYPE, DROPBOX.name)
    config.put(UNIT_TEST, "true")
    config.put(LISTING_APP_SERVER_URL, "123")
    config.put(DIR_SCANNING_MODE, DIRECTORIES.name)

    new FileSourceConnectorConfig(config)
  }

  private def listUnloadedFilesS3(sourceCfg: JdbcSourceConnectorConfig, unloadFormat: WarehouseCopyFileFormat, query: String) = {
    val s3Dest = Paths.get(sourceCfg.tempS3UploadBucket, s"source-${sourceCfg.sourceId}", pipelineContext.runId.toString).toString

    val fileDestinationUri = new URI(s3Dest)
    val bucketPath = StringUtils.removeStart(fileDestinationUri.getPath, "/")
    val bucketPrefix = toBucketPrefix(bucketPath, true)

    val s3ConnectorService = new S3ConnectorService()

    val fileConfig = fileSourceConfig(sourceCfg, s3Dest)

    val workDir = Files.createTempDirectory("fast-warehouse")
    workDir.toFile.deleteOnExit()

    val unloadFiles = s3ConnectorService
      .listS3Objects(fileConfig, bucketPrefix.bucket, bucketPrefix.prefix)
      .asScala
      .toSeq

    val files = Source.single {
      unloadFiles.map ( obj => FileToReplicate(None, obj.key(), Some(unloadFormat), None, query))
    }

    RemoteReplicationMetadata(files, fileConfig, s3ConnectorService)
  }

  private def unloadFileFormat(sourceCfg: JdbcSourceConnectorConfig,
                               sourceDialect: DbDialect) = {
    val desiredSourceFormat = sourceCfg.unloadFormat.asScala
      .orElse(sourceDialect.defaultSourceFileFormat().asScala)

    val connectionType = pipelineContext.sink.getConnectionType
      if (connectionType.isWarehouseSink) {
        val sinkCfg = new JdbcSinkConnectorConfig(sinkConfig())
        val sinkDialect = dialectRegistry.fromConnectionString(sinkCfg.authConfig)
        val desiredSinkFormat = List(sinkCfg.fileFormat.asScala, sinkDialect.defaultSinkFileFormat().asScala).flatten

        val possibleFormats = sinkDialect
          .sinkFileFormats()
          .asScala
          .intersect(
            sourceDialect.sourceFileFormats().asScala
          )

        if (possibleFormats.isEmpty) {
          desiredSourceFormat.get
        } else {
          desiredSinkFormat
            .collectFirst {
              case x if possibleFormats.contains(x) => x
            }
            .orElse(desiredSourceFormat
              .filter(possibleFormats.contains))
            .getOrElse(possibleFormats.head)
        }
      } else if (connectionType.equals(ConnectionType.BIGQUERY)) {
        val sinkCfg = new BigQuerySinkConnectorConfig(sinkConfig())
        val desiredSinkFormat = List(sinkCfg.fileFormat.asScala, Some(JSON), Some(JSON_GZIP)).flatten
        desiredSinkFormat
          .collectFirst {
            case x if sourceDialect.sourceFileFormats().asScala.contains(x) => x
          }
          .orElse(sourceDialect.defaultSourceFileFormat().asScala)
          .get
      } else {
        desiredSourceFormat.get
      }
  }

  private def fileSourceConfig(sourceCfg: JdbcSourceConnectorConfig, s3Dest: String) = {
    val fileSource = new DataSource()
    fileSource.setId(sourceCfg.sourceId)
    fileSource.setSourceConfig(Map[String, AnyRef](PATH -> s3Dest).asJava)
    fileSource.setConnectionType(ConnectionType.S3)

    val dataCredentials = new DataCredentials()
    dataCredentials.setCredentialsEnc(sourceCfg.credsEnc)
    dataCredentials.setCredentialsEncIv(sourceCfg.credsEncIv)
    dataCredentials.setId(sourceCfg.credsId)
    fileSource.setDataCredentials(dataCredentials)

    val enrichedConfig = SourceConfigUtils.createSourceConfig(fileSource, props.enrichSourceParams)
    val baseConfig = enrichWithCredentialsStore(enrichedConfig, FileSourceConnectorConfig.configDef())

    val fileResultConfig: util.Map[String, String] = Maps.newHashMap()
    fileResultConfig.putAll(sourceCfg.originalsStrings())
    fileResultConfig.putAll(baseConfig)

    enrichWithDataCredentials(adminApiClient, fileResultConfig)

    fileResultConfig.put(CREDS_ENC, sourceCfg.originals().get(S3_CREDS_ENC).toString)
    fileResultConfig.put(CREDS_ENC_IV, sourceCfg.originals().get(S3_CREDS_ENCIV).toString)

    val fileConfig = new FileSourceConnectorConfig(fileResultConfig)
    fileConfig
  }

  val sourceConfig: JdbcSourceConnectorConfig = {
    val dataSource = pipelineContext.source
    val sourceConfig = fullDataSourceConfig(props.config, dataSource, props.enrichSourceParams, JdbcSourceConnectorConfig.configDef())

    enrichWithDataCredentials(adminApiClient, sourceConfig)
    new JdbcSourceConnectorConfig(sourceConfig)
  }

  private def sinkConfig() = {
    val sinkConfig = fullDataSinkConfig(props.config, pipelineContext.sink, props.enrichSinkParams, JdbcSinkConnectorConfig.configDef())

    enrichWithDataCredentials(adminApiClient, sinkConfig)
  }

}
