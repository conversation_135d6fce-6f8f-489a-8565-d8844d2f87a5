package com.nexla.replication_connector.connectors.sources

import akka.stream.scaladsl.{Sink, Source}
import akka.{Done, NotUsed}
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials
import com.nexla.common.ResourceType
import com.nexla.common.probe.ProbeControllerConstants.DISPLAY_PATH
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient}
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.pipeline.metadata.{FileToReplicate, RemoteReplicationMetadata}
import com.nexla.replication_connector.state.State
import com.nexla.sc.client.listing.ListedFile
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}

import java.util.Optional
import scala.concurrent.{ExecutionContext, Future}

class FileSource(
                  pipelineContext: Context,
                  pipelineState: State,
                )(
                  adminApiClient: AdminApiClient,
                  javaListingClient: ListingClient,
                  listingStreamSource: ListingStreamSource,
                  fileVaultClient: FileVaultClient,
                  probeFactory: ProbeFactory,
                  props: AppProps,
                )
                (implicit ec: ExecutionContext)
  extends ReplicationSourceConnector
    with StrictNexlaLogging
    with ConfigEnricher
    with WithLogging {

  def prepareReplicationSourceRemote(): Future[RemoteReplicationMetadata] = Future {

    val resultConfig = fullDataSourceConfig(props.config, pipelineContext.source, props.enrichSourceParams, FileSourceConnectorConfig.configDef())
    enrichWithDataCredentials(adminApiClient, resultConfig)
    val sourceConfig: FileSourceConnectorConfig = new FileSourceConnectorConfig(resultConfig)

    val source: Source[FileToReplicate, NotUsed] = listFiles()
      .map { listedFile =>
        val fullLocalPath = listedFile.metadata
          .flatMap(meta => meta.get(DISPLAY_PATH))
          .flatten
          .getOrElse(listedFile.fullPath)

        FileToReplicate(Option(listedFile.id), fullLocalPath, None, None, fullLocalPath)
      }
      .watchTermination() { case (_, eventualDone: Future[Done]) =>
        eventualDone.onComplete { _ =>
          logger.info(s"Stopping listFiles")
          }
        NotUsed
      }


    val probeService: FileConnectorService[_] = {
      val ps = probeFactory.getProbeService(adminApiClient, javaListingClient, props.decryptKey, sourceConfig.sourceType, fileVaultClient)
      ps.initLogger(ResourceType.SOURCE, sourceConfig.sourceId, Optional.empty())
      ps
    }

    RemoteReplicationMetadata(source.map(List(_)), sourceConfig, probeService)
  }

  private def listFiles(): Source[ListedFile, _] =
    listingStreamSource.listFiles(pipelineContext.source.getId, () => pipelineState.updateDataIngestionTs())
      .alsoTo(Sink.foreach(nexlaFile => pipelineState.addFileForHeartbeat(nexlaFile.id)))

}
