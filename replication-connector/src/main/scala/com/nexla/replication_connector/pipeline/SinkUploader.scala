package com.nexla.replication_connector.pipeline

import akka.NotUsed
import akka.actor.{ActorSystem, Cancellable}
import akka.stream.scaladsl.{Flow, Sink}
import com.nexla.replication_connector.connectors.sinks.ReplicationSinkConnector
import com.nexla.replication_connector.connectors.sinks.ReplicationSinkConnector.{SinkUploadError, SinkUploadR<PERSON>ult, SinkUploadSuccess}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.flow_logs.FlowLogsSender
import com.nexla.replication_connector.metrics.MetricsSender
import com.nexla.replication_connector.pipeline.SourceDownloader.LocallyReplicatedFile
import com.nexla.replication_connector.state.State
import com.nexla.sc.client.listing.{FileStatuses, ListingAppClient}
import com.nexla.sc.util.StrictNexlaLogging

import java.util.concurrent.atomic.{AtomicBoolean, AtomicReference}
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class SinkUploader(pipelineContext: Context, listingClient: ListingAppClient, flowLogsSender: FlowLogsSender, metricsSender: MetricsSender, pipelineState: State)
                  (implicit system: ActorSystem, ec: ExecutionContext) extends StrictNexlaLogging {

  def uploadToDestination(sink: ReplicationSinkConnector): Flow[LocallyReplicatedFile, Unit, NotUsed] = {
    val sinkMetricsFlow = if (sink.sendsItsOwnMetrics) Sink.ignore else metricsSender.sinkMetrics(pipelineContext.sink, pipelineContext.dataset, pipelineContext.runId)

    Flow[LocallyReplicatedFile]
      .alsoTo(flowLogsSender.sinkStartingFlowLogs(pipelineContext.sink, pipelineContext.runId))
      .alsoTo(notifyOnStart())
      .mapAsync(1) { localFile => uploadFileToSink(sink, localFile) }
      .alsoTo(markFileInListing)
      .alsoTo(cleanUpLocalFiles)
      .alsoTo(sinkMetricsFlow)
      .alsoTo(flowLogsSender.sinkEndingFlowLogs(pipelineContext.sink, pipelineContext.runId))
      .alsoTo(notifyOnEnd())
      .map(_ => ())
  }

  private def uploadFileToSink(sink: ReplicationSinkConnector, localFile: LocallyReplicatedFile): Future[SinkUploadResult] =
    sink.uploadFile(localFile).map {
      case s: SinkUploadSuccess =>
        logger.info(s"File ${s.displayName} successfully uploaded to sink")
        s
      case s: SinkUploadError =>
        logger.info(s"File ${s.displayName} upload to sink failed", s.ex)
        s
    }

  private val cleanUpLocalFiles: Sink[SinkUploadResult, NotUsed] = Flow[SinkUploadResult]
    .mapAsync(1) { result =>
      Future {
        val fileDeleted = result.file.delete()
        if (fileDeleted) {
          logger.info(s"Local file was successfully deleted: ${result.displayName}")
        } else {
          logger.warn(s"Local file was not deleted: ${result.displayName} (${result.file.getPath})")
        }
      }
    }.to(Sink.ignore)

  private val markFileInListing: Sink[SinkUploadResult, NotUsed] = Flow[SinkUploadResult]
    .map { r => (r.listedId, r) }
    .collect { case (Some(listedId), r) => (listedId, r) }
    .mapAsync(1) { case (listedId, r) =>
      r match {
        case uploadError: SinkUploadError =>
          val errorMessage = uploadError.ex.getMessage
          logger.warn(s"File [$listedId, ${uploadError.displayName}] failed to be uploaded because of $errorMessage - setting its status to STOPPED and removing from heartbeating")
          pipelineState.removeFileForHeartbeat(listedId)
          listingClient.setFileStatus(pipelineContext.source.getId, listedId, FileStatuses.Stopped, None, Option(errorMessage))
        case uploadSuccess: SinkUploadSuccess =>
          logger.debug(s"File [$listedId, ${uploadSuccess.displayName}] successfully uploaded - setting its status to DONE and removing from heartbeating")
          pipelineState.removeFileForHeartbeat(listedId)
          listingClient.setFileStatus(pipelineContext.source.getId, listedId, FileStatuses.Done, None, None)
      }
    }.to(Sink.ignore)

  private val heartbeatSwitch: AtomicReference[Cancellable] = new AtomicReference[Cancellable]()

  private def notifyOnStart(): Sink[Any, Unit] = {
    val wasExecuted = new AtomicBoolean(false)
    Sink.foreach[Any] { _ =>
      if (!wasExecuted.get()) {
        val cancellable = system.scheduler.scheduleWithFixedDelay(1.minute, 1.minute)(() => pipelineState.updateDataIngestionTs())
        pipelineState.notifyWriteStart()
        pipelineState.markSinkAsNonEmpty()
        wasExecuted.set(true)
        heartbeatSwitch.set(cancellable)
      }
    }
  }.mapMaterializedValue(_ => ())

  private def notifyOnEnd(): Sink[Any, Unit] = Flow[Any]
    .watchTermination()((_, done) => done.onComplete {
      case Success(_) =>
        pipelineState.notifyWriteDone()
        Option(heartbeatSwitch.get()).map(_.cancel())
      case Failure(_) =>
        pipelineState.notifyWriteFailed()
        Option(heartbeatSwitch.get()).map(_.cancel())
    }).to(Sink.ignore)


}
