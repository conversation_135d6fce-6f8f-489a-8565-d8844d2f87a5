USING CHARACTER SET UTF8
DEFINE JOB #nexla_export_job_name# DESCRIPTION 'Exports table #source_schema_name#.#source_table_name# from #nexla_export_job_name# to a local CSV file for Nexla'
(
	DEFINE OPERATOR nexla_export_#nexla_export_job_name#_csv_consumer TYPE DATACONNECTOR CONSUMER
	SCHEMA *
	ATTRIBUTES (
	    VARCHAR FileName, VARCHAR Format, VARCHAR OpenMode, INTEGER BlockSize, INTEGER BufferSize, INTEGER RetentionPeriod, INTEGER RowsPerInstance,
	    INTEGER SecondarySpace, INTEGER UnitCount, INTEGER VigilElapsedTime, INTEGER VigilWaitTime, INTEGER VolumeCount, VARCHAR AccessModuleName,
	    VARCHAR AccessModuleInitStr, VARCHAR DirectoryPath, VARCHAR ExpirationDate, VARCHAR IndicatorMode, VARCHAR PrimarySpace, VARCHAR PrivateLogName,
	    VARCHA<PERSON> RecordFormat, <PERSON><PERSON><PERSON><PERSON>, VARCHAR <PERSON>, <PERSON><PERSON><PERSON><PERSON>elimiter, VARCHA<PERSON>FileName, <PERSON>RC<PERSON><PERSON> VigilStartTime,
	    VARCHAR VigilStopTime, VARCHAR VolSerNumber, VARCHAR UnitType
	);

	DEFINE SCHEMA nexla_export_#nexla_export_job_name#_csv_schema ( #export_column_schema# );

	DEFINE OPERATOR nexla_export_#nexla_export_job_name#_operator TYPE EXPORT
	SCHEMA nexla_export_#nexla_export_job_name#_csv_schema
	ATTRIBUTES (
		VARCHAR UserName, VARCHAR UserPassword, VARCHAR LogonMech, VARCHAR LogonMechData, VARCHAR ExportDateForm, VARCHAR SelectStmt,
		INTEGER BlockSize, INTEGER MaxSessions, INTEGER MinSessions, INTEGER TenacityHours, INTEGER TenacitySleep, INTEGER MaxDecimalDigits,
		VARCHAR AccountID, VARCHAR DateForm, VARCHAR NotifyExit, VARCHAR NotifyExitIsDLL, VARCHAR NotifyLevel, VARCHAR NotifyMethod,
		VARCHAR NotifyString, VARCHAR SpoolMode, VARCHAR PrivateLogName, VARCHAR TdpId, VARCHAR TraceLevel, VARCHAR WorkingDatabase
	);

	APPLY TO OPERATOR (
		nexla_export_#nexla_export_job_name#_csv_consumer[#write_parallelism#]

		ATTRIBUTES (
			FileName = '#local_file_path#', OpenMode = 'Write',
			Format = 'DELIMITED', IndicatorMode = 'N', TextDelimiter = '#format_delimiter#'
		)
	)

	SELECT * FROM OPERATOR (
		nexla_export_#nexla_export_job_name#_operator[#read_parallelism#]

		ATTRIBUTES (
			UserName = '#nexla_username#', UserPassword = '#nexla_password#', LogonMech = 'TD2', TdpId = '#nexla_db_host#',
			WorkingDatabase = '#source_schema_name#', SelectStmt = '#generate_sql_export_statement#',
            #additional_properties#
		)
	);
);
