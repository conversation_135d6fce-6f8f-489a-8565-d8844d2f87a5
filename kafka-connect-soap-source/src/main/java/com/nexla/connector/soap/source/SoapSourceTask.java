package com.nexla.connector.soap.source;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.oauth2.RefreshingTokenProvider;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.soap.SoapSourceConnectorConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.soap.SoapIteration;
import com.nexla.soap.SoapIterationChainBuilder;
import com.nexla.soap.SoapIterationContext;
import com.nexla.soap.SoapIterationOffset;
import com.nexla.soap.pojo.SoapResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.net.util.Base64;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.joda.time.DateTime;
import org.springframework.web.client.RestTemplate;
import scala.Function2;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.ConnectionType.SOAP;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.connector.properties.RestConfigAccessor.PARTITION_KEY;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.http.BaseRequestSender.createSenderPool;
import static com.nexla.soap.SoapIterationOffset.TERMINAL_OFFSET;
import static java.lang.String.format;
import static java.util.Collections.EMPTY_LIST;
import static java.util.Collections.singletonMap;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;
import static org.joda.time.DateTimeZone.UTC;

public class SoapSourceTask extends BaseSourceTask<SoapSourceConnectorConfig> {

	private SoapIterationChainBuilder builder;
	private List<SoapIterationContext> callerContexts;
	private SoapIterationContext lastContext;
	private List<SoapIteration> restIterations;
	private Optional<DetailedFlowInsightsSender> flowInsightsSender;

	private AtomicInteger pollCounter = new AtomicInteger(0);

	public SoapSourceTask() {
	}

	public SoapSourceTask(SchemaDetectionUtils schemaDetection) {
		this.schemaDetection = schemaDetection;
	}

	@Override
	protected SoapSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new SoapSourceConnectorConfig(props);
	}

	protected DateTime now() {
		return DateTimeUtils.nowUTC();
	}

	@SneakyThrows
	@Override
	public CollectRecordsResult collectRecords() {
		if (config.runOnce && pollCounter.get() > 0) {
			logger.info("Task ran once, returning empty records");
			return new CollectRecordsResult(Collections.emptyList());
		}

		List<SourceRecord> sourceRecords = detectSchemaIfNecessary(false, processRecords(), Optional.empty());
		if (isNotEmpty(sourceRecords)) {
			lastContext.getLastResult().ifPresent(result ->
				sendMetrics(result.getUrl(), sourceRecords.size(), result.getByteCounter().get(), 0L,
					result.getMetric().getStartInMillis()));
		}

		return new CollectRecordsResult(sourceRecords);
	}

	@Override
	public ConfigDef configDef() {
		return SoapSourceConnectorConfig.configDef();
	}

	@Override
	public void doStart(Map<String, String> props) throws ConnectException {
		this.sourceTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sourceId, SOAP.name(), SOURCE, true));
		this.flowInsightsSender = Optional.ofNullable(DetailedFlowInsightsSender.from(
				config.logVerbose, getSuccessFlowInsightsSender(), getErrorFlowInsightsSender(), config.detailedFlowInsightsAbbreviatedLength));
		RefreshingTokenProvider tokenProvider = new RefreshingTokenProvider(adminApiClient, config.decryptKey);
		ScriptEvalClient scriptEvalClient = new ScriptEvalClient(config.probeAppUrl, config.nexlaUsername, config.nexlaPassword, restTemplate);

		NexlaPool<RequestSender> pool = createSenderPool(
			config.requestParallelismCount,
			// XXX: always does URL encoding
			() -> createSender(config.authConfig, tokenProvider, scriptEvalClient, config.logVerbose, false, flowInsightsSender)
				.withLoggerPrefix(logger.getPrefix(), "[request]"));

		this.builder = new SoapIterationChainBuilder(pool);
		this.restIterations = Lists.newArrayList(builder.buildIterations(config.restIterationConfig, logger));
		Optional<Map<String, Object>> offsetMap = getOffsetMap(context.offsetStorageReader(), restIterations);
		initContextChain(now());
	}

	private void initContextChain(DateTime dateTime) {
		this.callerContexts = builder.buildContexts(restIterations, dateTime);
		this.lastContext = callerContexts.get(callerContexts.size() - 1);
	}

	private Optional<Map<String, Object>> getOffsetMap(OffsetStorageReader offsetStorageReader, List<SoapIteration> restIterations) {
		SoapIteration lastCaller = restIterations.get(restIterations.size() - 1);
		Map<String, Object> offsetMap = offsetStorageReader.offset(singletonMap(PARTITION_KEY, lastCaller.getPartition()));
		return Optional.ofNullable(offsetMap);
	}

	@SneakyThrows
	private List<SourceRecordCreator> processRecords() {

		List<SourceRecordCreator> records = lastContext
			.iterateOverContext()
			.flatMap(result -> processResult(lastContext))
			.orElse(EMPTY_LIST);

		this.pollCounter.incrementAndGet();
		return records;
	}

	private Optional<List<SourceRecordCreator>> processResult(SoapIterationContext lastContext) {
		return lastContext.getLastResult().map(result -> {
			Map<String, String> contextOffsets = StreamEx.of(callerContexts)
				.filter(context -> context.getLastResult().isPresent())
				.toMap(
					context -> context.getRestIteration().getCode(),
					context -> context.getLastResult().get().getOffset().toJson()
				);
			List<SoapResultEntry> entries = result.getEntries();
			Stream<SourceRecordCreator> sourceRecordsStream = entries.stream()
				.map(entry -> {
					Map<String, String> sourceOffset = Maps.newHashMap(contextOffsets);

					// if SourceRecord has no next recordNumber, remember this special case marking it as TERMINAL_OFFSET
					SoapIterationOffset nextOffset = lastContext.getNextOffset().orElse(TERMINAL_OFFSET);
					sourceOffset.put(lastContext.getRestIteration().getCode(), nextOffset.toJson());
					return createRecord(
						lastContext.getRestIteration(),
						entry.getDataMap(),
						Long.valueOf(entry.getOffset()),
						entry.getNexlaMetaRangeFrom(),
						result.getUrl(),
						sourceOffset,
						result.getByteCounter());
				});

			return sourceRecordsStream.collect(Collectors.toList());
		});
	}

	private SourceRecordCreator createRecord(
		SoapIteration caller,
		LinkedHashMap<String, Object> dataMap,
		Long offset,
		Object nexlaMetaRangeFrom,
		String url,
		Map<String, ?> sourceOffset,
		AtomicLong byteCounter
	) {
		String sourceKey = nexlaMetaRangeFrom != null ? format("%s, from=%s", url, nexlaMetaRangeFrom) : url;
		long now = DateTime.now(UTC).getMillis();

		Function2<Integer, String, NexlaMessage> nexlaMessageCreator = (dataSetId, dataSetTopic) -> {
			SourceItem nexlaSourceTrackerIdItem = SourceItem.fullTracker(
				this.config.sourceId,
				dataSetId,
				new String(Base64.encodeBase64(url.getBytes())),
				offset != null ? offset : 0,
				this.config.version,
				now);

			NexlaMetaData metaData = new NexlaMetaData(
				SOAP,
				now,
				offset,
				sourceKey,
				dataSetTopic,
				SOURCE,
				config.sourceId,
				false,
				new Tracker(Tracker.TrackerMode.FULL, nexlaSourceTrackerIdItem),
				runId);

			return new NexlaMessage(dataMap, metaData);
		};

		Function2<Integer, String, SourceRecord> sourceRecordCreator = (dataSetId, dataSetTopic) -> {

			NexlaMessage message = nexlaMessageCreator.apply(dataSetId, dataSetTopic);
			byteCounter.addAndGet(calcBytes(message.toJsonString()));

			return new SourceRecord(singletonMap(PARTITION_KEY, caller.getPartition()), sourceOffset, dataSetTopic, null, null, null, STRING_SCHEMA, toJsonString(message));
		};

		return new SourceRecordCreator(dataMap, sourceRecordCreator, nexlaMessageCreator);
	}

}