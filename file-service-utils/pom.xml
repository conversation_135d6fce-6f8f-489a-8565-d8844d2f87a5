<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.nexla.probe</groupId>
    <artifactId>file-service-utils</artifactId>

    <parent>
        <groupId>com.nexla</groupId>
        <artifactId>backend-connectors</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <dependencies>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>telemetry</artifactId>
            <version>${nexla-backend-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>gcs-probe</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>azure-blob-probe</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>delta-lake-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>ftp-probe</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>dropbox-probe</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>box-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>gdrive-probe</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>webdav-probe</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>one-drive-probe</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>sharepoint-probe</artifactId>
            <version>${project.version}</version>
        </dependency>


    </dependencies>

</project>