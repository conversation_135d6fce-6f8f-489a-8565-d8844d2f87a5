package com.nexla.connector.sql.poll;

import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@UtilityClass
public final class TimestampUtils { // todo consider moving or renaming.
	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	public static long getTimestampForDateTime(String dateTimeAsString) {
		return LocalDateTime.parse(dateTimeAsString, DATE_TIME_FORMATTER)
				.atZone(ZoneOffset.UTC)
				.toEpochSecond() * 1000;
	}

	public static long getTimestampForDate(String dateAsString) {
		return LocalDate.parse(dateAsString, DATE_FORMATTER)
				.atStartOfDay()
				.atZone(ZoneOffset.UTC)
				.toEpochSecond() * 1000;
	}
}
