package com.nexla.connector.sql.poll;

import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.probe.sql.MinMaxHolder;
import com.nexla.probe.sql.SqlConnectorService;
import org.apache.kafka.connect.connector.ConnectorContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;

import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connect.common.NexlaConnectorUtils.publishException;

/**
 * Thread that monitors for changes to sql table that this connector should
 * load data from.
 */
class RebalancingTask {

	final ReentrantLock pkStateLock = new ReentrantLock();

	private final Logger logger;
	private final ConnectorContext context;

	private final SqlConnectorService probeService;
	private final JdbcSourceConnectorConfig config;
	private final ControlMessageProducer controlMessageProducer;

	private MinMaxHolder minMax;

	public RebalancingTask(
			ConnectorContext context,
			JdbcSourceConnectorConfig config,
			SqlConnectorService probeService,
			ControlMessageProducer controlMessageProducer
	) {
		this.context = context;

		this.config = config;
		this.probeService = probeService;
		this.controlMessageProducer = controlMessageProducer;

		this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SOURCE, config.sourceId, Optional.empty()));
	}

	public void run() {
		if (updateMinMax()) {
			context.requestTaskReconfiguration();
		}
	}

	public MinMaxHolder getMinMax() {
		pkStateLock.lock();
		try {
			if (minMax == null) {
				updateMinMax();
			}
			return minMax;
		} catch (Exception e) {
			logger.error("Get min-max error: ", e);
			publishException(controlMessageProducer, 0L, SOURCE, config.sourceId, 0L,
					config.table.orElseGet(() -> config.query.get()), getErrorDetails(e, Optional.empty()));
			throw e;
		} finally {
			pkStateLock.unlock();
		}
	}

	boolean updateMinMax() {
		pkStateLock.lock();
		try {
			MinMaxHolder dbMinMax = probeService.selectMinMax(config);

			if (this.minMax == null) {
				this.minMax = dbMinMax;
				return !this.minMax.isTableEmpty();

			} else {

				if (dbMinMax.isTableEmpty()) {
					return false;
				}

				if ((dbMinMax.getMaxIncrementingValue() != null) &&
					((minMax.getMaxIncrementingValue() == null) ||
					 (dbMinMax.getMaxIncrementingValue() > minMax.getMaxIncrementingValue()))) {

					minMax = dbMinMax;
					return true;
				}

				if ((dbMinMax.getMaxTimestamp() != null) &&
					((minMax.getMaxTimestamp() == null) ||
					 (dbMinMax.getMaxTimestamp().compareTo(minMax.getMaxTimestamp()) > 0))) {

					minMax = dbMinMax;
					return true;
				}

				return false;
			}

		} finally {
			pkStateLock.unlock();
		}
	}

	public void shutdown() {
		probeService.close();
	}
}
