name: Dockerhub ECR Republisher

on:
  workflow_dispatch:
    inputs:
      ecr_image_tag:
        type: string
        description: e.g. kafka-connect-file-source:v3.0.0-SNAPSHOT-v3.0.0-deaff00dd00d
        required: true

jobs:
  republish:
    runs-on: nexla-dind-runners
    steps:
      - name: Login to ECR
        uses: nexla/cloud-actions/actions/ecr-login@v1

      - name: Pull ECR image
        env:
          AWS_ECR_ACCOUNT_URL: ************.dkr.ecr.us-east-1.amazonaws.com
        shell: bash
        run: |
          # login into ECR with docker
          echo ${{ env.ECR_PASS_INTERACTIVE }} | docker login --username AWS --password-stdin ${{ env.AWS_ECR_ACCOUNT_URL }}
          # pull image from ECR
          docker pull ${{ env.AWS_ECR_ACCOUNT_URL }}/${{ github.event.inputs.ecr_image_tag }}

      - name: Push image to Dockerhub
        env:
          AWS_ECR_ACCOUNT_URL: ************.dkr.ecr.us-east-1.amazonaws.com
        shell: bash
        run: |
          # login into dockerhub
          echo ${{ secrets.DOCKERHUB_PASSWORD }} | docker login --username ${{ secrets.DOCKERHUB_USERNAME }} --password-stdin
          # tag image
          docker tag ${{ env.AWS_ECR_ACCOUNT_URL }}/${{ github.event.inputs.ecr_image_tag }} nexla/${{ github.event.inputs.ecr_image_tag }}
          # push image to dockerhub
          docker push nexla/${{ github.event.inputs.ecr_image_tag }}
