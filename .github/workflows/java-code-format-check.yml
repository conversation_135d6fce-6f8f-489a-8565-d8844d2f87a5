name: Java Code Format Check

on:
  pull_request:
    paths:
      - '**/*.java'

jobs:
  java-format-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          java-version: 17
          distribution: 'temurin'

      - name: Cache Google Java Format jar
        uses: actions/cache@v4
        with:
          path: .cache
          key: google-java-format-v1  # change version to v2 if JAR version changes

      # Add JAR download logic here (before pre-commit)
      - name: Download google-java-format jar if missing
        run: |
          VERSION="1.24.0"
          JAR=".cache/google-java-format-${VERSION}-all-deps.jar"
          mkdir -p .cache
          
          # Remove if corrupt
          if [ -f "$JAR" ] && ! file "$JAR" | grep -q 'Zip archive data'; then
            echo "Corrupt JAR detected. Deleting..."
            rm -f "$JAR"
          fi

          # Download if missing
          if [ ! -f "$JAR" ]; then
            echo "Downloading google-java-format $VERSION..."
            curl -L -o "$JAR" "https://github.com/google/google-java-format/releases/download/v${VERSION}/google-java-format-${VERSION}-all-deps.jar"
            chmod 755 "$JAR"
          fi

      - name: Run pre-commit hooks
        uses: pre-commit/action@v3.0.1