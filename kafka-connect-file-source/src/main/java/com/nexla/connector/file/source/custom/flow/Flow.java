package com.nexla.connector.file.source.custom.flow;

import com.nexla.connector.file.source.ReadBatchResult;
import org.apache.kafka.connect.source.SourceRecord;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class Flow {
    public abstract ReadBatchResult<SourceRecord> poll();
    public abstract void stop();

    protected ReadBatchResult<SourceRecord> merge(List<ReadBatchResult<SourceRecord>> results) {
        List<SourceRecord> messages = results
                .stream()
                .map(x -> x.messages)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        boolean archiveListingUpdated = results
                .stream()
                .anyMatch(x -> x.archiveListingUpdated);

        boolean listingIsEmpty = results
                .stream()
                .allMatch(x -> x.listingIsEmpty);

        return new ReadBatchResult<>(messages, null, archiveListingUpdated, listingIsEmpty);
    }
}
