package com.nexla.otel.sampler;

import static org.junit.jupiter.api.Assertions.*;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.context.Context;
import io.opentelemetry.sdk.trace.data.LinkData;
import io.opentelemetry.sdk.trace.samplers.Sampler;
import io.opentelemetry.sdk.trace.samplers.SamplingDecision;
import java.util.Collections;
import lombok.val;
import org.junit.jupiter.api.Test;

public class RuleBasedRoutingSamplerTest {

  @Test
  public void testShouldSampleWithMatchingRule() {
    val attributeKey = AttributeKey.stringKey("test.attribute");
    val pattern = "test.*";
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .recordAndSample(attributeKey, pattern)
            .build();

    val traceId = "00000000000000000000000000000001";
    val spanName = "testSpan";
    val attributes = Attributes.builder().put(attributeKey, "test-value").build();
    val parentLinks = Collections.<LinkData>emptyList();

    val result =
        sampler.shouldSample(Context.root(), traceId, spanName, spanKind, attributes, parentLinks);

    assertNotNull(result);
    assertEquals(SamplingDecision.RECORD_AND_SAMPLE, result.getDecision());
  }

  @Test
  public void testShouldSampleWithNonMatchingRule() {
    val attributeKey = AttributeKey.stringKey("test.attribute");
    val pattern = "test.*";
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .recordAndSample(attributeKey, pattern)
            .build();

    val traceId = "00000000000000000000000000000001";
    val spanName = "testSpan";
    val attributes = Attributes.builder().put(attributeKey, "non-matching-value").build();
    val parentLinks = Collections.<LinkData>emptyList();

    val result =
        sampler.shouldSample(Context.root(), traceId, spanName, spanKind, attributes, parentLinks);

    assertNotNull(result);
    assertEquals(SamplingDecision.DROP, result.getDecision());
  }

  @Test
  public void testShouldSampleWithDifferentSpanKind() {
    val attributeKey = AttributeKey.stringKey("test.attribute");
    val pattern = "test.*";
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .recordAndSample(attributeKey, pattern)
            .build();

    val traceId = "00000000000000000000000000000001";
    val spanName = "testSpan";
    val attributes = Attributes.builder().put(attributeKey, "test-value").build();
    val parentLinks = Collections.<LinkData>emptyList();
    val differentSpanKind = SpanKind.CLIENT;

    val result =
        sampler.shouldSample(
            Context.root(), traceId, spanName, differentSpanKind, attributes, parentLinks);

    assertNotNull(result);
    assertEquals(SamplingDecision.DROP, result.getDecision());
  }

  @Test
  public void testShouldSampleWithMissingAttribute() {
    val attributeKey = AttributeKey.stringKey("test.attribute");
    val pattern = "test.*";
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .recordAndSample(attributeKey, pattern)
            .build();

    val traceId = "00000000000000000000000000000001";
    val spanName = "testSpan";
    val attributes = Attributes.builder().build(); // No attributes
    val parentLinks = Collections.<LinkData>emptyList();

    val result =
        sampler.shouldSample(Context.root(), traceId, spanName, spanKind, attributes, parentLinks);

    // Assert
    assertNotNull(result);
    assertEquals(SamplingDecision.DROP, result.getDecision());
  }

  @Test
  public void testShouldSampleWithThreadNameAttribute() {
    val threadNameKey = AttributeKey.stringKey("thread.name");
    val pattern = ".*"; // Match any thread name
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .recordAndSample(threadNameKey, pattern)
            .build();

    val traceId = "00000000000000000000000000000001";
    val spanName = "testSpan";
    val attributes =
        Attributes.builder().build(); // No attributes needed as thread name is retrieved internally
    val parentLinks = Collections.<LinkData>emptyList();

    val result =
        sampler.shouldSample(Context.root(), traceId, spanName, spanKind, attributes, parentLinks);

    assertNotNull(result);
    assertEquals(SamplingDecision.RECORD_AND_SAMPLE, result.getDecision());
  }

  @Test
  public void testShouldSampleWithMultipleRules() {
    val attributeKey1 = AttributeKey.stringKey("test.attribute1");
    val attributeKey2 = AttributeKey.stringKey("test.attribute2");
    val pattern1 = "test1.*";
    val pattern2 = "test2.*";
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .drop(attributeKey1, pattern1)
            .recordAndSample(attributeKey2, pattern2)
            .build();

    val traceId = "00000000000000000000000000000001";
    val spanName = "testSpan";
    val attributes =
        Attributes.builder()
            .put(attributeKey1, "non-matching")
            .put(attributeKey2, "test2-value")
            .build();
    val parentLinks = Collections.<LinkData>emptyList();

    val result =
        sampler.shouldSample(Context.root(), traceId, spanName, spanKind, attributes, parentLinks);

    assertNotNull(result);
    assertEquals(SamplingDecision.RECORD_AND_SAMPLE, result.getDecision());
  }

  @Test
  public void testGetDescription() {
    val attributeKey = AttributeKey.stringKey("test.attribute");
    val pattern = "test.*";
    val spanKind = SpanKind.SERVER;
    val fallbackSampler = Sampler.alwaysOff();

    val sampler =
        RuleBasedRoutingSampler.builder(spanKind, fallbackSampler)
            .recordAndSample(attributeKey, pattern)
            .build();

    val description = sampler.getDescription();

    assertNotNull(description);
    assertTrue(description.contains("RuleBasedRoutingSampler"));
    assertTrue(description.contains("test.attribute"));
    assertTrue(description.contains("test.*"));
  }
}
