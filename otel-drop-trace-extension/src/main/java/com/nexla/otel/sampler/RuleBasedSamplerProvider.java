package com.nexla.otel.sampler;

import com.nexla.otel.sampler.config.DropRuleConfigReader;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.context.Context;
import io.opentelemetry.sdk.autoconfigure.spi.ConfigProperties;
import io.opentelemetry.sdk.autoconfigure.spi.traces.ConfigurableSamplerProvider;
import io.opentelemetry.sdk.trace.data.LinkData;
import io.opentelemetry.sdk.trace.samplers.Sampler;
import io.opentelemetry.sdk.trace.samplers.SamplingResult;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.logging.Logger;
import javax.annotation.Nonnull;
import lombok.val;

public class RuleBasedSamplerProvider implements ConfigurableSamplerProvider {
  private static final String ENV_RULE_SAMPLER_DROP_YAML_FILE = "otel.nexla.sampler.drop.conf.file";
  private static final Logger logger = Logger.getLogger(RuleBasedSamplerProvider.class.getName());
  private static final String name = "RuleBasedSamplerProvider";

  @Override
  public Sampler createSampler(@Nonnull ConfigProperties configProperties) {
    logger.info("Creating Sampler for " + name);
    val defaultSampler = Sampler.parentBased(Sampler.alwaysOn());
    val dropRules = readDropRulesFromYaml();
    val samplersBySpanKind = new HashMap<SpanKind, Sampler>();

    for (val entry : dropRules.entrySet()) {
      val spanKind = entry.getKey();
      val attributes = entry.getValue();
      val builder = RuleBasedRoutingSampler.builder(spanKind, defaultSampler);
      for (val e : attributes.entrySet()) {
        for (val pattern : e.getValue()) {
          builder.drop(e.getKey(), pattern);
        }
      }
      samplersBySpanKind.put(spanKind, builder.build());
    }

    return new Sampler() {
      @Override
      public SamplingResult shouldSample(
          @Nonnull Context context,
          @Nonnull String traceId,
          @Nonnull String name,
          @Nonnull SpanKind spanKind,
          @Nonnull Attributes attributes,
          @Nonnull List<LinkData> parentLinks) {
        val sampler = samplersBySpanKind.getOrDefault(spanKind, defaultSampler);
        return sampler.shouldSample(context, traceId, name, spanKind, attributes, parentLinks);
      }

      @Override
      public String getDescription() {
        return "CustomCompositeSampler";
      }
    };
  }

  @Override
  public String getName() {
    return name;
  }

  private Map<SpanKind, Map<AttributeKey<String>, Set<String>>> readDropRulesFromYaml() {
    var yamlFile =
        Optional.ofNullable(System.getProperty(ENV_RULE_SAMPLER_DROP_YAML_FILE))
            .orElse(System.getenv(ENV_RULE_SAMPLER_DROP_YAML_FILE.replace('.', '_').toUpperCase()));

    if (yamlFile == null || yamlFile.trim().isEmpty()) {
      logger.warning(
          "Neither system property nor environment variable "
              + ENV_RULE_SAMPLER_DROP_YAML_FILE
              + " is set.");
      return Map.of();
    }
    return new DropRuleConfigReader().readDropRulesFromYaml(Paths.get(yamlFile));
  }
}
