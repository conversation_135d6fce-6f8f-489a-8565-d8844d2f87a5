package com.nexla.probe.kafka;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.Lists;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.NexlaMessage;
import com.nexla.common.Resource;
import com.nexla.common.ui.PaginatedResult;
import com.nexla.connector.config.kafka.KafkaBackendReadConfig;
import com.nexla.kafka.control.listener.conf.NexlaKafkaConfigApplier;
import lombok.AllArgsConstructor;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndTimestamp;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.nexla.common.NexlaNamingUtils.getQuarantineTopic;
import static com.nexla.common.NexlaNamingUtils.nameDataSetTopic;
import static com.nexla.common.StreamUtils.jsonUtil;
import static com.nexla.common.TraceMessage.NX_RUN_TRACE;
import static com.nexla.common.datetime.DateTimeUtils.timed;
import static java.util.Collections.emptyMap;
import static java.util.Collections.shuffle;
import static java.util.Collections.singletonList;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.kafka.clients.consumer.ConsumerConfig.*;

@AllArgsConstructor
public class KafkaBackendReadService {

	private static final int KAFKA_POLL = 50;
	private static final int MAX_POLL_ITERATIONS = 100;

	private static final Logger LOGGER = LoggerFactory.getLogger(KafkaBackendReadService.class);

	/**
	 * Polls and returns last {@link KafkaBackendReadConfig#eventCount} messages
	 * from Kafka topic as {@link NexlaMessage} stream.
	 *
	 * If {@link KafkaBackendReadConfig#eventCount} isn't specified, by default
	 * {@link KafkaBackendReadConfig#DEFAULT_EVENT_COUNT} messages are being polled.
	 *
	 * In order to poll messages starting from specific time, {@link KafkaBackendReadConfig#startTimeMs}
	 * parameter has to be specified in the parameters list.
	 *
	 * Number of messages could be limited in two cases:
	 * 1. total number of unacked messages in the topic < eventCount
	 * 2. # of messages available starting from specific timestamp < eventCount
	 *
	 */
	public static StreamEx<NexlaMessage> readSamples(KafkaBackendReadConfig config, NexlaKafkaConfig kafkaConfig) {
		String topic = config.topic != null ? config.topic : nameDataSetTopic(config.resourceId);
		try (KafkaConsumer<String, String> consumer = getKafkaConsumerInstance(kafkaConfig, config)) {
			Map<String, List<PartitionInfo>> topicList = timed(() -> {
				Map<String, List<PartitionInfo>> topicListResult = consumer.listTopics();
				LOGGER.info("Searching for topic {}, result {}", topic, topicListResult.containsKey(topic));
				return topicListResult;
			}, "Get topics list");

			if (!topicList.containsKey(topic)) {
				return StreamEx.empty();
			}

			Integer eventCount = config.eventCount;
			List<TopicPartition> topicPartitions = assignConsumerPartition(config, topic, consumer);
			final Map<TopicPartition, Long> endOffsets = getTopicPartitionEndOffsets(topic, consumer);
			Map<TopicPartition, Long> beginOffsets = consumer.beginningOffsets(topicPartitions);
			AtomicLong recordCounts = new AtomicLong();

			Optional<Map<TopicPartition, OffsetAndTimestamp>> startOffsetMap = getStartOffsetMap(config, topic, consumer,
					recordCounts, endOffsets, beginOffsets);

			setConsumerOffsets(config, consumer, eventCount, config.latest, topicPartitions,
					startOffsetMap, empty(), of(endOffsets), of(beginOffsets));

			List<NexlaMessage> messages = timed(() -> getSampleMessagesFromTopic(consumer, config.eventCount), "Read samples");

			shuffle(messages);

			return StreamEx.of(messages.iterator());

		} catch (Exception e) {
			LOGGER.error("Error reading topic", e);
			throw new RuntimeException(e);
		}
	}

	public static PaginatedResult readQuarantine(KafkaBackendReadConfig config, NexlaKafkaConfig kafkaConfig) {
		String topic = getQuarantineTopic(new Resource(config.resourceId, config.resourceType), of(config.id));

		try (KafkaConsumer<String, String> consumer = getKafkaConsumerInstance(kafkaConfig, config)) {
			Map<String, List<PartitionInfo>> topicList = consumer.listTopics();

			if (!topicList.containsKey(topic)) {
				return new PaginatedResult<>();
			}

			try {
				AtomicLong recordCounts = new AtomicLong();

				List<TopicPartition> topicPartitions = assignConsumerPartition(config, topic, consumer);
				final Map<TopicPartition, Long> endOffsets = getTopicPartitionEndOffsets(topic, consumer);
				final Map<TopicPartition, Long> beginOffsets = consumer.beginningOffsets(topicPartitions);

				Optional<Map<TopicPartition, OffsetAndTimestamp>> startOffsetMap = getStartOffsetMap(config, topic, consumer,
					recordCounts, endOffsets, beginOffsets);

				Optional<Integer> startPageOffset = config.pageNumber.map(page -> (page - 1) * config.pageSize);
				List<Object> outputMessages = Collections.emptyList();
				if (recordCounts.get() > startPageOffset.orElse(0)) {
					Integer eventCount = config.pageNumber
							.map(page -> Math.min(config.pageSize, recordCounts.intValue()))
							.orElse(config.eventCount);

					setConsumerOffsets(config, consumer, eventCount, config.latest, topicPartitions,
							startOffsetMap, startPageOffset, of(endOffsets), of(beginOffsets));

					outputMessages = readFromQuarantine(config, consumer, eventCount);
				}

				return new PaginatedResult<>()
					.setData(outputMessages)
					.setMeta(config.pageNumber.orElse(1), recordCounts.intValue(), config.pageSize);

			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
	}

	private static List<Object> readFromQuarantine(KafkaBackendReadConfig config, KafkaConsumer<String, String> consumer, Integer eventCount) {
		List<Object> messages = readFromKafkaConsumer(consumer, eventCount)
			.stream()
			.filter(Objects::nonNull)
			.map(JsonUtils::jsonToObject)
			.collect(toList());

		if (config.sortOrderLatest) {
			return Lists.reverse(messages);
		} else {
			return messages;
		}
	}

	private static List<String> readFromKafkaConsumer(KafkaConsumer<String, String> consumer, Integer eventCount) {
		List<String> result = Lists.newArrayList();
		List<String> prevPoll = null;
		for (int i = 0; i < MAX_POLL_ITERATIONS; i++) {
			List<String> poll = StreamEx.of(consumer.poll(KAFKA_POLL).iterator())
				.map(ConsumerRecord::value)
				.toList();

			if (poll.isEmpty() && prevPoll != null && prevPoll.isEmpty()) {
				// two subsequent empty polls mean "no data"
				String topicName = consumer.assignment()
					.stream()
					.map(TopicPartition::topic)
					.distinct()
					.collect(Collectors.joining(","));

				LOGGER.info("Two subsequent empty polls for " + topicName);
				return result;
			}
			result.addAll(poll);
			if (result.size() >= eventCount) {
				return result.subList(0, eventCount);
			}
			prevPoll = poll;
		}
		return result;
	}

	/**
	 * Retrieves desired offsets for topics and partitions considering the existence
	 * of {@link KafkaBackendReadConfig#startTimeMs} value. If that values is present,
	 * offset is shifted according to it, based on KafkaConsumer lookup result.
	 *
	 * @return Map of {@link TopicPartition} and according {@link OffsetAndTimestamp} values.
	 */
	private static Optional<Map<TopicPartition, OffsetAndTimestamp>> getStartOffsetMap(
		KafkaBackendReadConfig config,
		String topic,
		KafkaConsumer<String, String> consumer,
		AtomicLong recordCounts,
		Map<TopicPartition, Long> endOffsets,
		Map<TopicPartition, Long> beginOffsets
	) {
		final Optional<Map<TopicPartition, OffsetAndTimestamp>> startOffsetMap;
		if (config.startTimeMs.isPresent()) {
			startOffsetMap = ofNullable(getPartitionOffsetForTimestamp(topic, consumer, config.startTimeMs.get()));

			Map<Integer, Long> startPartitionOffsets = startOffsetMap
				.map(startOffset -> EntryStream.of(startOffset)
					.mapKeyValue((topicPartition, offsetAndTimestamp) -> Pair.with(topicPartition.partition(), effectiveOffset(offsetAndTimestamp, endOffsets, topicPartition)))
					.collect(toMap(Pair::getValue0, Pair::getValue1))
				)
				.orElse(emptyMap());

			if (config.endTimeMs.isPresent()) {
				Map<TopicPartition, OffsetAndTimestamp> endOffsetMap = getPartitionOffsetForTimestamp(topic, consumer, config.endTimeMs.get());

				endOffsetMap
					.forEach((topicPartition, offsetAndTimestamp) -> recordCounts.addAndGet(effectiveOffset(offsetAndTimestamp, endOffsets, topicPartition) - startPartitionOffsets.get(topicPartition.partition())));

			} else {
				endOffsets
					.forEach((topicPartition, offset) -> recordCounts.addAndGet(offset - startPartitionOffsets.get(topicPartition.partition())));

			}

		} else {
			startOffsetMap = empty();
			endOffsets
				.forEach((key, value) -> recordCounts.addAndGet(value - beginOffsets.get(key)));
		}
		return startOffsetMap;
	}

	private static Map<TopicPartition, Long> getTopicPartitionEndOffsets(String topic, KafkaConsumer<String, String> consumer) {
		List<TopicPartition> allPartitions = consumer.partitionsFor(topic)
			.stream()
			.map(info -> new TopicPartition(info.topic(), info.partition()))
			.collect(toList());

		return consumer.endOffsets(allPartitions);
	}

	/**
	 * Look up the offsets for the given topics and partitions by timestamp. The returned offset
	 * for each partition is the earliest offset whose timestamp is greater than or equal to the
	 * given timestamp in the corresponding partition.
	 *
	 * @param topic name to perform the lookup in
	 * @param startTime - timestamp in millis as starting point of time for search
	 */
	private static Map<TopicPartition, OffsetAndTimestamp> getPartitionOffsetForTimestamp(String topic, KafkaConsumer<String, String> consumer, Long startTime) {
		Map<TopicPartition, Long> timestampMap = StreamEx.of(consumer.partitionsFor(topic))
			.toMap(partitionInfo -> new TopicPartition(partitionInfo.topic(), partitionInfo.partition()), info -> startTime);
		return consumer.offsetsForTimes(timestampMap);
	}

	private static void setConsumerOffsets(
		KafkaBackendReadConfig config,
		KafkaConsumer<String, String> consumer,
		Integer eventCount,
		boolean latest,
		List<TopicPartition> topicPartitions,
		Optional<Map<TopicPartition, OffsetAndTimestamp>> startOffsetMap,
		Optional<Integer> startPageOffset,
		Optional<Map<TopicPartition, Long>> endOffsetMap,
		Optional<Map<TopicPartition, Long>> beginOffsets
	) {
		if (startOffsetMap.isPresent() && endOffsetMap.isPresent()) {
			int startOffset = startPageOffset.orElse(0);
			startOffsetMap.get()
				.forEach((key, value) -> consumer.seek(key, (effectiveOffset(value, endOffsetMap.get(), key) + startOffset)));
		} else if (config.partition != null) {
			if (config.offset != null) {
				consumer.seek(topicPartitions.get(0), config.offset);
			} else if (latest) {
				Long lastOffset = consumer.endOffsets(topicPartitions).get(topicPartitions.get(0));
				// shift eventCount back
				consumer.seek(topicPartitions.get(0), Math.max(0, lastOffset - eventCount));
			} else {
				consumer.seekToBeginning(topicPartitions);
			}

		} else {
			if (latest) {
				seekConsumerToValidOffsets(consumer, topicPartitions, beginOffsets, eventCount);
			} else {
				consumer.seekToBeginning(topicPartitions);
			}
		}
	}

	private static long effectiveOffset(OffsetAndTimestamp offsetTime, Map<TopicPartition, Long> endOffsetMap, TopicPartition topicPartition) {
		return offsetTime == null ? endOffsetMap.get(topicPartition) : offsetTime.offset();
	}

	private static List<TopicPartition> assignConsumerPartition(
		KafkaBackendReadConfig config,
		String topic,
		KafkaConsumer<String, String> consumer
	) {

		List<TopicPartition> topicPartitions;
		if (config.partition != null) {
			topicPartitions = singletonList(new TopicPartition(topic, config.partition));
		} else {
			topicPartitions = consumer.partitionsFor(topic)
				.stream()
				.map(info -> new TopicPartition(info.topic(), info.partition()))
				.collect(toList());
		}

		consumer.assign(topicPartitions);
		return topicPartitions;
	}

	private static void seekConsumerToValidOffsets(
		KafkaConsumer<String, String> consumer,
		List<TopicPartition> topicPartitions,
		Optional<Map<TopicPartition, Long>> beginOffsets,
		Integer eventCount
	) {
		Map<TopicPartition, Long> endOffsets = consumer.endOffsets(topicPartitions);
		if (beginOffsets.isPresent()) {
			topicPartitions.forEach(p -> {
				long offsetNumber = Math.max(beginOffsets.get().get(p), endOffsets.get(p) - eventCount);
				consumer.seek(p, offsetNumber);
			});
		} else {
			topicPartitions.forEach(p -> consumer.seek(p, Math.max(0, endOffsets.get(p) - eventCount)));
		}
	}

	private static List<NexlaMessage> getSampleMessagesFromTopic(KafkaConsumer<String, String> consumer, Integer numberOfMessages) {
		return readFromKafkaConsumer(consumer, numberOfMessages)
			.stream()
			.map(KafkaBackendReadService::parseMessageSafe)
			.filter(Objects::nonNull)
			.limit(numberOfMessages)
			.collect(toList());
	}

	private static NexlaMessage parseMessageSafe(String record) {
		try {
			NexlaMessage msg = jsonUtil().stringToType(record, NexlaMessage.class);

			// trace message, should not be included in samples
			if (msg.getRawMessage() == null && record.contains(NX_RUN_TRACE)) {
				return null;
			}

			return msg;
		} catch (Exception e) {
			LOGGER.error("Error while parsing message, skipping", e);
			return null;
		}
	}


	/**
	 * Do not forget to close the consumer
	 */
	private static KafkaConsumer<String, String> getKafkaConsumerInstance(NexlaKafkaConfig kafkaConfig, KafkaBackendReadConfig config) {
		Map<String, Object> props = new HashMap<>();
		props.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.bootstrapServer);
		props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true");
		props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");
		props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
		// Using random ID so that there are no saved offsets
		String randomId = UUID.randomUUID().toString();
		props.put(ConsumerConfig.GROUP_ID_CONFIG, randomId);
		props.put(ConsumerConfig.CLIENT_ID_CONFIG, "sample" + randomId);
		if (config.fetchMaxBytes != null) {
			props.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, config.fetchMaxBytes);
		}
		if (config.maxPartitionFetchBytes != null) {
			props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, config.maxPartitionFetchBytes);
		}
		Map<String, Object> propsWithSsl = new NexlaKafkaConfigApplier(kafkaConfig).apply(props);
		return new KafkaConsumer<>(propsWithSsl);
	}

}
