<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>[%date{yyyy-MM-dd HH:mm:ss.SSS}] [${git_hash}] %highlight{%-5level} [%20.20thread] %cyan(%-40logger{40}) - %msg%n%throwable</pattern>
    </encoder>
  </appender>

  <!-- Specific loggers -->
  <logger name="com.nexla" level="debug" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="com.nexla.writer.JsonFileWriter" level="info" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="com.joestelmach.natty" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.kafka" level="info" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.kafka.connect" level="info" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.hadoop.io.compress.CodecPool" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="io.confluent.connect.json" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="io.confluent.kafka.serializers.json" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.spark.scheduler" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.spark.executor" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.spark.storage" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.spark.sql" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="org.apache.parquet.hadoop" level="warn" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <logger name="io.tabular.iceberg.connect" level="debug" additivity="false">
    <appender-ref ref="CONSOLE" />
  </logger>

  <!-- Root logger -->
  <root level="info">
    <appender-ref ref="CONSOLE" />
  </root>
</configuration>