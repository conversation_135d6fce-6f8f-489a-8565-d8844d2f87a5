package com.nexla.db.util;

import static com.nexla.db.util.DbOps.getJooq;

import com.mchange.v2.c3p0.ComboPooledDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.function.Consumer;
import org.jooq.CloseableDSLContext;
import org.jooq.DSLContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JooqDelayedCommit {
  private static final Logger logger = LoggerFactory.getLogger(JooqDelayedCommit.class);
  public static ScheduledExecutorService EXECUTOR_SERVICE =
      Executors.newSingleThreadScheduledExecutor();

  private final BlockingQueue<Consumer<DSLContext>> tasks = new LinkedBlockingDeque<>();

  private final int commitAtSize;
  private final ComboPooledDataSource connectionPool;
  private final int commitDelay;

  private ScheduledFuture<?> debounceTask;

  public JooqDelayedCommit(
      ComboPooledDataSource connectionPool, int commitDelay, int commitAtSize) {
    this.connectionPool = connectionPool;
    this.commitDelay = commitDelay;
    this.commitAtSize = commitAtSize;
  }

  public void withJooq(Consumer<DSLContext> block) {
    tasks.add(block);

    Optional.ofNullable(debounceTask).ifPresent(s -> s.cancel(false));

    if (tasks.size() >= commitAtSize) {
      execute();
      return;
    }

    this.debounceTask =
        EXECUTOR_SERVICE.schedule(this::execute, commitDelay, TimeUnit.MILLISECONDS);
  }

  public void execute() {
    List<Consumer<DSLContext>> tasksToExec = currentTasks();
    if (tasksToExec.isEmpty()) {
      return;
    }

    try (Connection c = connectionPool.getConnection();
        CloseableDSLContext jooq = getJooq(c)) {
      tasksToExec.forEach(fn -> fn.accept(jooq));

      c.commit();
    } catch (SQLException e) {
      logger.error("Failed to execute tasks", e);
    }
  }

  private List<Consumer<DSLContext>> currentTasks() {
    List<Consumer<DSLContext>> objects = new ArrayList<>();
    // thread-safe via internal lock
    tasks.drainTo(objects);
    return objects;
  }
}
