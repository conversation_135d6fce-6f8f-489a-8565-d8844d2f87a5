package com.nexla.db.util;

import static one.util.streamex.StreamEx.of;
import static org.jooq.conf.ParamType.INLINED;
import static org.jooq.impl.DSL.field;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.Time;
import java.time.LocalDateTime;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.joda.time.DateTime;
import org.jooq.*;
import org.jooq.impl.DSL;

public class DbUtils {

  private static final String REMOVE_DEFAULT = "default CURRENT_TIMESTAMP(3)";

  public static void recreate(Table table, DSLContext jooq) {
    jooq.dropTableIfExists(table).execute();
    of(jooq.ddl(table).queries())
        .map(q -> q.getSQL(INLINED).replace(REMOVE_DEFAULT, ""))
        .forEach(jooq::execute);
  }

  public static void create(Table table, DSLContext jooq) {
    of(jooq.ddl(table).queries())
        .map(q -> q.getSQL(INLINED).replace(REMOVE_DEFAULT, ""))
        .forEach(jooq::execute);
  }

  @SneakyThrows
  public static String getPostgresSqlScript() {
    return fetchResource("schema_postgres.sql");
  }

  @SneakyThrows
  public static String getOracleSqlScript() {
    return fetchResource("schema_oracle.sql");
  }

  @SneakyThrows
  public static String getMysqlSqlScriptMetrics() {
    return fetchResource("schema.sql");
  }

  public static SQLDialect calculateDialect(String url) {
    if (url.contains("postgres")) {
      return SQLDialect.POSTGRES;
    } else if (url.contains("mysql")) {
      return SQLDialect.MYSQL;
      //		} else if (url.contains("oracle")) {
      // todo oracle - uncomment
      //			return SQLDialect.ORACLE;
    } else {
      throw new IllegalArgumentException();
    }
  }

  public static boolean isOracle(SQLDialect sqlDialect) {
    return false;
    // todo oracle - uncomment
    // return sqlDialect == SQLDialect.ORACLE;
  }

  public static boolean isPostgres(SQLDialect sqlDialect) {
    return sqlDialect == SQLDialect.POSTGRES;
  }

  @SneakyThrows
  public static String getOracleSqlScriptMetrics() {
    return fetchResource("schema_oracle_metrics.sql");
  }

  public static Field<Long> unixTs(Field<LocalDateTime> arg, SQLDialect dialect) {
    if (isOracle(dialect)) {
      return field(
          "round((cast({0} as date) - date '1970-01-01') * 24 * 60 * 60 * 1000)", Long.class, arg);
    } else if (isPostgres(dialect)) {
      return field("date_part('epoch', date({0}))", Long.class, arg);
    } else {
      return field("UNIX_TIMESTAMP({0})", Long.class, arg).mul(1000);
    }
  }

  public static Field<LocalDateTime> timestampField(DateTime dayFrom, SQLDialect dialect) {
    if (isOracle(dialect)) {
      return field(
          "TO_TIMESTAMP({0}, 'YYYY-MM-DD HH24:mi:ss')",
          LocalDateTime.class, dayFrom.toString("yyyy-MM-dd HH:mm:ss"));
    } else {
      return field("'" + dayFrom.toString("yyyy-MM-dd HH:mm:ss") + "'", LocalDateTime.class);
    }
  }

  public static Field<LocalDateTime> truncToDate(Field<LocalDateTime> ts, SQLDialect dialect) {
    if (isOracle(dialect) || isPostgres(dialect)) {
      return DSL.trunc(ts);
    } else {
      return field(
          "TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(UNIX_TIMESTAMP({0})), '%Y-%m-%d'))",
          LocalDateTime.class, ts);
    }
  }

  public static Field<LocalDateTime> trunc(
      Field<LocalDateTime> ts, SQLDialect dialect, DatePart datePart) {
    if (isOracle(dialect) || isPostgres(dialect)) {
      return DSL.trunc(ts, datePart);
    } else {
      String format;
      switch (datePart) {
        case DAY:
          format = "%Y-%m-%d";
          break;
        case HOUR:
          format = "%Y-%m-%d %H";
          break;
        case MINUTE:
          format = "%Y-%m-%d %H:%i";
          break;
        default:
          throw new IllegalArgumentException(String.format("Unimplemented for %s", datePart));
      }
      return field(
          String.format("TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(UNIX_TIMESTAMP({0})), '%s'))", format),
          LocalDateTime.class,
          ts);
    }
  }

  public static Field<Time> timeField(Field<LocalDateTime> time, SQLDialect dialect) {
    if (isOracle(dialect)) {
      return field(
          "to_timestamp('1970-01-01 ' || to_char({0}, 'HH24:mi:ss'), 'YYYY-MM-DD HH24:mi:ss')",
          Time.class, time);
    } else if (isPostgres(dialect)) {
      return field("{0}::timestamp::time", Time.class, time);
    } else {
      return field("time({0})", Time.class, time);
    }
  }

  private static String fetchResource(String resourceName) throws IOException {
    try (InputStream reader = DbUtils.class.getClassLoader().getResourceAsStream(resourceName)) {
      return IOUtils.toString(reader, StandardCharsets.UTF_8);
    }
  }
}
