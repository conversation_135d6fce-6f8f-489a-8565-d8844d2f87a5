package connect.jdbc.sink.dialect.copy;

import com.nexla.common.NexlaMessage;
import com.nexla.common.sink.TopicPartition;
import com.nexla.common.tracker.Tracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.SinkConnectorConfig;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.config.SinkConnectorConfig.*;
import static com.nexla.connector.properties.SqlConfigAccessor.CDC_ENABLED;

public class CopyOperationLocalBufferTest {

    private Map<String, String> stubCfg() {
        Map<String, String> cfg = new HashMap<>();
        cfg.put(SINK_ID, "123");
        cfg.put(DATASET_ID, "345");
        cfg.put(VERSION, "99");

        cfg.put(CREDS_ENC, "encoded");
        cfg.put(CREDS_ENC_IV, "encoded-IV");
        cfg.put(CREDS_ID, "9123");

        cfg.put(CDC_ENABLED, "false");
        cfg.put(MAPPING, "{\n" +
                "                    \"mapping\": {\n" +
                "                        \"assetname\": {\n" +
                "                            \"assetname\": \"VARCHAR(4096)\"\n" +
                "                        }\n" +
                "                    },\n" +
                "                    \"mode\": \"manual\",\n" +
                "                    \"tracker_mode\": \"FULL\"\n" +
                "                }");
        cfg.put(TRACKER_ENCRYPTION_ENABLED, "false");
        cfg.put(TRACKER_ENCRYPTION_KEY, "NONE");
        cfg.put(NODE_TAG, "tagg");
        cfg.put(INACTIVITY_TIMEOUT_BEFORE_FLUSH_MIN, "1");
        cfg.put(FIRST_FLUSH_DELAY_MINUTES, "1");
        cfg.put(FLUSH_COMBINE_RUN_IDS, "true");
        return cfg;
    }

    @Test
    public void testUsageOfCustomTrackerPattern() {
        Map<String, String> orig = stubCfg();
        String trackerValue = "5010:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010";
        orig.put(SinkConnectorConfig.FIXED_TRACKER_VALUE, trackerValue);
        SinkConnectorConfig cfg = new SinkConnectorConfig(orig);
        CopyOperationLocalBuffer instance = new CopyOperationLocalBuffer(cfg);

        LinkedHashMap<String, Object> lhm = new LinkedHashMap<>();
        lhm.put("a", "b");
        NexlaMessage msg = new NexlaMessage();
        msg.setRawMessage(lhm);
        TopicPartition tp = new TopicPartition("topic", 23);
        NexlaMessageContext context = new NexlaMessageContext(msg, msg, tp, 191921L);

        List<NexlaMessage> expected = Stream.of(context).map(NexlaMessageContext::getOriginal).collect(Collectors.toList());
        instance.write(List.of(context), List.of(context), 1);
        List<NexlaMessage> actual = instance.getRecords().collect(Collectors.toList());
        assert (actual.equals(expected));
        Tracker fromBuf = expected.get(0).getNexlaMetaData().getTrackerId();
        assert(fromBuf.getSets().size() == 2);
        assert(fromBuf.getSink().getId() == 4444); // even though in the props it was 123
    }

    @Test
    public void testNoCfgResultsInUsualBehavior() {
        Map<String, String> orig = stubCfg();
        SinkConnectorConfig cfg = new SinkConnectorConfig(orig);
        CopyOperationLocalBuffer instance = new CopyOperationLocalBuffer(cfg);

        LinkedHashMap<String, Object> lhm = new LinkedHashMap<>();
        lhm.put("a", "b");
        NexlaMessage msg = new NexlaMessage();
        msg.setRawMessage(lhm);
        TopicPartition tp = new TopicPartition("topic", 23);
        NexlaMessageContext context = new NexlaMessageContext(msg, msg, tp, 191921L);

        List<NexlaMessage> expected = Stream.of(context).map(NexlaMessageContext::getOriginal).collect(Collectors.toList());
        instance.write(List.of(context), List.of(context), 1);
        List<NexlaMessage> actual = instance.getRecords().collect(Collectors.toList());
        assert (actual.equals(expected));
        assert(actual.get(0).getNexlaMetaData().getTrackerId() == null); // as there's nothing in the metadata
    }
}
