package connect.jdbc.sink.dialect.copy.exception;

import com.nexla.common.NexlaMessage;
import org.javatuples.Pair;
import org.junit.Test;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

public class OracleAutonomousUtilityEntitiesToNexlaMessageMapperTest {
	private final OracleAutonomousUtilityEntitiesToNexlaMessageMapper messageMapper
			= new OracleAutonomousUtilityEntitiesToNexlaMessageMapper();

	@Test
	public void mapsBadFileToNexlaMessage_allBatchErrored() {
		final List<String> badBodies = new ArrayList<>();
		badBodies.add("123;Mark;2");
		badBodies.add("125;John;3");

		final List<NexlaMessage> nexlaMessageList = new ArrayList<>();
		LinkedHashMap<String, Object> value1 = new LinkedHashMap<>();
		value1.put("id", 123);
		value1.put("name", "Mark");
		value1.put("goods", "2");
		LinkedHashMap<String, Object> value2 = new LinkedHashMap<>();
		value2.put("id", 125);
		value2.put("name", "John");
		value2.put("goods", "3");

		nexlaMessageList.add(new NexlaMessage(value1));
		nexlaMessageList.add(new NexlaMessage(value2));


		List<Pair<String, Optional<NexlaMessage>>> badBodyToNexlaMessageMapping =
				messageMapper.createBadBodyToNexlaMessageMapping(nexlaMessageList, badBodies);
		assertThat(badBodyToNexlaMessageMapping).hasSize(2);
		Pair<String, Optional<NexlaMessage>> firstOne = badBodyToNexlaMessageMapping.get(0);
		assertThat(firstOne.getValue1()).isPresent();
		assertThat(firstOne.getValue1().get()).isEqualTo(new NexlaMessage(value1));
		assertThat(firstOne.getValue0()).contains("123");

		Pair<String, Optional<NexlaMessage>> secondOne = badBodyToNexlaMessageMapping.get(1);
		assertThat(secondOne.getValue1()).isPresent();
		assertThat(secondOne.getValue1().get()).isEqualTo(new NexlaMessage(value2));
		assertThat(secondOne.getValue0()).contains("125");
	}

	@Test
	public void mapsBadFileToNexlaMessage_oneErrorPerBatch() {
		final List<String> badBodies = new ArrayList<>();
		badBodies.add("123;Mark;2");

		final List<NexlaMessage> nexlaMessageList = new ArrayList<>();
		LinkedHashMap<String, Object> value1 = new LinkedHashMap<>();
		value1.put("id", 123);
		value1.put("name", "Mark");
		value1.put("goods", "2");
		LinkedHashMap<String, Object> value2 = new LinkedHashMap<>();
		value2.put("id", 125);
		value2.put("name", "John");
		value2.put("goods", "3");
		LinkedHashMap<String, Object> value3 = new LinkedHashMap<>();
		value3.put("id", 126);
		value3.put("name", "Kate");
		value3.put("goods", "13");
		LinkedHashMap<String, Object> value4 = new LinkedHashMap<>();
		value4.put("id", 127);
		value4.put("name", "Mark");
		value4.put("goods", "7");
		LinkedHashMap<String, Object> value5 = new LinkedHashMap<>();
		value5.put("id", 387);
		value5.put("name", "Mike");
		value5.put("goods", "1");

		nexlaMessageList.add(new NexlaMessage(value1));
		nexlaMessageList.add(new NexlaMessage(value2));
		nexlaMessageList.add(new NexlaMessage(value3));
		nexlaMessageList.add(new NexlaMessage(value4));
		nexlaMessageList.add(new NexlaMessage(value5));


		List<Pair<String, Optional<NexlaMessage>>> badBodyToNexlaMessageMapping =
				messageMapper.createBadBodyToNexlaMessageMapping(nexlaMessageList, badBodies);
		assertThat(badBodyToNexlaMessageMapping).hasSize(1);
		Pair<String, Optional<NexlaMessage>> firstOne = badBodyToNexlaMessageMapping.get(0);
		assertThat(firstOne.getValue1()).isPresent();
		assertThat(firstOne.getValue1().get()).isEqualTo(new NexlaMessage(value1));
		assertThat(firstOne.getValue0()).contains("123");
	}

	@Test
	public void mapsBadFileToNexlaMessage_cantMap() {
		final List<String> badBodies = new ArrayList<>();
		badBodies.add("123;Mark;2");

		final List<NexlaMessage> nexlaMessageList = new ArrayList<>();
		LinkedHashMap<String, Object> value1 = new LinkedHashMap<>();
		value1.put("id", 123);
		value1.put("name", "Mark");
		value1.put("goods", "2");
		LinkedHashMap<String, Object> value2 = new LinkedHashMap<>();
		value2.put("id", 125);
		value2.put("name", "John");
		value2.put("goods", "3");
		LinkedHashMap<String, Object> value3 = new LinkedHashMap<>();
		value3.put("id", 126);
		value3.put("name", "Kate");
		value3.put("goods", "123");
		LinkedHashMap<String, Object> value4 = new LinkedHashMap<>();
		value4.put("id", 2);
		value4.put("name", "Mark");
		value4.put("goods", "7");
		LinkedHashMap<String, Object> value5 = new LinkedHashMap<>();
		value5.put("id", 387);
		value5.put("name", "Mike");
		value5.put("goods", "1");

		nexlaMessageList.add(new NexlaMessage(value1));
		nexlaMessageList.add(new NexlaMessage(value2));
		nexlaMessageList.add(new NexlaMessage(value3));
		nexlaMessageList.add(new NexlaMessage(value4));
		nexlaMessageList.add(new NexlaMessage(value5));


		List<Pair<String, Optional<NexlaMessage>>> badBodyToNexlaMessageMapping =
				messageMapper.createBadBodyToNexlaMessageMapping(nexlaMessageList, badBodies);
		assertThat(badBodyToNexlaMessageMapping).hasSize(1);
		Pair<String, Optional<NexlaMessage>> firstOne = badBodyToNexlaMessageMapping.get(0);
		assertThat(firstOne.getValue1()).isEmpty();
	}
}
