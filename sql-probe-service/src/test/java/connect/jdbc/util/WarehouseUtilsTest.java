package connect.jdbc.util;

import com.nexla.common.ConnectionType;
import com.nexla.common.storage.WarehouseTempStorageType;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.test.UnitTests;
import connect.jdbc.sink.dialect.copy.storage.SnowflakeCopyAzureBlobStorage;
import connect.jdbc.sink.dialect.copy.storage.SnowflakeCopyS3Storage;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyS3Storage;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.nexla.common.ConnectionType.REDSHIFT;
import static com.nexla.common.ConnectionType.SNOWFLAKE;
import static com.nexla.common.NexlaConstants.AZURE_CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaConstants.S3_CREDS_ENC;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaConstants.SOURCE_ID;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.TEMP_AZURE_BLOB_DELETE;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.TEMP_AZURE_BLOB_UPLOAD_BUCKET;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.TEMP_AZURE_BLOB_UPLOAD_PREFIX;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.TEMP_S3_DELETE;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.TEMP_S3_UPLOAD_BUCKET;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.TEMP_S3_UPLOAD_PREFIX;
import static com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig.TEMP_STORAGE_TYPE;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Category(UnitTests.class)
class WarehouseUtilsTest {

	@Test
	void testGetCopyOperationTempStorageForJdbcSourceWhenDefault() {
		var tempUploadBucket = "/test/upload-bucket";
		var deleteTempBucket = Boolean.FALSE;
		var sourceConfig = getSourceConfig(REDSHIFT, tempUploadBucket, deleteTempBucket, false);
		var tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig);

		assertTrue(tempStorage instanceof WarehouseCopyS3Storage);
		assertEquals(tempUploadBucket, tempStorage.getTempUploadBucket());
		assertEquals(deleteTempBucket, tempStorage.getDeleteTempBucket());
		assertNull(tempStorage.getTempUploadPrefix());
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSourceWhenS3() {
		var tempUploadBucket = "/test/upload-bucket";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSourceConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, false);
		var tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig);

		assertTrue(tempStorage instanceof SnowflakeCopyS3Storage);
		assertEquals(tempUploadBucket, tempStorage.getTempUploadBucket());
		assertEquals(deleteTempBucket, tempStorage.getDeleteTempBucket());
		assertNull(tempStorage.getTempUploadPrefix());
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSourceWhenS3AndConfigIsNull() {
		var tempUploadBucket = "/test/upload-bucket";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSourceConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, false);
		sourceConfig.s3AuthConfig = null;
		assertThrows(IllegalArgumentException.class, () -> WarehouseUtils.getCopyOperationTempStorage(sourceConfig));
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSourceWhenAzureBlob() {
		var tempUploadBucket = "/test/upload-bucket";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSourceConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, true);
		var tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig);

		assertTrue(tempStorage instanceof SnowflakeCopyAzureBlobStorage);
		assertEquals(tempUploadBucket, tempStorage.getTempUploadBucket());
		assertEquals(deleteTempBucket, tempStorage.getDeleteTempBucket());
		assertNull(tempStorage.getTempUploadPrefix());
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSourceWhenAzureBlobAndConfigIsNull() {
		var tempUploadBucket = "/test/upload-bucket";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSourceConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, true);
		sourceConfig.azureAuthConfig = null;
		assertThrows(IllegalArgumentException.class, () -> WarehouseUtils.getCopyOperationTempStorage(sourceConfig));
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSinkWhenDefault() {
		var tempUploadBucket = "/test/upload-bucket";
		var tempUploadPrefix = "prefix";
		var deleteTempBucket = Boolean.FALSE;
		var sourceConfig = getSinkConfig(REDSHIFT, tempUploadBucket, deleteTempBucket, false, tempUploadPrefix);
		var tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig);

		assertTrue(tempStorage instanceof WarehouseCopyS3Storage);
		assertEquals(tempUploadBucket, tempStorage.getTempUploadBucket());
		assertEquals(deleteTempBucket, tempStorage.getDeleteTempBucket());
		assertEquals(tempUploadPrefix, tempStorage.getTempUploadPrefix());
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSinkWhenS3() {
		var tempUploadBucket = "/test/upload-bucket";
		var tempUploadPrefix = "prefix";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSinkConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, false, tempUploadPrefix);
		var tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig);

		assertTrue(tempStorage instanceof SnowflakeCopyS3Storage);
		assertEquals(tempUploadBucket, tempStorage.getTempUploadBucket());
		assertEquals(deleteTempBucket, tempStorage.getDeleteTempBucket());
		assertEquals(tempUploadPrefix, tempStorage.getTempUploadPrefix());
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSoinkWhenS3AndConfigIsNull() {
		var tempUploadBucket = "/test/upload-bucket";
		var tempUploadPrefix = "prefix";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSinkConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, false, tempUploadPrefix);
		sourceConfig.s3AuthConfig = null;
		assertThrows(IllegalArgumentException.class, () -> WarehouseUtils.getCopyOperationTempStorage(sourceConfig));
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSinkWhenAzureBlob() {
		var tempUploadBucket = "/test/upload-bucket";
		var tempUploadPrefix = "prefix";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSinkConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, true, tempUploadPrefix);
		var tempStorage = WarehouseUtils.getCopyOperationTempStorage(sourceConfig);

		assertTrue(tempStorage instanceof SnowflakeCopyAzureBlobStorage);
		assertEquals(tempUploadBucket, tempStorage.getTempUploadBucket());
		assertEquals(deleteTempBucket, tempStorage.getDeleteTempBucket());
		assertEquals(tempUploadPrefix, tempStorage.getTempUploadPrefix());
	}

	@Test
	void testGetCopyOperationTempStorageForJdbcSinkWhenAzureBlobAndConfigIsNull() {
		var tempUploadBucket = "/test/upload-bucket";
		var tempUploadPrefix = "prefix";
		var deleteTempBucket = Boolean.TRUE;
		var sourceConfig = getSinkConfig(SNOWFLAKE, tempUploadBucket, deleteTempBucket, true, tempUploadPrefix);
		sourceConfig.azureAuthConfig = null;
		assertThrows(IllegalArgumentException.class, () -> WarehouseUtils.getCopyOperationTempStorage(sourceConfig));
	}

	private JdbcSourceConnectorConfig getSourceConfig(
			ConnectionType connectionType,
			String tempUploadBucket,
			boolean deleteTempBucket,
			boolean azureCreds) {

		var paramsMock = azureCreds
				? getAzureParams(connectionType, tempUploadBucket, deleteTempBucket, "")
				: getS3Params(connectionType, tempUploadBucket, deleteTempBucket, "");

		return new JdbcSourceConnectorConfig(paramsMock);
	}

	private JdbcSinkConnectorConfig getSinkConfig(
			ConnectionType connectionType,
			String tempUploadBucket,
			boolean deleteTempBucket,
			boolean azureCreds,
			String tempUploadPrefix) {

		var paramsMock = azureCreds
				? getAzureParams(connectionType, tempUploadBucket, deleteTempBucket, tempUploadPrefix)
				: getS3Params(connectionType, tempUploadBucket, deleteTempBucket, tempUploadPrefix);

		return new JdbcSinkConnectorConfig(paramsMock);
	}

	private Map<String, String> getS3Params(
			ConnectionType connectionType,
			String tempUploadBucket,
			boolean deleteTempBucket,
			String tempUploadPrefix
	) {
		return Map.of(
				UNIT_TEST, Boolean.TRUE.toString(),
				SOURCE_ID, "1",
				SINK_ID, "1",
				INSERT_MODE, "INSERT",
				CREDENTIALS_TYPE, connectionType.name().toLowerCase(),
				S3_CREDS_ENC, "",
				TEMP_S3_UPLOAD_BUCKET, tempUploadBucket,
				TEMP_S3_DELETE, String.valueOf(deleteTempBucket),
				TEMP_S3_UPLOAD_PREFIX, tempUploadPrefix,
				TEMP_STORAGE_TYPE, WarehouseTempStorageType.AMAZON_S3.name());
	}

	private Map<String, String> getAzureParams(
			ConnectionType connectionType,
			String tempUploadBucket,
			boolean deleteTempBucket,
			String tempUploadPrefix
	) {
		return Map.of(
				UNIT_TEST, Boolean.TRUE.toString(),
				SOURCE_ID, "1",
				SINK_ID, "1",
				INSERT_MODE, "INSERT",
				CREDENTIALS_TYPE, connectionType.name().toLowerCase(),
				AZURE_CREDS_ENC, "",
				TEMP_AZURE_BLOB_UPLOAD_BUCKET, tempUploadBucket,
				TEMP_AZURE_BLOB_DELETE, String.valueOf(deleteTempBucket),
				TEMP_AZURE_BLOB_UPLOAD_PREFIX, tempUploadPrefix,
				TEMP_STORAGE_TYPE, WarehouseTempStorageType.AZURE_BLOB.name());
	}
}