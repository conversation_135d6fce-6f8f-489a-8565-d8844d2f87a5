package com.nexla.probe.sql.connection;

import com.nexla.common.ConnectionType;
import com.nexla.common.RSAKeyUtils;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.AthenaAuthConfig;
import com.nexla.connector.config.file.NexlaAWSCredentialsProvider;
import com.nexla.connector.config.file.SpannerAuthConfig;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DialectRegistry;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.*;

import static com.nexla.common.ConnectionType.*;
import static com.nexla.connector.config.file.IamAuth.iamAuthToken;
import static com.nexla.connector.config.file.S3Constants.*;
import static com.nexla.connector.config.ssh.tunnel.SshTunnel.IPV4_PATTERN;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.joining;

public abstract class ConnectionStrategy {

	public static final Logger logger = LoggerFactory.getLogger(ConnectionStrategy .class);

	public abstract Connection getConnection(JdbcAuthConfig authConfig);

	public abstract void disconnect();

	public static ConnectionStrategy getInstance(JdbcAuthConfig jdbcAuthConfig) {
		if ((SNOWFLAKE.equals(jdbcAuthConfig.dbType) && jdbcAuthConfig.snowflakeRSAConfig.isPresent())
        || jdbcAuthConfig.dbType.equals(REDSHIFT)){
			logger.info("Using single connection strategy.");
			return new SingleConnection();
		}
		logger.info("Using pooled connection strategy.");
		return new PooledConnection();
	}

	protected String getUrl(JdbcAuthConfig config) {
		return getUrl(config, ConnectionTlsStrategy.Tls12);
	}

	protected String getUrl(JdbcAuthConfig config, ConnectionTlsStrategy tlsStrategy) {

		ConnectionType connectionType = config.dbType;
		if (config.url != null) {
			return config
					.sshTunnelConfig
					.flatMap(notUsed -> JdbcAuthConfig
							.extractHostPortFromUrl(config.getCredsId(), config.url)
							.map(hostPort -> {
									 // for sql tunneling there is only 1 port forwarding
									 String result = config.url.replaceFirst(":" + hostPort.getPort(), ":" + config.getSshTunnelPorts().get(0));
									 // Use localhost only if host is an IP address. Don't replace otherwise, host will be redirected by MAPPED_HOST_RESOLVER in SshTunnel class
									 if (IPV4_PATTERN.matcher(hostPort.getHost()).matches()) {
										 result = result.replace(hostPort.getHost(), "localhost");
									 }
									 return result;
								 }
							))
					.orElse(config.url);
		}

		if (Objects.equals(TERADATA, connectionType)) {
			return getTeradataUrl(config);
		}

		if (Objects.equals(config.dbType, GCP_SPANNER)) {
			return getSpannerUrl(config);
		}
		
		if (Objects.equals(config.dbType, AWS_ATHENA)) {
			return getAthenaUrl(config);
		}

		String url = config.sshTunnelConfig
				.map(notUsed ->{
						// Use localhost only if host is an IP address. Don't replace otherwise, host will be redirected by MAPPED_HOST_RESOLVER in SshTunnel class
						// for sql tunneling there is only 1 port forwarding
						 String host = config.host != null && IPV4_PATTERN.matcher(config.host).matches() ? "localhost" : config.host;
						 return connectionType.connectionStringPrefix + host + ":" + config.getSshTunnelPorts().get(0);
					 })
				.orElse(connectionType.connectionStringPrefix + config.host + ofNullable(config.port).map(x -> ":" + x).orElse(""));

		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionString(connectionType, url);

		Map<String, String> parameters = new LinkedHashMap<>();

		if (config.dbType.equals(SNOWFLAKE)) {
			withSnowflakeParams(config, parameters);
		} else if (StringUtils.isNotEmpty(config.databaseName)) {
			url += dbDialect.getJdbcDatabaseName(config.databaseName);
		}

		if (dbDialect.getWarehouseParameterInConnectionString().isPresent() && !StringUtils.isEmpty(config.warehouseName)) {
			parameters.put(dbDialect.getWarehouseParameterInConnectionString().get(), config.warehouseName);
		}

		// Add schema parameter to URL for databases that support it
		// For PostgreSQL, we'll handle the search_path separately in addSchemaToConnection
		if (dbDialect.getSchemaParameterInConnectionString().isPresent() &&
			!StringUtils.isEmpty(config.schemaName) &&
			!ConnectionType.POSTGRES.equals(config.dbType)) {
			parameters.put(dbDialect.getSchemaParameterInConnectionString().get(), config.schemaName);
		}

		if (config.dbType.equals(REDSHIFT)) {
			parameters.put("ssl", "true");
		}

		if (config.dbType.equals(MYSQL)) {
			parameters.put("rewriteBatchedStatements", "true");
			if (tlsStrategy == ConnectionTlsStrategy.Tls12) {
				parameters.put("enabledTLSProtocols", "TLSv1.2");
			}
		}

		if (Objects.equals(config.dbType, DATABRICKS)) {
			return getDatabricksUrl(config);
		}

		if (!parameters.isEmpty()) {
			url += "?" + parameters.entrySet()
				.stream()
				.map(property -> property.getKey() + "=" + property.getValue())
				.collect(joining("&"));
		}
		return url;
	}

	protected Properties getProperties(JdbcAuthConfig config) {
		return config.awsAuth
				.filter(x -> config.jdbcIAMAuthEnabled)
				.map(awsAuthConfig -> {
					String iamToken = iamAuthToken(awsAuthConfig,
							config.host,
							String.valueOf(config.port),
							config.userName);

					Properties params = new Properties();
					params.put("verifyServerCertificate", "true");
					params.put("useSSL", "true");
					params.put("requireSSL", "true");
					params.put("user", config.userName);
					params.put("awsRegion", awsAuthConfig.region);
					params.put("password", iamToken);
					return params;
				})
				.orElseGet(() -> {
					Properties properties = new Properties();

					if (Objects.equals(NETSUITE_JDBC, config.dbType) && config.netsuiteTBAAuthConfig.isPresent()) {
						properties.put("user", "TBA");
						properties.put("password", config.netsuiteTBAAuthConfig.get().getToken());
					} else {
						Optional.ofNullable(config.userName)
								.ifPresent(u -> properties.put("user", u));

						Optional.ofNullable(config.password)
								.ifPresent(pwd -> properties.put("password", pwd));
					}
					properties.putAll(config.jdbcParameters);

					if (Objects.equals(SQLSERVER, config.dbType)) {
						if (!properties.containsKey("trustServerCertificate")) {
							properties.put("trustServerCertificate", "true");
						}
					}

					return withRsaProperties(config, properties);

				});
	}

	private String getTeradataUrl(JdbcAuthConfig config) {
		String host = config.sshTunnelConfig.map(notUsed -> "localhost").orElse(config.host);
		Integer port = ofNullable(config.getSshTunnelPorts())
			.filter(x -> !x.isEmpty())
			.map(x -> x.get(0))
			.orElse(config.port);

		StringBuilder urlBuilder = new StringBuilder(String.format("jdbc:teradata://**/TMODE=ANSI", host));
		ofNullable(config.databaseName).ifPresent(db -> urlBuilder.append(String.format(",DATABASE=**", db)));
		ofNullable(port).ifPresent(p -> urlBuilder.append(String.format(",DBS_PORT=**", port)));
		return urlBuilder.toString();
	}

	private String getSpannerUrl(JdbcAuthConfig jdbcAuthConfig) {
		SpannerAuthConfig spannerAuthConfig = jdbcAuthConfig.spannerAuth.get();
		String jsonCredentialsUrl = getCredentialsURL(spannerAuthConfig.credsMap.get("json_creds").toString());
		String url = String.format("jdbc:cloudspanner:/projects/**/instances/**/databases/**?credentials=**",
				spannerAuthConfig.projectId,
				spannerAuthConfig.instaceId,
				spannerAuthConfig.databaseName,
				jsonCredentialsUrl);
		return url;
	}

	private String getDatabricksUrl(JdbcAuthConfig jdbcAuthConfig) {
		if (StringUtils.isEmpty(jdbcAuthConfig.warehouseName)) {
			throw new RuntimeException("Warehouse Id is required for Databricks JDBC connection, you can find it in "
					+ "the SQL Warehouse Overview page in the Databricks console.");
		}

		String url = jdbcAuthConfig.sshTunnelConfig
				.map(notUsed ->{
					// Use localhost only if host is an IP address. Don't replace otherwise, host will be redirected by MAPPED_HOST_RESOLVER in SshTunnel class
					// for sql tunneling there is only 1 port forwarding
					String host = jdbcAuthConfig.host != null && IPV4_PATTERN.matcher(jdbcAuthConfig.host).matches() ? "localhost" : jdbcAuthConfig.host;
					return jdbcAuthConfig.dbType.connectionStringPrefix + host + ":" + jdbcAuthConfig.getSshTunnelPorts().get(0);
				})
				.orElse(jdbcAuthConfig.dbType.connectionStringPrefix + jdbcAuthConfig.host + ofNullable(jdbcAuthConfig.port).map(x -> ":" + x).orElse(""));

		url += "/" + ofNullable(jdbcAuthConfig.databaseName).orElse("default");

		Map<String, String> parameters = new LinkedHashMap<>();

		parameters.putIfAbsent("UserAgentEntry", "nexla-connector");
		parameters.putIfAbsent("transportMode", "http");
		parameters.putIfAbsent("ssl", "1");
		parameters.putIfAbsent("httpPath", String.format("/sql/1.0/warehouses/**", jdbcAuthConfig.warehouseName));

		if (!parameters.isEmpty()) {
			url += ";" + parameters.entrySet()
					.stream()
					.map(property -> property.getKey() + "=" + property.getValue())
					.collect(joining(";"));
		}
		return url;
	}

	private void withSnowflakeParams(JdbcAuthConfig config, Map<String, String> properties) {
		if (StringUtils.isNotEmpty(config.databaseName)) {
			properties.put("db", getEscapedDatabaseName(config.databaseName));
		}
		properties.put("application", "nexla");
	}

	private String getAthenaUrl(JdbcAuthConfig jdbcAuthConfig) {
		AthenaAuthConfig athenaAuthConfig = jdbcAuthConfig.athenaAuthConfig.get();
		if (athenaAuthConfig.awsRoleArn != null) {
			Map awsProps = new HashMap(jdbcAuthConfig.originals());
			awsProps.put(ACCESS_KEY_ID, athenaAuthConfig.accessKeyId);
			awsProps.put(SECRET_KEY, athenaAuthConfig.secretKey);
			awsProps.put(REGION, athenaAuthConfig.awsRegion);
			awsProps.put(ARN, athenaAuthConfig.awsRoleArn);
			awsProps.put(EXTERNAL_ID, athenaAuthConfig.awsExternalId);

			var authConfig = new AWSAuthConfig(awsProps, jdbcAuthConfig.getCredsId());
			var provider = NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig);
			if (provider instanceof StaticCredentialsProvider) {
				var staticProvider = (StaticCredentialsProvider) provider;
				if (staticProvider.resolveCredentials() instanceof AwsSessionCredentials) {
					var sessionCredentials = (AwsSessionCredentials) staticProvider.resolveCredentials();
					logger.info("Connecting to AWS using role ARN");
					return String.format("jdbc:athena://User=**;Password=**;SessionToken=**;OutputLocation=**;Region=**",
							sessionCredentials.accessKeyId(),
							sessionCredentials.secretAccessKey(),
							sessionCredentials.sessionToken(),
							athenaAuthConfig.s3OutputLocation,
							athenaAuthConfig.awsRegion);
				}
			}
		}
		return String.format("*************************************************************",
				athenaAuthConfig.accessKeyId,
				athenaAuthConfig.secretKey,
				athenaAuthConfig.s3OutputLocation,
				athenaAuthConfig.awsRegion);
	}

	/**
	 * Encodes databaseName, so we can connect to non-upper case db's ("myNew_db").
	 * By default, Snowflake creates connection string with uppercase elements only. myDb -> MYDB
	 * However, if you create database using quotes, Snowflake SHOULD treat this case. "myDb" -> %22myDb%22.
	 * Current version of jdbc-connector (3.13.26) throws an exception if non URL-encoded signs are provided, so we
	 * need to encode it on our side.
	 */
	private String getEscapedDatabaseName(String databaseName) {
		return URLEncoder.encode(databaseName, StandardCharsets.UTF_8);
	}

	protected Properties withRsaProperties(JdbcAuthConfig config, Properties properties) {
		// RSA specific behavior for every connection type
		config.snowflakeRSAConfig.ifPresent(rsaConfig -> {
			try {
				properties.put("privateKey", RSAKeyUtils.PrivateKeyReader.getBase64(rsaConfig.getPrivateKey(), rsaConfig.getPassphrase()));
			} catch (Exception e) {
				throw new IllegalArgumentException(
						String.format("Invalid RSA key parameters for db: ** name: **. Error: **", config.dbType, config.databaseName, e.getMessage()),
						e
				);
			}
			properties.remove("password");
		});
		return properties;
	}

	@SneakyThrows
	private String getCredentialsURL(String credsJson) {
		File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".json");
		try (FileOutputStream fos = new FileOutputStream(tempFile)) {
			IOUtils.write(credsJson, fos);
		}
		tempFile.deleteOnExit();
		return tempFile.getAbsolutePath();
	}
}