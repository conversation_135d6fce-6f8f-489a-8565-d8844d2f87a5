package com.nexla.probe.sql;

import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.common.schema.SchemaKey;
import com.nexla.common.schema.SchemaType;
import connect.jdbc.sink.dialect.AutomaticBinding;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
public class AutomaticBindingFactory {

	private final AdminApiClient adminApiClient;

	public AutomaticBinding create(Map<String, Object> originalConfig) {
		Map<String, SchemaType> typeBinding = Maps.newLinkedHashMap();
		Map<String, String> formatBinding = Maps.newLinkedHashMap();

		if (originalConfig.containsKey("dataset_id")) {
			int datasetId = Integer.parseInt(originalConfig.get("dataset_id").toString());
			updateBindings(datasetId, typeBinding, formatBinding);
		}
		return new AutomaticBinding(typeBinding, formatBinding);
	}

	private void updateBindings(
		int datasetId,
		Map<String, SchemaType> typeBinding,
		Map<String, String> formatBinding
	) {
		adminApiClient.getDataSet(datasetId)
			.ifPresent(dataset -> {
				Map<String, Object> schema = (Map<String, Object>) dataset.getOutputSchema().getProperties();

				schema.forEach((key, value) -> {
					if (value instanceof List) {
						throw new IllegalArgumentException("List not allowed in mapping config");
					} else if (value instanceof Map) {
						Map valueMap = (Map) value;
						if (valueMap.containsKey(SchemaKey.ANY_OF)) {
							List list = (List) valueMap.get(SchemaKey.ANY_OF);
							valueMap = (Map) list.get(0);
						}
						Object type = valueMap.get(SchemaKey.TYPE.toString());
						Object format = valueMap.get(SchemaKey.FORMAT.toString());
						if (type != null) {
							typeBinding.put(key, SchemaType.fromString(type.toString()));
						}
						if (format != null) {
							formatBinding.put(key, format.toString());
						}
					} else {
						typeBinding.put(key, SchemaType.fromString(value.toString()));
					}
				});
			});
	}
}
