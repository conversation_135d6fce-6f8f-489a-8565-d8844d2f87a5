package connect.jdbc.sink.dialect;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.base.Supplier;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.nexla.common.ConnectionType;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.schema.SchemaType;
import com.nexla.common.tracker.Tracker;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Date;
import connect.data.Struct;
import connect.data.Time;
import connect.data.Timestamp;
import connect.data.*;
import connect.jdbc.sink.dialect.copy.SinkCopyOperation;
import connect.jdbc.sink.dialect.copy.SourceCopyOperation;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import connect.jdbc.util.DateTimeUtils;
import io.debezium.time.ZonedTime;
import io.debezium.time.ZonedTimestamp;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.nexla.common.ConnectionType.AZURE_SYNAPSE;
import static com.nexla.common.ConnectionType.SYBASE;
import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static connect.data.Schema.Type.*;
import static connect.jdbc.sink.dialect.AutomaticBinding.DEFAULT_DECIMAL_SCALE;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static connect.jdbc.sink.dialect.StringBuilderUtil.nCopiesToBuilder;
import static java.util.Arrays.stream;
import static java.util.Collections.emptySet;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.removeStart;

public abstract class DbDialect {

	private static final Logger logger = LoggerFactory.getLogger(DbDialect.class);

	public static final String CATALOG = "catalog";
	public static final String SCHEMA = "schema";

	private final String escapeStart;
	private final String escapeEnd;
	private final Optional<String> schemaParam;
	private final Optional<String> warehouseParam;
	private final Optional<String> defaultSchemaName;
	public final boolean mapNumerics;

	// Warehouse is applicable only for Snowflake databases
	protected DbDialect(
		String escapeStart,
		String escapeEnd,
		Optional<String> schemaParam,
		Optional<String> warehouseParam,
		Optional<String> defaultSchemaName,
		boolean mapNumerics
	) {
		this.escapeStart = escapeStart;
		this.escapeEnd = escapeEnd;
		this.schemaParam = schemaParam;
		this.warehouseParam = warehouseParam;
		this.defaultSchemaName = defaultSchemaName;
		this.mapNumerics = mapNumerics;
	}

	public Set<WarehouseCopyFileFormat> sourceFileFormats() {
		return emptySet();
	}

	public Set<WarehouseCopyFileFormat> sinkFileFormats() {
		return emptySet();
	}

	public Optional<WarehouseCopyFileFormat> defaultSourceFileFormat() {
		return empty();
	}

	public Optional<WarehouseCopyFileFormat> defaultSinkFileFormat() {
		return empty();
	}

	public DateTimeFormatter getDateFormatter() {
		return DateTimeFormat.forPattern("MM/dd/yyyy");
	}

	public DateTimeFormatter getDateTimeFormatter() {
		return DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
	}

	public boolean supportPrimaryKeys() {
		return true;
	}

	public StreamEx<String> getSqlTypes() {
		throw new UnsupportedOperationException("Get data types not supported for this database");
	}

	public Schema.Type getSchemaTypeByDbType(String dbType) {
		throw new UnsupportedOperationException("Get schema type by db type not supported for this database!");
	}

	@SneakyThrows
	protected long sqlDateTimeToMillis(Object obj) {
		if (obj instanceof java.sql.Date) {
			return((java.sql.Date) obj).getTime();
		}
		if (obj instanceof LocalDateTime) {
			return ((LocalDateTime) obj).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
		}

		return ((java.sql.Timestamp) obj).getTime();
	}

	@SneakyThrows
	public java.sql.Timestamp getTimestamp(ResultSet rs, int column) {
		return rs.getTimestamp(column, DateTimeUtils.UTC_CALENDAR.get());
	}

	@SneakyThrows
	public java.sql.Timestamp getTimestampRoundUp(ResultSet rs, int column) {
		return this.getTimestamp(rs, column);
	}

	public Set<String> systemSchemas() {
		return emptySet();
	}

	public String getDatabaseTerm() {
		return CATALOG;
	}

	@SneakyThrows
	public NexlaDbInfo extractDbInfo(Connection connection, String url, Set<String> allSchemas, boolean isSupportingSchema) {
		URI uri = URI.create(removeStart(url, "jdbc:"));
		String dbName = removeStart(uri.getPath(), "/");
		Map<String, String> properties = uri.getQuery() != null
			? stream(uri.getQuery().split("&")).map(s -> s.split("=")).collect(toMap(e -> e[0].toLowerCase(), e -> e[1]))
			: Collections.emptyMap();

		String schema = ofNullable(
			schemaParam
				.map(properties::get)
				.map(StringUtils::trimToNull)
				.orElse(null))
			.orElse(isSupportingSchema ? connection.getSchema() : null);

		return new NexlaDbInfo(dbName, schema == null ? allSchemas : Sets.newHashSet(schema));
	}

	public Set<DialectFeature> getFeatures() {
		return emptySet();
	}

	public String getUpdateSql(String tableName, List<String> keyColumns, List<String> nonKeyColumns) {
		if(keyColumns.isEmpty()) {
			return "UPDATE " + tableName + "\n" +
					"SET " + nonKeyColumns.stream().map(f -> escapeStart + f + escapeEnd + " = ?").collect(joining(",\n")) + "\n" +
					"WHERE " + nonKeyColumns.stream().map(f -> escapeStart + f + escapeEnd + " = ?").collect(joining("\nAND "));
		}

		return "UPDATE " + tableName + "\n" +
			"SET " + nonKeyColumns.stream().map(f -> escapeStart + f + escapeEnd + " = ?").collect(joining(",\n")) + "\n" +
			"WHERE " + keyColumns.stream().map(f -> escapeStart + f + escapeEnd + " = ?").collect(joining("\nAND "));
	}

	public String getDeleteSql(String tableName, List<String> columns) {
			return "DELETE FROM " + tableName + "\n" +
					"WHERE " + columns.stream().map(f -> escapeStart + f + escapeEnd + " = ?").collect(joining("\nAND "));
	}

	public String getTruncateSql(String tableName) {
		return "TRUNCATE TABLE " + tableName;
	}

	public String getInsertSql(String tableName, Collection<String> keyColumns, Collection<String> nonKeyColumns) {
		StringBuilder builder = new StringBuilder("INSERT INTO ");
		builder.append(tableName);
		builder.append("(");
		joinToBuilder(builder, ",", keyColumns, nonKeyColumns, escaper());
		builder.append(") VALUES(");
		nCopiesToBuilder(builder, ",", "?", keyColumns.size() + nonKeyColumns.size());
		builder.append(")");
		return builder.toString();
	}

	public String getCreateSql(
		String tableName,
		Collection<String> keyColumns,
		Collection<String> nonKeyColumns,
		Supplier<AutomaticBinding> automaticBinding,
		MappingConfig mappingConfig,
		JdbcSinkConnectorConfig sinkConnectorConfig) {

		StringBuilder builder = new StringBuilder("CREATE TABLE ");
		builder.append(tableName);
		builder.append(" (");
		if (mappingConfig.getTrackerMode() != Tracker.TrackerMode.NONE) {
			nonKeyColumns.add(mappingConfig.getTrackerFieldName());
		}
		joinToBuilder(builder, ",", keyColumns, nonKeyColumns, (b, val) -> {
			String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(val))
				.findFirst()
				.map(e -> e.get(val))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElseGet(() -> automaticBinding.get().getSqlType(val, this));

			b.append(q(val)).append(" ").append(sqlType);
		});
		builder.append(",");

		if (isNotEmpty(keyColumns)) {
			builder.append("PRIMARY KEY(");
			joinToBuilder(builder, ",", keyColumns, escaper());
			builder.append(")");
		} else {
			//Deleting the final ,
			builder.deleteCharAt(builder.length() - 1);
		}

		builder.append(")");

		//To support cluserting keys
		if (isNotEmpty(sinkConnectorConfig.clusteringingColumns)
				&& sinkConnectorConfig.clusteringingColumns.size() > 0) {
			builder.append(" CLUSTER BY (");
			joinToBuilder(builder, ",", sinkConnectorConfig.clusteringingColumns, escaper());
			builder.append(")");
		}

		return builder.toString();
	}

	public String getAlterSql(
			String tableName,
			Collection<String> nonKeyColumns,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		StringBuilder builder = new StringBuilder("ALTER TABLE ");
		builder.append(tableName);
		if (mappingConfig.getTrackerMode() != Tracker.TrackerMode.NONE) {
			nonKeyColumns.add(mappingConfig.getTrackerFieldName());
		}
		joinToBuilder(builder, ",", Collections.emptyList(), nonKeyColumns, (b, val) -> {
			String sqlType = mappingConfig
					.getMapping()
					.values()
					.stream()
					.filter(map -> map.containsKey(val))
					.findFirst()
					.map(e -> e.get(val))
					.filter(t -> !t.equals(DEFAULT_MAPPING))
					.orElse(automaticBinding.get().getSqlType(val, this));

			b.append(" ADD ").append(q(val)).append(" ").append(sqlType);
		});
		return builder.toString();
	}

	public List<String> getAlterModifySqls(
			String tableName,
			String nonKeyCol,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(nonKeyCol))
				.findFirst()
				.map(e -> e.get(nonKeyCol))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElse(automaticBinding.get().getSqlType(nonKeyCol, this));

		return Collections.singletonList("ALTER TABLE " + tableName + " MODIFY " + q(nonKeyCol) + " " + sqlType);
	}
	public String getAlterAddSql(
			String tableName,
			String nonKeyCol,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(nonKeyCol))
				.findFirst()
				.map(e -> e.get(nonKeyCol))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElse(automaticBinding.get().getSqlType(nonKeyCol, this));

		return "ALTER TABLE " + tableName + " ADD " + q(nonKeyCol) + " " + sqlType;
	}

	public String getAlterDropSql(
			String tableName,
			String nonKeyCol) {
		return "ALTER TABLE " + tableName + " DROP COLUMN " + q(nonKeyCol);
	}

	public String getSelectQuery(
		String tableName,
		List<String> keyFields,
		List<String> nonKeyFields,
		long numRecords
	) {
		StringBuilder builder = new StringBuilder("SELECT ");
		joinToBuilder(builder, ",", keyFields, escaper());
		if (isNotEmpty(nonKeyFields)) {
			builder.append(",");
		}
		joinToBuilder(builder, ",", nonKeyFields, escaper());
		builder.append(" FROM ").append(tableName);
		builder.append(" WHERE (");
		joinToBuilder(builder, ",", keyFields, escaper());
		builder.append(") IN (");
		nCopiesToBuilder(builder, ",", buildStatement(keyFields.size()), numRecords);
		builder.append(")");

		return builder.toString();
	}

	private String buildStatement(int numKeys) {
		StringBuilder builder = new StringBuilder("(");
		nCopiesToBuilder(builder, ",", "?", numKeys);
		builder.append(")");
		return builder.toString();
	}

	public String getUpsertQuery(String table, Collection<String> keyColumns, Collection<String> columns) {
		throw new UnsupportedOperationException();
	}

	public List<String> getUpsertQueryFields(List<String> keyFields, List<String> nonKeyFields, List<String> allFieldsOrdered) {
		return allFieldsOrdered;
	}

	public String getSqlType(String schemaName, Map<String, String> parameters, Schema.Type type) {
		if (schemaName != null) {
			String formatType = getDbTypeByKafka(schemaName, parameters);
			if (formatType != null) {
				return formatType;
			}
		}
		return getDbType(type);
	}

	public abstract String getDbTypeByKafka(String schemaName, Map<String, String> parameters);

	public abstract String getDbType(Schema.Type schemaName);

	protected StringBuilderUtil.Transform<String> escaper() {
		return (builder, identifier) -> builder.append(escapeStart).append(identifier).append(escapeEnd);
	}

	protected StringBuilderUtil.Transform<String> prefixedEscaper(final String prefix) {
		return (builder, identifier) -> builder.append(prefix).append(escapeStart).append(identifier).append(escapeEnd);
	}

	public boolean supportsFractional(Connection connection) {
		return true;
	}

	public void resolveSqlType(ResultSetMetaData metadata, int col, SchemaBuilder builder, boolean mapNumerics, String fieldName, int sqlType, boolean optional) throws SQLException {
		switch (sqlType) {
			case Types.BOOLEAN: {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_BOOLEAN_SCHEMA);
				} else {
					builder.field(fieldName, Schema.BOOLEAN_SCHEMA);
				}
				break;
			}

			// ints <= 8 bits
			case Types.BIT: {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_INT8_SCHEMA);
				} else {
					builder.field(fieldName, Schema.INT8_SCHEMA);
				}
				break;
			}

			case Types.TINYINT: {
				if (optional) {
					if (metadata.isSigned(col)) {
						builder.field(fieldName, Schema.OPTIONAL_INT8_SCHEMA);
					} else {
						builder.field(fieldName, Schema.OPTIONAL_INT16_SCHEMA);
					}
				} else {
					if (metadata.isSigned(col)) {
						builder.field(fieldName, Schema.INT8_SCHEMA);
					} else {
						builder.field(fieldName, Schema.INT16_SCHEMA);
					}
				}
				break;
			}

			// 16 bit ints
			case Types.SMALLINT: {
				if (optional) {
					if (metadata.isSigned(col)) {
						builder.field(fieldName, Schema.OPTIONAL_INT16_SCHEMA);
					} else {
						builder.field(fieldName, Schema.OPTIONAL_INT32_SCHEMA);
					}
				} else {
					if (metadata.isSigned(col)) {
						builder.field(fieldName, Schema.INT16_SCHEMA);
					} else {
						builder.field(fieldName, Schema.INT32_SCHEMA);
					}
				}
				break;
			}

			// 32 bit ints
			case Types.INTEGER: {
				if (optional) {
					if (metadata.isSigned(col)) {
						builder.field(fieldName, Schema.OPTIONAL_INT32_SCHEMA);
					} else {
						builder.field(fieldName, Schema.OPTIONAL_INT64_SCHEMA);
					}
				} else {
					if (metadata.isSigned(col)) {
						builder.field(fieldName, Schema.INT32_SCHEMA);
					} else {
						builder.field(fieldName, Schema.INT64_SCHEMA);
					}
				}
				break;
			}

			// 64 bit ints
			case Types.BIGINT: {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_INT64_SCHEMA);
				} else {
					builder.field(fieldName, Schema.INT64_SCHEMA);
				}
				break;
			}

			// REAL is a single precision floating point value, i.e. a Java float
			case Types.REAL: {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_FLOAT32_SCHEMA);
				} else {
					builder.field(fieldName, Schema.FLOAT32_SCHEMA);
				}
				break;
			}

			// FLOAT is, confusingly, double precision and effectively the same as DOUBLE. See REAL
			// for single precision
			case Types.FLOAT:
			case Types.DOUBLE: {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_FLOAT64_SCHEMA);
				} else {
					builder.field(fieldName, Schema.FLOAT64_SCHEMA);
				}
				break;
			}

			case Types.NUMERIC:
				if (mapNumerics) {
					boolean processed = processNumeric(metadata, col, builder, fieldName, optional);
					if (!processed) {
						processDecimal(metadata, col, builder, fieldName, optional);
					}
				} else {
					processDecimal(metadata, col, builder, fieldName, optional);
				}
				break;
			case Types.DECIMAL: {
				processDecimal(metadata, col, builder, fieldName, optional);
				break;
			}

			case Types.CHAR:
			case Types.VARCHAR:
			case Types.LONGVARCHAR:
			case Types.NCHAR:
			case Types.NVARCHAR:
			case Types.LONGNVARCHAR:
			case Types.CLOB:
			case Types.NCLOB:
			case Types.DATALINK:
			case Types.SQLXML: {
				// Some of these types will have fixed size, but we drop this from the schema conversion
				// since only fixed byte arrays can have a fixed size
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_STRING_SCHEMA);
				} else {
					builder.field(fieldName, Schema.STRING_SCHEMA);
				}
				break;
			}

			// Binary == fixed bytes
			// BLOB, VARBINARY, LONGVARBINARY == bytes
			case Types.BINARY:
			case Types.BLOB:
			case Types.VARBINARY:
			case Types.LONGVARBINARY: {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_BYTES_SCHEMA);
				} else {
					builder.field(fieldName, Schema.BYTES_SCHEMA);
				}
				break;
			}

			// Date is day + moth + year
			case Types.DATE: {
				SchemaBuilder dateSchemaBuilder = Date.builder();
				if (optional) {
					dateSchemaBuilder.optional();
				}
				builder.field(fieldName, dateSchemaBuilder.build());
				break;
			}

			// Time is a time of day -- hour, minute, seconds, nanoseconds
			case Types.TIME_WITH_TIMEZONE:
			case Types.TIME: {
				SchemaBuilder timeSchemaBuilder = Time.builder();
				if (optional) {
					timeSchemaBuilder.optional();
				}
				builder.field(fieldName, timeSchemaBuilder.build());
				break;
			}

			// Timestamp is a date + time
			case Types.TIMESTAMP_WITH_TIMEZONE:
			case Types.TIMESTAMP: {
				SchemaBuilder tsSchemaBuilder = Timestamp.builder();
				if (optional) {
					tsSchemaBuilder.optional();
				}
				builder.field(fieldName, tsSchemaBuilder.build());
				break;
			}

			case Types.ARRAY:
				String arrayType = metadata.getColumnTypeName(col);
				Schema arraysSchemaString = getArrayTypeSchema(arrayType, optional);
				if (arraysSchemaString != null) {
					builder.field(fieldName, arraysSchemaString);
				}
				break;
			case Types.JAVA_OBJECT:
			case Types.OTHER:
			case Types.DISTINCT:
			case Types.STRUCT:
			case Types.REF:
			case Types.ROWID:
			default: {
				String typeName = metadata.getColumnTypeName(col);
				switch (typeName) {
					case "json":
					case "jsonb":
						if (optional) {
							builder.field(fieldName, Schema.OPTIONAL_JSON_SCHEMA);
						} else {
							builder.field(fieldName, Schema.JSON_SCHEMA);
						}
						break;
					default:
						logger.warn("JDBC type '{}/{}' not currently supported. Field name = {}", sqlType, typeName, fieldName);
						Schema schema = SchemaBuilder.unsupported().name(typeName).build();
						builder.field(fieldName, schema);
						break;
				}
			}
		}
	}

	private void processDecimal(ResultSetMetaData metadata, int col, SchemaBuilder builder, String fieldName, boolean optional) throws SQLException {
		int scale = metadata.getScale(col);
		if (scale == -127) //NUMBER without precision defined for OracleDB
			scale = 127;
		SchemaBuilder fieldBuilder = Decimal.builder(scale);
		if (optional) {
			fieldBuilder.optional();
		}
		builder.field(fieldName, fieldBuilder.build());
	}

	@SneakyThrows
	protected boolean processNumeric(ResultSetMetaData metadata, int col, SchemaBuilder builder, String fieldName, boolean optional) {
		int precision = metadata.getPrecision(col);
		if (metadata.getScale(col) == 0 && precision < 19) { // integer
			Schema schema;
			if (precision > 9) {
				schema = (optional) ? Schema.OPTIONAL_INT64_SCHEMA :
					Schema.INT64_SCHEMA;
			} else if (precision > 4) {
				schema = (optional) ? Schema.OPTIONAL_INT32_SCHEMA :
					Schema.INT32_SCHEMA;
			} else if (precision > 2) {
				schema = (optional) ? Schema.OPTIONAL_INT16_SCHEMA :
					Schema.INT16_SCHEMA;
			} else {
				schema = (optional) ? Schema.OPTIONAL_INT8_SCHEMA :
					Schema.INT8_SCHEMA;
			}
			builder.field(fieldName, schema);
			return true;
		}
		return false;
	}

	public ResultSet fixResultSet(ResultSet resultSet) {
		return resultSet;
	}

	public void convertFieldValue(
		ResultSet resultSet,
		int col,
		int colType,
		Struct struct,
		String fieldName,
		boolean mapNumerics,
		String typeName
	)
		throws SQLException, IOException {

		final Object colValue;
		switch (colType) {
			case Types.NULL: {
				colValue = null;
				break;
			}

			case Types.BOOLEAN: {
				colValue = resultSet.getBoolean(col);
				break;
			}

			case Types.BIT: {
				/**
				 * BIT should be either 0 or 1.
				 * TODO: Postgres handles this differently, returning a string "t" or "f". See the
				 * elasticsearch-jdbc plugin for an example of how this is handled
				 */
				colValue = resultSet.getByte(col);
				break;
			}

			// 8 bits int
			case Types.TINYINT: {
				if (resultSet.getMetaData().isSigned(col)) {
					colValue = resultSet.getByte(col);
				} else {
					colValue = resultSet.getShort(col);
				}
				break;
			}

			// 16 bits int
			case Types.SMALLINT: {
				if (resultSet.getMetaData().isSigned(col)) {
					colValue = resultSet.getShort(col);
				} else {
					colValue = resultSet.getInt(col);
				}
				break;
			}

			// 32 bits int
			case Types.INTEGER: {
				if (resultSet.getMetaData().isSigned(col)) {
					colValue = resultSet.getInt(col);
				} else {
					colValue = resultSet.getLong(col);
				}
				break;
			}

			// 64 bits int
			case Types.BIGINT: {
				colValue = resultSet.getLong(col);
				break;
			}

			// REAL is a single precision floating point value, i.e. a Java float
			case Types.REAL: {
				colValue = resultSet.getFloat(col);
				break;
			}

			// FLOAT is, confusingly, double precision and effectively the same as DOUBLE. See REAL
			// for single precision
			case Types.FLOAT:
			case Types.DOUBLE: {
				colValue = resultSet.getDouble(col);
				break;
			}

			case Types.NUMERIC:
				if (mapNumerics) {
					ResultSetMetaData metadata = resultSet.getMetaData();
					int precision = metadata.getPrecision(col);
					if (metadata.getScale(col) == 0 && precision < 19) { // integer
						if (precision > 9) {
							colValue = resultSet.getLong(col);
						} else if (precision > 4) {
							colValue = resultSet.getInt(col);
						} else if (precision > 2) {
							colValue = resultSet.getShort(col);
						} else {
							colValue = resultSet.getByte(col);
						}
						break;
					}
				}
			case Types.DECIMAL: {
				ResultSetMetaData metadata = resultSet.getMetaData();
				int scale = metadata.getScale(col);
				if (scale == -127) {
					scale = 127;
				}
				colValue = resultSet.getBigDecimal(col);
				break;
			}

			case Types.CHAR:
			case Types.VARCHAR:
			case Types.LONGVARCHAR: {
				colValue = resultSet.getString(col);
				break;
			}

			case Types.NCHAR:
			case Types.NVARCHAR:
			case Types.LONGNVARCHAR: {
				colValue = resultSet.getNString(col);
				break;
			}

			// Binary == fixed, VARBINARY and LONGVARBINARY == bytes
			case Types.BINARY:
			case Types.VARBINARY:
			case Types.LONGVARBINARY: {
				colValue = resultSet.getBytes(col);
				break;
			}

			// Date is day + moth + year
			case Types.DATE: {
				colValue = resultSet.getDate(col, DateTimeUtils.UTC_CALENDAR.get());
				break;
			}

			// Time is a time of day -- hour, minute, seconds, nanoseconds
			case Types.TIME_WITH_TIMEZONE:
			case Types.TIME: {
				colValue = resultSet.getTime(col, DateTimeUtils.UTC_CALENDAR.get());
				break;
			}

			// Timestamp is a date + time
			case Types.TIMESTAMP_WITH_TIMEZONE:
			case Types.TIMESTAMP: {
				colValue = resultSet.getTimestamp(col, DateTimeUtils.UTC_CALENDAR.get());
				break;
			}

			// Datalink is basically a URL -> string
			case Types.DATALINK: {
				URL url = resultSet.getURL(col);
				colValue = (url != null ? url.toString() : null);
				break;
			}

			// BLOB == fixed
			case Types.BLOB: {
				Blob blob = resultSet.getBlob(col);
				if (blob == null) {
					colValue = null;
				} else {
					if (blob.length() > Integer.MAX_VALUE) {
						throw new IOException("Can't process BLOBs longer than Integer.MAX_VALUE");
					}
					colValue = blob.getBytes(1, (int) blob.length());
					blob.free();
				}
				break;
			}
			case Types.CLOB:
			case Types.NCLOB: {
				Clob clob = (colType == Types.CLOB ? resultSet.getClob(col) : resultSet.getNClob(col));
				if (clob == null) {
					colValue = null;
				} else {
					if (clob.length() > Integer.MAX_VALUE) {
						throw new IOException("Can't process BLOBs longer than Integer.MAX_VALUE");
					}
					colValue = clob.getSubString(1, (int) clob.length());
					clob.free();
				}
				break;
			}

			// XML -> string
			case Types.SQLXML: {
				SQLXML xml = resultSet.getSQLXML(col);
				colValue = (xml != null ? xml.getString() : null);
				break;
			}

			case Types.ARRAY:
				Object[] arr = ofNullable(resultSet.getArray(col))
					.map(array -> (Object[]) getArray(array, typeName))
					.orElse(new Object[0]);
				colValue = Lists.newArrayList(arr);
				break;
			case Types.JAVA_OBJECT:
			case Types.OTHER:
			case Types.DISTINCT:
			case Types.STRUCT:
			case Types.REF:
			case Types.ROWID:
			default: {
				if ("jsonb".equalsIgnoreCase(typeName) || "json".equalsIgnoreCase(typeName)) {
					String strValue = resultSet.getString(col);
					colValue = Optional.ofNullable(strValue)
						.map(JsonUtils::jsonToObject)
						// fix common issues with json typed values
						// like writing '{}' as a string (by rfc7159 string is valid JSON), which most probably should be an empty json object
						.map(this::fixJsonValue)
						.orElse(null);
					break;
				} else {
					// should not be null to throw correct 'not currently supported' exception
					colValue = StringUtils.EMPTY;
					break;
				}
			}
		}

		// FIXME: Would passing in some extra info about the schema so we can get the Field by index
		// be faster than setting this by name?
		struct.put(col, resultSet.wasNull() ? null : colValue);
	}

	/**
	 *
	 * A JSON value MUST be an object, array, number, or string, or one of
	 * the following three literal names:
	 * false null true
	 *
	 * @see <a href="https://www.ietf.org/rfc/rfc7159.txt">rfc7159</a>
	 */
	private Object fixJsonValue(Object value) {
		if (value == null) {
			return null;
		}

		if (value instanceof Map || value instanceof Collection) {
			return value;
		}

		if (value instanceof String) {
			if ("{}".equals(value)) {
				return Collections.emptyMap();
			}
		}

		// connect.data.ConnectSchema supports only instances of Map.class, List.class, String.class, Number.class, Boolean.class classes as a valid JSON values
		if (!(value instanceof Number || value instanceof String || value instanceof Boolean)) {
			logger.warn("{} of {} type is not a valid JSON value", value, value.getClass().getSimpleName());
			return value.toString();
		}

		return value;
	}

	@SneakyThrows
	protected Object getArray(Array array, String typeName) {
		return array.getArray();
	}

	public Schema.Type schemaTypeToKafkaType(SchemaType schemaType) {
		switch (schemaType) {
			case STRING:
				return STRING;
			case INTEGER:
				return INT32;
			case NUMBER:
				return FLOAT64;
			case BOOLEAN:
				return BOOLEAN;
			default:
				return BYTES;
		}
	}

	/*
	Returns the schema parameter in connection string. Returns null if schema is not supported in the db
	(default implementation)
	 */
	public Optional<String> getSchemaParameterInConnectionString() {
		return schemaParam;
	}

	public Optional<String> getWarehouseParameterInConnectionString() {
		return warehouseParam;
	}

	public String getQualifiedTableName(String table, String schemaName, String databaseName) {
		return isEmpty(schemaName) ? q(table) : q(schemaName) + "." + q(table);
	}

	public String getQualifiedSchemaName(String schemaName, String databaseName) {
		if (!isEmpty(schemaName)) {
			return q(schemaName);
		}

		return getDefaultSchemaName().map(this::q).orElse("");
	}

	public SinkCopyOperation newSinkCopyOperation(JdbcSinkConnectorConfig config) {
		throw new UnsupportedOperationException();
	}

	public SourceCopyOperation newSourceCopyOperation(JdbcSourceConnectorConfig config,
													  WarehouseCopyFileFormat fileFormat,
													  Logger logger,
													  boolean isReplicationContext,
													  WarehouseCopyTempStorage storage,
													  FlowType flowType) {
		throw new UnsupportedOperationException();
	}

	public DataWarehouseSink newDataWarehouseSink(JdbcSinkConnectorConfig config,
	                                              WarehouseCopyFileFormat fileFormat,
	                                              Schema schema,
	                                              DbDialect dbDialect,
	                                              NexlaLogger logger) {
		throw new UnsupportedOperationException();
	}

	public String q(String column) {
		return escapeStart + column + escapeEnd;
	}

	public Optional<String> getDefaultSchemaName() {
		return defaultSchemaName;
	}

	public Schema getArrayTypeSchema(String arrayType, boolean optional) {
		return null;
	}

	public String getJdbcDatabaseName(String databaseName) {
		return "/" + databaseName;
	}

	@SneakyThrows
	public boolean executePreparedStatement(PreparedStatement ps) {
		return ps.execute();
	}

	@SneakyThrows
	public boolean executeStatement(Statement st, String sqlCommand) {
		return st.execute(sqlCommand);
	}

	public boolean supportsSchema() {
		return true;
	}

	@SneakyThrows
	protected boolean executeInImplicitTransaction(PreparedStatement ps) {
		final Connection connection = ps.getConnection();
		final boolean autoCommit = connection.getAutoCommit();
		connection.setAutoCommit(true);
		final boolean result = ps.execute();
		connection.setAutoCommit(autoCommit);
		return result;
	}

	@SneakyThrows
	protected boolean executeInImplicitTransaction(Statement st, String command) {
		final Connection connection = st.getConnection();
		final boolean autoCommit = connection.getAutoCommit();
		connection.setAutoCommit(true);
		final boolean result = st.execute(command);
		connection.setAutoCommit(autoCommit);
		return result;
	}

	/**
	 * Rounds up Timestamp to avoid losing records in resultSet.
	 * Timestamp will be cropped to milliseconds later on, round down, like:
	 * {@code 2023-04-10T09:46:35.401838 -> 2023-04-10T09:46:35.401}, causing the result to be cropped.
	 * {@code WHERE ts <= 2023-04-10T09:46:35.401} will crop the one or more records form the result, since .401838 > .401
	 *
	 * @return round-up Timestamp like 2023-04-10T09:46:35.402
	 */
	protected java.sql.Timestamp roundUpTimestamp(java.sql.Timestamp timestamp) {
		return java.sql.Timestamp.from(timestamp.toInstant()
				.plus(500, ChronoUnit.MICROS).truncatedTo(ChronoUnit.MILLIS));
	}

	public boolean isAutoCommit() {
		return false;
	}

	public boolean tableCatSupported() {
		return true;
	}

	public String getFieldName(String columnName, String columnLabel) {
		// Label is what the query requested the column name be using an "AS" clause, name is the original
		return columnLabel != null && !columnLabel.isEmpty() ? columnLabel : columnName;
	}

	public Optional<String> convertSemanticToDBType(String semantic) {
		Map<String, String> params = Map.of(Decimal.SCALE_FIELD, DEFAULT_DECIMAL_SCALE);

		String type = null;
		if (StringUtils.isNotBlank(semantic)) {
			String tempSchema;
			switch (semantic) {
				case ZonedTime.SCHEMA_NAME:
					tempSchema = Time.LOGICAL_NAME;
					break;
				case ZonedTimestamp.SCHEMA_NAME:
					tempSchema = Timestamp.LOGICAL_NAME;
					break;
				default:
					tempSchema = semantic;
					break;
			}

			type = getDbTypeByKafka(tempSchema, params);
		}
		return Optional.ofNullable(type);
	}

	public String getTargetName(String originalName) {
		return TableNameMapper.getName(originalName, TableNameMapper.groupNamePattern6, true, 63, "[\\w$]", TableNameMapper.prefix);
	}
	
	public boolean convertNullStringsToNullsUnload() {
		return true;
	}

	public Map<Schema.Type, List<Schema.Type>> allowedColumnModifications() {
		return Map.of(
				Schema.Type.JSON, List.of(),
				Schema.Type.BOOLEAN, List.of(Schema.Type.STRING),
				Schema.Type.STRING, List.of(),
				Schema.Type.FLOAT64, List.of(Schema.Type.STRING, Schema.Type.JSON),
				Schema.Type.INT64, List.of(Schema.Type.STRING, Schema.Type.JSON, Schema.Type.FLOAT64));
	}

	public String getSelectRawDataTypesQuery(String table, String schema) {
		return null;
	}
}
