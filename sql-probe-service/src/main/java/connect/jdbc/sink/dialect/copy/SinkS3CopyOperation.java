package connect.jdbc.sink.dialect.copy;

import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import com.nexla.probe.s3.S3ConnectorService;
import lombok.SneakyThrows;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.util.function.Consumer;
import java.util.function.Supplier;

public abstract class SinkS3CopyOperation extends BaseSinkCopyOperation {

    private static final Logger LOGGER = LoggerFactory.getLogger(SinkS3CopyOperation.class);

    protected S3Client s3Client;
    protected static final Integer MAX_TOKEN_DURATION = 55 * 60 * 1000;
    protected DateTime lastRefreshTime;

    @Override
    protected void initCloudObjectStoreClient() {
        this.s3Client = S3ConnectorService.createS3ClientFromCreds(config.s3AuthConfig, null);
    }

    @Override
    protected void deleteCloudFile(File localFile) {
        if (config.tempS3Delete) {
            String s3FileName = config.tempS3UploadPrefix + "/" + localFile.getName();
            withClientRetrievable(() -> s3Client, (s3Client) -> {
                DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                        .bucket(config.tempS3UploadBucket)
                        .key(s3FileName)
                        .build();
                s3Client.deleteObject(deleteObjectRequest);
            });
        }
    }

    @Override
    public void uploadFile(File localFile) {
        logger.info(
                "putObject(" + config.tempS3UploadBucket + ", " +
                        config.tempS3UploadPrefix + "/" + localFile.getName() + ")");

        withClientRetrievable(() -> s3Client, (s3Client) -> {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(config.tempS3UploadBucket)
                    .key(config.tempS3UploadPrefix + "/" + localFile.getName())
                    .build();
            s3Client.putObject(putObjectRequest, RequestBody.fromFile(localFile));
        });
    }

    @Override
    protected String getTempFilePrefix() {
        return "s3_";
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
        s3Client.close();
    }

    @SneakyThrows
    protected static <T> void withClientRetrievable(Supplier<T> supplier, Consumer<T> consumer) {
        try {
            T client = supplier.get();
            consumer.accept(client);
        } catch (SdkClientException ex) {
            LOGGER.error("Retrievable exception on CopyOperation", ex);
            T client = supplier.get();
            consumer.accept(client);
        }
    }
}
