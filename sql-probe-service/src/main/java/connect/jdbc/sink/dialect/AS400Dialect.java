package connect.jdbc.sink.dialect;

import com.google.common.collect.Sets;
import com.nexla.common.ConnectionType;
import connect.data.Schema;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.sql.ResultSet;
import java.util.Map;
import java.util.Set;

import static java.util.Optional.empty;

public class AS400Dialect extends DbDialect {

	public AS400Dialect() {
		super("\"", "\"", empty(), empty(), empty(), true);
	}

	@Override
	public Set<String> systemSchemas() {
		return Sets.newHashSet("$TEMP", "QSYS2", "SYSIBM");
	}

	@SneakyThrows
	@Override
	public java.sql.Timestamp getTimestamp(ResultSet rs, int column) {
		Object obj = rs.getObject(column);
		return ((oracle.sql.TIMESTAMP) obj).timestampValue();
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return Set.of();
	}

	@Override
	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		return null;
	}

	@Override
	public String getDbType(Schema.Type schemaName) {
		return null;
	}

	@Override
	public String getDatabaseTerm() {
		return SCHEMA;
	}

	public StreamEx<String> getSqlTypes() {
		return StreamEx.empty();
	}
}
