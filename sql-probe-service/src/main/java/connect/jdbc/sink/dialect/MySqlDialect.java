package connect.jdbc.sink.dialect;

import com.mysql.cj.jdbc.ConnectionImpl;
import connect.data.Date;
import connect.data.Decimal;
import connect.data.Schema;
import connect.data.Time;
import connect.data.Timestamp;
import connect.jdbc.sink.dialect.type.MySqlType;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

import static connect.jdbc.sink.dialect.DialectFeature.MERGE;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static connect.jdbc.sink.dialect.StringBuilderUtil.nCopiesToBuilder;
import static java.util.Collections.singleton;
import static java.util.Optional.empty;
import static org.apache.commons.lang3.StringUtils.isEmpty;

public class MySqlDialect extends DbDialect {

	private static final Set<DialectFeature> DIALECT_FEATURES = singleton(MERGE);

	public MySqlDialect() {
		super("`", "`", empty(), empty(), empty(), false);
	}

	@Override
	public String getQualifiedTableName(String table, String schemaName, String databaseName) {
		StringBuilder tableName = new StringBuilder();

		if (!isEmpty(databaseName)) {
			tableName.append(q(databaseName));
			tableName.append(".");
		}

		if (!isEmpty(schemaName)) {
			tableName.append(q(schemaName));
			tableName.append(".");
		}

		tableName.append(q(table));

		return tableName.toString();
	}


	@Override
	public Set<DialectFeature> getFeatures() {
		return DIALECT_FEATURES;
	}

	public StreamEx<String> getSqlTypes() {
		return StreamEx
			.of(MySqlType.values())
			.map(x -> x.type);
	}
	@Override
	@SneakyThrows
	public java.sql.Timestamp getTimestampRoundUp(ResultSet rs, int column) {
		return this.roundUpTimestamp(super.getTimestamp(rs, column));
	}

	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				// Maximum precision supported by MySQL is 65
				return "DECIMAL(65," + parameters.get(Decimal.SCALE_FIELD) + ")";
			case Date.LOGICAL_NAME:
				return MySqlType.DATE.type;
			case Time.LOGICAL_NAME:
				return "TIME(3)";
			case Timestamp.LOGICAL_NAME:
				return "DATETIME(3)";
			default:
				return null;
		}
	}

	public String getDbType(Schema.Type type) {
		switch (type) {
			case INT8:
				return MySqlType.TINYINT.type;
			case INT16:
				return MySqlType.SMALLINT.type;
			case INT32:
				return MySqlType.INT.type;
			case INT64:
				return MySqlType.BIGINT.type;
			case FLOAT32:
				return MySqlType.FLOAT.type;
			case FLOAT64:
				return MySqlType.DOUBLE.type;
			case BOOLEAN:
				return MySqlType.BOOLEAN.type;
			case STRING:
				return MySqlType.VARCHAR.type;
			case BYTES:
				return MySqlType.VARBINARY.type;
			default:
				return null;
		}
	}

	@Override
	public String getUpsertQuery(String table, Collection<String> keyCols, Collection<String> cols) {
		//MySql doesn't support SQL 2003:merge so here how the upsert is handled

		final StringBuilder builder = new StringBuilder();
		builder.append("INSERT INTO ");
		builder.append(table);
		builder.append("(");
		joinToBuilder(builder, ",", keyCols, cols, escaper());
		builder.append(") VALUES (");
		nCopiesToBuilder(builder, ",", "?", cols.size() + keyCols.size());
		builder.append(") ON DUPLICATE KEY UPDATE ");
		joinToBuilder(builder,
			",",
			cols.isEmpty() ? keyCols : cols,
			(builder1, col) -> builder1.append(q(col) + " = VALUES (" + q(col) + ")"));
		return builder.toString();
	}

	@SneakyThrows
	@Override
	public boolean supportsFractional(Connection connection) {
		if (connection.isWrapperFor(ConnectionImpl.class)) {
			ConnectionImpl conn =  connection.unwrap(ConnectionImpl.class);
			return conn.versionMeetsMinimum(5, 6, 4);
		}
		return false;
	}
}
