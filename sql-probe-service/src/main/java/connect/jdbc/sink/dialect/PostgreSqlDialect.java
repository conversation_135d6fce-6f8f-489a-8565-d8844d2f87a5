package connect.jdbc.sink.dialect;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.base.Supplier;
import com.nexla.connector.config.MappingConfig;
import connect.data.Date;
import connect.data.Decimal;
import connect.data.Schema;
import connect.data.SchemaBuilder;
import connect.data.Struct;
import connect.data.Time;
import connect.data.Timestamp;
import connect.jdbc.sink.dialect.type.PostgreSqlType;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.postgresql.util.PGobject;

import java.io.IOException;
import java.sql.*;
import java.util.*;

import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static connect.jdbc.sink.dialect.DialectFeature.MERGE;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static java.util.Collections.singleton;
import static java.util.Optional.empty;
import static java.util.Optional.of;

public class PostgreSqlDialect extends DbDialect {

	private static final Set<DialectFeature> DIALECT_FEATURES = singleton(MERGE);

	public static final String SCHEMA_PARAMETER = "search_path";
	private static final String DEFAULT_SCHEMA_NAME = "public";

	public PostgreSqlDialect() {
		super("\"", "\"", of(SCHEMA_PARAMETER), empty(), of(DEFAULT_SCHEMA_NAME), false);
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return DIALECT_FEATURES;
	}

	@Override
	public String getDatabaseTerm() {
		return SCHEMA;
	}

	public StreamEx<String> getSqlTypes() {
		return StreamEx
			.of(PostgreSqlType.values())
			.map(x -> x.type);
	}

	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return PostgreSqlType.DECIMAL.type;
			case Date.LOGICAL_NAME:
				return PostgreSqlType.DATE.type;
			case Time.LOGICAL_NAME:
				return PostgreSqlType.TIME.type;
			case Timestamp.LOGICAL_NAME:
				return PostgreSqlType.TIMESTAMP.type;
			default:
				return null;
		}
	}

	@Override
	@SneakyThrows
	public java.sql.Timestamp getTimestampRoundUp(ResultSet rs, int column) {
		return this.roundUpTimestamp(super.getTimestamp(rs, column));
	}

	public String getDbType(Schema.Type type) {
		switch (type) {
			case INT8:
				return PostgreSqlType.SMALLINT.type;
			case INT16:
				return PostgreSqlType.SMALLINT.type;
			case INT32:
				return PostgreSqlType.INT.type;
			case INT64:
				return PostgreSqlType.BIGINT.type;
			case FLOAT32:
				return PostgreSqlType.REAL.type;
			case FLOAT64:
				return PostgreSqlType.DOUBLE_PRECISION.type;
			case BOOLEAN:
				return PostgreSqlType.BOOLEAN.type;
			case STRING:
				return PostgreSqlType.TEXT.type;
			case BYTES:
				return PostgreSqlType.BYTEA.type;
			default:
				return null;
		}
	}

	@Override
	public String getUpsertQuery(final String table, final Collection<String> keyCols, final Collection<String> cols) {
		final StringBuilder builder = new StringBuilder();
		builder.append("INSERT INTO ");
		builder.append(table);
		builder.append(" (");
		joinToBuilder(builder, ",", keyCols, cols, escaper());
		builder.append(") VALUES (");
		StringBuilderUtil.nCopiesToBuilder(builder, ",", "?", cols.size() + keyCols.size());
		builder.append(") ON CONFLICT (");
		joinToBuilder(builder, ",", keyCols, escaper());
		if (cols.isEmpty()) {
			builder.append(") DO NOTHING");
		} else {
			builder.append(") DO UPDATE SET ");
			joinToBuilder(builder, ",", cols,
				(b, col) -> b.append(q(col) + "=EXCLUDED." + q(col)));
		}
		return builder.toString();
	}

	@Override
	public void resolveSqlType(ResultSetMetaData metadata, int col, SchemaBuilder builder, boolean mapNumerics, String fieldName, int sqlType, boolean optional) throws SQLException {
		if ("boolean".equals(metadata.getColumnTypeName(col)) || "bool".equals(metadata.getColumnTypeName(col))) {
			sqlType = Types.BOOLEAN;
		}
		if (PostgreSqlType.isVarcharType(metadata.getColumnTypeName(col))) {
			sqlType = Types.VARCHAR;
		}

		super.resolveSqlType(metadata, col, builder, mapNumerics, fieldName, sqlType, optional);
	}

	@Override
	@SneakyThrows
	public void convertFieldValue(
		ResultSet resultSet,
		int col,
		int colType,
		Struct struct,
		String fieldName,
		boolean mapNumerics,
		String typeName
	) {
		if ("boolean".equals(resultSet.getMetaData().getColumnTypeName(col)) ||
			"bool".equals(resultSet.getMetaData().getColumnTypeName(col))) {
			colType = Types.BOOLEAN;
		}
		if (PostgreSqlType.isVarcharType(resultSet.getMetaData().getColumnTypeName(col))) {
			colType = Types.VARCHAR;
		}
		super.convertFieldValue(resultSet, col, colType, struct, fieldName, mapNumerics, typeName);
	}

	public Schema getArrayTypeSchema(String arrayType, boolean optional) {
		if (optional) {
			switch (arrayType) {
				case "_varchar":
				case "_char":
				case "_text":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_STRING;
				case "_bool":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_BOOLEAN;
				case "_int2":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_INT16;
				case "_int4":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_INT32;
				case "_int8":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_INT64;
				case "_float4":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_FLOAT32;
				case "_float8":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_FLOAT64;
				case "_json":
				case "_jsonb":
					return Schema.OPTIONAL_ARRAYS_SCHEMA_JSON;
				default:
					throw new IllegalArgumentException("Array Type " + arrayType + " is not supported");
			}
		} else {
			switch (arrayType) {
				case "_varchar":
				case "_char":
				case "_text":
					return Schema.ARRAYS_SCHEMA_STRING;
				case "_bool":
					return Schema.ARRAYS_SCHEMA_BOOLEAN;
				case "_int2":
					return Schema.ARRAYS_SCHEMA_INT16;
				case "_int4":
					return Schema.ARRAYS_SCHEMA_INT32;
				case "_int8":
					return Schema.ARRAYS_SCHEMA_INT64;
				case "_float4":
					return Schema.ARRAYS_SCHEMA_FLOAT32;
				case "_float8":
					return Schema.ARRAYS_SCHEMA_FLOAT64;
				case "_json":
				case "_jsonb":
					return Schema.ARRAYS_SCHEMA_JSON;
				default:
					throw new IllegalArgumentException("Array Type " + arrayType + " is not supported");
			}
		}
	}

	@SneakyThrows
	protected Object getArray(Array array, String typeName) {
		if ("_jsonb".equals(typeName) || "_json".equals(typeName)) {
			return Arrays.stream((Object[]) array.getArray())
					.map(PGobject.class::cast)
					.map(PGobject::getValue)
					.map(JsonUtils::jsonToObject)
					.toArray(Object[]::new);
		}

		return super.getArray(array, typeName);
	}

	@Override
	public List<String> getAlterModifySqls(
			String tableName,
			String nonKeyCol,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(nonKeyCol))
				.findFirst()
				.map(e -> e.get(nonKeyCol))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElse(automaticBinding.get().getSqlType(nonKeyCol, this));

		return Collections.singletonList("ALTER TABLE " + tableName + " ALTER COLUMN " + q(nonKeyCol) + " TYPE " + sqlType + " USING " + q(nonKeyCol) + "::" + sqlType);
	}

	@Override
	public String getTargetName(String originalName) {
		return TableNameMapper.getName(originalName, TableNameMapper.groupNamePattern4, false, 62, "[\\w$]", TableNameMapper.prefix);
	}
}
