package connect.jdbc.sink.dialect.copy.storage;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaFile;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.file.service.FileConnectorService;

import java.io.File;
import java.nio.file.Paths;
import java.util.List;

public interface WarehouseCopyTempStorage {

	String getSourceCopyQuery(WarehouseCopyFileFormat format,
							  String destination,
							  String source,
							  String compression,
							  String single,
							  boolean optionallyEnclosed);

	String getSinkCopyQuery(String table,
							String columns,
							String destination,
							String format,
							String compression,
							String skipHeaders,
							String columnMatchMode,
							boolean stopOnError);

	String getUrlPrefix();

	List<NexlaFile> listObjects(FileSourceConnectorConfig fileSourceConnectorConfig, String bucketDest);

	String getTempUploadBucket();

	String getTempUploadPrefix();

	Boolean getDeleteTempBucket();

	ConnectionType getConnectionType();

	FileConnectorService getConnectorService();

	void deleteFile(String fileName);

	void uploadFile(File localFile);

	default String tempFileLocation(String fileName, String tempUploadPrefix) {
		return getUrlPrefix() + Paths.get(getTempUploadBucket(), tempUploadPrefix, fileName);
	}

}
