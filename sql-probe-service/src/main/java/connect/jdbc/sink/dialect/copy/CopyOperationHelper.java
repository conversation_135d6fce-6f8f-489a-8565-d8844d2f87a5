package connect.jdbc.sink.dialect.copy;

import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.jdbc.sink.dialect.AzureSynapseDialect;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.OracleAutonomousDialect;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

import static java.util.stream.Collectors.joining;
import static org.apache.commons.lang3.StringUtils.isEmpty;

public class CopyOperationHelper {

	static String createCommandSnowflake(String tempTable, String qualifiedTableName) {
		return "CREATE TEMPORARY TABLE " + tempTable +
			   " LIKE " + qualifiedTableName;
	}

	static String createCommandRedshift(String tempTable, String qualifiedTableName) {
		return "CREATE TEMPORARY TABLE " + tempTable +
			   " (LIKE " + qualifiedTableName + ")";
	}

	private static String pkJoinConditionsCommand(
		List<String> primaryKeys, DbDialect dbDialect, String tempTable, String qualifiedTableName) {

		return primaryKeys.stream()
			.map(col -> qualifiedTableName + "." + dbDialect.q(col) + " = " + tempTable + "." + dbDialect.q(col))
			.collect(joining(" AND "));
	}

	static String deleteRowsCommand(
		List<String> primaryKeys, DbDialect dbDialect, String schema, String tempTable, String qualifiedTableName) {

		if (dbDialect instanceof OracleAutonomousDialect) {
			String table;

			if (isEmpty(schema) || tempTable.contains(schema)) {
				table = tempTable;
			} else {
				table = dbDialect.q(schema) + "." + tempTable;
			}
			return deleteRowsCommandWithInSelect(primaryKeys, table, qualifiedTableName);
		}

		if (dbDialect instanceof AzureSynapseDialect) {
			return deleteRowsCommandWithInSelect(primaryKeys, tempTable, qualifiedTableName);
		}

		return "DELETE FROM " + qualifiedTableName + " " +
			   "USING " + tempTable + " " +
			   "WHERE " + pkJoinConditionsCommand(primaryKeys, dbDialect, tempTable, qualifiedTableName);
	}

	private static String deleteRowsCommandWithInSelect(
			List<String> primaryKeys, String tempTable, String qualifiedTableName) {

		final String keyColumns = String.join(", ", primaryKeys);

		return "DELETE FROM " + qualifiedTableName + " " +
				"WHERE (" + keyColumns + ") " +
				"IN (SELECT " + keyColumns + " FROM " + tempTable + ")";
	}

	public static String redshiftServerlessFetchQuery(Optional<String> fileName) {
		if (fileName.isPresent()) {
			return "SELECT * FROM SYS_LOAD_HISTORY" +
					" WHERE query_id = " +
					"(SELECT query_id FROM SYS_QUERY_HISTORY" +
					" WHERE query_text LIKE '" +
					"%" + fileName.get() + "%" +
					"' ORDER BY start_time DESC" +
					" LIMIT 1)";
		} else {
			return "SELECT * FROM SYS_LOAD_HISTORY WHERE query_id = PG_LAST_QUERY_ID()";
		}
	}

	public static String stlLoadErrorFetchQuery(Optional<String> fileName, boolean isServerless) {
		if (isServerless) {
			return redshiftServerlessFetchQuery(fileName);
		} else {
			return stlLoadErrorFetchQueryUsualRedshift(fileName);
		}
	}

	public static String stlLoadErrorFetchQueryUsualRedshift(Optional<String> fileName) {
		if (fileName.isPresent()) {
			return "SELECT * FROM stl_load_errors" +
				   " WHERE query = " +
				   "(SELECT query FROM stl_query" +
				   " WHERE querytxt LIKE '" +
				   "%" + fileName.get() + "%" +
				   "' ORDER BY starttime DESC" +
				   " LIMIT 1)";
		} else {
			return "SELECT * FROM stl_load_errors WHERE query = PG_LAST_QUERY_ID()";
		}
	}

	static String dropCommand(String table) {
		return "DROP TABLE " + table;
	}

	public static String createFileFormat(WarehouseCopyFileFormat fileFormat, String formatName){

		String sql;
		if (fileFormat.format == WarehouseCopyFileFormat.Format.CSV_FORMAT) {
			// EMPTY_FIELD_AS_NULL = FALSE or use FIELD_OPTIONALLY_ENCLOSED_BY
			sql = String.format("CREATE OR REPLACE FILE FORMAT %s " +
							"TYPE = CSV " +
							"COMPRESSION = %s " +
							"FIELD_DELIMITER = '%s' " +
							"ESCAPE = '\\\\ " +
							"FIELD_OPTIONALLY_ENCLOSED_BY = '\"' " +
							"NULL_IF = ('') ",
					formatName, fileFormat.compression.toString(), fileFormat.delimiter);
		} else {
			sql = String.format("CREATE OR REPLACE FILE FORMAT %s " +
							"TYPE = JSON " +
							"NULL_IF = ('') " +
							"FILE_EXTENSION = 'json'",
					formatName);
		}

		return sql;
	}

	public static String createStage(String stageName, String formatName){
		return "CREATE OR REPLACE STAGE " + stageName + " FILE_FORMAT = '" + formatName + "'";
	}

	public static String uploadResultSql(String file, String unqualifiedTableName, boolean direct) {

		if (!direct) {
			return "SELECT * FROM information_schema.load_history\n" +
					"WHERE TABLE_NAME = '" + unqualifiedTableName.toUpperCase() + "'\n" +
					"AND FILE_NAME = '" + file + "'\n" +
					"ORDER BY LAST_LOAD_TIME DESC\n" +
					"LIMIT 1";
		} else {
			return "SELECT * FROM information_schema.load_history\n" +
					"WHERE TABLE_NAME = '" + unqualifiedTableName.toUpperCase() + "'\n" +
					"AND FILE_NAME like '%" + file + "%'\n" +
					"ORDER BY LAST_LOAD_TIME DESC\n" +
					"LIMIT 1";
		}
	}
}
