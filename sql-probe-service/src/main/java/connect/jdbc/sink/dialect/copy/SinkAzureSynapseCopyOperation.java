package connect.jdbc.sink.dialect.copy;

import com.google.common.annotations.VisibleForTesting;
import com.nexla.connector.config.file.AzureAuthConfig;
import connect.data.Schema;
import connect.jdbc.sink.dialect.AzureSynapseDialect;
import connect.jdbc.sink.dialect.DbDialect;
import lombok.SneakyThrows;

import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.Optional;
import java.util.function.Supplier;

import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Compression.GZIP;
import static connect.jdbc.sink.dialect.copy.CopyOperationHelper.dropCommand;

public class SinkAzureSynapseCopyOperation extends SinkAzureCopyOperation {

	private static final int MAX_ERRORS = 100000;

	public static final AzureSynapseDialect DIALECT = new AzureSynapseDialect();

	@Override
	@SneakyThrows
	protected void executeCopyCommand(String blobLocation, Connection connection, Optional<ReplicationContext> replicationContext) {
		String sql = copyCommand(schema, blobLocation, DIALECT.q(config.table), replicationContext);
		try (PreparedStatement loadStatement = connection.prepareStatement(logSql(sql))) {
			loadStatement.execute();
			connection.commit();
		}
	}

	@Override
	public DbDialect dbDialect() {
		return DIALECT;
	}

	@Override
	String tempFileLocation(String fileName) {
		return "https://" + Paths.get(config.azureAuthConfig.storageAccountName + ".blob.core.windows.net"
			, config.tempAzureBlobUploadBucket, config.tempAzureBlobUploadPrefix, fileName);
	}

	@VisibleForTesting
	@Override
	public String copyCommand(Schema schema, String blobLocation, String table, Optional<ReplicationContext> replicationContext) {
		String columns = columns(schema, replicationContext.flatMap(ReplicationContext::getColumns));
		AzureAuthConfig azureAuthConfig = config.azureAuthConfig;
		String tableNameWithSchema = config.authConfig.schemaName != null
			? DIALECT.q(config.authConfig.schemaName) + "." + table
			: table;

		String compression = fileFormat.compression == GZIP ? " COMPRESSION = 'Gzip'," : "";

		return String.format("COPY INTO %s %s" +
			" FROM '%s'" +
			" WITH" +
			" (" +
			" FILE_TYPE = 'CSV'," +
			" CREDENTIAL=(IDENTITY= 'Storage Account Key', SECRET='%s')," +
			compression +
			" FIELDTERMINATOR =  ';'," +
			" MAXERRORS = %d" +
			" )", tableNameWithSchema, columns, blobLocation, azureAuthConfig.storageAccountKey, MAX_ERRORS);

	}

	@Override
	protected String createCommand(String tempTable, String qualifiedTableName) {
		return String.format("CREATE TABLE %s" +
			" WITH (DISTRIBUTION = ROUND_ROBIN)" +
			" AS (SELECT * FROM %s WHERE 1 = 2)", tempTable, qualifiedTableName);
	}

	@Override
	public String getTempTableName() {
		return dbDialect().q("#" + config.table + "_temp");
	}

	@SneakyThrows
	@Override
	protected void createTempTable(Statement st, String createCommand) {
		// azure synapse does not support DDL such as CREATE TABLE inside a user-defined transaction
		executeInImplicitTransaction(st, () -> createCommand);
	}

	@SneakyThrows
	@Override
	public void dropTempTable(Statement st, String tempTable) {
		executeInImplicitTransaction(st, () -> dropCommand(tempTable));
	}

	@SneakyThrows
	private void executeInImplicitTransaction(Statement st, Supplier<String> command) {
		st.getConnection().setAutoCommit(true);
		st.execute(logSql(command.get()));
		st.getConnection().setAutoCommit(false);
	}
}
