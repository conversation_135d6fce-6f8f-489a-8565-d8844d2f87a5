package connect.jdbc.sink.dialect.type;

public enum SqlServerType {

	BIGINT("BIGINT"),
	INT("INT"),
	SMALLINT("SMALLINT"),
	TINYINT("TINYINT"),
	BIT("BIT"),
	<PERSON>UMERIC("<PERSON><PERSON><PERSON><PERSON>"),
	<PERSON><PERSON><PERSON><PERSON>("<PERSON>ONE<PERSON>"),
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("SMALLMONEY"),
	R<PERSON><PERSON>("REAL"),
	DATETIME("DATETIME"),
	SMALLDATETIM<PERSON>("SMALLDATETIME"),
	CHAR("CHAR(3000)"),
	VARCHAR_100("VARCHAR(100)"),
	VARCHAR("VARCHAR(3000)"),
	VARCHAR_MAX("VARCHAR(max)"),
	TEXT("TEXT"),
	NCHAR("NCHAR"),
	NVARCHAR("NVARCHAR"),
	NTEXT("NTEXT"),
	BINARY("BINARY"),
	VARBINARY("VARBINARY"),
	TAB<PERSON>("TABLE"),
	UNIQUEIDENTIFIER("UNIQUEIDENTIFIER"),
	DECIMAL("DECIMAL(18, 4)"),
	FLOAT("FLOAT(12)");

	public final String type;

	SqlServerType(String type) {
		this.type = type;
	}
}