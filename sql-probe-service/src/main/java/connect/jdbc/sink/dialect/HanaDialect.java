package connect.jdbc.sink.dialect;

import connect.data.Date;
import connect.data.Decimal;
import connect.data.Schema;
import connect.data.Time;
import connect.data.Timestamp;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

import static connect.jdbc.sink.dialect.DialectFeature.MERGE;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static connect.jdbc.sink.dialect.StringBuilderUtil.nCopiesToBuilder;
import static java.util.Optional.empty;

public class HanaDialect extends DbDialect {

	public static final Set<DialectFeature> DIALECT_FEATURES = Collections.singleton(MERGE);

	public HanaDialect() {
		super("\"", "\"", empty(), empty(), empty(), false);
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return DIALECT_FEATURES;
	}

	@Override
	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return "DECIMAL";
			case Date.LOGICAL_NAME:
				return "DATE";
			case Time.LOGICAL_NAME:
				return "DATE";
			case Timestamp.LOGICAL_NAME:
				return "TIMESTAMP";
			default:
				return null;
		}
	}

	@Override
	public String getDbType(Schema.Type type) {
		switch (type) {
			case INT8:
				return "TINYINT";
			case INT16:
				return "SMALLINT";
			case INT32:
				return "INTEGER";
			case INT64:
				return "BIGINT";
			case FLOAT32:
				return "REAL";
			case FLOAT64:
				return "DOUBLE";
			case BOOLEAN:
				return "BOOLEAN";
			case STRING:
				return "VARCHAR(1000)";
			case BYTES:
				return "BLOB";
			default:
				return null;
		}
	}

	@Override
	public String getUpsertQuery(final String table, Collection<String> keyCols, Collection<String> cols) {
		// https://help.sap.com/hana_one/html/sql_replace_upsert.html
		StringBuilder builder = new StringBuilder("UPSERT ");
		builder.append(table);
		builder.append("(");
		joinToBuilder(builder, ",", keyCols, cols, escaper());
		builder.append(") VALUES(");
		nCopiesToBuilder(builder, ",", "?", keyCols.size() + cols.size());
		builder.append(")");
		builder.append(" WITH PRIMARY KEY");
		return builder.toString();
	}

	@Override
	public String getDatabaseTerm() {
		return SCHEMA;
	}
}
