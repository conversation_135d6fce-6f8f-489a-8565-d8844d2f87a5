package connect.jdbc.sink.dialect;

import com.google.common.collect.Sets;
import connect.data.Date;
import connect.data.Decimal;
import connect.data.Schema;
import connect.data.SchemaBuilder;
import connect.data.Struct;
import connect.data.Time;
import connect.data.Timestamp;
import connect.jdbc.sink.dialect.type.OracleType;
import connect.jdbc.sink.dialect.type.RedshiftType;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.nexla.common.StreamUtils.map;
import static connect.jdbc.sink.dialect.DialectFeature.MERGE;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static java.util.Collections.singleton;
import static java.util.Optional.empty;

public class OracleDialect extends DbDialect {

	private static final Set<DialectFeature> DIALECT_FEATURES = singleton(MERGE);

	protected final Map<String, Integer> typeMappings = map(
			OracleType.BINARY_DOUBLE.type, Types.DOUBLE,
			OracleType.BINARY_FLOAT.type, Types.FLOAT,
			OracleType.INTERVAL_DAY_TO_SECOND.type, Types.VARCHAR,
			OracleType.INTERVAL_YEAR_TO_MONTH.type, Types.VARCHAR,
			OracleType.INTERVAL_DS.type, Types.VARCHAR,
			OracleType.INTERVAL_YM.type, Types.VARCHAR,
			OracleType.VARCHAR2.type, Types.VARCHAR,
			OracleType.NVARCHAR2.type, Types.VARCHAR,
			OracleType.LONG.type, Types.LONGVARCHAR,
			OracleType.RAW.type, Types.VARBINARY,
			OracleType.DATE.type, Types.DATE,
			OracleType.TIMESTAMP_WITH_TIME_ZONE.type, Types.VARCHAR,
			OracleType.TIMESTAMP_WITH_LOCAL_TIME_ZONE.type, Types.VARCHAR,
			OracleType.ROWID.type, Types.VARCHAR,
			OracleType.UROWID.type, Types.VARCHAR
	);

	public OracleDialect() {
		super("\"", "\"", empty(), empty(), empty(), true);
	}

	@Override
	public Set<String> systemSchemas() {
		return Sets.newHashSet("ADMIN", "ANONYMOUS", "APPQOSSYS", "AUDSYS", "CTXSYS", "DBSNMP", "DIP",
			"GSMADMIN_INTERNAL", "GSMCATUSER", "GSMUSER", "OUTLN", "RDSADMIN", "SYS", "SYSBACKUP", "SYSDG", "SYSKM",
			"SYSTEM", "XDB", "XS$NULL");
	}

	@Override
	@SneakyThrows
	protected long sqlDateTimeToMillis(Object obj) {
		return super.sqlDateTimeToMillis(((oracle.sql.TIMESTAMP) obj).timestampValue());
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return DIALECT_FEATURES;
	}

	@Override
	public String getDatabaseTerm() {
		return SCHEMA;
	}

	public StreamEx<String> getSqlTypes() {
		return StreamEx
			.of(OracleType.values())
			.map(x -> x.type);
	}

	@Override
	public void resolveSqlType(ResultSetMetaData metadata, int col, SchemaBuilder builder, boolean mapNumerics, String fieldName, int sqlType, boolean optional) throws SQLException {
		int resultSqlType = typeMappings.getOrDefault(metadata.getColumnTypeName(col), sqlType);
		super.resolveSqlType(metadata, col, builder, mapNumerics, fieldName, resultSqlType, optional);
	}

	@Override
	@SneakyThrows
	public void convertFieldValue(
		ResultSet resultSet,
		int col,
		int colType,
		Struct struct,
		String fieldName,
		boolean mapNumerics,
		String typeName
	) {
		colType = typeMappings.getOrDefault(typeName, colType);

		super.convertFieldValue(resultSet, col, colType, struct, fieldName, mapNumerics, typeName);
	}

	@Override
	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return "NUMBER(*," + parameters.get(Decimal.SCALE_FIELD) + ")";
			case Date.LOGICAL_NAME:
				return "DATE";
			case Time.LOGICAL_NAME:
				return "DATE";
			case Timestamp.LOGICAL_NAME:
				return "TIMESTAMP";
			default:
				return null;
		}
	}

	@Override
	public String getDbType(Schema.Type type) {
		switch (type) {
			case INT8:
				return "NUMBER(3,0)";
			case INT16:
				return "NUMBER(5,0)";
			case INT32:
				return "NUMBER(10,0)";
			case INT64:
				return "NUMBER(19,0)";
			case FLOAT32:
				return "BINARY_FLOAT";
			case FLOAT64:
				return "BINARY_DOUBLE";
			case BOOLEAN:
				return "NUMBER(1,0)";
			case STRING:
				return "VARCHAR2(3000)";
			case BYTES:
				return "BLOB";
			default:
				return null;
		}
	}

	@Override
	public String getUpsertQuery(String tableName, Collection<String> keyCols, Collection<String> cols) {
		// https://blogs.oracle.com/cmar/entry/using_merge_to_do_an

		StringBuilder builder = new StringBuilder();
		builder.append("merge into ");
		builder.append(tableName);
		builder.append(" using (select ");
		joinToBuilder(builder, ", ", keyCols, cols, prefixedEscaper("? "));
		builder.append(" FROM dual) incoming on(");
		joinToBuilder(builder, " and ", keyCols, new StringBuilderUtil.Transform<String>() {
			@Override
			public void apply(StringBuilder builder, String col) {
				builder.append(tableName).append(".").append(q(col)).append("=incoming.").append(q(col));
			}
		});
		builder.append(")");
		if (cols != null && cols.size() > 0) {
			builder.append(" when matched then update set ");
			joinToBuilder(builder, ",", cols, new StringBuilderUtil.Transform<String>() {
				@Override
				public void apply(StringBuilder builder, String col) {
					builder.append(tableName).append(".").append(q(col)).append("=incoming.").append(q(col));
				}
			});
		}

		builder.append(" when not matched then insert(");
		joinToBuilder(builder, ",", cols, keyCols, prefixedEscaper(tableName + "."));
		builder.append(") values(");
		joinToBuilder(builder, ",", cols, keyCols, prefixedEscaper("incoming."));
		builder.append(")");
		return builder.toString();
	}
}
