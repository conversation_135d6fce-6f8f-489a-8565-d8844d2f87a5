package connect.jdbc.sink;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.nexla.common.logging.NexlaLogger;
import connect.data.Date;
import connect.data.Decimal;
import connect.data.Field;
import connect.data.Schema;
import connect.data.Time;
import connect.data.Timestamp;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.util.DateTimeUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Pattern;

import static java.lang.Long.parseLong;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNumeric;
import static org.apache.commons.lang3.math.NumberUtils.BYTE_ONE;
import static org.apache.commons.lang3.math.NumberUtils.BYTE_ZERO;
import static org.joda.time.DateTimeZone.UTC;
import static org.joda.time.format.ISODateTimeFormat.date;
import static org.slf4j.helpers.MessageFormatter.format;

public class PreparedStatementBinder {

	private static final Pattern MM_DD_YYYY = Pattern.compile("\\d{2}/\\d{2}/\\d{4}");

	private Supplier<NexlaLogger> logger =
		Suppliers.memoize(() -> new NexlaLogger(LoggerFactory.getLogger(getClass()), ""));

	private final PreparedStatement statement;
	private static final Set<String> validPositiveBooleanValues = new HashSet<>() {{
		// redshift and bigquery
		add("TRUE");
		add("t");
		add("true");
		add("y");
		add("yes");
		add("1");
		// snowflake
		add("on");
	}};

	public PreparedStatementBinder(PreparedStatement statement) {
		this.statement = statement;
	}

	public PreparedStatementBinder withLoggerPrefix(Object... prefixParts) {
		this.logger.get().setPrefix(prefixParts);
		return this;
	}

	@SneakyThrows
	public void bindRecord(Map<String, Object> record, Schema schema, List<String> allFieldsOrdered) {
		int index = 1;
		bindRecord(index, record, schema, allFieldsOrdered);
		logger.get().trace("Record itself: {}", record);
		statement.addBatch();
	}

	@SneakyThrows
	public void bindUpdateRecord(Map<String, Object> before, Map<String, Object> after, Schema schema, List<String> allFieldsOrdered) {
		int startIndex = 1;
		// first populate the SET section with new values
		startIndex = bindRecord(startIndex, after, schema, allFieldsOrdered);
		// second populate the WHERE section with old values
		bindRecord(startIndex, before, schema, allFieldsOrdered);
		logger.get().trace("Before Record itself: {}", before);
		logger.get().trace("After Record itself: {}", after);
		statement.addBatch();
	}

	@SneakyThrows
	private int bindRecord(int index, Map<String, Object> record, Schema schema, List<String> allFieldsOrdered) {
		for (String fieldName : allFieldsOrdered) {
			Field field = schema.field(fieldName);
			try {
				bindField(index, field.schema(), convertValue(record.get(fieldName), field.schema()));
				index++;
			} catch (Exception e) {
				String errorMsg = format("expected column \"{}\" to be of format \"{}\"",
						fieldName, field.schema().type().getName()).getMessage();
				logger.get().error("Failed binding on index {}, " + errorMsg, index);

				if (e.getMessage() != null) {
					errorMsg = e.getMessage() + ", " + errorMsg;
				}
				throw new Exception(errorMsg, e);
			}
		}
		return index;
	}

	@SneakyThrows
	public void bindMultipleRecords(Map<String, Object> record, Schema schema, List<String> allFieldsOrdered, int index) {
		for (String fieldName : allFieldsOrdered) {
			Field field = schema.field(fieldName);
			bindField(index, field.schema(), convertValue(record.get(fieldName), field.schema()));
			index++;
		}
	}

	@SneakyThrows
	public void addBatch() {
		statement.addBatch();
	}

	@SneakyThrows
	public static String convertValueToString(Object value, Field field, Logger logger, DbDialect dbDialect) {
		if (value == null || ("null".equalsIgnoreCase(value.toString()) && dbDialect.convertNullStringsToNullsUnload())) {
			return null;
		}
		if (value.toString().isEmpty()) {
			return StringUtils.EMPTY;
		}
		Schema fieldSchema = field.schema();
		String valStr = value.toString();
		try {
			return maybeConvertToStringLogical(fieldSchema, valStr, dbDialect)
 				.orElseGet(() -> convertValue(value, fieldSchema).toString());
		} catch (Exception e) {
			logger.info("Failed binding for value '{}' on field '{}'", valStr, field.name(), e);
			throw new Exception("Failed binding for value '" + valStr + "' on field '" + field.name() + "'", e);
		}
	}

	private static Object convertValue(Object value, Schema fieldSchema) {
		if (value == null || value.toString().isEmpty()) {
			return null;
		}
		if (fieldSchema.name() != null) {
			switch (fieldSchema.name()) {
				case Decimal.LOGICAL_NAME:
					return new BigDecimal(value.toString());
				case Date.LOGICAL_NAME:
				case Time.LOGICAL_NAME:
				case Timestamp.LOGICAL_NAME:
					return new java.util.Date(com.nexla.common.datetime.DateTimeUtils.epochFromTimeStamp(value.toString()).get());
			}
		}

		switch (fieldSchema.type()) {
			case INT8:
				if (value instanceof Boolean) { // some dbs (ie Oracle) store booleans as byte
					return (Boolean) value ? BYTE_ONE : BYTE_ZERO;
				} else {
					return Byte.parseByte(value.toString());
				}
			case INT16:
				return new BigDecimal(value.toString()).shortValueExact();
			case INT32:
				return new BigDecimal(value.toString()).intValueExact();
			case INT64:
				return Long.parseLong(value.toString());
			case FLOAT32:
				return new BigDecimal(value.toString()).floatValue();
			case FLOAT64:
				return new BigDecimal(value.toString()).doubleValue();
			case BOOLEAN:
				return validPositiveBooleanValues.contains(value.toString()) ||
						Boolean.parseBoolean(value.toString());
			case STRING:
				return value.toString();
			case BYTES:
				// todo
			default:
				throw new RuntimeException("Unsupported source data type: " + fieldSchema.type());
		}
	}

	private void bindField(int index, Schema schema, Object value) throws SQLException {
		bindField(statement, index, schema, value);
	}

	private static void bindField(PreparedStatement statement, int index, Schema schema, Object value) throws SQLException {

		if (value == null) {
			statement.setObject(index, null);
			return;
		}

		if (maybeBindLogical(statement, index, schema, value)) {
			return;
		}

		switch (schema.type()) {
			case INT8:
				statement.setByte(index, (Byte) value);
				break;
			case INT16:
				statement.setShort(index, (Short) value);
				break;
			case INT32:
				statement.setInt(index, (Integer) value);
				break;
			case INT64:
				statement.setLong(index, (Long) value);
				break;
			case FLOAT32:
				statement.setFloat(index, (Float) value);
				break;
			case FLOAT64:
				statement.setDouble(index, (Double) value);
				break;
			case BOOLEAN:
				statement.setBoolean(index, (Boolean) value);
				break;
			case STRING:
				statement.setString(index, (String) value);
				break;
			case BYTES:
				final byte[] bytes;
				if (value instanceof ByteBuffer) {
					final ByteBuffer buffer = ((ByteBuffer) value).slice();
					bytes = new byte[buffer.remaining()];
					buffer.get(bytes);
				} else {
					bytes = (byte[]) value;
				}
				statement.setBytes(index, bytes);
				break;
			default:
				throw new RuntimeException("Unsupported source data type: " + schema.type());
		}
	}

	private static Optional<String> maybeConvertToStringLogical(Schema schema, String valStr, DbDialect dbDialect) {

		if (schema.name() != null) {
			switch (schema.name()) {
				case Date.LOGICAL_NAME:
					if (isNumeric(valStr)) {
						return ofNullable(new DateTime(parseLong(valStr), UTC).toString(date()));
					} else if (MM_DD_YYYY.matcher(valStr).matches()) {
						return ofNullable(dbDialect.getDateFormatter().parseLocalDate(valStr).toString(date()));
					} else {
						return Optional.of(valStr);
					}
				case Decimal.LOGICAL_NAME:
					return ofNullable(valStr);
				case Time.LOGICAL_NAME:
					return ofNullable(isNumeric(valStr) ? new LocalTime(parseLong(valStr), UTC).toString() : valStr);
				case Timestamp.LOGICAL_NAME:
					if (isNumeric(valStr)) {
						return ofNullable(new DateTime(parseLong(valStr), UTC)
							.toString(dbDialect.getDateTimeFormatter()));
					} else if (MM_DD_YYYY.matcher(valStr).matches()) {
						return ofNullable(dbDialect.getDateFormatter().parseDateTime(valStr).toString(date()));
					} else {
						return Optional.of(valStr);
					}
			}
		}
		return Optional.empty();
	}

	private static boolean maybeBindLogical(PreparedStatement statement, int index, Schema schema, Object value) throws SQLException {

		if (schema.name() != null) {
			switch (schema.name()) {
				case Date.LOGICAL_NAME:
					statement.setDate(index, new java.sql.Date(((java.util.Date) value).getTime()), DateTimeUtils.UTC_CALENDAR.get());
					return true;
				case Decimal.LOGICAL_NAME:
					statement.setBigDecimal(index, value != null ? new BigDecimal(value.toString()) : (BigDecimal) value);
					return true;
				case Time.LOGICAL_NAME:
					statement.setTime(index, new java.sql.Time(((java.util.Date) value).getTime()), DateTimeUtils.UTC_CALENDAR.get());
					return true;
				case Timestamp.LOGICAL_NAME:
					statement.setTimestamp(index, new java.sql.Timestamp(((java.util.Date) value).getTime()), DateTimeUtils.UTC_CALENDAR.get());
					return true;
				default:
					return false;
			}
		}
		return false;
	}
}
