package connect.jdbc.util.debezium;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.NexlaMessage;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @deprecated Moved to connector commons {@link com.nexla.connect.common.cdc.DebeziumData}
 */
@RequiredArgsConstructor
@ToString
@Deprecated
public class DebeziumData {
	private static final Logger logger = LoggerFactory.getLogger(DebeziumData.class);

	@Getter
	@JsonProperty("primary_key")
	public final List<String> primaryKey;

	@Getter
	@JsonProperty("before")
	public final LinkedHashMap<String, Object> beforeData;

	@Getter
	@JsonProperty("after")
	public final LinkedHashMap<String, Object> afterData;

	@Getter
	// value contains kafka connect Literal type as a key and kafka connect Semantic type as a value if exist
	public final LinkedHashMap<String, Map<String, String>> schema;

	public static DebeziumData newDebeziumData(NexlaMessage nm, int sinkId){

		Object info = nm.getRawMessage().get(DebeziumConstants.NEXLA_CDC_INFO);
		try {
			String dataStr = JsonUtils.toJsonString(info);
			return JsonUtils.stringToType(dataStr, DebeziumData.class);
		} catch (Exception e) {
			logger.warn("[SINK-{}] can't parse NEXLA_CDC_INFO, trying again... \nerror={}, \nmsg={}",
					sinkId, e.getMessage(), info);
			return JsonUtils.stringToType(info.toString(), DebeziumData.class);
		}
	}
}
