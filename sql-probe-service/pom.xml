<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.nexla</groupId>
        <artifactId>backend-connectors</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <groupId>com.nexla.probe</groupId>
    <artifactId>sql-probe</artifactId>


    <!--NEX-9297 library bom override because global pom is using an older version that doesn't support spanner data boost new feature-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>libraries-bom</artifactId>
                <version>26.22.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>common-sc</artifactId>
            <version>${nexla-backend-common.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>s3-probe</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>azure-blob-probe</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>common</artifactId>
            <version>${nexla-backend-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>common-connector</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facebook.presto</groupId>
            <artifactId>presto-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-jaxb</artifactId>
            <version>${jersey-media-jaxb.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hbase.thirdparty</groupId>
            <artifactId>hbase-shaded-jetty</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.hbase.thirdparty</groupId>
            <artifactId>hbase-shaded-jersey</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>${apache.ant.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
            <version>4.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-mapreduce</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.thrift</groupId>
                    <artifactId>libthrift</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.ant</groupId>
                    <artifactId>ant</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-hdfs</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-jaxb</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-protocol-shaded</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase.thirdparty</groupId>
                    <artifactId>hbase-shaded-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hbase</groupId>
            <artifactId>hbase-mapreduce</artifactId>
            <version>2.6.2</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hbase</groupId>
            <artifactId>hbase-client</artifactId>
            <version>2.6.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-protocol-shaded</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase.thirdparty</groupId>
                    <artifactId>hbase-shaded-gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hbase</groupId>
            <artifactId>hbase-server</artifactId>
            <version>2.6.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-protocol-shaded</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase.thirdparty</groupId>
                    <artifactId>hbase-shaded-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase.thirdparty</groupId>
                    <artifactId>hbase-shaded-jersey</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-hdfs</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>reload4j</artifactId>
                    <groupId>ch.qos.reload4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hadoop.thirdparty</groupId>
                    <artifactId>hadoop-shaded-guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-server</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty-all.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <version>${apache.thrift.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>8.5.75</version>
        </dependency>

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${zookeeper.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.oracle.ojdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.oracle.ojdbc</groupId>
                    <artifactId>simplefan</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.oracle.ojdbc</groupId>
                    <artifactId>ons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>Spark</groupId>
            <artifactId>SparkJDBC42</artifactId>
            <version>${sparkJDBC.version}</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>

        <dependency>
            <groupId>net.sf.jt400</groupId>
            <artifactId>jt400-jdk9</artifactId>
        </dependency>

        <!-- https://forums.aws.amazon.com/thread.jspa?threadID=263573
             Dealing with spring-boot problems loading Redshift JDBC class -->
        <dependency>
            <groupId>com.amazon.redshift</groupId>
            <artifactId>redshift-jdbc42</artifactId>
            <version>${amazon.redshift.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>redshift</artifactId>
        </dependency>

        <dependency>
            <groupId>net.snowflake</groupId>
            <artifactId>snowflake-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
        </dependency>

        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <dependency>
            <groupId>io.firebolt</groupId>
            <artifactId>firebolt-jdbc</artifactId>
        </dependency>
        <!-- log4j-core is needed only for Firebolt.
               Firebolt driver was a jar download that included various common artifacts like log4j and jackson.
               The versions of those artifacts conflicted with our version. So they were removed from the jar,
               but log4j-core is needed so add it back in here-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j2.version}</version>
        </dependency>

        <dependency>
            <groupId>com.netsuite.jdbc.openaccess</groupId>
            <artifactId>NQjc</artifactId>
            <version>${netsuite-jdbc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.teradata.jdbc</groupId>
            <artifactId>terajdbc4</artifactId>
            <version>${teradata-jdbc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.netsuite.jdbc.openaccess</groupId>
            <artifactId>NQjc</artifactId>
            <version>${netsuite-jdbc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sybase.jdbc</groupId>
            <artifactId>jconn4</artifactId>
            <version>${sybase-jdbc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.amazon.athena</groupId>
            <artifactId>athena-jdbc</artifactId>
            <version>${athenaJDBC.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>jcc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sap.cloud.db.jdbc</groupId>
            <artifactId>ngdbc</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-spanner-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.databricks</groupId>
            <artifactId>databricks-jdbc</artifactId>
            <version>2.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.databricks</groupId>
            <artifactId>databricks-sdk-java</artifactId>
            <version>0.29.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.23.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.23.2</version>
        </dependency>

        <!-- tests -->

        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.dimafeng</groupId>
            <artifactId>testcontainers-scala-scalatest_${scala.short.version}</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.dimafeng</groupId>
            <artifactId>testcontainers-scala-postgresql_${scala.short.version}</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.24.2</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-embedded</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>

            <!--NEX-9297 shading all google dependencies that are necessary for spanner data boost to work. Otherwise global pom declaration would override the most recent version -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <artifactSet>
                                <includes>
                                    <include>com.google.android:annotations:*</include>
                                    <include>com.google.api.grpc:grpc-google-cloud-spanner-admin-database-v1:*</include>
                                    <include>com.google.api.grpc:grpc-google-cloud-spanner-admin-instance-v1:*</include>
                                    <include>com.google.api.grpc:grpc-google-cloud-spanner-v1:*</include>
                                    <include>com.google.api.grpc:grpc-google-common-protos:*</include>
                                    <include>com.google.api.grpc:proto-google-cloud-spanner-admin-database-v1:*</include>
                                    <include>com.google.api.grpc:proto-google-cloud-spanner-admin-instance-v1:*</include>
                                    <include>com.google.api.grpc:proto-google-cloud-spanner-v1:*</include>
                                    <include>com.google.api.grpc:proto-google-common-protos:*</include>
                                    <include>com.google.api.grpc:proto-google-iam-v1:*</include>
                                    <include>com.google.api:api-common:*</include>
                                    <include>com.google.api:gax-grpc:*</include>
                                    <include>com.google.api:gax-httpjson:*</include>
                                    <include>com.google.api:gax:*</include>
                                    <include>com.google.auth:google-auth-library-credentials:*</include>
                                    <include>com.google.auth:google-auth-library-oauth2-http:*</include>
                                    <include>com.google.auto.value:auto-value-annotations:*</include>
                                    <include>com.google.cloud:google-cloud-core-grpc:*</include>
                                    <include>com.google.cloud:google-cloud-core:*</include>
                                    <include>com.google.cloud:google-cloud-spanner:*</include>
                                    <include>com.google.cloud:google-cloud-spanner-jdbc:*</include>
                                    <include>com.google.cloud:grpc-gcp:*</include>
                                    <include>com.google.code.findbugs:jsr305:*</include>
                                    <include>com.google.code.gson:gson:*</include>
                                    <include>com.google.errorprone:error_prone_annotations:*</include>
                                    <include>com.google.guava:failureaccess:*</include>
                                    <include>com.google.guava:guava:*</include>
                                    <include>com.google.guava:listenablefuture:*</include>
                                    <include>com.google.http-client:google-http-client-gson:*</include>
                                    <include>com.google.http-client:google-http-client:*</include>
                                    <include>com.google.j2objc:j2objc-annotations:*</include>
                                    <include>com.google.protobuf:protobuf-java-util:*</include>
                                    <include>com.google.protobuf:protobuf-java:*</include>
                                    <include>com.google.re2j:re2j:*</include>
                                    <include>commons-codec:commons-codec:*</include>
                                    <include>commons-logging:commons-logging:*</include>
                                    <include>io.grpc:grpc-alts:*</include>
                                    <include>io.grpc:grpc-api:*</include>
                                    <include>io.grpc:grpc-auth:*</include>
                                    <include>io.grpc:grpc-context:*</include>
                                    <include>io.grpc:grpc-core:*</include>
                                    <include>io.grpc:grpc-googleapis:*</include>
                                    <include>io.grpc:grpc-grpclb:*</include>
                                    <include>io.grpc:grpc-netty-shaded:*</include>
                                    <include>io.grpc:grpc-protobuf-lite:*</include>
                                    <include>io.grpc:grpc-protobuf:*</include>
                                    <include>io.grpc:grpc-rls:*</include>
                                    <include>io.grpc:grpc-services:*</include>
                                    <include>io.grpc:grpc-stub:*</include>
                                    <include>io.grpc:grpc-xds:*</include>
                                    <include>io.opencensus:opencensus-api:*</include>
                                    <include>io.opencensus:opencensus-contrib-grpc-util:*</include>
                                    <include>io.opencensus:opencensus-contrib-http-util:*</include>
                                    <include>io.opencensus:opencensus-proto:*</include>
                                    <include>io.perfmark:perfmark-api:*</include>
                                    <include>javax.annotation:javax.annotation-api:*</include>
                                    <include>org.apache.httpcomponents:httpclient:*</include>
                                    <include>org.apache.httpcomponents:httpcore:*</include>
                                    <include>org.checkerframework:checker-qual:*</include>
                                    <include>org.codehaus.mojo:animal-sniffer-annotations:*</include>
                                    <include>org.conscrypt:conscrypt-openjdk-uber:*</include>
                                    <include>org.threeten:threetenbp:*</include>
                                </includes>
                            </artifactSet>
                            <relocations>
                                <relocation>
                                    <pattern>com.google.</pattern>
                                    <shadedPattern>shaded.nexla.com.google.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>io.grpc.netty.shaded.io.grpc.</pattern>
                                    <shadedPattern>shaded.nexla.io.grpc.netty.shaded.io.grpc.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>io.grpc.</pattern>
                                    <shadedPattern>shaded.nexla.io.grpc.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>io.opencensus.</pattern>
                                    <shadedPattern>shaded.nexla.io.opencensus.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.codehaus.mojo.</pattern>
                                    <shadedPattern>shaded.nexla.org.codehaus.mojo.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.threeten.</pattern>
                                    <shadedPattern>shaded.nexla.org.threeten.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.conscrypt.</pattern>
                                    <shadedPattern>shaded.nexla.org.conscrypt.</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>io.perfmark.</pattern>
                                    <shadedPattern>shaded.nexla.io.perfmark.</shadedPattern>
                                </relocation>
                            </relocations>

                            <!--this shades META-INF services that google libraries need -->
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>

                            <shadeSourcesContent>true</shadeSourcesContent>

                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
