package com.nexla.redis;

import static com.nexla.common.ResourceType.LOOKUP;
import static com.nexla.redis.dto.MasterEntry.*;
import static com.nexla.redis.dto.MasterEntry.DataModelVersion.V1;
import static com.nexla.redis.dto.MasterEntry.DataModelVersion.V2;

import com.google.common.collect.Maps;
import com.nexla.admin.client.DataMap;
import com.nexla.common.exception.LimitReachedException;
import com.nexla.common.exception.NexlaException;
import com.nexla.common.exception.ValidationException;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.redis.dto.MasterEntry;
import com.nexla.redis.exception.LookupMigrationException;
import com.nexla.redis.util.Poller;
import io.lettuce.core.ScanArgs;
import io.lettuce.core.ScanCursor;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.slf4j.LoggerFactory;

public class RedisLookup implements Lookup {

  private final NexlaLogger logger;

  private static final String DM_ENTRY = "dm:entry";
  public static final int DEFAULT_SCAN_COUNT = 30_000;
  private static final int DEFAULT_DATA_MODEL_MIGRATION_AWAIT_DELAY_MS = 1000;
  private static final int DEFAULT_DATA_MODEL_MIGRATION_AWAIT_TIMEOUT_MS = 120_000;

  private final int id;
  private final int migrationAwaitDelayMs;
  private final int migrationTimeoutDelayMs;
  private final RedisAccessor redis;

  public RedisLookup(RedisAccessor redis, int id) {
    this(
        redis,
        id,
        DEFAULT_DATA_MODEL_MIGRATION_AWAIT_DELAY_MS,
        DEFAULT_DATA_MODEL_MIGRATION_AWAIT_TIMEOUT_MS);
  }

  protected RedisLookup(
      RedisAccessor redis, int id, int migrationAwaitDelayMs, int migrationTimeoutDelayMs) {
    this.redis = redis;
    this.id = id;
    this.migrationAwaitDelayMs = migrationAwaitDelayMs;
    this.migrationTimeoutDelayMs = migrationTimeoutDelayMs;
    this.logger =
        new NexlaLogger(
            LoggerFactory.getLogger(this.getClass()),
            new NexlaLogKey(LOOKUP, id, Optional.empty()));
  }

  @Override
  public int getId() {
    return id;
  }

  /**
   * A lookup (data map) is composed of a collection of hash entries coordinated with a master
   * entry.
   *
   * <p>Each hash entry in the collection has a version number. For continuously-updated maps
   * (USE_VERSIONING = false), the version is fixed at the most-recent version number applied
   * (usually the default, 1). For atomically-updated maps (USE_VERSIONING = true), the
   * CURRENT_VERSION field of the master entry contains the most-recent fully committed map. To
   * update the map atomically, we first add the hashed entries with a new version number, then
   * update (or insert) the master entry with the new version number in the CURRENT_VERSION field.
   * NOTE, THIS MODE IS NOT PROTECTED BY A LOCK and is vulnerable to corruption if more than one
   * process calls it at a time.
   */
  @Override
  public void save(DataMap dataMap) {
    int size = dataMap.getDataMap() == null ? 0 : dataMap.getDataMap().size();
    logger.info("Saving lookup {} with {} entries", dataMap, size);

    MasterEntry masterEntry = getMasterEntry();
    verifyNotBeingMigrated(masterEntry);

    updateMetadata(dataMap);

    boolean appendMode = dataMap.getUseVersioning() == null || !dataMap.getUseVersioning();
    if (appendMode) {
      // USE_VERSIONING == false. In this case, we use either the
      // existing value of CURRENT_VERSION, or we set it to 1.
      String currentVersion = masterEntry.currentVersion;
      if (currentVersion == null) {
        currentVersion = "1";
        setMasterEntryField(CURRENT_VERSION, currentVersion);
      }
      if (size == 0) {
        logger.info("Updated lookup metadata only, 0 entries to update");
        setMasterEntryField(UPDATED_AT, DateTimeFormatter.ISO_INSTANT.format(Instant.now()));
        return;
      }
      setMasterEntryField(NEXT_VERSION, currentVersion);
    } else if (masterEntry.nextVersion != null) {
      // this can happen only for dynamic lookups than can be only updated via redis sink
      logger.info(
          "Dynamic lookup entries can only be updated via dynamic lookup sink, skipping entries"
              + " update");
      setMasterEntryField(UPDATED_AT, DateTimeFormatter.ISO_INSTANT.format(Instant.now()));
      return;
    }
    beginUpdateEntries();

    try {
      int updatedCount = updateEntries(dataMap.getDataMap());
      logger.info("Updated {} entries", updatedCount);
    } catch (NexlaException e) {
      // FIXME Exception is not visible to the End-user. Propagate and handle it in a proper way
      logger.error("Failed to update entries", e);
    }

    endUpdateEntries();
  }

  private void updateMetadata(DataMap dataMap) {
    setMasterEntryField(MAP_PRIMARY_KEY, dataMap.getMapPrimaryKey());
    setMasterEntryField(EMIT_DEFAULTS, String.valueOf(dataMap.getEmitDataDefault()));
    setMasterEntryField(USE_VERSIONING, String.valueOf(dataMap.getUseVersioning()));
    setMasterEntryField(DATA_MODEL_VERSION, String.valueOf(dataMap.getDataModelVersion()));
    Map<String, String> defaults = dataMap.getDataDefaults();
    if (defaults != null) {
      setDataDefaults(defaults);
    }
  }

  @Override
  public void beginUpdateEntries() {
    logger.info("Preparing entries update");
    MasterEntry masterEntry = getMasterEntry();
    verifyNotBeingMigrated(masterEntry);

    if (!masterEntry.useVersioning) {
      // In this case, we aren't using atomic updates,
      // so there's no need to open an update window by
      // incrementing NEXT_VERSION. Just ensure there is
      // a valid CURRENT_VERSION and return.
      if (masterEntry.currentVersion == null) {
        masterEntry.currentVersion = "1";
        setMasterEntryField(CURRENT_VERSION, masterEntry.currentVersion);
      }
      logger.info("Not versioned, updating next version and skipping");
      setMasterEntryField(NEXT_VERSION, masterEntry.currentVersion);
      return;
    }

    if (masterEntry.nextVersion != null) {
      // If NEXT_VERSION exists, beginUpdateEntries was already
      // called without a subsequent endUpdateEntries(). Don't
      // start a new one, just return.
      logger.info("Next version already set, skipping");
      return;
    }

    long next =
        (masterEntry.currentVersion == null) ? 1L : Long.parseLong(masterEntry.currentVersion) + 1;

    logger.info("Cleaning up lookup metadata before update");
    setMasterEntryField(NEXT_VERSION, Long.toString(next));
    setMasterEntryField(MAP_SIZE, Long.toString(0L));
    setMasterEntryField(LIMIT_REACHED, Boolean.FALSE.toString());
    if (V2 == masterEntry.dataModelVersion) {
      redis.del(getNextVersionEntriesIndexKey());
    }
  }

  private MasterEntry getMasterEntryForUpdateEntries(List<Map<String, String>> entries)
      throws NexlaException {
    int entriesSize = entries == null || entries.isEmpty() ? 0 : entries.size();
    logger.info("Updating {} lookup entries", entriesSize);

    if (entriesSize == 0) {
      return null;
    }

    final MasterEntry masterEntry = getMasterEntry();
    verifyNotBeingMigrated(masterEntry);
    validateMasterEntry(masterEntry);

    if (sizeLimitReached(masterEntry)) {
      throw new LimitReachedException(
          "Lookup exceeds the maximum size of " + masterEntry.mapSizeLimit);
    }

    return masterEntry;
  }

  /**
   * Push Entry records to Redis database
   *
   * @return the number of successfully inserted records
   */
  @Override
  public int updateEntries(List<Map<String, String>> entries) throws NexlaException {
    MasterEntry masterEntry = getMasterEntryForUpdateEntries(entries);
    if (masterEntry == null) {
      return 0;
    }
    return doUpdateEntries(masterEntry, entries);
  }

  @Override
  public List<NexlaException> updateEntriesAndReturnErrors(List<Map<String, String>> entries)
      throws NexlaException {
    MasterEntry masterEntry = getMasterEntryForUpdateEntries(entries);
    if (masterEntry == null) {
      return new ArrayList<>();
    }
    List<NexlaException> nexlaExceptionList = new ArrayList<>();
    for (Map<String, String> entryMap : entries) {
      NexlaException nexlaException = validatePushEntryToRedis(masterEntry, entryMap);
      if (nexlaException != null) {
        nexlaExceptionList.add(nexlaException);
      }
    }
    doUpdateEntries(masterEntry, entries);
    return nexlaExceptionList;
  }

  private int doUpdateEntries(MasterEntry masterEntry, List<Map<String, String>> entries) {
    int updatedEntriesCount = 0;
    try {
      for (Map<String, String> entryMap : entries) {
        if (validatePushEntryToRedis(masterEntry, entryMap) == null) {
          pushEntryToRedis(masterEntry, entryMap);
          updatedEntriesCount++;
        }
      }
      if (V2 == masterEntry.dataModelVersion) {
        String pk = masterEntry.mapPrimaryKey;
        String[] entryIds = entries.stream().map(m -> m.get(pk)).toArray(String[]::new);
        String indexKey =
            masterEntry.useVersioning ? getNextVersionEntriesIndexKey() : getEntriesIndexKey();
        redis.sadd(indexKey, entryIds);
      }
    } finally {
      String previousEntriesCount = getMasterEntryField(MAP_SIZE);
      if (previousEntriesCount != null) {
        setMasterEntryField(PREVIOUS_VERSION_MAP_SIZE, previousEntriesCount);
      }
      incrementMasterEntryField(MAP_SIZE, updatedEntriesCount);
    }
    return updatedEntriesCount;
  }

  private void validateMasterEntry(MasterEntry masterEntry) throws NexlaException {
    if (masterEntry.isEmpty) {
      throw new ValidationException("Master entry is missing");
    }
    if (masterEntry.mapPrimaryKey == null) {
      throw new ValidationException("Master entry has no map primary key");
    }
    if (masterEntry.nextVersion == null) {
      throw new ValidationException("Master entry next version is not set");
    }
    if (masterEntry.mapSizeLimit <= 0) {
      logger.warn("Lookup has no size limit");
    }
  }

  private boolean sizeLimitReached(MasterEntry masterEntry) {
    // Check if the limit was already reached in a previous call.
    if (masterEntry.limitReached) {
      return true;
    }

    // Limit is approximate because Lookup may be updated from different threads and there are no
    // guarantees for keys uniqueness.
    // The precise limitation is sacrificed because it requires continuous synchronization between
    // threads and Redis DB,
    // which will affect the overall performance and will increase code complexity.
    // Just check if limit is reached before the processing of records batch
    if (masterEntry.mapSizeLimit > 0 && masterEntry.mapSize >= masterEntry.mapSizeLimit) {
      // The number of inserted entries exceeds the maximum size.
      // Recheck the actual number of entries in Redis database (in case there were duplicates)
      calcAndUpdateMapSize(masterEntry);
      return masterEntry.limitReached;
    }
    return false;
  }

  private void calcAndUpdateMapSize(MasterEntry masterEntry) {
    logger.info("Calculating actual map size and checking if the actual limit reached");
    masterEntry.mapSize = countEntries(masterEntry.nextVersion, masterEntry.dataModelVersion);
    masterEntry.limitReached = masterEntry.mapSize >= masterEntry.mapSizeLimit;

    setMasterEntryField(LIMIT_REACHED, Boolean.toString(masterEntry.limitReached));
    setMasterEntryField(MAP_SIZE, String.valueOf(masterEntry.mapSize));
  }

  private void pushEntryToRedis(final MasterEntry masterEntry, final Map<String, String> entryMap) {
    final String mapPrimaryKeyValue = entryMap.get(masterEntry.mapPrimaryKey);
    final String entryKey = getEntryKey(masterEntry.nextVersion, mapPrimaryKeyValue);
    final Map<String, String> entryMapFiltered =
        entryMap.entrySet().stream()
            .map(
                entry -> {
                  if (entry.getKey() == null || entry.getValue() == null) {
                    return null;
                  }
                  return entry;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    redis.hset(entryKey, entryMapFiltered);
  }

  private NexlaException validatePushEntryToRedis(
      final MasterEntry masterEntry, final Map<String, String> entryMap) {
    if (entryMap.isEmpty()) {
      logger.warn(
          "Skipping record due to entryMap is Empty: mapPrimaryKey={}", masterEntry.mapPrimaryKey);
      return new NexlaException(
          String.format(
              "Skipping record due to entryMap is Empty: mapPrimaryKey=%s",
              masterEntry.mapPrimaryKey),
          entryMap);
    }

    final String mapPrimaryKeyValue = entryMap.get(masterEntry.mapPrimaryKey);
    if (mapPrimaryKeyValue == null) {
      logger.warn(
          "Skipping record due to missing mapPrimaryKey: mapPrimaryKey={}, entryMap={}",
          masterEntry.mapPrimaryKey,
          entryMap);
      return new NexlaException(
          String.format(
              "Skipping record due to missing mapPrimaryKey: mapPrimaryKey=%s, entryMap=%s",
              masterEntry.mapPrimaryKey, entryMap),
          entryMap);
    }

    final String entryKey = getEntryKey(masterEntry.nextVersion, mapPrimaryKeyValue);
    if (entryKey == null) {
      logger.warn(
          "Skipping record due to missing entryKey: mapPrimaryKey={}, entryMap={}",
          masterEntry.mapPrimaryKey,
          entryMap);
      return new NexlaException(
          String.format(
              "Skipping record due to missing entryKey: mapPrimaryKey=%s, entryMap=%s",
              masterEntry.mapPrimaryKey, entryMap),
          entryMap);
    }

    final Map<String, String> entryMapFiltered =
        entryMap.entrySet().stream()
            .map(
                entry -> {
                  if (entry.getKey() == null || entry.getValue() == null) {
                    return null;
                  }
                  return entry;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    if (entryMapFiltered.isEmpty()) {
      logger.warn(
          "Skipping record due entryMap is empty, mapPrimaryKey={}, entryKey={}",
          masterEntry.mapPrimaryKey,
          entryKey);
      return new NexlaException(
          String.format(
              "Skipping record due entryMap is empty, mapPrimaryKey=%s, entryKey=%s",
              masterEntry.mapPrimaryKey, entryKey),
          entryMap);
    }

    return null;
  }

  @Override
  public void endUpdateEntries() {
    logger.info("Finalize entries update");
    MasterEntry masterEntry = getMasterEntry();

    verifyNotBeingMigrated(masterEntry);

    if (masterEntry.nextVersion == null) {
      return;
    }

    String prevVersion = masterEntry.currentVersion;

    setMasterEntryField(UPDATED_AT, DateTimeFormatter.ISO_INSTANT.format(Instant.now()));
    setMasterEntryField(CURRENT_VERSION, masterEntry.nextVersion);
    setMasterEntryField(NEXT_VERSION, null);

    if (masterEntry.useVersioning) {
      Set<String> oldEntryIds = null;
      if (V2 == masterEntry.dataModelVersion) {
        oldEntryIds = redis.smembers(getEntriesIndexKey());
        redis.del(getEntriesIndexKey());
        var newEntryIds = redis.smembers(getNextVersionEntriesIndexKey());
        redis.del(getNextVersionEntriesIndexKey());
        if (!newEntryIds.isEmpty()) {
          redis.sadd(getEntriesIndexKey(), newEntryIds.toArray(new String[0]));
        }
      }
      if (prevVersion != null) {
        logger.info("Deleting redundant historical keys for previous version '{}'", prevVersion);
        if (oldEntryIds != null) {
          deleteEntriesAsync(oldEntryIds, prevVersion);
        } else {
          deleteEntriesAsync(prevVersion);
        }
      }
    }
  }

  @SneakyThrows
  public void delete() {
    deleteAsync().get();
  }

  @Override
  public CompletableFuture<Void> deleteAsync() {
    logger.info("Deleting lookup");
    MasterEntry master = getMasterEntry();

    verifyNotBeingMigrated(master);

    redis.del(getMasterKey());
    redis.del(getDataDefaultsKey());
    if (V2 == master.dataModelVersion) {
      Set<String> entryIds = redis.smembers(getEntriesIndexKey());
      redis.del(getEntriesIndexKey());
      return deleteEntriesAsync(entryIds, master.currentVersion);
    } else {
      return deleteEntriesAsync("*");
    }
  }

  @Override
  public Boolean exists() {
    return redis.exists(getMasterKey());
  }

  @Override
  public Boolean hasDefaults() {
    return redis.exists(getDataDefaultsKey());
  }

  protected String getMasterKey() {
    return "dm:master:" + id;
  }

  protected String getEntriesIndexKey() {
    return "dm:entries:" + id;
  }

  protected String getNextVersionEntriesIndexKey() {
    return "dm:next_version_entries:" + id;
  }

  private String getDataDefaultsKey() {
    return "dm:defaults:" + id;
  }

  @Override
  public String getEntryKey(String version, String key) {
    return (version != null) ? DM_ENTRY + ":" + String.join(":", id + "", version, key) : null;
  }

  @Override
  public String getEntryKeyPrefix() {
    return String.format(DM_ENTRY + ":%s", id);
  }

  protected String getEntryKeyPattern(String mapVersion) {
    return String.format(DM_ENTRY + ":%s:%s:*", id, mapVersion);
  }

  @Override
  public Map<String, Object> getMasterEntryAndDataDefaults() {
    Map<String, String> master = redis.hgetAll(getMasterKey());
    Map<String, String> defaults = redis.hgetAll(getDataDefaultsKey());

    Map<String, Object> result = new HashMap<>();
    result.put("master", master);
    result.put("defaults", defaults);
    return result;
  }

  @Override
  public MasterEntry getMasterEntry() {
    Map<String, String> entryData = redis.hgetAll(getMasterKey());
    return MasterEntry.fromMap(entryData);
  }

  @Override
  public String getMasterEntryField(String fieldKey) {
    return redis.hget(getMasterKey(), fieldKey);
  }

  @Override
  public Map<String, String> getEntry(String version, String key) {
    String entryKey = getEntryKey(version, key);
    return (entryKey == null) ? Collections.emptyMap() : redis.hgetAll(entryKey);
  }

  @Override
  public List<String> getPrimaryKeys() {
    MasterEntry masterEntry = getMasterEntry();
    return getPrimaryKeys(masterEntry, masterEntry.currentVersion).toList();
  }

  @Override
  public StreamEx<String> getPrimaryKeys(MasterEntry masterEntry, String version) {
    if (V2 == masterEntry.dataModelVersion) {
      return StreamEx.of(redis.smembers(getEntriesIndexKey()))
          .map(key -> getEntryKey(version, key));
    } else {
      String pattern = getEntryKeyPattern(version);
      return getScanResult(pattern);
    }
  }

  @Override
  public List<Map<String, String>> getAllEntries() {
    MasterEntry masterEntry = getMasterEntry();
    if (V2 == masterEntry.dataModelVersion) {
      return redis.smembers(getEntriesIndexKey()).stream()
          .map(key -> getEntryKey(masterEntry.currentVersion, key))
          .map(redis::hgetAll)
          .collect(Collectors.toList());
    } else {
      String patternForCurrentKeys = getEntryKeyPattern(masterEntry.currentVersion);
      return getMatchingKeys(patternForCurrentKeys).toList();
    }
  }

  @Override
  public List<Map<String, String>> getEntriesByPattern(String keyPattern, boolean exactMatch) {
    MasterEntry masterEntry = getMasterEntry();
    return getEntriesByPattern(masterEntry, masterEntry.currentVersion, keyPattern, exactMatch)
        .toList();
  }

  @Override
  public StreamEx<Map<String, String>> getEntriesByPattern(
      MasterEntry masterEntry, String version, String keyPattern, boolean exactMatch) {
    if (V2 == masterEntry.dataModelVersion) {
      String valuePattern = resolvePattern(keyPattern, exactMatch);
      return getValueScanResult(getEntriesIndexKey(), valuePattern)
          .map(key -> getEntryKey(masterEntry.currentVersion, key))
          .map(redis::hgetAll);
    } else {
      String pattern = getEntryKeyPattern(version, keyPattern, exactMatch);
      return getMatchingKeys(pattern);
    }
  }

  private String getEntryKeyPattern(String mapVersion, String keyPattern, boolean exactMatch) {
    return String.format(
        DM_ENTRY + ":%s:%s:%s", id, mapVersion, resolvePattern(keyPattern, exactMatch));
  }

  private String resolvePattern(String keyPattern, boolean exactMatch) {
    // If the caller's pattern does not contain a *, it is treated as a simple prefix.
    if (!keyPattern.contains("*") && !exactMatch) {
      return keyPattern + "*";
    } else {
      return keyPattern;
    }
  }

  @Override
  public Map<String, String> setEntry(String key, Map<String, String> entry) {
    MasterEntry masterEntry = getMasterEntry();
    verifyNotBeingMigrated(masterEntry);

    String entryKey = getEntryKey(masterEntry.currentVersion, key);
    // Filtering out null values, as Redis cannot store them
    entry = Maps.filterValues(entry, Objects::nonNull);
    redis.hset(entryKey, entry);
    if (V2 == masterEntry.dataModelVersion) {
      redis.sadd(getEntriesIndexKey(), key);
    }
    return redis.hgetAll(entryKey);
  }

  @Override
  public boolean deleteEntry(String key) {
    MasterEntry masterEntry = getMasterEntry();
    verifyNotBeingMigrated(masterEntry);

    String entryKey = getEntryKey(masterEntry.currentVersion, key);
    Long result = redis.del(entryKey);
    if (V2 == masterEntry.dataModelVersion) {
      redis.srem(getEntriesIndexKey(), key);
    }
    return result != null && result == 1;
  }

  @Override
  public Long countEntries(String version, DataModelVersion dataModelVersion) {
    if (V2 == dataModelVersion) {
      return redis.scard(getEntriesIndexKey());
    } else {
      return countMatching(getEntryKeyPattern(version));
    }
  }

  @Override
  public void setMasterEntryField(String fieldKey, String fieldValue) {
    verifyNotBeingMigrated(getMasterEntry());

    if (fieldValue != null) {
      redis.hset(getMasterKey(), fieldKey, fieldValue);
    } else {
      redis.hdel(getMasterKey(), fieldKey);
    }
  }

  private long incrementMasterEntryField(String fieldKey, int amount) {
    return redis.hincrBy(getMasterKey(), fieldKey, amount);
  }

  private void setDataDefaults(Map<String, String> entry) {
    if (!entry.isEmpty()) {
      redis.hset(getDataDefaultsKey(), entry);
    }
  }

  private CompletableFuture<Void> deleteEntriesAsync(String mapVersion) {
    return scanAndDeleteByPatternAsync(getEntryKeyPattern(mapVersion), Optional.empty());
  }

  private CompletableFuture<Void> deleteEntriesAsync(Set<String> entryIds, String version) {
    return CompletableFuture.supplyAsync(
        () -> {
          entryIds.stream().map(key -> getEntryKey(version, key)).forEach(redis::del);
          logger.info("Deleted {} entries", entryIds.size());
          return null;
        });
  }

  @Override
  public void migrateDataModelVersion(DataModelVersion fromVersion, DataModelVersion toVersion)
      throws LookupMigrationException {
    logger.info("Migrating data model version from {} to {}", fromVersion, toVersion);
    if (fromVersion == toVersion) {
      throw new LookupMigrationException("Invalid args: from/to version can not be the same");
    }
    MasterEntry masterEntry = getMasterEntry();
    if (masterEntry.dataModelVersion != fromVersion) {
      throw new LookupMigrationException(
          String.format(
              "Invalid args: expected version=%s, actual version=%s",
              fromVersion, masterEntry.dataModelVersion));
    }
    startDataModelVersionMigration(masterEntry);
    switch (toVersion) {
      case V2:
        Set<String> allVersionsKeys = getScanResult(getEntryKeyPattern("*")).toSet();
        migrateToV2(masterEntry, allVersionsKeys);
        break;
      case V1:
        migrateToV1();
        break;
    }
    endDataModelVersionMigration();
  }

  @Override
  public void startDataModelVersionMigration(MasterEntry masterEntry) {
    if (masterEntry.isMigrationInProgress()) {
      throw new LookupMigrationException(String.format("Lookup %s is already being migrated", id));
    }
    // mark lookup as being migrated
    redis.hset(
        getMasterKey(), MIGRATION_STARTED_AT, DateTimeFormatter.ISO_INSTANT.format(Instant.now()));
  }

  @Override
  public void endDataModelVersionMigration() {
    // mark lookup as migrated
    redis.hdel(getMasterKey(), MIGRATION_STARTED_AT);
  }

  @Override
  public void migrateDataModelVersion(
      DataModelVersion fromVersion, DataModelVersion toVersion, Set<String> allKeys) {
    logger.info("Migrating data model version from {} to {}", fromVersion, toVersion);
    if (fromVersion == toVersion) {
      throw new LookupMigrationException("Invalid args: from/to version can not be the same");
    }
    MasterEntry masterEntry = getMasterEntry();
    if (masterEntry.dataModelVersion != fromVersion) {
      throw new LookupMigrationException(
          String.format(
              "Invalid args: expected version=%s, actual version=%s",
              fromVersion, masterEntry.dataModelVersion));
    }
    if (!masterEntry.isMigrationInProgress()) {
      throw new LookupMigrationException("Lookup migration is not started");
    }
    switch (toVersion) {
      case V2:
        migrateToV2(masterEntry, allKeys);
        break;
      case V1:
        migrateToV1();
        break;
    }
  }

  private void migrateToV2(MasterEntry masterEntry, Set<String> allKeys) {
    String currentVersionKeysPrefix = getEntryKeyPrefix() + ":" + masterEntry.currentVersion + ":";
    Set<String> currentVersionKeys =
        allKeys.stream()
            .filter(key -> key.startsWith(currentVersionKeysPrefix))
            .map(key -> key.substring(currentVersionKeysPrefix.length()))
            .collect(Collectors.toSet());
    if (!currentVersionKeys.isEmpty()) {
      logger.info("Adding entries index. {} keys to process", currentVersionKeys.size());
      redis.sadd(getEntriesIndexKey(), currentVersionKeys.toArray(new String[] {}));
      redis.hset(getMasterKey(), MAP_SIZE, String.valueOf(currentVersionKeys.size()));
    } else {
      logger.info("Lookup has no entries, skipping entries index init");
      redis.hset(getMasterKey(), MAP_SIZE, "0");
    }

    if (masterEntry.useVersioning && masterEntry.nextVersion != null) {
      logger.info("Migrating intermediate state for versioned lookup.");
      String nextVersionKeysPrefix = getEntryKeyPrefix() + ":" + masterEntry.nextVersion + ":";
      var nextVersionKeys =
          allKeys.stream()
              .filter(key -> key.startsWith(nextVersionKeysPrefix))
              .map(key -> key.substring(nextVersionKeysPrefix.length()))
              .collect(Collectors.toSet());
      if (!nextVersionKeys.isEmpty()) {
        logger.info(
            "Adding next version entries index. {} keys to process", nextVersionKeys.size());
        redis.sadd(getNextVersionEntriesIndexKey(), nextVersionKeys.toArray(new String[] {}));
        redis.hset(getMasterKey(), MAP_SIZE, String.valueOf(nextVersionKeys.size()));
      }
    }

    logger.info("Updating data model version to {}", V2);
    redis.hset(getMasterKey(), DATA_MODEL_VERSION, V2.toString());
  }

  private void migrateToV1() {
    logger.info("Updating data model version to {}", V1);
    redis.hset(getMasterKey(), DATA_MODEL_VERSION, V1.toString());
    logger.info("Clearing entries index");
    redis.del(getEntriesIndexKey());
    redis.del(getNextVersionEntriesIndexKey());
  }

  private void verifyNotBeingMigrated(MasterEntry masterEntry) {
    if (masterEntry.isMigrationInProgress()) {
      logger.warn("Lookup is being migrated. Waiting until migration is completed");
      boolean isMigrated =
          new Poller()
              .waitUntil(
                  () -> !getMasterEntry().isMigrationInProgress(),
                  migrationAwaitDelayMs,
                  migrationTimeoutDelayMs);
      if (!isMigrated) {
        throw new LookupMigrationException(
            String.format(
                "Lookup %s is still being migrated. Migration is not yet done after %s ms.",
                id, migrationAwaitDelayMs));
      }
    }
  }

  public CompletableFuture<Void> scanAndDeleteByPatternAsync(
      String keyPattern, Optional<ScanCursor> startCursor) {
    ScanArgs scanArgs = ScanArgs.Builder.matches(keyPattern).limit(DEFAULT_SCAN_COUNT);

    return CompletableFuture.supplyAsync(
            () ->
                startCursor
                    .map(cursor -> redis.scanAsync(scanArgs, cursor))
                    .orElse(redis.scanAsync(scanArgs)))
        .thenCompose(
            cursor ->
                cursor.thenApply(
                    keyScanCursor -> {
                      List<String> keysToDelete =
                          StreamEx.of(keyScanCursor.getKeys()).collect(Collectors.toList());
                      deleteKeys(keysToDelete, keyPattern);
                      return keyScanCursor;
                    }))
        .thenCompose(
            keyScanCursor ->
                keyScanCursor.isFinished()
                    ? CompletableFuture.completedFuture(null)
                    : scanAndDeleteByPatternAsync(keyPattern, Optional.of(keyScanCursor)))
        .exceptionally(
            ex -> {
              logger.error("Error occurred while scanning and deleting keys.", ex);
              return null;
            });
  }

  private void deleteKeys(List<String> keys, String keyPattern) {
    if (keys.isEmpty()) {
      return;
    }
    String[] keysArray = keys.toArray(new String[0]);
    redis
        .delAsync(keysArray)
        .thenAccept(
            deletedKeysCount ->
                logger.info("Deleted {} keys by key pattern '{}'", deletedKeysCount, keyPattern))
        .exceptionally(
            ex -> {
              logger.error("Failed to delete keys.", ex);
              return null;
            });
  }

  private Long countMatching(String keyPattern) {
    return getScanResult(keyPattern).count();
  }

  private StreamEx<String> getScanResult(String keyPattern) {
    ScanArgs params = ScanArgs.Builder.matches(keyPattern).limit(DEFAULT_SCAN_COUNT);

    return redis.scan(params);
  }

  private StreamEx<String> getValueScanResult(String key, String pattern) {
    ScanArgs params = ScanArgs.Builder.matches(pattern).limit(DEFAULT_SCAN_COUNT);

    return redis.sscan(key, params);
  }

  private StreamEx<Map<String, String>> getMatchingKeys(String keyPattern) {
    return getScanResult(keyPattern).map(redis::hgetAll);
  }
}
