package com.nexla.redis.dto;

import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ToString
@EqualsAndHashCode
public class MasterEntry {

  public enum DataModelVersion {
    V1, // legacy, not indexed entries
    V2; // indexed entries

    public static DataModelVersion fromString(String str) {
      if (str == null) {
        log.debug("Data model version not defined, using default (V1)");
        return V1;
      }
      try {
        return DataModelVersion.valueOf(str);
      } catch (Exception e) {
        log.error("Invalid data model: {}. Fallback to V1", str);
        return V1;
      }
    }
  }

  public static final String EMIT_DEFAULTS = "emit_defaults";
  public static final String USE_VERSIONING = "use_versioning";
  public static final String DATA_MODEL_VERSION = "data_model_version";
  public static final String CURRENT_VERSION = "current_version";
  public static final String NEXT_VERSION = "next_version";
  public static final String UPDATED_AT = "updated_at";
  public static final String MAP_PRIMARY_KEY = "map_primary_key";
  public static final String MAP_SIZE = "map_size";
  public static final String PREVIOUS_VERSION_MAP_SIZE = "previous_version_map_size";
  public static final String MAP_SIZE_LIMIT = "map_size_limit";
  public static final String LIMIT_REACHED = "limit_reached";
  public static final String MIGRATION_STARTED_AT = "migration_started_at";

  public boolean isEmpty = true;
  public DataModelVersion dataModelVersion = DataModelVersion.V1;
  // we don't fully support versioning at the moment, it's rather append(useVersioning=false) or
  // overwrite(useVersioning=true) mode
  public boolean useVersioning = false;
  public String currentVersion;
  public String nextVersion;
  public String mapPrimaryKey;
  // approximate map size (calculated as a count of updates until reaching a limit - then it's
  // recalculated)
  public long mapSize = 0;
  public long previousMapSize = 0;
  public long mapSizeLimit = 0;
  // always false for lookups that use versioning
  public boolean limitReached = false;
  public String migrationStartedAt;
  public String updatedAt;

  public boolean isMigrationInProgress() {
    return migrationStartedAt != null;
  }

  public static MasterEntry fromMap(Map<String, String> entryData) {
    MasterEntry masterEntry = new MasterEntry();
    masterEntry.isEmpty = entryData.isEmpty();
    masterEntry.useVersioning =
        Boolean.parseBoolean(entryData.getOrDefault(USE_VERSIONING, "false"));
    masterEntry.currentVersion = entryData.get(CURRENT_VERSION);
    masterEntry.dataModelVersion =
        DataModelVersion.fromString(
            entryData.getOrDefault(DATA_MODEL_VERSION, DataModelVersion.V1.toString()));
    masterEntry.nextVersion = entryData.get(NEXT_VERSION);
    masterEntry.mapPrimaryKey = entryData.get(MAP_PRIMARY_KEY);
    masterEntry.updatedAt = entryData.get(UPDATED_AT);
    masterEntry.mapSize = Integer.parseInt(entryData.getOrDefault(MAP_SIZE, "0"));
    masterEntry.previousMapSize =
        Integer.parseInt(entryData.getOrDefault(PREVIOUS_VERSION_MAP_SIZE, "0"));
    masterEntry.mapSizeLimit = Integer.parseInt(entryData.getOrDefault(MAP_SIZE_LIMIT, "0"));
    masterEntry.limitReached = Boolean.parseBoolean(entryData.getOrDefault(LIMIT_REACHED, "false"));
    masterEntry.migrationStartedAt = entryData.get(MIGRATION_STARTED_AT);
    return masterEntry;
  }
}
