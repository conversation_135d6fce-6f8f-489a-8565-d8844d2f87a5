package com.nexla.spec

import com.nexla.common.ConnectionType
import com.nexla.connector.config.api_streams.ApiStreamsSourceConnectorConfig
import com.nexla.connector.config.big_query.{BigQuerySinkConnectorConfig, BigQuerySourceConnectorConfig}
import com.nexla.connector.config.deltalake.DeltaLakeAuthConfig
import com.nexla.connector.config.documentdb.dynamodb.DynamoDbAuthConfig
import com.nexla.connector.config.documentdb.firebase.FirebaseAuthConfig
import com.nexla.connector.config.documentdb.mongo.MongoAuthConfig
import com.nexla.connector.config.documentdb.{DocumentDbSinkConnectorConfig, DocumentDbSourceConnectorConfig}
import com.nexla.connector.config.file._
import com.nexla.connector.config.iceberg.{IcebergSinkConnectorConfig, IcebergSourceConnectorConfig}
import com.nexla.connector.config.jdbc.{Jdbc<PERSON>uth<PERSON>onfig, JdbcSinkConnectorConfig, JdbcSourceConnectorConfig}
import com.nexla.connector.config.kafka._
import com.nexla.connector.config.kafka.pubsub.{PubSubAuthConfig, PubSubSinkConnectorConfig, PubSubSourceConnectorConfig}
import com.nexla.connector.config.kinesis.KinesisSinkConnectorConfig
import com.nexla.connector.config.redis.{RedisAuthConfig, RedisConnectorConfig}
import com.nexla.connector.config.rest.{BaseAuthConfig, RestAuthConfig, RestSinkConnectorConfig, RestSourceConnectorConfig}
import com.nexla.connector.config.soap.{SoapSinkConnectorConfig, SoapSourceConnectorConfig}
import com.nexla.connector.config.vectordb.pinecone.PineconeAuthConfig
import com.nexla.connector.config.vectordb.{VectorSinkConnectorConfig, VectorSourceConnectorConfig}
import com.nexla.connector.config.{SinkConnectorConfig, SourceConnectorConfig}

object Connectors {

  val fileFtp = ConnectorSpec(
    connectionType = ConnectionType.FTP,
    authConfig = Some(FtpAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[FtpAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.FTP_SOURCE_GROUP
        ))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.FTP_SINK_GROUP
        ))),
    meta = Map("displayName" -> "FTP"))

  val fileWebdav = ConnectorSpec(
    connectionType = ConnectionType.WEBDAV,
    authConfig = Some(WebDAVAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[WebDAVAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.FTP_SOURCE_GROUP
        ))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.FTP_SINK_GROUP
        ))),
    meta = Map("displayName" -> "WEBDAV"))

  val webhook = ConnectorSpec(
    connectionType = ConnectionType.NEXLA_REST,
    authConfig = None,
    authConfigClass = None,
    sourceConfig = None,
    sinkConfig = None)

  val fileS3 = ConnectorSpec(
    connectionType = ConnectionType.S3,
    authConfig = Some(AWSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AWSAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Amazon S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))

  val fileAzureBlob = ConnectorSpec(
    connectionType = ConnectionType.AZURE_BLB,
    authConfig = Some(AzureAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AzureAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Azure Blob Store"))

  val fileAzureDataLake = ConnectorSpec(
    connectionType = ConnectionType.AZURE_DATA_LAKE,
    authConfig = Some(AzureAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AzureAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Azure Data Lake G2"))

  val fileDeltaLakeS3 = ConnectorSpec(
    connectionType = ConnectionType.DELTA_LAKE_S3,
    authConfig = Some(DeltaLakeAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[DeltaLakeAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Delta Lake S3"))

  val fileDeltaLakeAzureBlob = ConnectorSpec(
    connectionType = ConnectionType.DELTA_LAKE_AZURE_BLB,
    authConfig = Some(DeltaLakeAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[DeltaLakeAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Delta Lake Azure Blob"))

  val fileDeltaLakeAzureDataLake = ConnectorSpec(
    connectionType = ConnectionType.DELTA_LAKE_AZURE_DATA_LAKE,
    authConfig = Some(DeltaLakeAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[DeltaLakeAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Delta Lake Azure Data Lake G2"))

  val fileS3ApacheIceberg = ConnectorSpec(
    connectionType = ConnectionType.S3_ICEBERG,
    authConfig = Some(AWSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AWSAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.iceberg.source.ApacheIcebergSourceConnector",
        configClass = classOf[IcebergSourceConnectorConfig],
        dockerImage = "kafka-connect-iceberg-source",
        config = IcebergSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.iceberg.sink.ApacheIcebergSinkConnector",
        configClass = classOf[IcebergSinkConnectorConfig],
        dockerImage = "kafka-connect-iceberg-sink",
        config = IcebergSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Apache Iceberg on Amazon S3"))

  val fileBox = ConnectorSpec(
    connectionType = ConnectionType.BOX,
    authConfig = Some(BoxAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[BoxAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Box",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/box.png"))

  val fileDropbox = ConnectorSpec(
    connectionType = ConnectionType.DROPBOX,
    authConfig = Some(DropBoxAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[DropBoxAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Dropbox",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/dropbox.png"))

  val fileGDrive = ConnectorSpec(
    connectionType = ConnectionType.GDRIVE,
    authConfig = Some(RestAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Dropbox",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/dropbox.png"))

  val gdriveSpreadsheet = ConnectorSpec(
    connectionType = ConnectionType.GDRIVE,
    authConfig = Some(BaseAuthConfig.baseAuthConfigDef()),
    authConfigClass = Some(classOf[BaseAuthConfig]),
    sourceConfig = None,
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.SpreadSheetSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-spreadsheets-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Spreadsheet",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/dropbox.png"),
    isSecondary = true)

  val rest = ConnectorSpec(
    connectionType = ConnectionType.REST,
    authConfig = Some(RestAuthConfig.AUTH_CONFIG_DEF),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.rest.source.RestSourceConnector",
        configClass = classOf[RestSourceConnectorConfig],
        dockerImage = "kafka-connect-rest-source",
        config = RestSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          RestSourceConnectorConfig.SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.rest.sink.RestSinkConnector",
        configClass = classOf[RestSinkConnectorConfig],
        dockerImage = "kafka-connect-rest-sink",
        config = RestSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          RestSinkConnectorConfig.SINK_GROUP))),
    meta = Map("displayName" -> "API"))

  val soap = ConnectorSpec(
    connectionType = ConnectionType.SOAP,
    authConfig = Some(RestAuthConfig.AUTH_CONFIG_DEF),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.soap.source.SoapSourceConnector",
        configClass = classOf[SoapSourceConnectorConfig],
        dockerImage = "kafka-connect-soap-source",
        config = SoapSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          SoapSourceConnectorConfig.SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.soap.sink.SoapSinkConnector",
        configClass = classOf[SoapSinkConnectorConfig],
        dockerImage = "kafka-connect-soap-sink",
        config = SoapSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          SoapSinkConnectorConfig.SINK_GROUP))),
    meta = Map("displayName" -> "SOAP"))

  val jdbcPostgres = ConnectorSpec(
    connectionType = ConnectionType.POSTGRES,
    authConfig = Some(JdbcAuthConfig.CONFIG_DEF),
    authConfigClass = Some(classOf[JdbcAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.poll.JdbcSourceConnector",
        configClass = classOf[JdbcSourceConnectorConfig],
        dockerImage = "kafka-connect-jdbc-source",
        config = JdbcSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          JdbcSourceConnectorConfig.JDBC_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.sink.JdbcSinkConnector",
        configClass = classOf[JdbcSinkConnectorConfig],
        dockerImage = "kafka-connect-jdbc-sink",
        config = JdbcSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          JdbcSinkConnectorConfig.JDBC_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Postgres",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/postgres.png"))

  val jdbcHive = ConnectorSpec(
    connectionType = ConnectionType.HIVE,
    authConfig = Some(JdbcAuthConfig.CONFIG_DEF),
    authConfigClass = Some(classOf[JdbcAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.poll.JdbcSourceConnector",
        configClass = classOf[JdbcSourceConnectorConfig],
        dockerImage = "kafka-connect-jdbc-source",
        config = JdbcSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          JdbcSourceConnectorConfig.JDBC_SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "Hive",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/postgres.png"))

  val jdbcDatabricks = ConnectorSpec(
    connectionType = ConnectionType.DATABRICKS,
    authConfig = Some(JdbcAuthConfig.CONFIG_DEF),
    authConfigClass = Some(classOf[JdbcAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.poll.JdbcSourceConnector",
        configClass = classOf[JdbcSourceConnectorConfig],
        dockerImage = "kafka-connect-jdbc-source",
        config = JdbcSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          JdbcSourceConnectorConfig.JDBC_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.sink.JdbcSinkConnector",
        configClass = classOf[JdbcSinkConnectorConfig],
        dockerImage = "kafka-connect-jdbc-sink",
        config = JdbcSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          JdbcSinkConnectorConfig.JDBC_SINK_GROUP))),
    meta = Map(
      "displayName" -> "databricks"))

  val jdbcNetsuite = ConnectorSpec(
    connectionType = ConnectionType.NETSUITE_JDBC,
    authConfig = Some(JdbcAuthConfig.CONFIG_DEF),
    authConfigClass = Some(classOf[JdbcAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.poll.JdbcSourceConnector",
        configClass = classOf[JdbcSourceConnectorConfig],
        dockerImage = "kafka-connect-jdbc-source",
        config = JdbcSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          JdbcSourceConnectorConfig.JDBC_SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "Netsuite"))

  val jdbcPresto = ConnectorSpec(
    connectionType = ConnectionType.PRESTO,
    authConfig = Some(JdbcAuthConfig.CONFIG_DEF),
    authConfigClass = Some(classOf[JdbcAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.sql.poll.JdbcSourceConnector",
        configClass = classOf[JdbcSourceConnectorConfig],
        dockerImage = "kafka-connect-jdbc-source",
        config = JdbcSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          JdbcSourceConnectorConfig.JDBC_SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "Presto",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/postgres.png"))

  val jdbcMysql = jdbcPostgres.copy(
    connectionType = ConnectionType.MYSQL,
    meta = Map(
      "displayName" -> "MySQL",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/mysql.png"))

  val jdbcSqlServer = jdbcPostgres.copy(
    connectionType = ConnectionType.SQLSERVER,
    meta = Map(
      "displayName" -> "MS Sql Server",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/oracle.png"))

  val jdbcAzureSynapse = jdbcPostgres.copy(
    connectionType = ConnectionType.AZURE_SYNAPSE,
    meta = Map(
      "displayName" -> "Azure Synapse",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/azure-synapse.png"))

  val jdbcOracle = jdbcPostgres.copy(
    connectionType = ConnectionType.ORACLE,
    meta = Map(
      "displayName" -> "Oracle",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/oracle.png"))

  val jdbcOracleAutonomous = jdbcPostgres.copy(
    connectionType = ConnectionType.ORACLE_AUTONOMOUS,
    meta = Map(
      "displayName" -> "Oracle",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/oracle.png"))

  val jdbcRedshift = jdbcPostgres.copy(
    connectionType = ConnectionType.REDSHIFT,
    meta = Map(
      "displayName" -> "Redshift",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/redshift.png"))

  val jdbcSnowflake = jdbcPostgres.copy(
    connectionType = ConnectionType.SNOWFLAKE,
    meta = Map(
      "displayName" -> "Snowflake",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/snowflake.png"))

  val jdbcFirebolt = jdbcPostgres.copy(
    connectionType = ConnectionType.FIREBOLT,
    meta = Map(
      "displayName" -> "Firebolt",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/firebolt.png"))

  val as400 = jdbcPostgres.copy(
    connectionType = ConnectionType.AS400,
    meta = Map(
      "displayName" -> "AS400"
    )
  )

  val db2 = jdbcPostgres.copy(
    connectionType = ConnectionType.DB2,
    meta = Map(
      "displayName" -> "DB2"
    )
  )

  val hana = jdbcPostgres.copy(
    connectionType = ConnectionType.HANA_JDBC,
    meta = Map(
      "displayName" -> "SAP HANA"
    )
  )

  val sybase = jdbcPostgres.copy(
    connectionType = ConnectionType.SYBASE,
    meta = Map(
      "displayName" -> "SYBASE"
    )
  )

  val teradata = jdbcPostgres.copy(
    connectionType = ConnectionType.TERADATA,
    meta = Map(
      "displayName" -> "Teradata",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/teradata.png"
    )
  )

  val gcpSpanner = jdbcPostgres.copy(
    connectionType = ConnectionType.GCP_SPANNER,
    meta = Map(
      "displayName" -> "GCP Spanner"
    )
  )

  val awsAthena = jdbcPostgres.copy(
    connectionType = ConnectionType.AWS_ATHENA,
    meta = Map(
      "displayName" -> "AWS Athena"
    )
  )

  val redisDataMap = ConnectorSpec(
    connectionType = ConnectionType.DATA_MAP,
    authConfig = Some(RedisAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[RedisAuthConfig]),

    // for probe app only /read/samples
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "",
        configClass = classOf[RedisConnectorConfig],
        dockerImage = "",
        config = RedisConnectorConfig.configDef(),
        List())),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.redis.sink.RedisSinkConnector",
        configClass = classOf[RedisConnectorConfig],
        dockerImage = "kafka-connect-redis-sink",
        config = RedisConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          RedisConnectorConfig.SINK_GROUP))),
    meta = Map("displayName" -> "Dynamic data map"))

  val kafka = ConnectorSpec(
    connectionType = ConnectionType.KAFKA,
    authConfig = Some(KafkaAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[KafkaAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.kafka.sink.KafkaSourceConnector",
        configClass = classOf[KafkaSourceConnectorConfig],
        dockerImage = "kafka-connect-kafka-source",
        config = KafkaSourceConnectorConfig.configDef(),
        List(KafkaSinkConnectorConfig.KAFKA_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.kafka.sink.KafkaSinkConnector",
        configClass = classOf[KafkaSinkConnectorConfig],
        dockerImage = "kafka-connect-kafka-sink",
        config = KafkaSinkConnectorConfig.configDef(),
        List(KafkaSinkConnectorConfig.KAFKA_GROUP))),
    meta = Map("displayName" -> "Kafka sink connector"))

  val confluentKafka = ConnectorSpec(
    connectionType = ConnectionType.CONFLUENT_KAFKA,
    authConfig = Some(KafkaAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[KafkaAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.kafka.sink.KafkaSourceConnector",
        configClass = classOf[KafkaSourceConnectorConfig],
        dockerImage = "kafka-connect-kafka-source",
        config = KafkaSourceConnectorConfig.configDef(),
        List(KafkaSinkConnectorConfig.KAFKA_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.kafka.sink.KafkaSinkConnector",
        configClass = classOf[KafkaSinkConnectorConfig],
        dockerImage = "kafka-connect-kafka-sink",
        config = KafkaSinkConnectorConfig.configDef(),
        List(KafkaSinkConnectorConfig.KAFKA_GROUP))),
    meta = Map("displayName" -> "Confluent Kafka sink connector"))

  val kinesis = ConnectorSpec(
    connectionType = ConnectionType.KINESIS,
    authConfig = None,
    authConfigClass = None,
    sourceConfig = None,
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.kinesis.sink.KinesisSinkConnector",
        configClass = classOf[KinesisSinkConnectorConfig],
        dockerImage = "kafka-connect-kinesis-sink",
        config = KinesisSinkConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          KinesisSinkConnectorConfig.SINK_GROUP))),
    meta = Map("displayName" -> "Kinesis"))

  val mongo = ConnectorSpec(
    connectionType = ConnectionType.MONGO,
    authConfig = Some(MongoAuthConfig.configDef()),
    authConfigClass = Some(classOf[MongoAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.documentdb.source.DocumentDbSourceConnector",
        configClass = classOf[DocumentDbSourceConnectorConfig],
        dockerImage = "kafka-connect-documentdb-source",
        config = DocumentDbSourceConnectorConfig.configDef())),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.documentdb.sink.DocumentDbSinkConnector",
        configClass = classOf[DocumentDbSinkConnectorConfig],
        dockerImage = "kafka-connect-documentdb-sink",
        config = DocumentDbSinkConnectorConfig.configDef())),
    meta = Map("displayName" -> "DocumentDb sink connector"))

  val firebase = ConnectorSpec(
    connectionType = ConnectionType.FIREBASE,
    authConfig = Some(FirebaseAuthConfig.configDef()),
    authConfigClass = Some(classOf[FirebaseAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.documentdb.source.DocumentDbSourceConnector",
        configClass = classOf[DocumentDbSourceConnectorConfig],
        dockerImage = "kafka-connect-documentdb-source",
        config = DocumentDbSourceConnectorConfig.configDef())),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.documentdb.sink.DocumentDbSinkConnector",
        configClass = classOf[DocumentDbSinkConnectorConfig],
        dockerImage = "kafka-connect-documentdb-sink",
        config = DocumentDbSinkConnectorConfig.configDef())),
    meta = Map("displayName" -> "DocumentDb sink connector"))

  val dynamodb = ConnectorSpec(
    connectionType = ConnectionType.DYNAMODB,
    authConfig = Some(DynamoDbAuthConfig.configDef()),
    authConfigClass = Some(classOf[DynamoDbAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.documentdb.source.DocumentDbSourceConnector",
        configClass = classOf[DocumentDbSourceConnectorConfig],
        dockerImage = "kafka-connect-documentdb-source",
        config = DocumentDbSourceConnectorConfig.configDef())),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.documentdb.sink.DocumentDbSinkConnector",
        configClass = classOf[DocumentDbSinkConnectorConfig],
        dockerImage = "kafka-connect-documentdb-sink",
        config = DocumentDbSinkConnectorConfig.configDef())),
    meta = Map("displayName" -> "DocumentDb sink connector"))

  val bigQuery = ConnectorSpec(
    connectionType = ConnectionType.BIGQUERY,
    authConfig = Some(BigQueryAuthConfig.configDef()),
    authConfigClass = Some(classOf[BigQueryAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.BigQuerySourceConnector",
        configClass = classOf[BigQuerySourceConnectorConfig],
        dockerImage = "kafka-connect-bigquery-source",
        config = BigQuerySourceConnectorConfig.configDef())),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.BigQuerySinkConnector",
        configClass = classOf[BigQuerySinkConnectorConfig],
        dockerImage = "kafka-connect-bigquery-sink",
        config = BigQuerySinkConnectorConfig.configDef())),
    meta = Map("displayName" -> "BigQuery sink connector"))

  val fileGcs = ConnectorSpec(
    connectionType = ConnectionType.GCS,
    authConfig = Some(GCSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[GCSAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Amazon S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))


  val fileUpload = ConnectorSpec(
    connectionType = ConnectionType.FILE_UPLOAD,
    authConfig = Some(GCSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[BaseAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = None,
    meta = Map(
      "displayName" -> "Amazon S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))

  val jms = ConnectorSpec(
    connectionType = ConnectionType.JMS,
    authConfig = Some(JmsAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[JmsAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "",
        configClass = classOf[JmsSourceConnectorConfig],
        dockerImage = "",
        config = JmsSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "",
        configClass = classOf[JmsSinkConnectorConfig],
        dockerImage = "",
        config = JmsSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Amazon S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))

  val fileMercuryS3 = ConnectorSpec(
    connectionType = ConnectionType.MERCURY_S3,
    authConfig = Some(AWSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AWSAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Amazon S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))

  val fileNeptuneS3 = ConnectorSpec(
    connectionType = ConnectionType.NEPTUNE_S3,
    authConfig = Some(AWSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AWSAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Amazon S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))

  val fileMinIoS3 = ConnectorSpec(
    connectionType = ConnectionType.MIN_IO_S3,
    authConfig = Some(AWSAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[AWSAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.push.sink.FileSinkConnector",
        configClass = classOf[FileSinkConnectorConfig],
        dockerImage = "kafka-connect-file-sink",
        config = FileSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP))),
    meta = Map(
      "displayName" -> "Min IO S3",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"))

  val pubsub = ConnectorSpec(
    connectionType = ConnectionType.GOOGLE_PUBSUB,
    authConfig = Some(PubSubAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[PubSubAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "",
        configClass = classOf[PubSubSourceConnectorConfig],
        dockerImage = "",
        config = PubSubSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP,
          FileSourceConnectorConfig.S3_SOURCE_GROUP
        )
      )
    ),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "",
        configClass = classOf[PubSubSinkConnectorConfig],
        dockerImage = "",
        config = PubSubSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          FileSinkConnectorConfig.FILE_SINK_GROUP,
          FileSinkConnectorConfig.S3_SINK_GROUP
        )
      )
    ),
    meta = Map(
      "displayName" -> "Google PubSub",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/s3.png"
    )
  )

  val fileSharepoint = ConnectorSpec(
    connectionType = ConnectionType.SHAREPOINT,
    authConfig = Some(RestAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "Sharepoint",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/sharepoint.png"),
    connectorServiceClass = Some("com.nexla.probe.sharepoint.SharepointConnectorService"))


  val fileOneDrive = ConnectorSpec(
    connectionType = ConnectionType.ONEDRIVE,
    authConfig = Some(RestAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.file.source.FileSourceConnector",
        configClass = classOf[FileSourceConnectorConfig],
        dockerImage = "kafka-connect-file-source",
        config = FileSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          FileSourceConnectorConfig.FILE_SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "OneDrive",
      "icon" -> "https://nex-ui.s3.amazonaws.com/ui/assets/data-sources/onedrive.png"))

  val apiStreams = ConnectorSpec(
    connectionType = ConnectionType.API_STREAMS,
    authConfig = Some(RestAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.apistreams.source.ApiStreamsSourceConnector",
        configClass = classOf[ApiStreamsSourceConnectorConfig],
        dockerImage = "kafka-connect-api-streams-source",
        config = ApiStreamsSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          ApiStreamsSourceConnectorConfig.SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "ApiStreams"))

  val apiMulti = ConnectorSpec(
    connectionType = ConnectionType.API_MULTI,
    authConfig = Some(RestAuthConfig.authConfigDef()),
    authConfigClass = Some(classOf[RestAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.apistreams.source.ApiStreamsSourceConnector",
        configClass = classOf[ApiStreamsSourceConnectorConfig],
        dockerImage = "kafka-connect-api-streams-source",
        config = ApiStreamsSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          ApiStreamsSourceConnectorConfig.SOURCE_GROUP))),
    meta = Map(
      "displayName" -> "ApiStreams"))

  val pinecone = ConnectorSpec(
    connectionType = ConnectionType.PINECONE,
    authConfig = Some(PineconeAuthConfig.configDef()),
    authConfigClass = Some(classOf[PineconeAuthConfig]),
    sourceConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.vectordb.source.VectorDbSourceConnector",
        configClass = classOf[VectorSourceConnectorConfig],
        dockerImage = "kafka-connect-vectordb-source",
        config = VectorSourceConnectorConfig.configDef(),
        List(
          SourceConnectorConfig.CONNECTOR_GROUP,
          VectorSourceConnectorConfig.VECTORDB_SOURCE_GROUP))),
    sinkConfig = Some(
      ConfigSpec(
        connectorClass = "com.nexla.connector.vectordb.sink.VectorDbSinkConnector",
        configClass = classOf[VectorSinkConnectorConfig],
        dockerImage = "kafka-connect-vectordb-sink",
        config = VectorSinkConnectorConfig.configDef(),
        List(
          SinkConnectorConfig.CONNECTOR_GROUP,
          VectorSinkConnectorConfig.SINK_GROUP))),
    meta = Map(
      "displayName" -> "VectorDB"))

  val configs = Seq(
    fileWebdav,
    fileFtp,
    fileS3,
    fileAzureBlob,
    fileAzureDataLake,
    fileDeltaLakeS3,
    fileDeltaLakeAzureBlob,
    fileDeltaLakeAzureDataLake,
    fileBox,
    fileDropbox,
    rest,
    soap,
    jdbcPostgres,
    jdbcMysql,
    jdbcOracle,
    jdbcOracleAutonomous,
    jdbcRedshift,
    jdbcSnowflake,
    jdbcSqlServer,
    jdbcAzureSynapse,
    jdbcHive,
    jdbcDatabricks,
    jdbcPresto,
    jdbcFirebolt,
    jdbcNetsuite,
    as400,
    db2,
    hana,
    sybase,
    teradata,
    gcpSpanner,
    awsAthena,
    redisDataMap,
    kinesis,
    kafka,
    confluentKafka,
    mongo,
    firebase,
    dynamodb,
    bigQuery,
    fileGcs,
    fileGDrive,
    gdriveSpreadsheet,
    webhook,
    fileUpload,
    jms,
    fileMercuryS3,
    fileNeptuneS3,
    fileMinIoS3,
    pubsub,
    fileSharepoint,
    fileOneDrive,
    apiStreams,
    fileS3ApacheIceberg,
    apiMulti,
    pinecone
  )

}
