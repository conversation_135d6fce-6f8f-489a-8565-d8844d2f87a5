package com.nexla.connector;

import com.google.common.collect.Lists;
import one.util.streamex.EntryStream;

import java.util.*;
import java.util.function.BiFunction;

import static java.util.Optional.of;

public class FilterMessageTraverse {

	public static final String DELIMITER = ".";

	private final BiFunction<String, Object, Boolean> filterFunction;

	private final Boolean excludeEmptyFields;

	public FilterMessageTraverse(BiFunction<String, Object, Boolean> filterFunction) {
		this.filterFunction = filterFunction;
		this.excludeEmptyFields = false;
	}

	public FilterMessageTraverse(BiFunction<String, Object, Boolean> filterFunction,
								 Boolean excludeEmptyFields) {
		this.filterFunction = filterFunction;
		this.excludeEmptyFields = excludeEmptyFields;
	}

	public LinkedHashMap<String, Object> filter(LinkedHashMap<String, Object> data) {
		return processMap(Optional.empty(), data);
	}

	private LinkedHashMap<String, Object> processMap(Optional<String> path, LinkedHashMap<String, Object> map) {
		LinkedHashMap<String, Object> result = new LinkedHashMap<>();
		EntryStream.of(map)
			.forKeyValue((k, elem) -> {
				String newPath = delimitedPath(path) + k;
				Boolean filter = filterFunction.apply(newPath, elem);
				if (filter) {
					if (elem instanceof List) {
						List<Object> processed = processList(of(newPath), (List<Object>) elem);
						if (!processed.isEmpty() || !excludeEmptyFields) {
							result.put(k, processed);
						}
					} else if (elem instanceof Map) {
						LinkedHashMap<String, Object> elemMap;
						if (elem instanceof LinkedHashMap) {
							elemMap = (LinkedHashMap<String, Object>) elem;
						} else {
							elemMap = new LinkedHashMap<>();
							elemMap.putAll((Map) elem);
						}
						Map<String, Object> processed = processMap(of(newPath), elemMap);
						if (!processed.isEmpty() || !excludeEmptyFields) {
							result.put(k, processed);
						}
					} else {
						if(elem instanceof String && ((String) elem).isEmpty() && excludeEmptyFields) {
							return;
						}
						result.put(k, elem);
					}
				}
			});
		return result;
	}

	private String delimitedPath(Optional<String> path) {
		return path.map(x -> x + DELIMITER).orElse("");
	}

	private List<Object> processList(Optional<String> path, List<Object> list) {
		List<Object> result = Lists.newArrayList();
		for (int i = 0; i < list.size(); i++) {
			Object elem = list.get(i);
			if (elem instanceof List) {
				List<Object> javaList = new ArrayList<Object>((List) elem);
				List<Object> processed = processList(path, javaList);
				if (!processed.isEmpty()) {
					result.add(processed);
				}
			} else if (elem instanceof Map) {
				LinkedHashMap<String, Object> javaMap = new LinkedHashMap<String, Object>((Map) elem);
				Map<String, Object> processed = processMap(path, javaMap);
				if (!processed.isEmpty()) {
					result.add(processed);
				}
			} else {
				Boolean filter = filterFunction.apply(path.orElse(""), elem);
				if (filter) {
					result.add(elem);
				}
			}
		}
		return result;
	}

}