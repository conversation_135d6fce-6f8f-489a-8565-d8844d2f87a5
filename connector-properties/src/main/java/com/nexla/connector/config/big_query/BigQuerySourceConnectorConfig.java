package com.nexla.connector.config.big_query;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.file.BigQueryAuthConfig;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class BigQuerySourceConnectorConfig extends SourceConnectorConfig {

	public static final String QUERY = "query";
	public static final String DATASET = "database";
	public static final String TEMP_TABLE_DATASET = "temp.table.dataset";
	public static final String TABLE = "table";
	public static final String GCS_LOCATION = "gcs.location";
	public static final String GCS_FORMAT = "gcs.format";
	public static final String NEXLA_QUERY_DATASET = "nexla_query_dataset";
	public static final String SYNC_MESSAGES_LIMIT = "sync.messages.limit";

	public final BigQueryAuthConfig authConfig;

	public final String dataset;

	public final String tempDestinationDataset;

	public final Optional<String> intermediateGcsBucket;

	public final Optional<String> table;

	public final String intermediateGcsFormat;

	public final Optional<String> query;

	public final Integer syncMessagesLimit;

	public BigQuerySourceConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = new BigQueryAuthConfig(authMap, credsId);
		this.dataset = getString(DATASET);
		this.tempDestinationDataset = opt(getString(TEMP_TABLE_DATASET))
			.orElse(NEXLA_QUERY_DATASET);
		this.table = opt(getString(TABLE));
		this.intermediateGcsBucket = opt(getString(GCS_LOCATION));
		this.intermediateGcsFormat = getString(GCS_FORMAT);
		this.query = opt(getString(QUERY)).map(this::replaceNowVars);
		this.syncMessagesLimit = getInt(SYNC_MESSAGES_LIMIT);
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef(sourceConfigDef())
			.withKey(nexlaKey(TEMP_TABLE_DATASET, STRING, null))
			.withKey(nexlaKey(DATASET, STRING, null))
			.withKey(nexlaKey(QUERY, STRING, null))
			.withKey(nexlaKey(TABLE, STRING, null))
			.withKey(nexlaKey(GCS_LOCATION, STRING, null))
			.withKey(nexlaKey(GCS_FORMAT, STRING, "JSON"))
			.withKey(nexlaKey(SYNC_MESSAGES_LIMIT, INT, 10_000));
	}

	public BigQueryAuthConfig getAuthConfig() {
		return authConfig;
	}
}
