package com.nexla.connector.config.vectordb.mapping;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.api.client.util.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VectorMappingConfig {

    public static final String MODE_MANUAL = "manual";
    public static final String MODE_AUTO = "auto";

    private String mode = MODE_AUTO;

    @JsonProperty("id_field")
    private String idFieldPath = "id";

    @JsonProperty("dense_values_field")
    private String denseValuesFieldPath = "dense_vector";

    @JsonProperty("sparse_values_field")
    private String sparseValuesFieldPath = "sparse_values";

    @JsonProperty("sparse_indexes_field")
    private String sparseIndexesFieldPath = "sparse_indices";

    @JsonProperty("metadata_mapping")
    private List<MetadataMapping> metadataMapping = Lists.newArrayList();

    @JsonIgnore
    public boolean isManual() {
        return MODE_MANUAL.equals(this.mode);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MetadataMapping {
        @JsonProperty("type")
        private MetadataMappingType type = MetadataMappingType.SELECT;

        @JsonProperty("field")
        private String field;

        @JsonProperty("from")
        private String from;
    }
}
