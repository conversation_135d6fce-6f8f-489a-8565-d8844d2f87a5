package com.nexla.connector.config.api_streams.properties;

import com.nexla.common.NexlaConstants;

public class ApiStreamsConfigAccessor {

    public static final String REST_ITERATIONS_JSON = NexlaConstants.REST_ITERATIONS_JSON;

    // commons
    public static final String SEQUENCE = "sequence";
    public static final String MAX_PARALLEL_FEEDS = "max_parallel_feeds";
    public static final String ORDER_FIRST = "order_first";
    public static final String ORDER_LAST = "order_last";
    public static final String INGESTION_MODE = "ingestion.mode";
    public static final String FEED_PARAMS = "feed_params";

    // stream-specifics
    public static final String BASELINE_CONFIG_TYPE = "base";
    public static final String DATA_FEED_CONFIG_TYPE = "data";
    public static final String REF_SPEC = "ref_spec";
    public static final String PARAMS = "params";
    public static final String FEEDS = "feeds";
    public static final String JWT_FILTER_KEY = "jwt";
    public static final String FEED_TYPE = "type";
    public static final String FEED_SPEC = "spec";
    public static final String PRIMARY_KEYS = "primary.keys";

    // root level
    public static final String COMMON_CONFIG_JSON = "common";

    public static final String FEED_SPEC_METADATA = "metadata";
}
