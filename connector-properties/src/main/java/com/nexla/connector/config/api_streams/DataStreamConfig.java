package com.nexla.connector.config.api_streams;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.RestIterationConfig;
import lombok.Getter;
import lombok.ToString;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.*;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

@ToString
@Getter
public class DataStreamConfig extends BaseEltStreamConfig {
    private final String refSpec;
    public final List<String> primaryKeys;
    public final Optional<Object> metadata;

    public DataStreamConfig(Map<String, Object> originals, Optional<Map<String, String>> params) {
        super(configDef(), originals, DATA_FEED_CONFIG_TYPE, params);
        this.refSpec = getString(REF_SPEC);
        this.primaryKeys = getList(PRIMARY_KEYS);
        this.metadata = Optional.ofNullable(originals.get(FEED_SPEC_METADATA));
    }

    @Override
    public List<RestIterationConfig> getRestIterationConfigs() {
        return Collections.emptyList();
    }

    @Override
    public Optional<String> getRefSpec() {
        return Optional.ofNullable(refSpec);
    }

    public static NexlaConfigDef configDef() {
        return new NexlaConfigDef()
                .withKey(nexlaKey(REF_SPEC, STRING, null)
                        .documentation("Reference spec for base config")
                        .displayName("Reference Spec"))
                .withKey(nexlaKey(PRIMARY_KEYS, LIST, null)
                        .documentation("Primary keys for columns")
                        .displayName("Primary keys"))
                .withKey(nexlaKey(FEED_SPEC_METADATA, STRING, null)
                    .documentation("Metadata containing extra configuration that must be used, format is JSON map, e.g {\"overridden_mappings\": { ... }}")
                    .displayName("Metadata"));
    }
}
