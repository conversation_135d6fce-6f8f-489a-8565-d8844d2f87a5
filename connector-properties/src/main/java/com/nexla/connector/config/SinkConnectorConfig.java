package com.nexla.connector.config;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.common.NexlaConstants;
import com.nexla.common.StreamUtils;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.common.utils.Utils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.StreamUtils.toLinkedHashMap;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.properties.SqlConfigAccessor.CDC_ENABLED;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.NO_DEFAULT_VALUE;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class SinkConnectorConfig extends BaseConnectorConfig {

	public static final String CONNECTOR_GROUP = "connector";
	public static final String INACTIVITY_TIMEOUT_BEFORE_FLUSH_MIN = "inactivity.timeout.before.flush.min";
	public static final String FLUSH_CRON = "flush.cron";
	public static final String SINK_CREATE_DATASOURCE = "create.datasource";
	public static final String SINK_CREATE_DATASOURCE_DOC = "Create DataSource with request, response";

	public static final String TRACKER_ENCRYPTION_ENABLED = "tracker.encryption.enabled";
	public static final String TRACKER_ENCRYPTION_KEY = "tracker.encryption.key";
	public static final String FIXED_TRACKER_VALUE = "fixed.tracker.value";
	public static final String REPLICATION = "replication.mode";
	public static final String NODE_TAG = "node.tag";
	public static final String EXTERNALLY_SET_METRIC_NAME = "externally.set.metric.name";
	private static final Set<String> MAPPING_MODES = Sets.newHashSet(MappingConfig.MODE_AUTO, MappingConfig.MODE_MANUAL);

	public static final String MAPPING = "mapping";
	public static final String DEFAULT_MAPPING = "default";

	public static final String FLUSH_BATCH_SIZE = "flush.batch.size";
	public static final String FIRST_FLUSH_DELAY_MINUTES = "first.flush.delay.min";
	public static final String DETAILED_FLOW_INSIGHTS_ERRORS_PER_MINUTE = "detailed.flow.insights.errorsPerMinute";
	public static final String DETAILED_FLOW_INSIGHTS_SUCCESSES_PER_MINUTE = "detailed.flow.insights.successesPerMinute";
	public static final String DETAILED_FLOW_INSIGHTS_ABBREVIATED_LENGTH = "detailed.flow.insights.abbreviatedLength";
	public static final int DEFAULT_FLOW_INSIGHTS_ERRORS_PER_MINUTE = 800;
	public static final int DEFAULT_FLOW_INSIGHTS_SUCCESSES_PER_MINUTE = 200;
	public static final int DEFAULT_FLOW_INSIGHTS_ABBREVIATED_LENGTH = 256;

	public final int sinkId;
	public final int datasetId;
	public final int version;
	public final String credsEnc;
	public final String credsEncIv;
	public final Optional<MappingConfig> mappingConfig;
	public final int credsId;
	public final boolean trackerEncryptionEnabled;
	public final String trackerEncryptionKey;
	public final String externallySetMetricName;
	public final Optional<String> fixedTrackerValue;
	public final Optional<String> nodeTag;
	public final Optional<String> flushCron;
	public final int inactivityTimeoutBeforeFlushMin;
	public final int firstFlushDelayMin;

	public final Optional<Integer> flushBatchSize;
	public final Boolean combineRunIds;
	public final boolean cdcEnabled;
	public final TelemetryConfig telemetryConfig;

	public final Optional<String> pipelineType;

	public final int detailedFlowInsightsErrorsPerMinute;
	public final int detailedFlowInsightsSuccessesPerMinute;
	public final int detailedFlowInsightsAbbreviatedLength;

	protected SinkConnectorConfig(NexlaConfigDef definition, Map<String, String> originals) {
		super(definition, originals);

		this.sinkId = getInt(SINK_ID);
		this.datasetId = getInt(DATASET_ID);
		this.version = getInt(VERSION);

		this.credsEnc = getString(CREDS_ENC);
		this.credsEncIv = getString(CREDS_ENC_IV);
		this.credsId = getInt(CREDS_ID);

		this.cdcEnabled = getBoolean(CDC_ENABLED);
		this.pipelineType = Optional.ofNullable(getString(PIPELINE_TYPE));
		this.mappingConfig = opt(getString(MAPPING)).map(SinkConnectorConfig::newMappingConfig);
		this.mappingConfig.ifPresent(this::validateMapping);
		this.trackerEncryptionEnabled = getBoolean(TRACKER_ENCRYPTION_ENABLED);
		this.trackerEncryptionKey = getString(TRACKER_ENCRYPTION_KEY);
		this.fixedTrackerValue = Optional.ofNullable(getString(FIXED_TRACKER_VALUE));
		this.nodeTag = opt(getString(NODE_TAG));
		this.externallySetMetricName = getString(EXTERNALLY_SET_METRIC_NAME);
		this.inactivityTimeoutBeforeFlushMin = getInt(INACTIVITY_TIMEOUT_BEFORE_FLUSH_MIN);
		this.flushCron = getFlushCronOrDefault();
		this.flushBatchSize = Optional.ofNullable(getInt(FLUSH_BATCH_SIZE));
		this.firstFlushDelayMin = getInt(FIRST_FLUSH_DELAY_MINUTES);
		this.combineRunIds = getBoolean(FLUSH_COMBINE_RUN_IDS);
		this.telemetryConfig = new TelemetryConfig(originals);
		this.detailedFlowInsightsErrorsPerMinute = getInt(DETAILED_FLOW_INSIGHTS_ERRORS_PER_MINUTE);
		this.detailedFlowInsightsSuccessesPerMinute = getInt(DETAILED_FLOW_INSIGHTS_SUCCESSES_PER_MINUTE);
		this.detailedFlowInsightsAbbreviatedLength = getInt(DETAILED_FLOW_INSIGHTS_ABBREVIATED_LENGTH);
	}

	private Optional<String> getFlushCronOrDefault() {
		Optional<String> cron = opt(getString(FLUSH_CRON));
		if (cron.isEmpty() && this.cdcEnabled) {
			// default value for CDC flow
			cron = Optional.of("0 */10 * ? * *");
		}

		return cron;
	}

	public static MappingConfig newMappingConfig(String str) {
		Map<String, Object> map = JsonUtils.jsonToMap(str);
		Object trackerMode = map.remove("trackerMode");
		if (trackerMode != null) {
			map.put("tracker_mode", trackerMode);
		}
		Map rawMapping = (Map) map.get(MAPPING);
		if (rawMapping != null) {
			LinkedHashMap<String, Object> mapping = toLinkedHashMap(
				EntryStream.of(rawMapping)
					.mapValues(SinkConnectorConfig::backwardCompatibility));

			map.put(MAPPING, mapping);
		}
		return StreamUtils.jsonUtil().stringToType(JsonUtils.toJsonString(map), MappingConfig.class);
	}

	private static Map<String, String> backwardCompatibility(Object v) {
		if (v instanceof String) {
			return map(v, DEFAULT_MAPPING);
		} else if (v instanceof List) {
			List<String> list = (List) v;
			LinkedHashMap<String, String> result = Maps.newLinkedHashMap();
			list.forEach(m -> result.put(m, DEFAULT_MAPPING));
			return result;
		} else {
			return (Map) v;
		}
	}

	public SinkConnectorConfig(Map<String, String> originals) {
		this(sinkConfigDef(), originals);
	}

	public static NexlaConfigDef sinkConfigDef() {

		return new NexlaConfigDef(TelemetryConfig.telemetryConfigDef(baseConfigDef()))
			.withKey(nexlaKey(SINK_ID, INT, NO_DEFAULT_VALUE)
				.importance(LOW)
				.group(CONNECTOR_GROUP)
				.displayName("Sink ID"))
			.withKey(nexlaKey(CREDS_ENC, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("Creds enc")
				.group(CONNECTOR_GROUP)
				.displayName("Creds enc"))
			.withKey(nexlaKey(CREDS_ENC_IV, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("Creds enc IV")
				.group(CONNECTOR_GROUP)
				.displayName("Creds enc IV"))
			.withKey(nexlaKey(CREDS_ID, INT, -1)
				.importance(LOW)
				.documentation("Credentials id")
				.group(CONNECTOR_GROUP)
				.displayName("Credentials id"))
			.withKey(nexlaKey(MAPPING, STRING, null)
				.group(CONNECTOR_GROUP)
				.displayName("Output schema mapping"))
			.withKey(nexlaKey(SINK_CREATE_DATASOURCE, BOOLEAN, false)
				.importance(LOW)
				.documentation(SINK_CREATE_DATASOURCE_DOC)
				.group(CONNECTOR_GROUP)
				.displayName("Create DataSource for a Sink flag"))
			.withKey(nexlaKey(VERSION, ConfigDef.Type.INT, 1)
				.importance(LOW)
				.documentation("Data sink version")
				.group(CONNECTOR_GROUP)
				.displayName("Data sink version"))

			.withKey(nexlaKey(TRACKER_ENCRYPTION_ENABLED, ConfigDef.Type.BOOLEAN, false)
				.importance(LOW)
				.group(CONNECTOR_GROUP))

			.withKey(nexlaKey(TRACKER_ENCRYPTION_KEY, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.group(CONNECTOR_GROUP))

			.withKey(nexlaKey(FIXED_TRACKER_VALUE, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Fixed tracker value to use, helps shrinking metadata for datasets which have 1-to-many rows in the flow")
				.group(CONNECTOR_GROUP))

			.withKey(nexlaKey(DATASET_ID, INT, -1)
				.importance(LOW)
				.documentation("Dataset that this sink is associated with")
				.group(CONNECTOR_GROUP))

            .withKey(nexlaKey(NODE_TAG, STRING, null)
                    .importance(LOW)
                    .group(CONNECTOR_GROUP))

			.withKey(nexlaKey(FLUSH_CRON, STRING, null)
					.documentation("Flush cron")
					.group(CONNECTOR_GROUP)
					.displayName("Flush cron"))

			.withKey(nexlaKey(INACTIVITY_TIMEOUT_BEFORE_FLUSH_MIN, INT, 5)
				.documentation("Flush if there were no new records for N minutes")
				.group(CONNECTOR_GROUP)
				.displayName("Flush if there were no new records for N minutes"))

			.withKey(nexlaKey(FLUSH_BATCH_SIZE, INT, null)
				.documentation("Specify if we should force flush after a certain number of records")
				.group(CONNECTOR_GROUP)
				.displayName("Flush Batch Size"))

			.withKey(nexlaKey(FIRST_FLUSH_DELAY_MINUTES, INT, 2)
					.documentation("Specify a delay of the first flush for stateful sinks")
					.group(CONNECTOR_GROUP)
					.displayName("First flush delay in minutes"))

			.withKey(nexlaKey(FLUSH_COMBINE_RUN_IDS, BOOLEAN, false)
				.documentation("Notify only 1 run id in WRITE_DONE event")
				.group(CONNECTOR_GROUP)
				.displayName("Combine run ids"))

			.withKey(nexlaKey(CDC_ENABLED, BOOLEAN, false)
				.importance(LOW)
				.group(CONNECTOR_GROUP))

			.withKey(nexlaKey(NexlaConstants.PIPELINE_TYPE, ConfigDef.Type.STRING, null)
					.importance(LOW)
					.group(CONNECTOR_GROUP))

			.withKey(nexlaKey(EXTERNALLY_SET_METRIC_NAME, ConfigDef.Type.STRING, null)
					.importance(LOW)
					.documentation("External metric name, necessary for the adaptive flows where the metrics name is produced elsewhere.")
					.displayName("Externally set metric name")
					.group(CONNECTOR_GROUP))

			.withKey(nexlaKey(DETAILED_FLOW_INSIGHTS_ERRORS_PER_MINUTE, INT, DEFAULT_FLOW_INSIGHTS_ERRORS_PER_MINUTE)
				.importance(LOW)
				.documentation("Maximum number of error events per minute to send to detailed flow insights")
				.group(CONNECTOR_GROUP)
				.displayName("Flow Insights Errors Per Minute"))

			.withKey(nexlaKey(DETAILED_FLOW_INSIGHTS_SUCCESSES_PER_MINUTE, INT, DEFAULT_FLOW_INSIGHTS_SUCCESSES_PER_MINUTE)
				.importance(LOW)
				.documentation("Maximum number of success events per minute to send to detailed flow insights")
				.group(CONNECTOR_GROUP)
				.displayName("Flow Insights Successes Per Minute"))

			.withKey(nexlaKey(DETAILED_FLOW_INSIGHTS_ABBREVIATED_LENGTH, INT, DEFAULT_FLOW_INSIGHTS_ABBREVIATED_LENGTH)
				.importance(LOW)
				.documentation("Maximum length of abbreviated contents in detailed flow insights")
				.group(CONNECTOR_GROUP)
				.displayName("Flow Insights Abbreviated Content Length"));
	}

	private void validateMapping(MappingConfig conf) {

		if (!MAPPING_MODES.contains(conf.getMode())) {
			throw new ConfigException("Mapping mode should be one of: " + MAPPING_MODES);
		}
		if (conf.getMapping().keySet().stream().anyMatch(key -> conf.getExcludes().contains(key))) {
			throw new ConfigException("Column Exclusions and " + MAPPING + " keys should not intersect");
		}
		if (conf.getOverriddenMappings().keySet().stream().anyMatch(key -> conf.getExcludeTables().contains(key))) {
			throw new ConfigException("Table Exclusions and 'overridden_mappings' keys should not intersect");
		}
		if (MappingConfig.MODE_MANUAL.equals(conf.getMode())) {
			if (!conf.getExcludes().isEmpty()) {
				throw new ConfigException("Column Exclusions are not allowed in manual mode");
			}

			if (conf.getMapping().isEmpty() && (!this.cdcEnabled && !isEltFlowType())) {
				throw new ConfigException("Mapping cannot be empty in manual mode");
			}
		}
	}

	public boolean isEltFlowType(){
		return pipelineType.filter("elt"::equalsIgnoreCase).isPresent();
	}

	public static class ValidStringNoCase implements ConfigDef.Validator {
		final Set<String> validStrings;

		private ValidStringNoCase(Set<String> validStrings) {
			this.validStrings = validStrings;
		}

		public static ValidStringNoCase in(String... validStrings) {
			Set<String> values = StreamEx.of(validStrings)
				.flatMap(value -> StreamEx.of(value.toUpperCase(), value.toLowerCase()))
				.toSet();

			return new ValidStringNoCase(values);
		}

		@Override
		public void ensureValid(String name, Object o) {
			String s = (String) o;
			if (!validStrings.contains(s)) {
				throw new ConfigException(name, o, "String must be one of: " + Utils.join(validStrings, ", "));
			}

		}

		public String toString() {
			return "[" + Utils.join(validStrings, ", ") + "]";
		}
	}
}
