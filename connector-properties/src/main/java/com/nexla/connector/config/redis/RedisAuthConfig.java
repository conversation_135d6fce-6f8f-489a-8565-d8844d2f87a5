package com.nexla.connector.config.redis;

import com.nexla.common.SSLCertificateStore;
import com.nexla.common.io.RedisConnect;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.BaseAuthConfig;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;

import java.io.File;
import java.util.*;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static java.util.Collections.emptyList;
import static java.util.Collections.singleton;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

public class RedisAuthConfig extends BaseAuthConfig {

	public static final String DEFAULT_PORT = "6379";

	public static final String REDIS_DB_INDEX = "redis.db.index";

	public static final String ID_FIELD_NAME = "id.field.name";

	public final Set<RedisConnect> hosts;
	public final int dbIndex;
	public final boolean redisClusterEnabled;
	public final Optional<String> password;
	public final Optional<RedisTlsContext> tlsContext;

	public static RedisAuthConfig useNexlaRedisCluster() {
		return new RedisAuthConfig(new HashMap<>(), -1);
	}

	public RedisAuthConfig(Map<String, String> originals, Integer credsId) {
		super(authConfigDef(), originals, credsId);

		Set<RedisConnect> hosts = StreamEx.of(getList(REDIS_HOSTS)).map(RedisConnect::fromString).toSet();
		if (hosts.isEmpty()) {
			String host = getString("redis.host");
			int port = getInt("redis.port");
			this.hosts = singleton(RedisConnect.fromParts(host, port));
		} else {
			this.hosts = hosts;
		}

		this.dbIndex = getInt(REDIS_DB_INDEX);
		this.redisClusterEnabled = getBoolean(REDIS_CLUSTER_ENABLED);
		this.password = opt(getString(REDIS_PASSWORD));

		this.tlsContext = tlsContext();
	}

	private Optional<RedisTlsContext> tlsContext() {
		return Optional.of(getBoolean(REDIS_TLS_ENABLED))
				.filter(enabled -> enabled)
				.map(b -> {
					String kstorePwd = getString(REDIS_TLS_KEYSTORE_PASSWORD);
					String tstorePwd = getString(REDIS_TLS_TRUSTSTORE_PASSWORD);

					Optional<SSLCertificateStore> optKs = Optional.ofNullable(getString(REDIS_TLS_KEYSTORE_P12))
							.map(t -> t.replaceAll(" ", ""))
							.map(t -> new SSLCertificateStore(t, kstorePwd));

					Optional<SSLCertificateStore> optTs = Optional.ofNullable(getString(REDIS_TLS_TRUSTSTORE_P12))
							.map(t -> t.replaceAll(" ", ""))
							.map(t -> new SSLCertificateStore(t, tstorePwd));

					return new RedisTlsContext(
							optKs.or(() -> fromFile(getString(REDIS_TLS_KEYSTORE_P12_PATH), kstorePwd)).orElse(null),
							optTs.or(() -> fromFile(getString(REDIS_TLS_TRUSTSTORE_P12_PATH), tstorePwd)).orElse(null)
					);
				});
	}

	private static Optional<SSLCertificateStore> fromFile(String maybePath, String pwd) {
		return Optional.ofNullable(maybePath)
				.map(File::new)
				.map(f -> new SSLCertificateStore(f, pwd));
	}

	private static String env(String key) {
		return System.getenv(key.toUpperCase().replaceAll("[.|-]", "_"));
	}

	public static NexlaConfigDef authConfigDef() {
		List<String> defaultRedisHosts = Optional.ofNullable(env(REDIS_HOSTS))
				.map(v -> Arrays.asList(v.trim().split(",")))
				.orElse(emptyList());

		return baseAuthConfigDef()
			.withKey(nexlaKey(REDIS_HOSTS, LIST, defaultRedisHosts)
				.documentation("URL")
				.displayName("URL"))

			.withKey(nexlaKey("redis.host", STRING, null)
				.documentation("URL")
				.displayName("URL"))

			.withKey(nexlaKey("redis.port", INT, DEFAULT_PORT)
				.documentation("Port")
				.displayName("Port"))

			.withKey(nexlaKey(REDIS_DB_INDEX, INT, 0)
				.documentation("DB index")
				.displayName("DB index"))

			.withKey(nexlaKey(REDIS_TLS_ENABLED, BOOLEAN, env(REDIS_TLS_ENABLED))
				.documentation(REDIS_TLS_ENABLED)
				.displayName(REDIS_TLS_ENABLED))

			.withKey(nexlaKey(REDIS_TLS_KEYSTORE_P12, STRING, env(REDIS_TLS_KEYSTORE_P12))
				.documentation(REDIS_TLS_KEYSTORE_P12)
				.displayName(REDIS_TLS_KEYSTORE_P12))

			.withKey(nexlaKey(REDIS_TLS_KEYSTORE_P12_PATH, STRING, env(REDIS_TLS_KEYSTORE_P12_PATH))
				.documentation(REDIS_TLS_KEYSTORE_P12_PATH)
				.displayName(REDIS_TLS_KEYSTORE_P12_PATH))

			.withKey(nexlaKey(REDIS_TLS_KEYSTORE_PASSWORD, STRING, env(REDIS_TLS_KEYSTORE_PASSWORD))
				.documentation(REDIS_TLS_KEYSTORE_PASSWORD)
				.displayName(REDIS_TLS_KEYSTORE_PASSWORD))

			.withKey(nexlaKey(REDIS_TLS_TRUSTSTORE_P12, STRING, env(REDIS_TLS_TRUSTSTORE_P12))
				.documentation(REDIS_TLS_TRUSTSTORE_P12)
				.displayName(REDIS_TLS_TRUSTSTORE_P12))

			.withKey(nexlaKey(REDIS_TLS_TRUSTSTORE_P12_PATH, STRING, env(REDIS_TLS_TRUSTSTORE_P12_PATH))
				.documentation(REDIS_TLS_TRUSTSTORE_P12_PATH)
				.displayName(REDIS_TLS_TRUSTSTORE_P12_PATH))

			.withKey(nexlaKey(REDIS_TLS_TRUSTSTORE_PASSWORD, STRING, env(REDIS_TLS_TRUSTSTORE_PASSWORD))
				.documentation(REDIS_TLS_TRUSTSTORE_PASSWORD)
				.displayName(REDIS_TLS_TRUSTSTORE_PASSWORD))

			.withKey(nexlaKey(REDIS_CLUSTER_ENABLED, ConfigDef.Type.BOOLEAN, env(REDIS_CLUSTER_ENABLED))
				.importance(LOW)
				.documentation("Enable Redis cluster mode")
				.displayName("Enable Redis cluster mode"))

			.withKey(nexlaKey(REDIS_PASSWORD, STRING, env(REDIS_PASSWORD))
				.importance(LOW)
				.documentation("Redis password")
				.displayName("Redis password"))
			;
	}

}
