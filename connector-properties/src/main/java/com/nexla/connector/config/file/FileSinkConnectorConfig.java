package com.nexla.connector.config.file;

import com.google.common.collect.Sets;
import com.nexla.common.ConnectionType;
import com.nexla.common.time.NexlaTimeUnit;
import com.nexla.common.time.VarUtils;
import com.nexla.common.time.VarUtils.VarInfo;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.FileEncryptConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.gdrive.SpreadsheetSinkConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RecourceAccessCallback;
import com.nexla.spec.Configs;
import java.util.*;



import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.parse.ParserConfigs.Csv.*;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_ROOT_TAG;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_ROOT_TAG_DEFAULT;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.SourceConnectorConfig.EnumValidator;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.*;
import static com.nexla.connector.config.file.FtpConstants.*;
import static com.nexla.connector.config.file.S3Constants.REGION;
import static com.nexla.connector.properties.FileConfigAccessor.*;
import static com.nexla.parser.ParserConfigs.EDI_EXTENSIONS;
import static com.nexla.parser.Writers.WRITER_NAMES;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.*;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

public class FileSinkConnectorConfig extends SinkConnectorConfig implements FileConnectorAuth {

	public static final String FILE_SINK_GROUP = "sink";

	public static final String FTP_SINK_GROUP = "ftp";
	public static final String S3_SINK_GROUP = "s3";

	public static final int DEFAULT_BUFFERED_WRITER_SIZE = 1024 * 1024;

	public static final int DEFAULT_MAX_FILE_SIZE_MB = 4 * 1024; // 5GB - Max allowable s3 file size is 5G
	public static final long RECORDS_PER_FILE_UNLIMITED = -1;
	public static final String DEFAULT_DATA_FORMAT = "json";
	public static final int DEFAULT_MAX_OPEN_FILES = 1000;

	public static final String DEFAULT_LOCAL_BUFFER_DIR = "/tmp";
	public static final String META_PATH = "meta.path";
	public static final String S3_UPLOAD_PARALLELISM = "s3.upload.parallelism";
	public static final String S3_UPLOAD_PART_MB = "s3.upload.part.mb";
	public static final String GOOGLE_SPREADSHEET = "GOOGLE_SPREADSHEET";
	public static final String FILENAME_OFFSET = "filename.offset";
	public static final String SINK_PARALLELISM = "sink.parallelism";
	public static final String FAST_PARALLELISM = "fast.parallelism";
	public static final String DEDICATED_NODE = "dedicated.node";
	public static final String EXTENSION = "extension";

	public static final String CUSTOM_CLASS_NAME = "custom.class.name";
	public static final String CUSTOM_JAR_PATH = "custom.jar.path";
	public static final String CUSTOM_CREDENTIAL_ID = "custom.jar.credential.id";
	public static final String OVERWRITE_EXISTING_FILE = "overwrite.files";
	public static final String COMPRESSION_TYPE = "compression.type";

	public static final String JOIN_FILES = "join.files";

	public static final String APPEND_MODE_ENABLED = "append.mode.enabled";

	public static final String SPARK_SESSION_CONFIGS = "spark.session.configs";

	public static final String BUFFERED_WRITER_SIZE = "buffered.writer.size";

	public final String path;
	public final Optional<String> metaPath;

	public final ConnectionType sinkType;

	public final String dataFormat;
	public final String extension;
	public final Optional<VarInfo> outputDirNamePattern;
	public final long maxRecordsPerFile;
	public final long maxFileSize;

	public final String region;

	/**
	 * Record keys used to split files by
	 */
	public final Set<String> groupPartitionKeys = new HashSet<>();
	/**
	 * Default values for missing record keys
	 */
	public final Map<String, String> groupPartitionDefaults = new HashMap<>();

	/**
	 * Optional prefix for file, can contain variables
	 */
	public final Optional<VarInfo> fileNamePrefix;
	public final Optional<VarInfo> fileName;

	public final int maxOpenFiles;
	public final boolean gzip;
	public final String localBufferDir;
	public final String timezone;
	public final boolean datetimePadding;

	public final BaseAuthConfig authConfig;
	public final String s3CredsEnc;
	public final String s3CredsEnvIv;

	public final int s3uploadParallelism;
	public final int s3uploadPartMb;

	public final Optional<FileEncryptConfig> fileEncryptConfig;
	public final Optional<SpreadsheetSinkConnectorConfig> spreadsheetConfig;
	public final Optional<RecourceAccessCallback> resourceAccessCallback;
	public final Optional<String> customJarClassName;
	public final Optional<String> customJarPath;
	public final Boolean overwriteExistingFile;
	public final Optional<Integer> customCredentialId;
	public final Boolean joinFiles;

	public boolean filenameOffset;
	public final Integer sinkParallelism;
	public final Integer fastParallelism;

	public final Optional<String> compressionType;

	public final boolean appendModeEnabled;

	public final boolean csvHeader;

	public final String sparkSessionConfigs;

	public final Integer bufferedWriterSize;

	public FileSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		if (originals.containsKey(PATH)) {
			this.path = normalizePath(getString(PATH));
		} else {
			this.path = normalizePath(opt(getString("bucket")).orElse("") + "/" + opt(getString("prefix")).orElse(""));
		}

		this.s3uploadParallelism = getInt(S3_UPLOAD_PARALLELISM);
		this.s3uploadPartMb = getInt(S3_UPLOAD_PART_MB);

		if (originals.containsKey(META_PATH) ||
			originals.containsKey("meta.bucket") ||
			originals.containsKey("meta.prefix")
		) {
			if (originals.containsKey(META_PATH)) {
				this.metaPath = opt(normalizePath(getString(META_PATH)));
			} else {
				this.metaPath = opt(normalizePath(getString("meta.bucket") + "/" + getString("meta.prefix")));
			}
		} else {
			this.metaPath = empty();
		}

		this.sinkType = ConnectionType.fromString(getString(SINK_TYPE).toUpperCase());

		this.dataFormat = getString(DATA_FORMAT);

		this.s3CredsEnc = getString(S3_CREDS_ENC);
		this.s3CredsEnvIv = getString(S3_CREDS_ENCIV);

		this.outputDirNamePattern = opt(getString(OUTPUT_DIR_NAME_PATTERN))
			.map(VarUtils::processStringWithVars)
			.map(this::enrichWithNowVars);

		this.fileNamePrefix = opt(getString(FILE_NAME_PREFIX))
			.map(VarUtils::processStringWithVars)
			.map(this::enrichWithNowVars);

		this.fileName = opt(getString(FILE_NAME)).map(VarUtils::processStringWithVars);

		outputDirNamePattern.ifPresent(varInfo -> {
			groupPartitionKeys.addAll(varInfo.variables);
			groupPartitionDefaults.putAll(varInfo.defaults);
		});
		fileNamePrefix.ifPresent(varInfo -> {
			groupPartitionKeys.addAll(varInfo.variables);
			groupPartitionDefaults.putAll(varInfo.defaults);
		});
		fileName.ifPresent(varInfo -> {
			groupPartitionKeys.addAll(varInfo.variables);
			groupPartitionDefaults.putAll(varInfo.defaults);
		});

		groupPartitionKeys.removeAll(NexlaTimeUnit.TIME_UNIT_BY_PATTERN.keySet());

		this.maxFileSize = 1024 * 1024 * getLong(MAX_FILE_SIZE_MB);

		this.maxRecordsPerFile = ofNullable(getLong(MAX_RECORDS_PER_FILE))
			.orElseGet(() ->
				EDI_EXTENSIONS.contains(dataFormat.toLowerCase())
					? 1
					: RECORDS_PER_FILE_UNLIMITED);

		this.maxOpenFiles = getInt(OPEN_FILES_CACHE_SIZE);
		this.gzip = getBoolean(GZIP);

		this.localBufferDir = getString(LOCAL_BUFFER_DIR);

		this.region = getString(REGION);

		this.timezone = getString(TIMEZONE);
		this.datetimePadding = getBoolean(DATETIME_PADDING);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = Configs.authConfigCreator(sinkType, authMap, credsId);

		this.fileEncryptConfig = Optional.of(new FileEncryptConfig(authMap))
			.filter(x -> x.encryptStandard.isPresent());

		if (GOOGLE_SPREADSHEET.equalsIgnoreCase(dataFormat)) {
			this.spreadsheetConfig = Optional.of(new SpreadsheetSinkConnectorConfig(originals));
		} else {
			this.spreadsheetConfig = empty();
		}
		this.filenameOffset = getBoolean(FILENAME_OFFSET);
		this.resourceAccessCallback = authConfig.resourceAccessCallbackConfig
			.map(c -> new JpmcFileResourceAccessCallback(
				c.takeResourceUrl.get(),
				c.takeResourceBody,
				c.releaseResourceUrl.get(),
				c.releaseResourceBody
			));
		this.sinkParallelism = getInt(SINK_PARALLELISM);
		this.customJarClassName = opt(getString(CUSTOM_CLASS_NAME));
		this.customJarPath = opt(getString(CUSTOM_JAR_PATH));
		this.customCredentialId = ofNullable(getInt(CUSTOM_CREDENTIAL_ID));
		this.overwriteExistingFile = getBoolean(OVERWRITE_EXISTING_FILE);
		this.extension = opt(getString(EXTENSION)).orElse(dataFormat);
		this.fastParallelism = getInt(FAST_PARALLELISM);
		this.compressionType = Optional.ofNullable(getString(COMPRESSION_TYPE));

		this.joinFiles = getBoolean(JOIN_FILES);
		this.appendModeEnabled = getBoolean(APPEND_MODE_ENABLED);
		this.sparkSessionConfigs = getString(SPARK_SESSION_CONFIGS);
		this.csvHeader = getBoolean(WRITE_HEADER);

		this.bufferedWriterSize = getInt(BUFFERED_WRITER_SIZE);

		validateAppendMode();
	}

	private void validateAppendMode() {
		if (appendModeEnabled) {
			if (fileName.isEmpty()) {
				throw new UnsupportedOperationException(String.format("%s property must be specified when %s is true!", FILE_NAME, APPEND_MODE_ENABLED));
			}
		}
	}

	public BaseAuthConfig getAuthConfig() {
		return authConfig;
	}

	@Override
	public String getRegion() {
		return region;
	}

	@Override
	public String getPath() {
		return path;
	}

	@Override
	public ConnectionType getConnectionType() {
		return sinkType;
	}

	@Override
	public Optional<RecourceAccessCallback> recourceAccessCallback() {
		return resourceAccessCallback;
	}

	@Override
	public BaseConnectorConfig getConnectorConfig() {
		return this;
	}

	public static NexlaConfigDef configDef() {

		Set<String> writerNames = Sets.newHashSet(WRITER_NAMES);
		writerNames.add(GOOGLE_SPREADSHEET);

		NexlaConfigDef connectorConfigDef = new NexlaConfigDef(sinkConfigDef())

			.withKey(nexlaKey(PATH, STRING, null)
				.documentation("Path to file or directory")
				.group(FILE_SINK_GROUP)
				.displayName("Path to file or directory"))

			.withKey(nexlaKey(META_PATH, STRING, null)
				.documentation("Meta directory")
				.group(FILE_SINK_GROUP)
				.displayName("Meta directory"))

			.withKey(nexlaKey(S3_UPLOAD_PARALLELISM, INT, 25)
				.documentation("S3 upload parallelism")
				.group(FILE_SINK_GROUP)
				.displayName("S3 upload parallelism"))

			.withKey(nexlaKey(S3_UPLOAD_PART_MB, INT, 50)
				.documentation("S3 upload part, mb")
				.group(FILE_SINK_GROUP)
				.displayName("S3 upload part, mb"))

			// now PATH=BUCKET / PREFIX, leaving them for backward compatibility
			.withKey(nexlaKey("bucket", STRING, null)
				.documentation("Bucket")
				.group(FILE_SINK_GROUP)
				.displayName("Bucket"))
			.withKey(nexlaKey("prefix", STRING, null)
				.documentation("Prefix")
				.group(FILE_SINK_GROUP)
				.displayName("Bucket"))

			.withKey(nexlaKey("meta.bucket", STRING, null)
				.documentation("Meta bucket")
				.group(FILE_SINK_GROUP)
				.displayName("Meta bucket"))
			.withKey(nexlaKey("meta.prefix", STRING, null)
				.documentation("Meta prefix")
				.group(FILE_SINK_GROUP)
				.displayName("Meta prefix"))

			.withKey(nexlaKey(S3_CREDS_ENC, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 Creds enc")
				.group(FILE_SINK_GROUP)
				.displayName("S3 Creds enc"))
			.withKey(nexlaKey(S3_CREDS_ENCIV, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 Creds enc IV")
				.group(FILE_SINK_GROUP)
				.displayName("S3 Creds enc IV"))

			.withKey(nexlaKey(SINK_TYPE, STRING, NO_DEFAULT_VALUE)
				.validator(EnumValidator.in(
					ConnectionType.MERCURY_S3,
					ConnectionType.NEPTUNE_S3,
					ConnectionType.MIN_IO_S3,
					ConnectionType.GCS,
					ConnectionType.FTP,
					ConnectionType.S3,
					ConnectionType.GDRIVE,
					ConnectionType.ONEDRIVE,
					ConnectionType.WEBDAV,
					ConnectionType.GCS,
					ConnectionType.BOX,
					ConnectionType.DROPBOX,
					ConnectionType.AZURE_BLB,
					ConnectionType.AZURE_DATA_LAKE,
					ConnectionType.DELTA_LAKE_S3,
					ConnectionType.DELTA_LAKE_AZURE_BLB,
					ConnectionType.DELTA_LAKE_AZURE_DATA_LAKE))
				.importance(LOW)
				.documentation("Connection type")
				.group(FILE_SINK_GROUP)
				.displayName("Connection type"))

			.withKey(nexlaKey(DATA_FORMAT, STRING, DEFAULT_DATA_FORMAT)
				.validator(ValidString.in(writerNames.toArray(new String[0])))
				.documentation("Data format")
				.group(FILE_SINK_GROUP)
				.displayName("Data format, defaults to json"))

			.withKey(nexlaKey(TIMEZONE, STRING, "UTC")
				.documentation("Timezone")
				.group(FILE_SINK_GROUP)
				.displayName("Timezone for path format"))

			.withKey(nexlaKey(DATETIME_PADDING, Type.BOOLEAN, true)
				.documentation("Enable padding for date components")
				.group(FILE_SINK_GROUP)
				.displayName("Enable padding for date components"))

			.withKey(nexlaKey(MAX_FILE_SIZE_MB, Type.LONG, DEFAULT_MAX_FILE_SIZE_MB)
				.documentation("Max file size")
				.group(FILE_SINK_GROUP)
				.displayName("Max file size, Mb"))

			.withKey(nexlaKey(MAX_RECORDS_PER_FILE, INT, null)
				.documentation("Max records per file")
				.group(FILE_SINK_GROUP)
				.displayName("Max records per file"))

			.withKey(nexlaKey(OPEN_FILES_CACHE_SIZE, INT, DEFAULT_MAX_OPEN_FILES)
				.documentation("Relevant only when the output directory or file name needs to be partitioned based on message fields.")
				.group(FILE_SINK_GROUP)
				.displayName("Max number of files a task can open"))

			.withKey(nexlaKey(GZIP, Type.BOOLEAN, false)
				.documentation("GZIP")
				.group(FILE_SINK_GROUP)
				.displayName("Use GZIP compression"))

			.withKey(nexlaKey(LOCAL_BUFFER_DIR, STRING, DEFAULT_LOCAL_BUFFER_DIR)
				.importance(LOW)
				.documentation("Local folder for storing temporary files")
				.group(FILE_SINK_GROUP)
				.displayName("Local folder"))

			.withKey(nexlaKey(OUTPUT_DIR_NAME_PATTERN, STRING, null)
				.documentation("Output directory pattern")
				.group(FILE_SINK_GROUP)
				.displayName("Output directory pattern"))

			.withKey(nexlaKey(FILE_NAME_PREFIX, STRING, null)
				.documentation("File name prefix")
				.group(FILE_SINK_GROUP)
				.displayName("File name prefix"))

			.withKey(nexlaKey(FILE_NAME, STRING, null)
				.documentation("File name")
				.group(FILE_SINK_GROUP)
				.displayName("File name"))

			.withKey(nexlaKey(CSV_DELIMITER, STRING, null)
				.importance(LOW)
				.documentation("CSV delimiter")
				.group(FILE_SINK_GROUP)
				.displayName("CSV delimiter"))

			.withKey(nexlaKey(CSV_QUOTE_CHAR, STRING, null)
				.importance(LOW)
				.documentation("CSV quote char")
				.group(FILE_SINK_GROUP)
				.displayName("CSV quote char"))

			.withKey(nexlaKey(FTP_TYPE, STRING, FTP)
				.validator(ValidString.in(FTP, FTPS, SFTP))
				.documentation("FTP connection type")
				.group(FTP_SINK_GROUP)
				.displayName("FTP connection type"))

			.withKey(nexlaKey(REGION, STRING, "us-east-1")
				.validator(ValidString.in(
					"us-east-2",
					"us-east-1",
					"us-west-1",
					"us-west-2",
					"ap-south-1",
					"ap-northeast-2",
					"ap-southeast-1",
					"ap-southeast-2",
					"ap-northeast-1",
					"ca-central-1",
					"eu-central-1",
					"eu-west-1",
					"eu-west-2",
					"sa-east-1"
				))
				.documentation("AWS Region for buckets, defaults to us-east-1")
				.group(S3_SINK_GROUP)
				.displayName("Region"))

			.withKey(nexlaKey(XML_ROOT_TAG, STRING, XML_ROOT_TAG_DEFAULT)
				.documentation("Custom XML root tag for the destination. The default is '" + XML_ROOT_TAG_DEFAULT + "'.")
				.group(FTP_SINK_GROUP)
				.displayName("Xml root tag"))

			.withKey(nexlaKey("orc.compress", STRING, "zlib")
				.validator(ValidStringNoCase.in("none", "zlib", "snappy", "lzo", "lz4"))
				.documentation("ORC compression type")
				.group(FTP_SINK_GROUP)
				.displayName("ORC compression type"))

			.withKey(nexlaKey(FILENAME_OFFSET, BOOLEAN, true)
				.documentation("Put offset as part as filename")
				.group(FILE_SINK_GROUP)
				.displayName("Put offset as part as filename"))

			.withKey(nexlaKey(SINK_PARALLELISM, INT, 1)
				.documentation("Sink parallelism")
				.group(FILE_SINK_GROUP)
				.displayName("Sink parallelism"))

			.withKey(nexlaKey(CUSTOM_CLASS_NAME, STRING, null)
				.documentation("For custom JAR pipelines execution: class name to execute in user's provided JAR")
				.group(FILE_SINK_GROUP)
				.displayName("Custom class name"))

			.withKey(nexlaKey(CUSTOM_JAR_PATH, STRING, null)
				.documentation("For custom JAR pipelines execution: path (url) to access custom JAR")
				.group(FILE_SINK_GROUP)
				.displayName("Custom jar url"))

			.withKey(nexlaKey(OVERWRITE_EXISTING_FILE, BOOLEAN, false)
				.documentation("If existing file file should be overwritten")
				.group(FILE_SINK_GROUP)
				.displayName("Overwrite files"))

			.withKey(nexlaKey(CUSTOM_CREDENTIAL_ID, INT, null)
				.documentation("Credential ID for jar download")
				.group(FILE_SINK_GROUP)
				.displayName("Credential ID for jar download"))

			.withKey(nexlaKey(EXTENSION, STRING, null)
				.documentation("File extension")
				.group(FILE_SINK_GROUP)
				.displayName("File extension"))

			.withKey(nexlaKey(FAST_PARALLELISM, INT, 1)
				.documentation("Fast connector parallelism")
				.group(FILE_SINK_GROUP)
				.displayName("Fast connector parallelism"))

			.withKey(nexlaKey(COMPRESSION_TYPE, STRING, "gzip")
				.validator(ValidStringNoCase.in("gzip", "snappy"))
				.documentation("Compression type")
				.group(FILE_SINK_GROUP)
				.displayName("Compression type"))

			.withKey(nexlaKey(JOIN_FILES, BOOLEAN, true)
				.documentation("Join unload files")
				.group(CONNECTOR_GROUP)
				.displayName("Join unload files"))

			.withKey(nexlaKey(APPEND_MODE_ENABLED, BOOLEAN, false)
					.documentation("Append mode will create file if it doesn't exist or append data to it if it exists")
					.group(FILE_SINK_GROUP)
					.displayName("Append mode will create file if it doesn't exist or append data to it if it exists"))

			.withKey(nexlaKey(SPARK_SESSION_CONFIGS, STRING, null)
					.documentation("Extra configs to set to spark session")
					.group(FILE_SINK_GROUP)
					.displayName("Extra configs to set to spark session"))

			.withKey(nexlaKey(WRITE_HEADER, BOOLEAN, true)
					.documentation("Whether to write header to csv, tsv and xlsx files or not")
					.group(FILE_SINK_GROUP)
					.displayName("Whether to write header to csv, tsv and xlsx files or not"))

			.withKey(nexlaKey(BUFFERED_WRITER_SIZE, INT, DEFAULT_BUFFERED_WRITER_SIZE)
					.documentation("Size of the buffered writer")
					.group(FILE_SINK_GROUP)
					.displayName("Size of the buffered writer"));
			;

		return FileEncryptConfig.configDef(connectorConfigDef);

	}
}
