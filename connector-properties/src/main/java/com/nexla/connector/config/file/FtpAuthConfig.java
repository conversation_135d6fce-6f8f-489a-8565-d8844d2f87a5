package com.nexla.connector.config.file;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.BaseAuthConfig;
import lombok.ToString;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.file.FtpConstants.*;

@ToString
public class FtpAuthConfig extends BaseAuthConfig {

	public final String host;
	public final int port;
	public final String ftpType;
	public final String ftpMode;
	public final boolean anonymous;
	public final boolean strictReplyParsing;
	public final String username;
	@ToString.Exclude
	public final String password;
	@ToString.Exclude
	public final String privateKey;
	@ToString.Exclude
	public final String privateKeyPassphrase;

	public FtpAuthConfig(Map<String, ?> parsedConfig, Integer credsId) {
		super(authConfigDef(), parsedConfig, credsId);

		this.host = getString(HOST);
		this.port = getInt(PORT);
		this.ftpType = getString(FTP_TYPE);
		this.ftpMode = getString(FTP_MODE);
		this.anonymous = getBoolean(ANONYMOUS);
		this.strictReplyParsing = getBoolean(STRICT_REPLY_PARSING);
		this.username = getString(USERNAME);
		this.password = getString(PASSWORD);
		this.privateKey = getString(PRIVATE_KEY);
		this.privateKeyPassphrase = getString(PASSPHRASE);
	}

	public static NexlaConfigDef authConfigDef() {

		return baseAuthConfigDef()

			.withKey(nexlaKey(HOST, ConfigDef.Type.STRING, "localhost")
				.documentation("Host")
				.displayName("Host"))

			.withKey(nexlaKey(PORT, ConfigDef.Type.INT, 21)
				.documentation("Defaults to 21 for FTP, 22 for secure FTP")
				.displayName("FTP port"))

			.withKey(nexlaKey(FTP_TYPE, ConfigDef.Type.STRING, FTP)
				.validator(ConfigDef.ValidString.in(FTP, FTPS, SFTP))
				.documentation("FTP connection type")
				.displayName("FTP connection type"))

			.withKey(nexlaKey(FTP_MODE, ConfigDef.Type.STRING, PASSIVE_LOCAL)
				.validator(ConfigDef.ValidString.in(ACTIVE, PASSIVE_LOCAL, PASSIVE_REMOTE))
				.documentation("FTP mode")
				.displayName("FTP mode"))

			.withKey(nexlaKey(ANONYMOUS, ConfigDef.Type.BOOLEAN, false)
				.documentation("Is access anonymous?")
				.displayName("Anonymous access"))

			.withKey(nexlaKey(STRICT_REPLY_PARSING, ConfigDef.Type.BOOLEAN, true)
				.documentation("FTP server strict reply parsing?")
				.displayName("Strict reply parsing"))

			.withKey(nexlaKey(USERNAME, ConfigDef.Type.STRING, null)
				.documentation("FTP username")
				.displayName("FTP username"))

			.withKey(nexlaKey(PASSWORD, ConfigDef.Type.STRING, null)
				.documentation("FTP password")
				.displayName("FTP password"))

			.withKey(nexlaKey(PASSPHRASE, ConfigDef.Type.STRING, null)
				.documentation("FTP private key passphrase")
				.displayName("FTP private key passphrase"))

			.withKey(nexlaKey(PRIVATE_KEY, ConfigDef.Type.STRING, null)
				.documentation("Private key for SFTP")
				.displayName("Private key"))

			;
	}

}
