package com.nexla.connector.config.sharepoint;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.RestAuthConfig;
import lombok.Getter;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

@Getter
public class SharepointAuthConfig extends RestAuthConfig {
    public static final String QUERY_PARAMETER = "sites.query.parameter";

    public static final NexlaConfigDef AUTH_CONFIG_DEF = authConfigDef();
    private static final String AUTH_GROUP = "auth-group";

    private final String queryParameter;

    public SharepointAuthConfig(Map<String, ?> originals, Integer credsId) {
        super(SharepointAuthConfig.AUTH_CONFIG_DEF, originals, credsId);

        this.queryParameter = getString(QUERY_PARAMETER);
    }

    public static NexlaConfigDef authConfigDef() {
        return RestAuthConfig.authConfigDef()
                .with<PERSON>ey(nexlaKey(QUERY_PARAMETER, STRING, "")
                        .documentation("Sites Query Parameter")
                        .group(AUTH_GROUP)
                        .displayName("Sites Query Parameter"));
    }
}
