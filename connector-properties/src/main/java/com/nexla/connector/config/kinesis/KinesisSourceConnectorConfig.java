package com.nexla.connector.config.kinesis;

import com.google.common.collect.Lists;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import org.apache.kafka.common.config.ConfigDef;

import java.util.List;
import java.util.Map;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.properties.FileConfigAccessor.MONITOR_POLL_MS;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Range.between;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class KinesisSourceConnectorConfig extends SourceConnectorConfig {

	public static final String KINESIS_CREDS_ENC = "kinesis.creds.enc";
	public static final String KINESIS_CREDS_ENC_IV = "kinesis.creds.enciv";
	public static final String STREAM_NAME_CONF = "kinesis.stream";
	public static final String KINESIS_POSISTION_CONF = "kinesis.position";
	public static final String KINESIS_RECORD_LIMIT_CONF = "kinesis.record.limit";
	public static final String KINESIS_SHARD_ID = "kinesis.shard.ids";
	public static final String KINESIS_SHARD_ID_DELIMITER = ";";

	public static final String KINESIS_GROUP = "";

	public final String streamName;
	public final String defaultKinesisPosition;
	public final int kinesisRecordLimit;
	public final long monitorPollMs;
	public final AWSAuthConfig authConfig;
	public final List<String> shardIds;

	public KinesisSourceConnectorConfig(Map<String, String> parsedConfig) {
		super(configDef(), parsedConfig);
		this.streamName = getString(STREAM_NAME_CONF);
		this.defaultKinesisPosition = getString(KINESIS_POSISTION_CONF);
		this.kinesisRecordLimit = getInt(KINESIS_RECORD_LIMIT_CONF);
		this.authConfig = new AWSAuthConfig(unitTest ? parsedConfig : getCreds(decryptKey, getString(KINESIS_CREDS_ENC), getString(KINESIS_CREDS_ENC_IV)), credsId);
		this.monitorPollMs = getLong(MONITOR_POLL_MS);
		this.shardIds = opt(getString(KINESIS_SHARD_ID))
			.map(shards -> Lists.newArrayList(shards.split(KINESIS_SHARD_ID_DELIMITER)))
			.orElse(Lists.newArrayList());
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef(sourceConfigDef())

			.withKey(nexlaKey(POLL_MS, ConfigDef.Type.LONG, DEFAULT_POLL_MS)
				.importance(LOW)
				.documentation("Poll interval, ms")
				.group(KINESIS_GROUP)
				.displayName("Poll interval, ms"))

			.withKey(nexlaKey(KINESIS_SHARD_ID, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Kinesis shards, ms")
				.group(KINESIS_GROUP)
				.displayName("Kinesis shards, ms"))

			.withKey(nexlaKey(MONITOR_POLL_MS, ConfigDef.Type.LONG, 60000L)
				.importance(LOW)
				.documentation("Monitoring interval")
				.group(KINESIS_GROUP)
				.displayName("Monitoring interval, ms"))

			.withKey(nexlaKey(KINESIS_CREDS_ENC, STRING, null)
				.importance(LOW)
				.documentation("S3 Creds enc")
				.group(KINESIS_GROUP)
				.displayName("S3 Creds enc"))

			.withKey(nexlaKey(KINESIS_CREDS_ENC_IV, STRING, null)
				.importance(LOW)
				.documentation("S3 Creds enc IV")
				.group(KINESIS_GROUP)
				.displayName("S3 Creds enc IV"))

			.withKey(nexlaKey(STREAM_NAME_CONF, STRING, null)
				.documentation("Kinesis Stream")
				.group(KINESIS_GROUP)
				.displayName("Kinesis Stream"))

			.withKey(nexlaKey(KINESIS_POSISTION_CONF, STRING, "TRIM_HORIZON")
				.documentation("Default Kinesis Stream position [AT_SEQUENCE_NUMBER, AFTER_SEQUENCE_NUMBER, TRIM_HORIZON, LATEST, AT_TIMESTAMP]")
				.group(KINESIS_GROUP)
				.displayName("Default Kinesis Stream position"))

			.withKey(nexlaKey(KINESIS_RECORD_LIMIT_CONF, ConfigDef.Type.INT, 500)
				.validator(between(1, 10000))
				.documentation("The number of records to read in each poll of the Kinesis shard")
				.group(KINESIS_GROUP)
				.displayName("The number of records to read in each poll of the Kinesis shard"));

	}

}