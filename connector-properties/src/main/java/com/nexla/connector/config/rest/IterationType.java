package com.nexla.connector.config.rest;

import java.util.NoSuchElementException;

public enum IterationType {
	PAGING_INCREMENTING("paging.incrementing"),
	PAGING_INCREMENTING_OFFSET("paging.incrementing.offset"),
	PAGING_NEXT_TOKEN("paging.next.token"),
	PAGING_NEXT_URL("paging.next.url"),
	STATIC_URL("static.url"),
	HORIZONTAL_ITERATION("horizontal.iteration"),
	LINK_HEADER("link.header"),
	EMIT_ONCE("emit.once"),
	RESPONSE_ID_NUMBER("response.id.number"),
	RESPONSE_ID_STRING("response.id.string"),
	DATA_MAP_KEY_QUEUE("data.map.key.queue"),
	ASYNC_ITERATION("async.poll"),
	BODY_AS_FILE_ITERATION("body.as.file"),
	GRAPHQL_CURSOR_ITERATION("graphql.cursor"),
	GRAPHQL_PAGE_ITERATION("graphql.page"),
	CODE_CONTAINER_ITERATION("code.container"),
	;

	public final String property;

	IterationType(String property) {
		this.property = property;
	}

	public static IterationType fromProperty(String property) {
		for (IterationType iterationType : values()) {
			if (iterationType.property.equals(property.toLowerCase())) {
				return iterationType;
			}
		}
		throw new NoSuchElementException(property);
	}
}
