package com.nexla.connector.config.file;

import com.azure.identity.ClientCertificateCredential;
import com.azure.identity.ClientCertificateCredentialBuilder;
import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.nexla.connector.config.file.AzureAuthConfig;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

import static org.apache.commons.lang.StringUtils.isEmpty;

public class AzureServicePrincipalAuthHelper {

    public static final String CERTIFICATE_FILE_PREFIX = "blob_cert_";
    public static final String PEM_CERTIFICATE_FILE_EXT = ".pem";
    public static final String PFX_CERTIFICATE_FILE_EXT = ".pfx";
    public static ClientSecretCredential buildClientServiceCredential(AzureAuthConfig authConfig) {
        return new ClientSecretCredentialBuilder()
                .clientId(authConfig.clientId)
                .clientSecret(authConfig.clientSecret)
                .tenantId(authConfig.tenantId)
                .build();
    }

    /**
     * Create {@link ClientCertificateCredential} depending on {@link AzureAuthConfig} params.
     * Builds client with PEM Certificate authentication if {@link AzureAuthConfig#pemCertificate} String value is provided.
     * Otherwise, builds it from password-protected PFX Certificate.
     *
     * {@link AzureAuthConfig#pfxCertificate} is a base64 representation of PFX certificate.
     * {@link AzureAuthConfig#pfxCertificatePassword} is a password that was generated along with PFX
     * certificate in order to protect it
     * @param authConfig
     * @return
     */
    public static ClientCertificateCredential buildClientCertificateCredential(AzureAuthConfig authConfig) {
        ClientCertificateCredentialBuilder clientCertCredBuilder = new ClientCertificateCredentialBuilder();
        clientCertCredBuilder.clientId(authConfig.clientId);
        if (!isEmpty(authConfig.pemCertificate)) {
            String pemCertificatePathString = createPemCertFile(authConfig.pemCertificate);
            clientCertCredBuilder.pemCertificate(pemCertificatePathString);
        } else if (!isEmpty(authConfig.pfxCertificate) && !isEmpty(authConfig.pfxCertificatePassword)) {
            String pfxCertificatePathString = createPfxCertFile(authConfig.pfxCertificate);
            clientCertCredBuilder.pfxCertificate(pfxCertificatePathString, authConfig.pfxCertificatePassword);
        }
        clientCertCredBuilder.tenantId(authConfig.tenantId);
        return clientCertCredBuilder.build();
    }

    @SneakyThrows
    private static String createPemCertFile(String certStringData) {
        File file = Files.createTempFile(CERTIFICATE_FILE_PREFIX, PEM_CERTIFICATE_FILE_EXT).toFile();
        return writeToTempFile(file, certStringData.getBytes(StandardCharsets.UTF_8));
    }
    @SneakyThrows
    private static String createPfxCertFile(String base64CertData) {
        File file = Files.createTempFile(CERTIFICATE_FILE_PREFIX, PFX_CERTIFICATE_FILE_EXT).toFile();
        byte[] decodedVal = Base64.decodeBase64(base64CertData);
        return writeToTempFile(file, decodedVal);
    }

    /**
     * Writes array of bytes to the temporary file which is deleted
     * after application is terminated.
     * @param outputFile
     * @param byteArrInputData
     * @return path to the newly created temporary file
     */
    @SneakyThrows
    private static String writeToTempFile(File outputFile, byte[] byteArrInputData) {
        outputFile.deleteOnExit();
        FileOutputStream fos = new FileOutputStream(outputFile);
        fos.write(byteArrInputData);
        fos.close();
        return outputFile.getAbsolutePath();
    }
}
