package com.nexla.connector.config.jdbc;

import lombok.SneakyThrows;

import java.sql.Driver;
import java.sql.DriverManager;
import java.util.Map;

import static com.google.common.base.MoreObjects.firstNonNull;
import static com.nexla.common.StreamUtils.toLinkedHashMap;
import static one.util.streamex.StreamEx.of;

public final class CDCUtils {

    private CDCUtils() {}

    @SneakyThrows
    public static HostPortDb getHostPortDb(JdbcAuthConfig authConfig) {
        final String host;
        final String port;
        final String dbName;

        if (authConfig.host == null) {
            Driver driver = DriverManager.getDriver(authConfig.url);
            Map<String, String> paramsFromUrl =
                    toLinkedHashMap(
                            of(driver.getPropertyInfo(authConfig.url, null))
                                    .mapToEntry(info -> info.name.toLowerCase(), info -> info.value));

            host = firstNonNull(paramsFromUrl.get("host"), paramsFromUrl.get("pghost"));
            port = firstNonNull(paramsFromUrl.get("port"), paramsFromUrl.get("pgport"));
            dbName = firstNonNull(paramsFromUrl.get("dbname"), paramsFromUrl.get("pgdbname"));
        } else {
            host = authConfig.host;
            port = authConfig.port + "";
            dbName = authConfig.databaseName;
        }

        return new HostPortDb(host, port, dbName);
    }
}


