package com.nexla.connector.config;

import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;

public class SinkInstanceConfig extends SinkConnectorConfig {

    public static final String DEDICATED_NODE = "dedicated.node";
    public static final String RECEIVE_TASKS_CRON = "receice.tasks.cron";
    public static final String APP_VERSION = "app.version";
    public static final String NODE_TAGS = "node.tags";
    public static final String TASK_TYPE = "task.type";

    public final Boolean dedicatedNode;
    public final String receiveTasksCron;
    public final Optional<String> appVersion;
    public final Optional<String> nodeTags;
    public final String taskType;

    public SinkInstanceConfig(NexlaConfigDef definition, Map<String, String> originals) {
        super(definition, originals);
        this.dedicatedNode = getBoolean(DEDICATED_NODE);
        this.receiveTasksCron = getString(RECEIVE_TASKS_CRON);
        this.appVersion = opt(getString(APP_VERSION));
        this.nodeTags = opt(getString(NODE_TAGS));
        this.taskType = getString(TASK_TYPE);
    }

    public static NexlaConfigDef configDef() {

        return SinkConnectorConfig.sinkConfigDef()
                .withKey(nexlaKey(DEDICATED_NODE, ConfigDef.Type.BOOLEAN, false))
                .withKey(nexlaKey(RECEIVE_TASKS_CRON, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(APP_VERSION, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(NODE_TAGS, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(TASK_TYPE, ConfigDef.Type.STRING, null))

                ;
    }

}
