package com.nexla.connector.config.file;

import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.time.VarUtils;
import com.nexla.connector.config.rest.RecourceAccessCallback;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.Optional;

import static com.nexla.common.StreamUtils.lhm;

public class JpmcFileResourceAccessCallback implements RecourceAccessCallback  {

	public static final String VAR_FILE_PATH = "filePath";
	private final RestTemplate restTemplate;
	private final VarUtils.VarInfo takeResourceUrl;
	private final Optional<VarUtils.VarInfo> takeResourceBody;
	private final VarUtils.VarInfo releaseResourceUrl;
	private final Optional<VarUtils.VarInfo> releaseResourceBody;

	public JpmcFileResourceAccessCallback(
		VarUtils.VarInfo takeResourceUrl,
		Optional<VarUtils.VarInfo> takeResourceBody,
		VarUtils.VarInfo releaseResourceUrl,
		Optional<VarUtils.VarInfo> releaseResourceBody
	) {
		this.restTemplate = new RestTemplateBuilder().build();
		this.takeResourceUrl = takeResourceUrl;
		this.takeResourceBody = takeResourceBody;
		this.releaseResourceUrl = releaseResourceUrl;
		this.releaseResourceBody = releaseResourceBody;
	}

	@Override
	public void resourceTaken(String file) {
		LinkedHashMap<String, String> replacementMap = lhm(VAR_FILE_PATH, file);
		String url = VarUtils.replaceVars(this.takeResourceUrl, replacementMap);
		String body = takeResourceBody
			.map(b -> VarUtils.replaceVars(b, replacementMap))
			.orElse(null);
		HttpEntity<String> entity = new HttpEntity<>(body);
		this.restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
	}

	@Override
	public void resourceReleased(String file) {
		LinkedHashMap<String, String> replacementMap = lhm(VAR_FILE_PATH, file);
		String url = VarUtils.replaceVars(this.releaseResourceUrl, replacementMap);
		String body = releaseResourceBody
			.map(b -> VarUtils.replaceVars(b, replacementMap))
			.orElse(null);
		HttpEntity<String> entity = new HttpEntity<>(body);
		this.restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
	}

}
