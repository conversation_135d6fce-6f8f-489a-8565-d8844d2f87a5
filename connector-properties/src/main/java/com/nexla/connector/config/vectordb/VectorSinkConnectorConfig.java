package com.nexla.connector.config.vectordb;

import com.nexla.common.ConnectionType;
import com.nexla.common.StreamUtils;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.vectordb.mapping.VectorMappingConfig;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.connector.properties.VectorDbConfigAccessor.*;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

@Getter
public class VectorSinkConnectorConfig extends SinkConnectorConfig implements WithVectorAuthConfig, WithVectorSinkConfig {

	public static final String SINK_GROUP = "";
	public final ConnectionType connectionType;
	public final BaseAuthConfig authConfig;
	public final Optional<String> collection;
	public final String database;
	public final VectorSinkConfig providerSinkConfig;
	public final VectorMappingConfig vectorMappingConfig;

	public VectorSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> parsedConfig = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		String connectionTypeStr = Optional.ofNullable(getString(CREDENTIALS_TYPE)).orElse(parsedConfig.get(CREDENTIALS_TYPE));

		this.vectorMappingConfig = opt(getString(VECTOR_MAPPING)).map(VectorSinkConnectorConfig::newVectorMappingConfig).orElse(new VectorMappingConfig());
		this.connectionType = ConnectionType.fromString(connectionTypeStr.toUpperCase());
		this.providerSinkConfig = newVectorSinkConfig(connectionType, originals);

		this.authConfig = newAuthConfig(connectionType, parsedConfig, credsId);
		this.collection = opt(getString(COLLECTION));
		this.database = getString(DATABASE);
	}

	public static NexlaConfigDef configDef() {
		return new NexlaConfigDef(sinkConfigDef())
			.withKey(nexlaKey(CREDENTIALS_TYPE, STRING, null))
			.withKey(nexlaKey(COLLECTION, STRING, null))
			.withKey(nexlaKey(DATABASE, STRING, null))
			.withKey(nexlaKey(VECTOR_MAPPING, STRING, null))
			;
	}

	public static VectorMappingConfig newVectorMappingConfig(String str) {
		return StreamUtils.jsonUtil().stringToType(str, VectorMappingConfig.class);
	}
}
