package com.nexla.connector.config.file;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RecourceAccessCallback;
import java.util.Optional;

public interface FileConnectorAuth {

	BaseAuthConfig getAuthConfig();

	String getRegion();

	String getPath();

	ConnectionType getConnectionType();

	Optional<RecourceAccessCallback> recourceAccessCallback();

	BaseConnectorConfig getConnectorConfig();

}
