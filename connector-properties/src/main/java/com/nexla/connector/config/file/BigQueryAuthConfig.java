package com.nexla.connector.config.file;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class BigQueryAuthConfig extends GCPAuthConfig {

	public BigQueryAuthConfig(Map<String, String> parsedConfig, Integer credsId) {
		super(configDef(), parsedConfig, credsId);
	}

	public static NexlaConfigDef configDef() {
		return GCPAuthConfig.configDef();
	}

}
