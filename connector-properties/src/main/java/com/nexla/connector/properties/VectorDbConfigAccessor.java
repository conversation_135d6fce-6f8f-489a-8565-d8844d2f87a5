package com.nexla.connector.properties;

public class VectorDbConfigAccessor {
	public static final String BATCH_SIZE = "batch.size";

	public static final String TOPK = "topK";
	public static final String SEARCH_BY = "search_by";
	public static final String VECTOR_ID = "vector_id";
	public static final String DENSE_VECTOR = "dense_vector";
	public static final String SPARSE_VECTOR_INDICES = "sparse_vector_indices";
	public static final String SPARSE_VECTOR_VALUES = "sparse_vector_values";

	public static final String QUERY_TYPE = "query_type";
	public static final String MODE = "mode";
	public static final String COLLECTION = "collection";
	public static final String DATABASE = "database";
	public static final String FETCH_VECTORS = "fetch_vectors";
	public static final String FETCH_IDS = "fetch_ids";
	public static final String SIMILARITY_SEARCH = "similarity_search";

	public static final String VECTOR_MAPPING = "vector_mapping";
}
