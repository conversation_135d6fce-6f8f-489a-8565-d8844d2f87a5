package com.nexla.client;

import com.nexla.common.StreamUtils;
import com.nexla.connector.config.rest.BodyMakerEvalDto;
import com.nexla.connector.config.rest.HmacSignerEvalDto;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.HttpCallParametersDto;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

import static com.nexla.common.AppUtils.authorizationHeader;
import static org.springframework.http.HttpMethod.POST;

public class ScriptEvalClient {

	private final HttpHeaders authorizationHeader;
	private final String url;
	private final RestTemplate restTemplate;

	public ScriptEvalClient(
		String url,
		String nexlaUsername,
		String nexlaPassword,
		RestTemplate restTemplate
	) {
		this.url = url;
		this.authorizationHeader = authorizationHeader(nexlaUsername, nexlaPassword);
		this.restTemplate = restTemplate;
	}

	@SneakyThrows
	public HttpCallParameters hmacSigner(HmacSignerEvalDto bodyDto) {
		String body = StreamUtils.OBJECT_MAPPER.writeValueAsString(bodyDto);
		HttpEntity request = new HttpEntity<>(body, authorizationHeader);
		String dtoJson = restTemplate.exchange(url + "/hmacSigner", POST, request, String.class).getBody();
		HttpCallParametersDto dto = StreamUtils.OBJECT_MAPPER.readValue(dtoJson, HttpCallParametersDto.class);
		return new HttpCallParameters(dto.url, dto.skipUrlEncoding, dto.method, dto.body, Optional.empty(), dto.restHeaders);
	}

	@SneakyThrows
	public String bodyMaker64(BodyMakerEvalDto bodyDto) {
		String bodyFn64 = new String(Base64.encodeBase64(bodyDto.bodyFn.getBytes()));
		BodyMakerEvalDto base64 = new BodyMakerEvalDto(bodyFn64, bodyDto.messages);
		String body = StreamUtils.OBJECT_MAPPER.writeValueAsString(base64);
		try {
			HttpEntity request = new HttpEntity<>(body, authorizationHeader);
			return restTemplate.exchange(url + "/bodyMaker64", POST, request, String.class).getBody();
		} catch (Exception e) {
			throw new ProbeEvalException(body, e);
		}
	}

}
