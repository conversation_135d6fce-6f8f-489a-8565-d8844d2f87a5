package com.nexla.admin.config;

import com.nexla.connector.config.NexlaConfigDef;
import org.apache.kafka.common.config.ConfigDef;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.junit.Assert.*;

public class ConfigUtilsTest {

	@Test
	public void shouldReturnValueWhenSet() {
		String propertyName = "some.property";
		Map<String, Object> config = Map.of("some.property", true);
		NexlaConfigDef configDef = new NexlaConfigDef().withKey(nexlaKey(propertyName, ConfigDef.Type.BOOLEAN, false));
		assertTrue(ConfigUtils.getBooleanFromConfig(propertyName, config, configDef).get());
	}

	@Test
	public void shouldReturnValueWhenSetString() {
		String propertyName = "some.property";
		Map<String, Object> config = Map.of("some.property", "true");
		NexlaConfigDef configDef = new NexlaConfigDef().withKey(nexlaKey(propertyName, ConfigDef.Type.BOOLEAN, false));
		assertTrue(ConfigUtils.getBooleanFromConfig(propertyName, config, configDef).get());
	}

	@Test
	public void shouldReturnConfigDefValueWhenNotSet() {
		String propertyName = "some.property";
		Map<String, Object> config = Map.of();
		NexlaConfigDef configDef = new NexlaConfigDef().withKey(nexlaKey(propertyName, ConfigDef.Type.BOOLEAN, true));
		assertTrue(ConfigUtils.getBooleanFromConfig(propertyName, config, configDef).get());
	}

	@Test
	public void shouldReturnConfigDefValueWhenSetNull() {
		String propertyName = "some.property";
		Map<String, Object> config = new HashMap<>();
		config.put(propertyName, null);
		NexlaConfigDef configDef = new NexlaConfigDef().withKey(nexlaKey(propertyName, ConfigDef.Type.BOOLEAN, true));
		assertTrue(ConfigUtils.getBooleanFromConfig(propertyName, config, configDef).get());
	}

	@Test
	public void shouldReturnConfigDefValueWhenSetEmpty() {
		String propertyName = "some.property";
		Map<String, Object> config = Map.of("some.property", "");
		NexlaConfigDef configDef = new NexlaConfigDef().withKey(nexlaKey(propertyName, ConfigDef.Type.BOOLEAN, true));
		assertTrue(ConfigUtils.getBooleanFromConfig(propertyName, config, configDef).get());
	}

	@Test
	public void shouldReturnEmptyWhenNotSet() {
		String propertyName = "some.property";
		Map<String, Object> config = Map.of();
		NexlaConfigDef configDef = new NexlaConfigDef().withKey(nexlaKey(propertyName, ConfigDef.Type.BOOLEAN, null));
		assertEquals(Optional.empty(), ConfigUtils.getBooleanFromConfig(propertyName, config, configDef));
	}
}