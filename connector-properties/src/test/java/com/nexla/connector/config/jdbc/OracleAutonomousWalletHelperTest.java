package com.nexla.connector.config.jdbc;

import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.Assert.assertTrue;

public class OracleAutonomousWalletHelperTest {

    private static final Logger logger = LoggerFactory.getLogger(OracleAutonomousWalletHelperTest.class);
    private String walletBase64 = "";

    @Before
    public void initFileContents() {
        String resourcePath = "/walletstub.zip.b64";

        try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
            if (inputStream != null) {
                byte[] buffer = new byte[inputStream.available()];
                int readBytes = inputStream.read(buffer);
                logger.info("read {} bytes from file", readBytes);
                walletBase64 = new String(buffer);
            } else {
                logger.error("Resource not found: " + resourcePath);
            }
        } catch (IOException e) {
            logger.error("file read error", e);
        }
    }

    @Test
    public void testGetDbNameExistingBehavior(){
        OracleAutonomousWalletHelper helper = new OracleAutonomousWalletHelper();
        String results = helper.processWalletAndGetUrl(walletBase64, null, "unittest", null);
        assertTrue(results.startsWith("*****************************************="));
    }

    @Test
    public void testGetDbNameWithDefinedLowServiceName(){
        OracleAutonomousWalletHelper helper = new OracleAutonomousWalletHelper();
        String results = helper.processWalletAndGetUrl(walletBase64, null, "unittest", "_low");
        assertTrue(results.startsWith("****************************************="));
    }
}
