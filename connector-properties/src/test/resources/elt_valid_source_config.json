{"unit.test": "true", "source_id": "1", "pipeline.type": "elt", "start.cron": "0 15 20 8 1 ? 2025", "common": {"sequence": "parallel", "max_parallel_feeds": 100, "order_first": [], "order_last": [], "feed_params": {}}, "feeds": {"base_full": {"type": "base", "spec": {"rest.iterations": [{"key": "step1", "method": "GET", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+=+'{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "static.url", "response.data.path": "$"}, {"key": "step2", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+='{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "code.container", "code.container.id": 13602}, {"key": "step3", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "method": "GET", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q={step2.query_start}+FROM+{step2.query_obj}", "iteration.type": "paging.next.url", "response.data.path": "$.records[*]", "response.next.url.data.path": "$.nextRecordsUrl"}]}}, "base_incremental": {"type": "base", "spec": {"rest.iterations": [{"key": "step1", "method": "GET", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+=+'{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "static.url", "response.data.path": "$"}, {"key": "step2", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q=SELECT+QualifiedApiName,+EntityDefinition.QualifiedApiName,+DataType,+Label+FROM+EntityParticle+WHERE+EntityDefinition.QualifiedApiName+='{feed_spec.object_name}'+AND+EntityDefinition.IsDeprecatedAndHidden+=+false", "iteration.type": "code.container", "code.container.id": 13602}, {"key": "step3", "date.format": "yyyy-MM-dd'T'HH:mm:ss'Z'", "date.time.unit": "dd", "method": "GET", "url.template": "{data_credential[\"oauth2.instance_url\"]}/services/data/v52.0/query/?q={step2.query_start}+FROM+{step2.query_obj}+WHERE+{step2.query_incremental_col}<{now}+and+{step2.query_incremental_col}>{now-1}", "iteration.type": "paging.next.url", "response.data.path": "$.records[*]", "response.next.url.data.path": "$.nextRecordsUrl"}]}}, "AIApplication": {"type": "data", "spec": {"ref_spec": "base_full", "primary.keys": ["Id"], "params": {"feed_spec.object_name": "AIApplication", "ui.feed_display_name": "AI Application"}}}, "AIApplicationConfig": {"type": "data", "spec": {"ref_spec": "base_full", "primary.keys": ["Id"], "metadata": {"overridden_db_type_mappings": {"tables": {"person": {"NULL": "VARCHAR(5000)"}}, "columns": {"id": "NUMBER(12,10)", "name": "BINARY"}, "general": {"NULL": "VARCHAR(3000)", "INTEGER": "INT64", "FLOAT": "FLOAT64", "JSON": "JSON", "BOOLEAN": "BOOLEAN", "STRING": "STRING"}}}, "params": {"feed_spec.object_name": "AIApplicationConfig", "ui.feed_display_name": "AI Application config"}}}, "AcceptedEventRelation": {"type": "data", "spec": {"primary.keys": ["Id"], "metadata": {"overridden_mappings": {"person": {"partial_enabled": true, "name": "person_overridden_mapping_partial", "columns": {"id": {"name": "ID_Person", "type": "VARCHAR(3000)"}, "name": {"name": "Name_Person", "type": "NUMBER"}}}}}, "params": {"feed_spec.object_name": "AcceptedEventRelation", "ui.feed_display_name": "Accepted Event Relation"}}}}}