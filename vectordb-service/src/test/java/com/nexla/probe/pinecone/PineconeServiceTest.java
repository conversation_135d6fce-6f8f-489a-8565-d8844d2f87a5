package com.nexla.probe.pinecone;

import com.google.protobuf.Struct;
import com.nexla.common.NexlaMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.vectordb.VectorSinkConnectorConfig;
import com.nexla.connector.config.vectordb.VectorSourceConnectorConfig;
import com.nexla.test.UnitTests;
import io.pinecone.clients.Index;
import io.pinecone.clients.Pinecone;
import io.pinecone.exceptions.PineconeAuthorizationException;
import io.pinecone.exceptions.PineconeException;
import io.pinecone.proto.*;
import io.pinecone.proto.Vector;
import io.pinecone.unsigned_indices_model.QueryResponseWithUnsignedIndices;
import io.pinecone.unsigned_indices_model.VectorWithUnsignedIndices;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.openapitools.control.client.model.IndexList;
import org.openapitools.control.client.model.IndexModel;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.vectordb.pinecone.PineconeAuthConfig.API_KEY;
import static com.nexla.connector.config.vectordb.pinecone.PineconeSinkConfig.PINECONE_UPSERT_PARALLELISM;
import static com.nexla.connector.config.vectordb.pinecone.PineconeSourceConfig.PINECONE_INCLUDE_METADATA;
import static com.nexla.connector.config.vectordb.pinecone.PineconeSourceConfig.PINECONE_INCLUDE_VALUES;
import static com.nexla.connector.properties.VectorDbConfigAccessor.*;
import static com.nexla.probe.pinecone.PineconeHelper.convertMap;
import static org.mockito.Mockito.*;

import scala.collection.JavaConverters$;
import scala.collection.Seq;

@Category(UnitTests.class)
public class PineconeServiceTest {

    private final Integer TEST_CRED_ID = 1;
    private final String TEST_API_KEY = "123";
    private final String TEST_SINK_ID = "2";

    @Mock
    private Index testIndex;

    @Mock
    private PineconeClientProvider pineconeClientProvider;

    @Mock
    private Pinecone pineconeClient;

    @Captor
    ArgumentCaptor<List<VectorWithUnsignedIndices>> upsertedVectors;

    private PineconeService pineconeService;

    private Struct noMetadata = null;

    private Struct testSimpleMetadata = toMetadata(new HashMap<>(){{
        put("tag", "value");
    }});

    private Struct testComplexMetadata = toMetadata(new HashMap<>(){{
        put("tag1", 1);
        put("tag2", "value");
        put("tag3", true);
        put("tag4", List.of("a", "b"));
    }});

    @SneakyThrows
    @Before
    public void onBefore() {
        MockitoAnnotations.initMocks(this);

        PineconeService.indexCache().setTestIndex(testIndex);
        pineconeService = spy(new PineconeService());
        pineconeService.setPinconeClientProvider(pineconeClientProvider);

        when(pineconeClientProvider.createPineconeClient(TEST_API_KEY)).thenReturn(pineconeClient);
    }

    @After
    public void tearDown() {
        reset(testIndex);
        reset(pineconeClientProvider);
        reset(pineconeClient);
    }

    @Test
    public void testAuthenticate() {
        setupListIndexes("testIndex");

        var response = pineconeService.authenticate(defaultSourceConfig().authConfig);
        Assert.assertEquals(SUCCESS, response);
    }

    @Test
    public void testAuthenticateFailure() {
        when(pineconeClient.listIndexes()).thenThrow(new PineconeAuthorizationException("Invalid API Key"));

        var response = pineconeService.authenticate(defaultSourceConfig().authConfig);
        Assert.assertEquals(false, response.success);
        Assert.assertEquals(Optional.of("Invalid API Key"), response.message);
    }

    @Test
    public void testListDatabases() {
        setupListIndexes("testIndex1", "testIndex2");

        var response = pineconeService.listDatabases(defaultSourceConfig()).sorted().collect(Collectors.toList());
        Assert.assertEquals(2, response.size());
        Assert.assertEquals("testIndex1", response.get(0));
        Assert.assertEquals("testIndex2", response.get(1));
    }

    @Test
    public void testListCollections() {
        setupListIndexes("testIndex");
        setupNamespacesForTestIndex("namespace1", "namespace2");

        var response = pineconeService.listCollections("testIndex", defaultSourceConfig()).sorted().collect(Collectors.toList());
        Assert.assertEquals(2, response.size());
        Assert.assertEquals("namespace1", response.get(0));
        Assert.assertEquals("namespace2", response.get(1));
    }

    @Test
    public void testReadSampleWithFetchIDs() {
        setupIndexIDsResponse("namespace1", 10, "1", "2");

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, FETCH_IDS);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\"}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\"}", response.getData().get(1).data);
    }

    @Test
    public void testFetchDenseVectorsNoMetadata() {
        setupIndexIDsResponse("namespace1", 10, "1", "2");
        setupFetchVectorResponse("namespace1",
            makeDenseVector("1", noMetadata,0.1f, 0.2f),
            makeDenseVector("2", noMetadata, 0.3f, 0.4f)
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, FETCH_VECTORS);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"dense_vector\":[0.1,0.2]}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"dense_vector\":[0.3,0.4]}", response.getData().get(1).data);
    }

    @Test
    public void testFetchDenseVectorsWithMetadata() {
        setupIndexIDsResponse("namespace1", 10, "1", "2");
        setupFetchVectorResponse("namespace1",
            makeDenseVector("1", testSimpleMetadata,0.1f, 0.2f),
            makeDenseVector("2", testComplexMetadata, 0.3f, 0.4f)
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, FETCH_VECTORS);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"dense_vector\":[0.1,0.2],\"metadata\":{\"tag\":\"value\"}}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"dense_vector\":[0.3,0.4],\"metadata\":{\"tag1\":1.0,\"tag2\":\"value\",\"tag3\":true,\"tag4\":[\"a\",\"b\"]}}", response.getData().get(1).data);
    }

    @Test
    public void testFetchSparseVectorsWithMetadata() {
        setupIndexIDsResponse("namespace1", 10, "1", "2");
        setupFetchVectorResponse("namespace1",
            makeSparseVector("1", testSimpleMetadata, List.of(0.1f, 0.2f), List.of(2, 10)),
            makeSparseVector("2", testComplexMetadata, List.of(0.3f, 0.4f), List.of(3, 8))
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, FETCH_VECTORS);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"sparse_values\":[0.1,0.2],\"sparse_indices\":[2,10],\"metadata\":{\"tag\":\"value\"}}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"sparse_values\":[0.3,0.4],\"sparse_indices\":[3,8],\"metadata\":{\"tag1\":1.0,\"tag2\":\"value\",\"tag3\":true,\"tag4\":[\"a\",\"b\"]}}", response.getData().get(1).data);
    }

    @Test
    public void testSimilaritySearchDenseVectorsNoMetadataNoValues() {
        setupSearchDenseVectorResponse("namespace1", 20, null, null, false, false,
            makeScoredDenseVector("1", 0.01f, noMetadata),
            makeScoredDenseVector("2", 0.02f, noMetadata)
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
            put(DENSE_VECTOR, "0.1,0.2");
            put(PINECONE_INCLUDE_VALUES, "false");
            put(PINECONE_INCLUDE_METADATA, "false");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"score\":0.01}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"score\":0.02}", response.getData().get(1).data);
    }

    @Test
    public void testSimilaritySearchDenseVectorsWithMetadataAndValues() {
        setupSearchDenseVectorResponse("namespace1", 10, null, null, true, true,
            makeScoredDenseVector("1", 0.01f, testSimpleMetadata, 0.1f, 0.2f),
            makeScoredDenseVector("2", 0.02f, testSimpleMetadata, 0.3f, 0.4f)
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
            put(DENSE_VECTOR, "0.1,0.2");
            put(TOPK, "10");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"dense_vector\":[0.1,0.2],\"metadata\":{\"tag\":\"value\"},\"score\":0.01}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"dense_vector\":[0.3,0.4],\"metadata\":{\"tag\":\"value\"},\"score\":0.02}", response.getData().get(1).data);
    }

    @Test
    public void testSimilaritySearchByDenseVector() {
        setupSearchByDenseVector("namespace1", List.of(0.1f, 0.2f),
            makeScoredDenseVector("1", 0.01f, testSimpleMetadata, 0.1f, 0.2f),
            makeScoredDenseVector("2", 0.02f, testSimpleMetadata, 0.3f, 0.4f)
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
            put(DENSE_VECTOR, "0.1,0.2");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"dense_vector\":[0.1,0.2],\"metadata\":{\"tag\":\"value\"},\"score\":0.01}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"dense_vector\":[0.3,0.4],\"metadata\":{\"tag\":\"value\"},\"score\":0.02}", response.getData().get(1).data);
    }

    @Test
    public void testSimilaritySearchBySparseVector() {
        setupSearchBySparseVector("namespace1", List.of(0.1f, 0.2f), List.of(2L, 5L),
            makeScoredSparseVector("1", 0.01f, noMetadata, List.of(0.1f, 0.2f), List.of(1, 3)),
            makeScoredSparseVector("2", 0.02f, testSimpleMetadata, List.of(0.3f, 0.4f), List.of(2, 5))
        );

        VectorSourceConnectorConfig vectorConfig = extendSourceConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
            put(DENSE_VECTOR, "0.1,0.2");
            put(SPARSE_VECTOR_VALUES, "0.1,0.2");
            put(SPARSE_VECTOR_INDICES, "2,5");
        }});

        var response = pineconeService.readSample(vectorConfig, false);

        Assert.assertEquals(2, response.getData().size());
        Assert.assertEquals("{\"id\":\"1\",\"sparse_values\":[0.1,0.2],\"sparse_indices\":[1,3],\"score\":0.01}", response.getData().get(0).data);
        Assert.assertEquals("{\"id\":\"2\",\"sparse_values\":[0.3,0.4],\"sparse_indices\":[2,5],\"metadata\":{\"tag\":\"value\"},\"score\":0.02}", response.getData().get(1).data);
    }

    @Test
    public void testBulkWriteDenseVectorWithDefaultMapping() {
        setupUpsertResponse("namespace1");

        var rawMessages = IntStream.range(1, 3).mapToObj(id ->
            new NexlaMessage(new LinkedHashMap<>(){{
                put("id", "id" + id);
                put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat(), RandomUtils.nextFloat()));
                put("metadata", "{\"tag\": \"value" + id + "\"}");
            }})
        );

        var messageSeq = toScalaSeq(rawMessages);

        VectorSinkConnectorConfig vectorConfig = extendSinkConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var recordMetric = new RecordMetric();
        var response = pineconeService.bulkWrite(vectorConfig, messageSeq, recordMetric);

        Assert.assertEquals(true, response.isSuccess());
        Assert.assertEquals(0, recordMetric.errorRecords.get());
        Assert.assertEquals(2, recordMetric.sentRecordsTotal.get());

        var vectors = upsertedVectors.getValue();
        Assert.assertEquals(2, vectors.size());
        Assert.assertEquals("id1", vectors.get(0).getId());
        Assert.assertEquals(3, vectors.get(0).getValuesList().size());
        Assert.assertEquals("fields {\n" +
                "  key: \"tag\"\n" +
                "  value {\n" +
                "    string_value: \"value1\"\n" +
                "  }\n" +
                "}\n", vectors.get(0).getMetadata().toString());
        Assert.assertEquals("id2", vectors.get(1).getId());
        Assert.assertEquals(3, vectors.get(1).getValuesList().size());
        Assert.assertEquals("fields {\n" +
                "  key: \"tag\"\n" +
                "  value {\n" +
                "    string_value: \"value2\"\n" +
                "  }\n" +
                "}\n", vectors.get(1).getMetadata().toString());
    }

    @Test
    public void testBulkWriteDenseVectorFailure() {
        setupUpsertFailure("namespace1");

        var rawMessages = IntStream.range(1, 3).mapToObj(id ->
            new NexlaMessage(new LinkedHashMap<>(){{
                put("id", "id" + id);
                put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat(), RandomUtils.nextFloat()));
                put("metadata", "{\"tag\": \"value" + id + "\"}");
            }})
        );

        var messageSeq = toScalaSeq(rawMessages);

        VectorSinkConnectorConfig vectorConfig = extendSinkConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var recordMetric = new RecordMetric();
        var response = pineconeService.bulkWrite(vectorConfig, messageSeq, recordMetric);

        Assert.assertEquals(false, response.isSuccess());
        Assert.assertEquals(2, recordMetric.errorRecords.get());
        Assert.assertEquals(2, recordMetric.quarantineMessages.size());
        Assert.assertEquals(0, recordMetric.sentRecordsTotal.get());
    }

    @Test
    public void testBulkWriteBrokenDataNoID() {
        setupUpsertFailure("namespace1");

        var rawMessages = IntStream.range(1, 3).mapToObj(id ->
            new NexlaMessage(new LinkedHashMap<>(){{
                put("some", "data" + id);
            }})
        );

        var messageSeq = toScalaSeq(rawMessages);

        VectorSinkConnectorConfig vectorConfig = extendSinkConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var recordMetric = new RecordMetric();
        var response = pineconeService.bulkWrite(vectorConfig, messageSeq, recordMetric);

        Assert.assertEquals(true, response.isSuccess());
        Assert.assertEquals(2, recordMetric.errorRecords.get());
        Assert.assertEquals(2, recordMetric.quarantineMessages.size());
        Assert.assertEquals("Vector id field 'id' is required", recordMetric.quarantineMessages.get(0).getError().getMessage());
        Assert.assertEquals(0, recordMetric.sentRecordsTotal.get());
    }

    @Test
    public void testBulkWriteBrokenDataNoVectors() {
        setupUpsertFailure("namespace1");

        var rawMessages = IntStream.range(1, 3).mapToObj(id ->
                new NexlaMessage(new LinkedHashMap<>(){{
                    put("id", "data" + id);
                }})
        );

        var messageSeq = toScalaSeq(rawMessages);

        VectorSinkConnectorConfig vectorConfig = extendSinkConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var recordMetric = new RecordMetric();
        var response = pineconeService.bulkWrite(vectorConfig, messageSeq, recordMetric);

        Assert.assertEquals(true, response.isSuccess());
        Assert.assertEquals(2, recordMetric.errorRecords.get());
        Assert.assertEquals(2, recordMetric.quarantineMessages.size());
        Assert.assertEquals("Vector field 'dense_vector' is required", recordMetric.quarantineMessages.get(0).getError().getMessage());
        Assert.assertEquals(0, recordMetric.sentRecordsTotal.get());
    }

    @Test
    public void testBulkWriteBrokenDataIncorrectVectorType() {
        setupUpsertFailure("namespace1");

        var rawMessages = IntStream.range(1, 3).mapToObj(id ->
            new NexlaMessage(new LinkedHashMap<>(){{
                put("id", "data" + id);
                put("dense_vector", "not a list");
            }})
        );

        var messageSeq = toScalaSeq(rawMessages);

        VectorSinkConnectorConfig vectorConfig = extendSinkConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
        }});

        var recordMetric = new RecordMetric();
        var response = pineconeService.bulkWrite(vectorConfig, messageSeq, recordMetric);

        Assert.assertEquals(true, response.isSuccess());
        Assert.assertEquals(2, recordMetric.errorRecords.get());
        Assert.assertEquals(2, recordMetric.quarantineMessages.size());
        Assert.assertEquals("Dense vector field 'dense_vector' must be an array of numbers", recordMetric.quarantineMessages.get(0).getError().getMessage());
        Assert.assertEquals(0, recordMetric.sentRecordsTotal.get());
    }

    @Test
    public void testBulkWriteMultiBatchParallel() {
        setupUpsertResponse("namespace1");

        var rawMessages = IntStream.range(1, 3001).mapToObj(id ->
            new NexlaMessage(new LinkedHashMap<>(){{
                put("id", "id" + id);
                put("dense_vector", List.of(RandomUtils.nextFloat(), RandomUtils.nextFloat(), RandomUtils.nextFloat()));
                put("metadata", "{\"tag\": \"value" + id + "\"}");
            }})
        );

        var messageSeq = toScalaSeq(rawMessages);

        VectorSinkConnectorConfig vectorConfig = extendSinkConfig(new HashMap<>(){{
            put(QUERY_TYPE, SIMILARITY_SEARCH);
            put(DATABASE, "testIndex");
            put(COLLECTION, "namespace1");
            put(PINECONE_UPSERT_PARALLELISM, "5");
        }});

        var recordMetric = new RecordMetric();
        var response = pineconeService.bulkWrite(vectorConfig, messageSeq, recordMetric);

        verify(testIndex, times(3)).upsert(any(), eq("namespace1"));

        Assert.assertEquals(true, response.isSuccess());
        Assert.assertEquals(0, recordMetric.errorRecords.get());
        Assert.assertEquals(3000, recordMetric.sentRecordsTotal.get());
    }

    private Seq<NexlaMessageContext> toScalaSeq(Stream<NexlaMessage> rawMessages) {
        var topic = new TopicPartition("test", 0);
        var messages = rawMessages.map(m -> new NexlaMessageContext(m, m, topic, 0L)).collect(Collectors.toList());
        return JavaConverters$.MODULE$.asScalaBuffer(messages).toSeq();
    }

    private VectorSourceConnectorConfig extendSourceConfig(Map<String, String> settings) {
        Map<String, Object> totalSettings = new HashMap<>();
        totalSettings.putAll(defaultSourceConfig().originalsStrings());
        totalSettings.putAll(settings);
        return new VectorSourceConnectorConfig(totalSettings);
    }

    private VectorSinkConnectorConfig extendSinkConfig(Map<String, String> settings) {
        Map<String, String> totalSettings = new HashMap<>();
        totalSettings.putAll(defaultSinkConfig().originalsStrings());
        totalSettings.putAll(settings);
        return new VectorSinkConnectorConfig(totalSettings);
    }

    private VectorSourceConnectorConfig defaultSourceConfig() {
        Map<String, Object> settings = new HashMap<>();
        settings.put(UNIT_TEST, "true");
        settings.put(API_KEY, TEST_API_KEY);
        settings.put(CREDENTIALS_TYPE, "pinecone");
        settings.put(SOURCE_ID, "1");
        settings.put(CREDS_ID, TEST_CRED_ID.toString());

        return new VectorSourceConnectorConfig(settings);
    }

    private VectorSinkConnectorConfig defaultSinkConfig() {
        Map<String, String> settings = new HashMap<>();
        settings.put(UNIT_TEST, "true");
        settings.put(API_KEY, TEST_API_KEY);
        settings.put(CREDENTIALS_TYPE, "pinecone");
        settings.put(CREDS_ID, TEST_CRED_ID.toString());
        settings.put(SINK_ID, TEST_SINK_ID);

        return new VectorSinkConnectorConfig(settings);
    }

    private Vector makeDenseVector(String id, Struct metadata, Float ...values) {
        Vector.Builder builder = Vector.newBuilder().setId(id).addAllValues(List.of(values));
        if (metadata != null) {
            builder.setMetadata(metadata);
        }
        return builder.build();
    }

    private Vector makeSparseVector(String id, Struct metadata, List<Float> values, List<Integer> indices) {
        Vector.Builder builder = Vector.newBuilder().setId(id);
        builder.setSparseValues(SparseValues.newBuilder().addAllValues(values).addAllIndices(indices).build());
        if (metadata != null) {
            builder.setMetadata(metadata);
        }
        return builder.build();
    }

    private ScoredVector makeScoredDenseVector(String id, float score, Struct metadata, Float ...values) {
        ScoredVector.Builder scoredVectorBuilder = ScoredVector.newBuilder().setId(id).setScore(score);
        if (metadata != null) {
            scoredVectorBuilder.setMetadata(metadata);
        }
        if (values.length > 0) {
            scoredVectorBuilder.addAllValues(List.of(values));
        }
        return scoredVectorBuilder.build();
    }

    private ScoredVector makeScoredSparseVector(String id, float score, Struct metadata, List<Float> values, List<Integer> indices) {
        ScoredVector.Builder scoredVectorBuilder = ScoredVector.newBuilder().setId(id).setScore(score);
        if (metadata != null) {
            scoredVectorBuilder.setMetadata(metadata);
        }
        scoredVectorBuilder.setSparseValues(SparseValues.newBuilder().addAllValues(values).addAllIndices(indices).build());
        return scoredVectorBuilder.build();
    }

    private Struct toMetadata(Map<String, Object> metadata) {
        return convertMap(metadata);
    }

    private void setupIndexIDsResponse(String namespace, int limit, String ...ids) {
        var items = List.of(ids).stream().map(id -> {
            ListItem item = ListItem.newBuilder().setId(id).build();
            return item;
        }).collect(Collectors.toList());

        var builder = ListResponse.newBuilder();
        builder.addAllVectors(items);
        var response = builder.build();

        when(testIndex.list(namespace, limit)).thenReturn(response);
    }

    private void setupUpsertResponse(String namespace) {
        UpsertResponse response = UpsertResponse.newBuilder().build();
        when(testIndex.upsert(upsertedVectors.capture(), eq(namespace))).thenReturn(response);
    }

    private void setupUpsertFailure(String namespace) {
        when(testIndex.upsert(upsertedVectors.capture(), eq(namespace))).thenThrow(new PineconeException("Failed to upsert"));
    }

    private void setupFetchVectorResponse(String namespace, Vector ...vectors) {
        var ids = List.of(vectors).stream().map(Vector::getId).collect(Collectors.toList());
        var vectorMap = List.of(vectors).stream().collect(Collectors.toMap(Vector::getId, Function.identity()));
        var builder = FetchResponse.newBuilder();
        builder.putAllVectors(vectorMap);
        var response = builder.build();

        when(testIndex.fetch(ids, namespace)).thenReturn(response);
    }

    private void setupSearchDenseVectorResponse(String namespace, int topK, String vectorId, Struct filter, boolean includeValues, boolean includeMetadata, ScoredVector...vectors) {
        QueryResponse queryResponse = QueryResponse.newBuilder().addAllMatches(List.of(vectors)).build();
        QueryResponseWithUnsignedIndices response = new QueryResponseWithUnsignedIndices(queryResponse);
        when(testIndex.query(eq(topK), any(), any(), any(), eq(vectorId), eq(namespace), eq(filter), eq(includeValues), eq(includeMetadata))).thenReturn(response);
    }

    private void setupSearchByDenseVector(String namespace, List<Float> vector, ScoredVector...vectors) {
        QueryResponse queryResponse = QueryResponse.newBuilder().addAllMatches(List.of(vectors)).build();
        QueryResponseWithUnsignedIndices response = new QueryResponseWithUnsignedIndices(queryResponse);
        when(testIndex.query(anyInt(), eq(vector), any(), any(), any(), eq(namespace), any(), anyBoolean(), anyBoolean())).thenReturn(response);
    }

    private void setupSearchBySparseVector(String namespace, List<Float> vector, List<Long> indexes, ScoredVector...vectors) {
        QueryResponse queryResponse = QueryResponse.newBuilder().addAllMatches(List.of(vectors)).build();
        QueryResponseWithUnsignedIndices response = new QueryResponseWithUnsignedIndices(queryResponse);
        when(testIndex.query(anyInt(), any(), eq(indexes), eq(vector), any(), eq(namespace), any(), anyBoolean(), anyBoolean())).thenReturn(response);
    }

    private void setupListIndexes(String ...indexNames) {
        var models = List.of(indexNames).stream().map(name -> {
            IndexModel model = new IndexModel();
            model.setName(name);
            return model;
        }).collect(Collectors.toList());

        IndexList indexList = new IndexList();
        indexList.setIndexes(models);

        when(pineconeClient.listIndexes()).thenReturn(indexList);
    }

    private void setupNamespacesForTestIndex(String ...namespaces) {
        DescribeIndexStatsResponse.Builder responseBuilder = DescribeIndexStatsResponse.newBuilder();

        List.of(namespaces).stream().forEach(namespace -> {
            var summary =  NamespaceSummary.newBuilder().setVectorCount(1).build();
            responseBuilder.putNamespaces(namespace, summary);
        });

        when(testIndex.describeIndexStats()).thenReturn(responseBuilder.build());
    }
}
