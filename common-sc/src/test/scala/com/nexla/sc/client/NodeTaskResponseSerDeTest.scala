package com.nexla.sc.client

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.nexla.common.ResourceType
import com.nexla.common.runtimes.KafkaClusterPropertiesUnmaterialized
import com.nexla.connector.config.PipelineTaskType
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import spray.json._
import com.nexla.sc.client.job_scheduler._
import org.apache.kafka.common.security.auth.SecurityProtocol

import java.util.Optional

class NodeTaskResponseSerDeTest extends AnyFlatSpec
  with Matchers
  with DefaultJsonProtocol
  with SprayJsonSupport
  with JobSchedulerMarshalling {

  "NodeTaskResponse" should "serialize and deserialize correctly" in {
    val localKafkaFlowRuntime = new KafkaClusterPropertiesUnmaterialized(
      "runtime-id",
      "zookeeper-connect-string",
      "bootstrap-server-string",
      SecurityProtocol.SSL,
      Optional.of("truststore-location"),
      Optional.of("truststore-secret"),
      Optional.of("truststore-key"),
      Optional.of("truststore-password-env"),
      Optional.of("keystore-location"),
      Optional.of("keystore-secret"),
      Optional.of("keystore-key"),
      Optional.of("keystore-password-env"),
      Optional.of("saslMechanism"),
      Optional.of("saslMechanism"),
      Optional.of("saslUsername"),
      Optional.of("saslPassword")
    );
    val nodeTaskResponse = NodeTaskResponse(
      ctrlMessages = None,
      invalidateResources = Some(Map(ResourceType.SOURCE -> Seq(1))),
      stopIds = Some(Map(ResourceType.SOURCE -> Set(1))),
      restartIds = Some(Map(ResourceType.SOURCE -> Set(1))),
      tasks = Map(PipelineTaskType.SINK_ICEBERG -> Seq(NodeTaskResponseElem("task1", PipelineTaskType.SINK_ICEBERG, None, restart = true, finish = Some(true)))),
      ready = true,
      shuttingDown = Some(false),
      decomissioning = Some(false)
    )

    val json = nodeTaskResponse.toJson
    val deserializedResponse = json.convertTo[NodeTaskResponse]

    deserializedResponse shouldEqual nodeTaskResponse
  }

  "NodeTaskResponse" should "tolerate extra fields" in {
    val nodeTaskResponse = NodeTaskResponse(
      ctrlMessages = None,
      invalidateResources = Some(Map(ResourceType.SOURCE -> Seq(1))),
      stopIds = Some(Map(ResourceType.SOURCE -> Set(1))),
      restartIds = Some(Map(ResourceType.SOURCE -> Set(1))),
      tasks = Map(PipelineTaskType.SINK_ICEBERG -> Seq(NodeTaskResponseElem("task1", PipelineTaskType.SINK_ICEBERG, None, restart = true, finish = Some(true)))),
      ready = true,
      shuttingDown = Some(false),
      decomissioning = Some(false)
    )

    val json = """{"a": "b","decomissioning":false,"invalidateResources":{"SOURCE":[1]},"ready":true,"restartIds":{"SOURCE":[1]},"shuttingDown":false,"stopIds":{"SOURCE":[1]},"tasks":{"SINK_ICEBERG":[{"finish":true,"restart":true,"taskId":"task1","taskType":"SINK_ICEBERG"}]}}""".parseJson
    val deserializedResponse = json.convertTo[NodeTaskResponse]

    deserializedResponse shouldEqual nodeTaskResponse
  }
}