akka {
  actor {
    default-dispatcher {
      executor = "com.nexla.telemetry.akka.InstrumentedForkJoinPoolConfigurator"
    }

    default-blocking-io-dispatcher {
      executor = "com.nexla.telemetry.akka.InstrumentedThreadPoolExecutorConfigurator"
    }
  }

  kafka {
     default-dispatcher {
       executor = "com.nexla.telemetry.akka.InstrumentedForkJoinPoolConfigurator"
     }
  }

  io {
    pinned-dispatcher {
        executor = "com.nexla.telemetry.akka.InstrumentedThreadPoolExecutorConfigurator"
    }
  }
}