package com.nexla.sc.client.job_scheduler

import com.nexla.common.ResourceType
import com.nexla.common.runtimes.KafkaClusterPropertiesUnmaterialized
import com.nexla.admin.client.RuntimeStatus
import com.nexla.common.metrics.MetricWithErrors
import com.nexla.common.{Resource, ResourceType}
import com.nexla.connector.config.PipelineTaskType
import com.nexla.control.message.ControlMessage
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum.PipelineTaskState
import com.nexla.sc.client.job_scheduler.TaskRequest.{ResourceId, TaskId}
import spray.json.{JsArray, JsNumber, JsObject, JsValue, RootJsonFormat, RootJsonReader}

import java.time.LocalDateTime
import java.util.{Optional, UUID}
import scala.compat.java8.OptionConverters._

object TaskRequest {

  type ResourceId = Int
  type TaskId = String

  def taskId(id: Int, typ: PipelineTaskType): TaskId = s"${typ.shortName.toLowerCase}-$id"

  private val taskIdPattern = java.util.regex.Pattern.compile("^([a-z_]+)-(\\d+)$")

  def parseTaskId(taskId: TaskId): (PipelineTaskType, Int) = {
    Option(taskId)
      .map(t => taskIdPattern.matcher(t))
      .filter(p => p.matches())
      .map(p => (PipelineTaskType.fromName(p.group(1)), p.group(2).toInt))
      .getOrElse(throw new IllegalArgumentException("Wrong task id provided: " + taskId))
  }

}

object NodeMaterializationEnum extends Enumeration {
  type NodeMaterializationType = Value
  val NotStarted = Value("NOT_STARTED")
  val Starting = Value("STARTING")
  val Started = Value("STARTED")
}

object PipelineTaskStateEnum extends Enumeration {
  type PipelineTaskState = Value
  val NotRunning = Value("NOT_RUNNING")
  val Running = Value("RUNNING")
  val Finished = Value("FINISHED")
  val Failed = Value("FAILED")
  val Decomissioning = Value("DECOMISSIONING")
}


abstract class ActionCommand(val action: String) {
  val resourceId: ResourceId
  val resourceType: ResourceType
}

object ActionCommand {
  val pauseAction: String = "activate"
  val activateAction: String = "pause"
}

case class Pause(resourceId: Int,
                 resourceType: ResourceType) extends ActionCommand(ActionCommand.pauseAction)

case class Activate(resourceId: Int,
                    resourceType: ResourceType) extends ActionCommand(ActionCommand.activateAction)

case class NodeTaskStatus(taskId: TaskId,
                          runId: Option[Long],
                          state: PipelineTaskState,
                          readState: Option[PipelineTaskState],
                          readStartTs: Option[LocalDateTime],
                          readDoneTs: Option[LocalDateTime],
                          writeState: Option[PipelineTaskState],
                          writeStartTs: Option[LocalDateTime],
                          writeDoneTs: Option[LocalDateTime],
                          lastDataTs: Option[LocalDateTime],
                          heartbeatTs: Option[LocalDateTime])

object RunIdEventTypes extends Enumeration {
  type RunIdEventType = Value
  val ReadDone = Value("READ_DONE")
  val ReadStart = Value("READ_START")
  val WriteDone = Value("WRITE_DONE")
  val WriteStart = Value("WRITE_START")
}

case class PipelineTaskInfo(isRunning: Boolean)

case class PipelineNodeDto(nodeId: String,
                           ip: String,
                           dedicatedNode: Boolean,
                           podName: Option[String],
                           taskType: Set[PipelineTaskType],
                           statuses: Seq[NodeTaskStatus],
                           tags: Option[Seq[String]],
                           version: Option[String],
                           serviceName: Option[String])

case class NodeIdWrapped(nodeId: String)

case class PipelineTask(taskId: TaskId,
                        taskType: PipelineTaskType,
                        maxInstances: Int,
                        dedicatedNode: Boolean,
                        state: PipelineTaskState,
                        excluded: Boolean,
                        meta: Option[PipelineTaskMeta],
                        lastActiveTs: Option[LocalDateTime], // when it started or received data
                        lastDataTs: Option[LocalDateTime], // when it received data
                        heartbeatTs: Option[LocalDateTime]) // when it was alive

object PipelineTaskMetaObj {

  def fromJava(sourceId: Optional[Integer],
               flowId: Optional[Integer],
               sinkId: Optional[Integer],
               dataSetFrom: Optional[Integer],
               dataSetTo: Optional[Integer],
               connectorClass: Optional[String],
               nodeTag: Optional[String],
               orgTag: Optional[String] = java.util.Optional.empty()): PipelineTaskMeta = {
    PipelineTaskMeta(
      sourceId = sourceId.asScala.map(_.intValue()),
      flowId = flowId.asScala.map(_.intValue()),
      sinkId = sinkId.asScala.map(_.intValue()),
      dataSetFrom = dataSetFrom.asScala.map(_.intValue()),
      dataSetTo = dataSetTo.asScala.map(_.intValue()),
      connectorClass = connectorClass.asScala,
      runId = None,
      nodeTag = nodeTag.asScala,
      orgTag = orgTag.asScala,
    )
  }

}

case class PipelineTaskMeta(sourceId: Option[Int] = None,
                            flowId: Option[Int] = None,
                            sinkId: Option[Int] = None,
                            dataSetFrom: Option[Int] = None,
                            dataSetTo: Option[Int] = None,
                            connectorClass: Option[String] = None,
                            runId: Option[Long] = None,
                            nodeTag: Option[String] = None,
                            orgTag: Option[String] = None,
                            dockerTag: Option[String] = None)

case class PipelineNodeTaskDto(nodeId: UUID,
                               taskId: TaskId,
                               state: PipelineTaskState,
                               heartbeatTs: LocalDateTime)

case class PipelineNodeTask(nodeId: UUID, taskId: TaskId, taskType: PipelineTaskType)

case class NodeTaskResponseElem(taskId: TaskId,
                                taskType: PipelineTaskType,
                                meta: Option[PipelineTaskMeta],
                                restart: Boolean,
                                finish: Option[Boolean])

// optional types for backward compatibility issues
case class NodeTaskResponse(ctrlMessages: Option[Iterable[ControlMessage]], // TODOAK: remove in 2.17
                            invalidateResources: Option[Map[ResourceType, Iterable[Int]]],
                            stopIds: Option[Map[ResourceType, Set[Int]]],
                            restartIds: Option[Map[ResourceType, Set[Int]]],
                            tasks: Map[PipelineTaskType, Iterable[NodeTaskResponseElem]],
                            ready: Boolean,
                            shuttingDown: Option[Boolean],
                            decomissioning: Option[Boolean])

case class NodeDecommissionRequest(nodes: Map[String, Boolean])

case class NodeDecommissionResponse(decommissionNodes: Set[TaskId])

object NodeDecommissionResponse {
  implicit object NodeDecommissionResponseFormat extends RootJsonReader[NodeDecommissionResponse] {

    def read(value: JsValue) = {
      value.asJsObject.getFields("decommission.nodes") match {
        case Seq(JsArray(taskIDs)) =>
          new NodeDecommissionResponse(taskIDs.map(_.toString).toSet)
      }
    }
  }
}

sealed trait Action

case class Start(taskId: TaskId, runId: Long) extends Action

case class Stop(taskId: TaskId) extends Action

case class Flush(taskId: TaskId) extends Action