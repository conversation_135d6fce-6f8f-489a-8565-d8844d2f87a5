package com.nexla.kafka.service;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.runtimes.KafkaClusterProperties;
import com.nexla.common.runtimes.KafkaClusterPropertiesUnmaterialized;
import com.nexla.kafka.KafkaTopicPartitionsCache;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

public class KafkaContextCache {

  // kafka context does not change after it was set up, we dont need to refresh this cache
  // same Kafka ID will always have same TopicMetaService etc
  private final Map<String, KafkaContext> kafkaContextCache = Maps.newConcurrentMap();

  @NotNull
  public KafkaContext getKafkaContext(KafkaClusterPropertiesUnmaterialized runtimeConfig) {
    return kafkaContextCache.computeIfAbsent(
        runtimeConfig.id,
        k -> {
          KafkaClusterProperties mat = runtimeConfig.materializeWithEnv();
          NexlaKafkaConfig kafkaConfig = new NexlaKafkaConfig(mat);
          TopicMetaService topicMetaService = new TopicMetaService(kafkaConfig);
          KafkaTopicPartitionsCache datasetTopicPartitionsCache =
              new KafkaTopicPartitionsCache(topicMetaService);
          KafkaLagCalculatorPooled kafkaLagCalculator = new KafkaLagCalculatorPooled(kafkaConfig);
          return new KafkaContext(
              runtimeConfig.id,
              kafkaConfig,
              topicMetaService,
              datasetTopicPartitionsCache,
              kafkaLagCalculator);
        });
  }
}
