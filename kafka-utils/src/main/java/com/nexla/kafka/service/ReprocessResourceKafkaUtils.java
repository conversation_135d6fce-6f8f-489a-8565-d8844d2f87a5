package com.nexla.kafka.service;

import static com.nexla.common.NexlaNamingUtils.*;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.common.Resource;
import com.nexla.common.exception.ResourceNotFoundException;
import com.nexla.common.exception.ResourceStateException;

public class ReprocessResourceKafkaUtils {

  public final AdminApiClient adminApiClient;

  public ReprocessResourceKafkaUtils(AdminApiClient adminApiClient) {
    this.adminApiClient = adminApiClient;
  }

  public String getReprocessConsumerGroup(final Resource resource) {
    switch (resource.type) {
      case SINK:
        // In case It's Sink - we need to process its Dataset offsets
        return adminApiClient
            .getDataSink(resource.id)
            .map(
                dataSink -> {
                  checkResourceState(dataSink.getStatus());
                  return connectorGroupId(resource);
                })
            .orElseThrow(
                () -> new ResourceNotFoundException("Sink was not found by ID: " + resource.id));
      case DATASET:
        // In case It's Dataset - we need to process its parent Dataset offsets
        adminApiClient
            .getDataSet(resource.id)
            .map(
                dataSet -> {
                  checkResourceState(dataSet.getStatus());
                  if (dataSet.getParentDatasets().isEmpty()) {
                    throw new ResourceStateException(
                        "You can not reprocess Source DataSet. In order to do that, please restart"
                            + " the Source.");
                  }
                  return transformDatasetsGroupId(
                      dataSet.getParentDatasets().get(0).getId(), dataSet.getId());
                })
            .orElseThrow(
                () -> new ResourceNotFoundException("DataSet was not found by ID: " + resource.id));
      default:
        throw new ResourceStateException(
            "You can not reprocess other resources than Sink or DataSet.");
    }
  }

  public String getReprocessTopic(final Resource resource) {
    switch (resource.type) {
      case SINK:
        // In case It's Sink - we need to process its Dataset offsets
        return adminApiClient
            .getDataSink(resource.id)
            .map(
                dataSink -> {
                  checkResourceState(dataSink.getStatus());
                  return nameDataSetTopic(dataSink.getDataSetId());
                })
            .orElseThrow(
                () -> new ResourceNotFoundException("Sink was not found by ID: " + resource.id));
      case DATASET:
        // In case It's Dataset - we need to process its parent Dataset offsets
        adminApiClient
            .getDataSet(resource.id)
            .map(
                dataSet -> {
                  checkResourceState(dataSet.getStatus());
                  if (dataSet.getParentDatasets().isEmpty()) {
                    throw new ResourceStateException(
                        "You can not reprocess Source DataSet. In order to do that, please restart"
                            + " the Source.");
                  }
                  return nameDataSetTopic(dataSet.getParentDatasets().get(0).getId());
                })
            .orElseThrow(
                () -> new ResourceNotFoundException("DataSet was not found by ID: " + resource.id));
      default:
        throw new ResourceStateException(
            "You can not reprocess other resources than Sink or DataSet.");
    }
  }

  private void checkResourceState(final ResourceStatus status) {
    if (status != ResourceStatus.PAUSED) {
      throw new ResourceStateException("To reprocess resource you should pause It first.");
    }
  }
}
