package com.nexla.kafka.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.kafka.common.TopicPartition;

public interface KafkaLagCalculator {

  Map<String, Long> getPipelineLag(Integer sinkId, List<Integer> dataSets);

  Map<TopicPartition, Long> getProducerOffsets(Collection<TopicPartition> topicPartitions);

  Map<TopicPartition, Long> getBeginningOffsets(Collection<TopicPartition> topicPartitions);

  Map<TopicPartition, Long> getConsumerGrpOffsets(String groupId);

  Map<TopicPartition, Long> getConsumerGrpOffsetsRaw(String groupId);

  static long computeLag(
      Map<TopicPartition, Long> consumerGrpOffsets, Map<TopicPartition, Long> producerOffsets) {
    long lagsTotal = 0;
    for (Map.Entry<TopicPartition, Long> entry : consumerGrpOffsets.entrySet()) {
      Long producerOffset = producerOffsets.get(entry.getKey());
      Long consumerOffset = consumerGrpOffsets.get(entry.getKey());
      long lag = Math.abs(producerOffset - consumerOffset);
      lagsTotal += lag;
    }

    return lagsTotal;
  }

  static double computeSpeed(
      Map<TopicPartition, Long> prevOffsets, Map<TopicPartition, Long> curOffsets, double time) {
    long eventCountTotal = 0;
    for (Map.Entry<TopicPartition, Long> entry : curOffsets.entrySet()) {
      Long curOffset = entry.getValue();
      Long prevOffset = prevOffsets.getOrDefault(entry.getKey(), 0L);
      long eventCount = curOffset - prevOffset;
      eventCountTotal += eventCount;
    }

    return eventCountTotal / time;
  }
}
