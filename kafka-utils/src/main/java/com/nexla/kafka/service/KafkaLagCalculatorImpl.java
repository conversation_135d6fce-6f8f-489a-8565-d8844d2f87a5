package com.nexla.kafka.service;

import static com.nexla.common.NexlaNamingUtils.connectorGroupId;
import static com.nexla.common.NexlaNamingUtils.streamingConnectorGroupId;
import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;
import static org.apache.kafka.clients.consumer.ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.Resource;
import com.nexla.kafka.control.listener.conf.NexlaKafkaConfigApplier;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import java.time.Duration;
import java.util.*;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ListConsumerGroupOffsetsResult;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;

@Data
public class KafkaLagCalculatorImpl implements KafkaLagCalculator {

  private final String bootstrapServers;
  private final KafkaConsumer<String, String> kafkaConsumer;
  private AdminClient adminClient;

  /** Prefer to use KafkaLagCalculatorCached instead of creating instances, when possible */
  private static final ExecutionMetricSet kafkaLagCalculatorImplMetrics =
      metricSet(KafkaLagCalculatorImpl.class, "default");

  public KafkaLagCalculatorImpl(NexlaKafkaConfig sslContext) {
    this.bootstrapServers = sslContext.bootstrapServer;
    this.adminClient = KafkaResourcesCache.getSharedKafkaAdminClient(sslContext);
    this.kafkaConsumer = getKafkaConsumer(sslContext);
    kafkaLagCalculatorImplMetrics.incCounter("created");
  }

  @Override
  public Map<String, Long> getPipelineLag(Integer sinkId, List<Integer> dataSets) {
    return getPipelineLag(sinkId, dataSets, false);
  }

  private Map<String, Long> getPipelineLag(
      Integer sinkId, List<Integer> dataSets, boolean isStreaming) {
    Integer prevDataSet = null;
    Set<String> groupIds = Sets.newConcurrentHashSet();
    for (Integer dataSet : dataSets) {
      if (prevDataSet != null) {
        groupIds.add("transform-dataset-" + prevDataSet + "-dataset-" + dataSet);
      }
      prevDataSet = dataSet;
    }
    if (isStreaming) {
      groupIds.add(streamingConnectorGroupId(Resource.sink(sinkId)));
    } else {
      groupIds.add(connectorGroupId(Resource.sink(sinkId)));
    }

    return getPipelineLag(groupIds);
  }

  private static final ExecutionMetricSet getPipelineLagMetrics =
      metricSet(KafkaLagCalculatorImpl.class, "getPipelineLag");

  @SneakyThrows
  private Map<String, Long> getPipelineLag(Set<String> groupIds) {
    getPipelineLagMetrics.markHist("groupIds.size", groupIds.size());
    return getPipelineLagMetrics.track(
        () -> {
          Map<String, Long> lags = Maps.newHashMap();
          StreamEx.of(groupIds).forEach(x -> lags.put(x, analyzeLag(x)));
          return lags;
        });
  }

  @SneakyThrows
  private long analyzeLag(String groupId) {
    Map<TopicPartition, Long> consumerGrpOffsets = getConsumerGrpOffsets(groupId);
    Map<TopicPartition, Long> producerOffsets = getProducerOffsets(consumerGrpOffsets.keySet());
    return KafkaLagCalculator.computeLag(consumerGrpOffsets, producerOffsets);
  }

  /**
   * Get the end offsets for the given partitions. In the default read_uncommitted isolation level,
   * the end offset is the high watermark (that is, the offset of the last successfully replicated
   * message plus one). For read_committed consumers, the end offset is the last stable offset
   * (LSO), which is the minimum of the high watermark and the smallest offset of any open
   * transaction. Finally, if the partition has never been written to, the end offset is 0.
   */
  private static final ExecutionMetricSet getProducerOffsetsMetrics =
      metricSet(KafkaLagCalculatorImpl.class, "getProducerOffsets");

  @Override
  public Map<TopicPartition, Long> getProducerOffsets(Collection<TopicPartition> topicPartitions) {
    getProducerOffsetsMetrics.markHist("topicPartitions.size", topicPartitions.size());
    return getProducerOffsetsMetrics.track(() -> kafkaConsumer.endOffsets(topicPartitions));
  }

  private static final ExecutionMetricSet getBeginningOffsetsMetrics =
      metricSet(KafkaLagCalculatorImpl.class, "getBeginningOffsets");

  @Override
  public Map<TopicPartition, Long> getBeginningOffsets(Collection<TopicPartition> topicPartitions) {
    getBeginningOffsetsMetrics.markHist("topicPartitions.size", topicPartitions.size());
    return getBeginningOffsetsMetrics.track(() -> kafkaConsumer.beginningOffsets(topicPartitions));
  }

  /** Do not forget to close the consumer */
  private KafkaConsumer<String, String> getKafkaConsumer(NexlaKafkaConfig sslContext) {
    Map<String, Object> props = new HashMap<>();
    props.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
    props.put(KEY_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
    props.put(VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);

    Map<String, Object> propsWithSsl = new NexlaKafkaConfigApplier(sslContext).apply(props);
    return new KafkaConsumer<>(propsWithSsl);
  }

  private static final ExecutionMetricSet getConsumerGrpOffsetsMetrics =
      metricSet(KafkaLagCalculatorImpl.class, "getConsumerGrpOffsets");

  @Override
  @SneakyThrows
  public Map<TopicPartition, Long> getConsumerGrpOffsets(String groupId) {
    return getConsumerGrpOffsetsMetrics.track(
        () -> {
          Map<TopicPartition, Long> groupOffsets = getConsumerGrpOffsetsRaw(groupId);
          // Check beginning offsets for partitions in case of expired records
          kafkaConsumer
              .beginningOffsets(groupOffsets.keySet())
              .forEach(
                  (topicPartition, beginningOffset) ->
                      groupOffsets.compute(
                          topicPartition,
                          (ignored, groupOffset) ->
                              Math.max(
                                  beginningOffset, Optional.ofNullable(groupOffset).orElse(0L))));
          return groupOffsets;
        });
  }

  /** Get the consumer group offsets without checking beginning offsets */
  private static final ExecutionMetricSet getConsumerGrpOffsetsRawMetrics =
      metricSet(KafkaLagCalculatorImpl.class, "getConsumerGrpOffsetsRaw");

  @Override
  @SneakyThrows
  public Map<TopicPartition, Long> getConsumerGrpOffsetsRaw(String groupId) {
    return getConsumerGrpOffsetsRawMetrics.track(
        () -> {
          ListConsumerGroupOffsetsResult info = adminClient.listConsumerGroupOffsets(groupId);
          Map<TopicPartition, OffsetAndMetadata> metadataMap =
              info.partitionsToOffsetAndMetadata().get();

          Map<TopicPartition, Long> groupOffsets = new HashMap<>();
          for (Map.Entry<TopicPartition, OffsetAndMetadata> entry : metadataMap.entrySet()) {
            TopicPartition key = entry.getKey();
            OffsetAndMetadata metadata = entry.getValue();
            groupOffsets.putIfAbsent(
                new TopicPartition(key.topic(), key.partition()), metadata.offset());
          }
          return groupOffsets;
        });
  }

  @SneakyThrows
  public void close() {
    kafkaConsumer.close(Duration.ofSeconds(30));
    kafkaLagCalculatorImplMetrics.incCounter("closed");
    // Do not close adminApiClient as it is shared instance
  }
}
