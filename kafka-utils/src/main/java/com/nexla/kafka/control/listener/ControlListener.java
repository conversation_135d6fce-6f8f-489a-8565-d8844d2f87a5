package com.nexla.kafka.control.listener;

import com.nexla.control.message.ControlMessage;
import com.nexla.control.message.ControlResourceType;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ControlListener {

  private static final Logger logger = LoggerFactory.getLogger(ControlListener.class);

  private final ControlMessageHandlerFactory handlerFactory;
  private final Set<ControlResourceType> supportedEvents;

  public ControlListener(
      ControlMessageHandlerFactory handlerFactory, Set<ControlResourceType> supportedEvents) {
    this.handlerFactory = handlerFactory;
    this.supportedEvents = supportedEvents;
  }

  public void onMessage(ControlMessage message) {
    try {
      ControlResourceType resourceType = message.getResourceType();
      if (resourceType == null) {
        logger.error("Missing resourceType={}", message);
      }
      if (supportedEvents.contains(resourceType)) {
        ControlMessageHandler handler = findHandler(resourceType);
        handler.handleMessage(message);
      }
    } catch (Exception e) {
      logger.error("Error on message {}", message, e);
    }
  }

  private ControlMessageHandler findHandler(ControlResourceType resourceType) {
    try {
      ControlMessageHandler controlMessageHandler =
          handlerFactory.getControlMessageHandler(resourceType.toString().toLowerCase());

      return controlMessageHandler;
    } catch (Exception e) {
      logger.error("No matching handler for resourceType={}", resourceType, e);
      throw e;
    }
  }
}
