package com.nexla.kafka.ntm;

import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.StreamUtils;
import com.nexla.control.coordination.NodeTaskManagerAssignment;
import com.nexla.kafka.control.listener.conf.NexlaKafkaConfigApplier;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.errors.WakeupException;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ConnectorAssignmentConsumer {

  private static final Logger LOGGER = LoggerFactory.getLogger(ConnectorAssignmentConsumer.class);

  private final NexlaKafkaConfig sslContext;
  private final String bootstrapServers;
  private final String nodeId;
  private final Consumer<NodeTaskManagerAssignment> connectorAssignmentConsumer;

  private KafkaConsumer<String, String> consumer;
  private Thread consumerThread;

  public ConnectorAssignmentConsumer(
      NexlaKafkaConfig sslContext,
      String bootstrapServers,
      String nodeId,
      Consumer<NodeTaskManagerAssignment> connectorAssignmentConsumer) {
    this.sslContext = sslContext;
    this.bootstrapServers = bootstrapServers;
    this.nodeId = nodeId;
    this.connectorAssignmentConsumer = connectorAssignmentConsumer;
  }

  public void start() {

    Map<String, Object> props = new HashMap<>();
    props.put("bootstrap.servers", bootstrapServers);
    props.put("key.deserializer", StringDeserializer.class);
    props.put("value.deserializer", StringDeserializer.class);
    props.put("group.id", nodeId);
    props.put("auto.offset.reset", "latest");

    Map<String, Object> propsWithSsl = new NexlaKafkaConfigApplier(sslContext).apply(props);
    consumer = new KafkaConsumer<>(propsWithSsl);
    consumer.subscribe(Arrays.asList(NexlaConstants.TOPIC_COORDINATION));

    consumerThread =
        new Thread(
            () -> {
              try {
                while (true) {
                  ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
                  for (ConsumerRecord<String, String> record : records) {
                    NodeTaskManagerAssignment assignment =
                        StreamUtils.jsonUtil()
                            .stringToType(record.value(), NodeTaskManagerAssignment.class);
                    connectorAssignmentConsumer.accept(assignment);
                  }
                }
              } catch (WakeupException we) {
                LOGGER.info("Consumer is shutting down");
              } finally {
                consumer.close();
                LOGGER.info("Consumer closed");
              }
            });

    consumerThread.start();
  }

  public void stop() {
    if (consumer != null && consumerThread != null) {
      try {
        consumer.wakeup();
        consumerThread.join(TimeUnit.SECONDS.toMillis(30));
      } catch (InterruptedException e) {
        LOGGER.error("Error while stopping consumer thread", e);
      }
    }
  }
}
