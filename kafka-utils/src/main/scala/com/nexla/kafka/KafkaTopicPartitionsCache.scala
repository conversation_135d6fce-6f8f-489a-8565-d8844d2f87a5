package com.nexla.kafka

import com.google.common.cache.{<PERSON><PERSON><PERSON><PERSON>er, <PERSON>acheLoader, LoadingCache}
import com.google.common.util.concurrent.{ListenableFuture, MoreExecutors, ThreadFactoryBuilder}
import com.nexla.kafka.service.TopicMetaService
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet

import java.util.concurrent.Executors
import scala.compat.java8.OptionConverters._
import scala.jdk.CollectionConverters._

class KafkaTopicPartitionsCache(val topicMetaService: TopicMetaService)
  extends StrictNexlaLogging with WithLogging {

  private val CACHE_KEY = "KEY"

  private val partitionsCache: LoadingCache[String, Map[String, Integer]] = CacheBuilder
    .newBuilder()
    .refreshAfterWrite(1, java.util.concurrent.TimeUnit.MINUTES)
    .build[String, Map[String, Integer]](
      new CacheLoader[String, Map[String, Integer]] {

        private val listeningExecutorService = MoreExecutors.listeningDecorator(
          Executors.newSingleThreadExecutor(new ThreadFactoryBuilder().setNameFormat("KafkaTopicPartitionsCache-%d").setDaemon(true).build))

        private val partitionsCacheLoadMetrics = metricSet(classOf[KafkaTopicPartitionsCache], "partitionsCacheLoad")
        override def load(ignored: String): Map[String, Integer] = tracked(partitionsCacheLoadMetrics)(() => {
          val topics = topicMetaService.getTopics.asScala
          partitionsCacheLoadMetrics.markHist("topics.size", topics.size)
          topicMetaService.getTopicPartitionsCount(topics.toList.asJava).asScala.toMap
        })

        override def reload(key: String, oldValue: Map[String, Integer]): ListenableFuture[Map[String, Integer]] = {
          // Load new values asynchronously, so that calls to read values from the cache don't block
          listeningExecutorService.submit(() => load(key))
        }
      })

  def refetchTopics(topics: Seq[String]): Unit = {
    partitionsCache.asMap().computeIfPresent(CACHE_KEY, (_, prev) => {
      prev ++ topicMetaService.getTopicPartitionsCount(topics.toList.asJava).asScala.toMap
    })
  }

  //noinspection AccessorLikeMethodIsEmptyParen
  def getAllPartitionsCounts(): Map[String, Integer] = {
    partitionsCache.get(CACHE_KEY)
  }

  private val getPartitionsCountMetrics = metricSet(classOf[KafkaTopicPartitionsCache], "getPartitionsCount")
  def getTopicPartitionsCount(topicName: String, refetchIfMissing: Boolean = true): Option[Integer] = {
    val maybeResult = partitionsCache.get(CACHE_KEY).get(topicName)
    maybeResult match {
      case Some(_) => maybeResult
      case None if refetchIfMissing=>
        logger.warn("Re-fetch topic partition count for: {}", topicName)
        getPartitionsCountMetrics.incCounter("refetchTopicPartitions")
        topicMetaService.getTopicPartitionCount(topicName).asScala
      case None =>
        logger.warn("Topic partition count not found for: {}", topicName)
        getPartitionsCountMetrics.incCounter("missingTopicPartitions")
        None
    }
  }

  def refresh(): Unit = {
    partitionsCache.refresh(CACHE_KEY)
  }
}