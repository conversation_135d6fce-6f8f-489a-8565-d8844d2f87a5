package com.nexla.telemetry.utils;

import com.nexla.telemetry.PrometheusTelemetry;
import com.nexla.telemetry.TelemetryContext;
import com.nexla.test.UnitTests;
import io.prometheus.client.*;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.assertj.core.api.Assertions;
import org.joor.Reflect;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;

@Category(UnitTests.class)
public class ExecutionMetricSetImplTest {

  private CollectorRegistry collectorRegistry = CollectorRegistry.defaultRegistry;
  private PrometheusTelemetry telemetry;

  @Before
  public void setUp() {
    telemetry = new PrometheusTelemetry("testEnv", "testApp");
    TelemetryContext.set(telemetry);
  }

  @After
  public void tearDown() {
    // Clear registry after each test to avoid interference between tests
    collectorRegistry.clear();
  }

  @Test
  public void testExecutionMetricsAreProperlyClosed() throws Exception {
    // Create different metric sets for different methods, but same class
    ExecutionMetricSetImpl metricsSet1 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "method1");
    ExecutionMetricSetImpl metricsSet2 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "method2");

    metricsSet1.incCounter("name1", 1);
    metricsSet1.incCounter("name2", 1);

    metricsSet2.incCounter("name1", 1);
    metricsSet2.incCounter("name2", 1);

    final Map<String, Collector> registeredCollectors =
        Reflect.on(collectorRegistry).field("namesToCollectors").get();
    printRegisteredMetrics(registeredCollectors);

    final Counter nexlaMetricCounter =
        (Counter)
            registeredCollectors.get(
                "nexla_execution_metric_counter"); // 1 counter, because the class is the same
    Assertions.assertThat(nexlaMetricCounter).isNotNull();

    final ConcurrentMap<List<String>, Counter.Child> children =
        Reflect.on(nexlaMetricCounter).field("children").get();
    Assertions.assertThat(children).isNotNull();
    Assertions.assertThat(children).hasSize(4); // 2 children for each labels set

    metricsSet2.close(); // close one of the metric sets
    Assertions.assertThat(children).hasSize(2); // only 2 children should remain

    metricsSet1.close();
    Assertions.assertThat(children).hasSize(0); // all children should be removed
  }

  @Test
  public void testHistogramMetricsAreProperlyClosed() throws Exception {
    // Create different metric sets for different methods, but same class
    ExecutionMetricSetImpl metricsSet1 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "histMethod1");
    ExecutionMetricSetImpl metricsSet2 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "histMethod2");

    double[] buckets = {0.1, 0.5, 1.0, 5.0};

    metricsSet1.markHist("hist1", 0.2, buckets);
    metricsSet1.markHist("hist2", 1.5, buckets);

    metricsSet2.markHist("hist1", 0.3, buckets);
    metricsSet2.markHist("hist2", 2.5, buckets);

    final Map<String, Collector> registeredCollectors =
        Reflect.on(collectorRegistry).field("namesToCollectors").get();
    printRegisteredMetrics(registeredCollectors);

    final Histogram nexlaMetricHistogram =
        (Histogram) registeredCollectors.get("nexla_execution_metric_hist");
    Assertions.assertThat(nexlaMetricHistogram).isNotNull();

    final ConcurrentMap<List<String>, Histogram.Child> children =
        Reflect.on(nexlaMetricHistogram).field("children").get();
    Assertions.assertThat(children).isNotNull();
    Assertions.assertThat(children).hasSize(4); // 2 children for each labels set

    metricsSet2.close(); // close one of the metric sets
    Assertions.assertThat(children).hasSize(2); // only 2 children should remain

    metricsSet1.close();
    Assertions.assertThat(children).hasSize(0); // all children should be removed
  }

  @Test
  public void testGaugeMetricsAreProperlyClosed() throws Exception {
    // Create different metric sets for different methods, but same class
    ExecutionMetricSetImpl metricsSet1 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "gaugeMethod1");
    ExecutionMetricSetImpl metricsSet2 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "gaugeMethod2");

    metricsSet1.setGauge("gauge1", 10.5);
    metricsSet1.setGauge("gauge2", 20.5);

    metricsSet2.setGauge("gauge1", 30.5);
    metricsSet2.setGauge("gauge2", 40.5);

    final Map<String, Collector> registeredCollectors =
        Reflect.on(collectorRegistry).field("namesToCollectors").get();
    printRegisteredMetrics(registeredCollectors);

    final Gauge nexlaMetricGauge = (Gauge) registeredCollectors.get("nexla_execution_metric_gauge");
    Assertions.assertThat(nexlaMetricGauge).isNotNull();

    final ConcurrentMap<List<String>, Gauge.Child> children =
        Reflect.on(nexlaMetricGauge).field("children").get();
    Assertions.assertThat(children).isNotNull();
    Assertions.assertThat(children).hasSize(4); // 2 children for each labels set

    metricsSet2.close(); // close one of the metric sets
    Assertions.assertThat(children).hasSize(2); // only 2 children should remain

    metricsSet1.close();
    Assertions.assertThat(children).hasSize(0); // all children should be removed
  }

  @Test
  public void testTimerMetricsAreProperlyClosed() throws Exception {
    // Create different metric sets for different methods, but same class
    ExecutionMetricSetImpl metricsSet1 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "timerMethod1");
    ExecutionMetricSetImpl metricsSet2 =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "timerMethod2");

    // Use the timer to measure execution time
    try (var timer1 = metricsSet1.time("timer1")) {
      TimeUnit.MILLISECONDS.sleep(10); // Simulate work
    }

    try (var timer2 = metricsSet1.time("timer2")) {
      TimeUnit.MILLISECONDS.sleep(15); // Simulate work
    }

    try (var timer1 = metricsSet2.time("timer1")) {
      TimeUnit.MILLISECONDS.sleep(20); // Simulate work
    }

    try (var timer2 = metricsSet2.time("timer2")) {
      TimeUnit.MILLISECONDS.sleep(25); // Simulate work
    }

    final Map<String, Collector> registeredCollectors =
        Reflect.on(collectorRegistry).field("namesToCollectors").get();
    printRegisteredMetrics(registeredCollectors);

    final Histogram nexlaMetricTimer =
        (Histogram) registeredCollectors.get("nexla_execution_metric_timer");
    Assertions.assertThat(nexlaMetricTimer).isNotNull();

    final ConcurrentMap<List<String>, Histogram.Child> children =
        Reflect.on(nexlaMetricTimer).field("children").get();
    Assertions.assertThat(children).isNotNull();
    Assertions.assertThat(children).hasSize(4); // 2 children for each labels set

    metricsSet2.close(); // close one of the metric sets
    Assertions.assertThat(children).hasSize(2); // only 2 children should remain

    metricsSet1.close();
    Assertions.assertThat(children).hasSize(0); // all children should be removed
  }

  @Test
  public void testTrackMethodWithExecutionTime() throws Exception {
    ExecutionMetricSetImpl metricsSet =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "trackMethod");

    // Using track method with runnable
    metricsSet.track(
        () -> {
          try {
            TimeUnit.MILLISECONDS.sleep(10);
          } catch (InterruptedException e) {
            throw new RuntimeException(e);
          }
        });

    // Using track method with callable
    String result =
        metricsSet.track(
            () -> {
              TimeUnit.MILLISECONDS.sleep(15);
              return "test-result";
            });

    Assertions.assertThat(result).isEqualTo("test-result");

    // Using track method with named metrics
    metricsSet.track(
        "named-track",
        () -> {
          try {
            TimeUnit.MILLISECONDS.sleep(20);
          } catch (InterruptedException e) {
            throw new RuntimeException(e);
          }
        });

    // Using track method with named metrics and callable
    Integer calculatedResult =
        metricsSet.track(
            "named-track-callable",
            () -> {
              TimeUnit.MILLISECONDS.sleep(25);
              return 42;
            });

    Assertions.assertThat(calculatedResult).isEqualTo(42);

    final Map<String, Collector> registeredCollectors =
        Reflect.on(collectorRegistry).field("namesToCollectors").get();
    final Histogram nexlaMetricTimer =
        (Histogram) registeredCollectors.get("nexla_execution_metric_timer");
    Assertions.assertThat(nexlaMetricTimer).isNotNull();

    // Clean up
    metricsSet.close();
  }

  @Test
  public void testTimeLoggingWithDefaultTimer() throws Exception {
    // Create a TestLogger
    TestLogger testLogger = new TestLogger("testTimeLoggingWithDefaultTimer");

    // Create metric set with time logger
    ExecutionMetricSetImpl metricsSet =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "loggerMethod");
    metricsSet.withTimeLogger(testLogger);

    // Use default timer with time logging
    long sleepTime = 50; // in milliseconds
    try (var timer = metricsSet.time()) {
      TimeUnit.MILLISECONDS.sleep(sleepTime);
    }

    // Verify log message
    List<TestLogger.LogEntry> logEntries = testLogger.getLogEntries();
    Assertions.assertThat(logEntries.size()).isGreaterThanOrEqualTo(2); // Debug + info messages

    // Find the INFO log entry with timing information
    TestLogger.LogEntry infoEntry =
        logEntries.stream()
            .filter(entry -> "INFO".equals(entry.getLevel()))
            .findFirst()
            .orElse(null);

    Assertions.assertThat(infoEntry).isNotNull();
    String logMessage = infoEntry.getMessage();

    // Check that log message contains the expected elements
    Assertions.assertThat(logMessage).contains("TIME=");
    Assertions.assertThat(logMessage).contains("millis");

    // Extract execution time from the log message and verify it's at least the sleep time
    long executionTime = extractExecutionTimeFromLogMessage(logMessage);
    Assertions.assertThat(executionTime).isGreaterThanOrEqualTo(sleepTime);

    // Clean up
    metricsSet.close();
  }

  @Test
  public void testTimeLoggingWithNamedTimer() throws Exception {
    // Create a TestLogger
    TestLogger testLogger = new TestLogger("testTimeLoggingWithNamedTimer");

    // Create metric set with time logger
    ExecutionMetricSetImpl metricsSet =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "namedLoggerMethod");
    metricsSet.withTimeLogger(testLogger);

    // Use named timer with time logging
    String timerName = "special-operation";
    long sleepTime = 75; // in milliseconds
    try (var timer = metricsSet.time(timerName)) {
      TimeUnit.MILLISECONDS.sleep(sleepTime);
    }

    // Verify log message
    List<TestLogger.LogEntry> logEntries = testLogger.getLogEntries();
    Assertions.assertThat(logEntries.size()).isGreaterThanOrEqualTo(2); // Debug + info messages

    // Find the INFO log entry with timing information
    TestLogger.LogEntry infoEntry =
        logEntries.stream()
            .filter(entry -> "INFO".equals(entry.getLevel()))
            .findFirst()
            .orElse(null);

    Assertions.assertThat(infoEntry).isNotNull();
    String logMessage = infoEntry.getMessage();

    // Check that log message contains the expected elements
    Assertions.assertThat(logMessage).contains("TIME=");
    Assertions.assertThat(logMessage).contains("millis");
    Assertions.assertThat(logMessage).contains(timerName);

    // Extract execution time from the log message and verify it's at least the sleep time
    long executionTime = extractExecutionTimeFromLogMessage(logMessage);
    Assertions.assertThat(executionTime).isGreaterThanOrEqualTo(sleepTime);

    // Clean up
    metricsSet.close();
  }

  @Test
  public void testMultipleTimersWithSameLogger() throws Exception {
    // Create a TestLogger
    TestLogger testLogger = new TestLogger("testMultipleTimersWithSameLogger");

    // Create metric set with time logger
    ExecutionMetricSetImpl metricsSet =
        new ExecutionMetricSetImpl(ExecutionMetricSetImplTest.class, "multiTimerMethod");
    metricsSet.withTimeLogger(testLogger);

    // Use multiple timers with time logging
    try (var timer1 = metricsSet.time("timer1")) {
      TimeUnit.MILLISECONDS.sleep(30);
    }

    try (var timer2 = metricsSet.time("timer2")) {
      TimeUnit.MILLISECONDS.sleep(40);
    }

    // Verify log messages
    List<TestLogger.LogEntry> logEntries = testLogger.getLogEntries();
    List<TestLogger.LogEntry> infoEntries =
        logEntries.stream()
            .filter(entry -> "INFO".equals(entry.getLevel()))
            .collect(Collectors.toList());

    Assertions.assertThat(infoEntries).hasSize(2);

    // Check first timer log
    TestLogger.LogEntry timer1Entry =
        infoEntries.stream()
            .filter(entry -> entry.getMessage().contains("timer1"))
            .findFirst()
            .orElse(null);

    Assertions.assertThat(timer1Entry).isNotNull();
    String log1 = timer1Entry.getMessage();
    Assertions.assertThat(log1).contains("TIME=");
    long time1 = extractExecutionTimeFromLogMessage(log1);
    Assertions.assertThat(time1).isGreaterThanOrEqualTo(30).isLessThan(40);

    // Check second timer log
    TestLogger.LogEntry timer2Entry =
        infoEntries.stream()
            .filter(entry -> entry.getMessage().contains("timer2"))
            .findFirst()
            .orElse(null);

    Assertions.assertThat(timer2Entry).isNotNull();
    String log2 = timer2Entry.getMessage();
    Assertions.assertThat(log2).contains("TIME=");
    long time2 = extractExecutionTimeFromLogMessage(log2);
    Assertions.assertThat(time2).isGreaterThanOrEqualTo(40);

    // Clean up
    metricsSet.close();
  }

  /**
   * Extracts the execution time in milliseconds from the log message. Example log format:
   * "com.nexla.telemetry.utils.ExecutionMetricSetImplTest.loggerMethod.default#default
   * TIME=00:00:00.054 (54 millis)"
   */
  private long extractExecutionTimeFromLogMessage(String logMessage) {
    // Primary pattern: extract time from parentheses (XX millis)
    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\((\\d+)\\s+millis\\)");
    java.util.regex.Matcher matcher = pattern.matcher(logMessage);

    if (matcher.find()) {
      try {
        return Long.parseLong(matcher.group(1));
      } catch (NumberFormatException e) {
        System.err.println("Failed to parse time from regex match: " + matcher.group(1));
      }
    }

    // If no pattern matches, log and return a default value
    System.err.println("Could not extract time from log message: " + logMessage);
    return 1;
  }

  private void printRegisteredMetrics(Map<String, Collector> registeredCollectors) {
    System.out.printf(
        "RegisteredCollectors: %s%n",
        registeredCollectors.entrySet().stream()
            .map(e -> e.getKey() + " -> " + e.getValue())
            .filter(x -> x.contains("nexla_execution_metric"))
            .collect(Collectors.joining("\n")));
  }
}
