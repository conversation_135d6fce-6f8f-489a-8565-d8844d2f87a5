package com.nexla.telemetry.utils;

import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.Marker;

/** Test implementation of SLF4J Logger that captures log entries for verification in tests. */
public class TestLogger implements Logger {
  private final String name;
  private final List<LogEntry> logEntries = new ArrayList<>();

  public TestLogger(String name) {
    this.name = name;
  }

  public List<LogEntry> getLogEntries() {
    return logEntries;
  }

  public void clearLogEntries() {
    logEntries.clear();
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public boolean isTraceEnabled() {
    return true;
  }

  @Override
  public void trace(String msg) {
    logEntries.add(new LogEntry("TRACE", msg));
  }

  @Override
  public void trace(String format, Object arg) {
    logEntries.add(new LogEntry("TRACE", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void trace(String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("TRACE", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void trace(String format, Object... arguments) {
    logEntries.add(new LogEntry("TRACE", formatWithArray(format, arguments)));
  }

  @Override
  public void trace(String msg, Throwable t) {
    logEntries.add(new LogEntry("TRACE", msg, t));
  }

  @Override
  public boolean isTraceEnabled(Marker marker) {
    return true;
  }

  @Override
  public void trace(Marker marker, String msg) {
    logEntries.add(new LogEntry("TRACE", msg));
  }

  @Override
  public void trace(Marker marker, String format, Object arg) {
    logEntries.add(new LogEntry("TRACE", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void trace(Marker marker, String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("TRACE", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void trace(Marker marker, String format, Object... argArray) {
    logEntries.add(new LogEntry("TRACE", formatWithArray(format, argArray)));
  }

  @Override
  public void trace(Marker marker, String msg, Throwable t) {
    logEntries.add(new LogEntry("TRACE", msg, t));
  }

  @Override
  public boolean isDebugEnabled() {
    return true;
  }

  @Override
  public void debug(String msg) {
    logEntries.add(new LogEntry("DEBUG", msg));
  }

  @Override
  public void debug(String format, Object arg) {
    logEntries.add(new LogEntry("DEBUG", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void debug(String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("DEBUG", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void debug(String format, Object... arguments) {
    logEntries.add(new LogEntry("DEBUG", formatWithArray(format, arguments)));
  }

  @Override
  public void debug(String msg, Throwable t) {
    logEntries.add(new LogEntry("DEBUG", msg, t));
  }

  @Override
  public boolean isDebugEnabled(Marker marker) {
    return true;
  }

  @Override
  public void debug(Marker marker, String msg) {
    logEntries.add(new LogEntry("DEBUG", msg));
  }

  @Override
  public void debug(Marker marker, String format, Object arg) {
    logEntries.add(new LogEntry("DEBUG", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void debug(Marker marker, String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("DEBUG", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void debug(Marker marker, String format, Object... arguments) {
    logEntries.add(new LogEntry("DEBUG", formatWithArray(format, arguments)));
  }

  @Override
  public void debug(Marker marker, String msg, Throwable t) {
    logEntries.add(new LogEntry("DEBUG", msg, t));
  }

  @Override
  public boolean isInfoEnabled() {
    return true;
  }

  @Override
  public void info(String msg) {
    logEntries.add(new LogEntry("INFO", msg));
  }

  @Override
  public void info(String format, Object arg) {
    logEntries.add(new LogEntry("INFO", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void info(String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("INFO", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void info(String format, Object... arguments) {
    logEntries.add(new LogEntry("INFO", formatWithArray(format, arguments)));
  }

  @Override
  public void info(String msg, Throwable t) {
    logEntries.add(new LogEntry("INFO", msg, t));
  }

  @Override
  public boolean isInfoEnabled(Marker marker) {
    return true;
  }

  @Override
  public void info(Marker marker, String msg) {
    logEntries.add(new LogEntry("INFO", msg));
  }

  @Override
  public void info(Marker marker, String format, Object arg) {
    logEntries.add(new LogEntry("INFO", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void info(Marker marker, String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("INFO", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void info(Marker marker, String format, Object... arguments) {
    logEntries.add(new LogEntry("INFO", formatWithArray(format, arguments)));
  }

  @Override
  public void info(Marker marker, String msg, Throwable t) {
    logEntries.add(new LogEntry("INFO", msg, t));
  }

  @Override
  public boolean isWarnEnabled() {
    return true;
  }

  @Override
  public void warn(String msg) {
    logEntries.add(new LogEntry("WARN", msg));
  }

  @Override
  public void warn(String format, Object arg) {
    logEntries.add(new LogEntry("WARN", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void warn(String format, Object... arguments) {
    logEntries.add(new LogEntry("WARN", formatWithArray(format, arguments)));
  }

  @Override
  public void warn(String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("WARN", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void warn(String msg, Throwable t) {
    logEntries.add(new LogEntry("WARN", msg, t));
  }

  @Override
  public boolean isWarnEnabled(Marker marker) {
    return true;
  }

  @Override
  public void warn(Marker marker, String msg) {
    logEntries.add(new LogEntry("WARN", msg));
  }

  @Override
  public void warn(Marker marker, String format, Object arg) {
    logEntries.add(new LogEntry("WARN", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void warn(Marker marker, String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("WARN", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void warn(Marker marker, String format, Object... arguments) {
    logEntries.add(new LogEntry("WARN", formatWithArray(format, arguments)));
  }

  @Override
  public void warn(Marker marker, String msg, Throwable t) {
    logEntries.add(new LogEntry("WARN", msg, t));
  }

  @Override
  public boolean isErrorEnabled() {
    return true;
  }

  @Override
  public void error(String msg) {
    logEntries.add(new LogEntry("ERROR", msg));
  }

  @Override
  public void error(String format, Object arg) {
    logEntries.add(new LogEntry("ERROR", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void error(String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("ERROR", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void error(String format, Object... arguments) {
    logEntries.add(new LogEntry("ERROR", formatWithArray(format, arguments)));
  }

  @Override
  public void error(String msg, Throwable t) {
    logEntries.add(new LogEntry("ERROR", msg, t));
  }

  @Override
  public boolean isErrorEnabled(Marker marker) {
    return true;
  }

  @Override
  public void error(Marker marker, String msg) {
    logEntries.add(new LogEntry("ERROR", msg));
  }

  @Override
  public void error(Marker marker, String format, Object arg) {
    logEntries.add(new LogEntry("ERROR", String.format(format.replace("{}", "%s"), arg)));
  }

  @Override
  public void error(Marker marker, String format, Object arg1, Object arg2) {
    logEntries.add(new LogEntry("ERROR", String.format(format.replace("{}", "%s"), arg1, arg2)));
  }

  @Override
  public void error(Marker marker, String format, Object... arguments) {
    logEntries.add(new LogEntry("ERROR", formatWithArray(format, arguments)));
  }

  @Override
  public void error(Marker marker, String msg, Throwable t) {
    logEntries.add(new LogEntry("ERROR", msg, t));
  }

  private String formatWithArray(String format, Object[] arguments) {
    if (arguments == null || arguments.length == 0) {
      return format;
    }

    String formattedString = format;
    for (Object arg : arguments) {
      formattedString = formattedString.replaceFirst("\\{\\}", String.valueOf(arg));
    }
    return formattedString;
  }

  public static class LogEntry {
    private final String level;
    private final String message;
    private final Throwable throwable;

    public LogEntry(String level, String message) {
      this(level, message, null);
    }

    public LogEntry(String level, String message, Throwable throwable) {
      this.level = level;
      this.message = message;
      this.throwable = throwable;
    }

    public String getLevel() {
      return level;
    }

    public String getMessage() {
      return message;
    }

    public Throwable getThrowable() {
      return throwable;
    }
  }
}
