package com.nexla.telemetry.dto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QuietAutoClosable implements AutoCloseable {
  private static final Logger LOGGER = LoggerFactory.getLogger(QuietAutoClosable.class);
  private final AutoCloseable autoCloseable;

  public QuietAutoClosable(AutoCloseable autoCloseable) {
    this.autoCloseable = autoCloseable;
  }

  @Override
  public void close() {
    try {
      autoCloseable.close();
    } catch (Exception e) {
      LOGGER.error("Failed to close AutoCloseable", e);
    }
  }
}
