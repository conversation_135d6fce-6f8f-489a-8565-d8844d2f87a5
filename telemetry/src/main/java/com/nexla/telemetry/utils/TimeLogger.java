package com.nexla.telemetry.utils;

import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;

/**
 * Utility class to be used to log execution time.
 * Usage example:
 *   try (TimeLogger ctx = new TimeLogger(logger, "Message")) {
 *     <code execution>
 *   }
 */
public class TimeLogger implements AutoCloseable {
  private final String message;
  private final Logger logger;
  private final StopWatch stopWatch;

  public TimeLogger(Logger logger, String message) {
    this.message = message;
    this.logger = logger;
    this.stopWatch = new StopWatch();
    logger.debug("Starting to {}", message);
    stopWatch.start();
  }

  @Override
  public void close() {
    stopWatch.stop();
    logger.info("{} TIME={} ({} millis)", message, stopWatch, stopWatch.getTime());
  }
}
