package com.nexla.cloud.job.connector.pipeline

import akka.Done
import akka.stream.KillSwitches
import com.nexla.admin.client.AdminApiClient
import com.nexla.cloud.job.connector.cloud.CloudProvider
import com.nexla.cloud.job.connector.compat.{<PERSON><PERSON><PERSON>, PipelineRunContext, PipelineStats}
import com.nexla.cloud.job.connector.infra.ClusterTracker
import com.nexla.cloud.job.connector.job.JobSender
import com.nexla.cloud.job.connector.{AppP<PERSON>, MetricsHelper, Utils}
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{ConnectionType, NexlaConstants, Resource, ResourceType}
import com.nexla.connector.config.FlowType
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import com.nexla.sc.client.listing.{FileStatuses, ListedFile, ListingAppClient}
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.slf4j.MDC

import java.time.{LocalDateTime, ZoneId}
import java.util.concurrent.atomic.AtomicReference
import scala.concurrent.duration.{Duration, MILLISECONDS}
import scala.concurrent.{Await, ExecutionContext, Future, Promise}
import scala.util.Try

case class ClusterAndLastOperationTs(srcId: Int, sinkId: Int, clusterId: String, lastOperationTs: Option[LocalDateTime],
                                     lastStatus: String, cloudProvider: Option[CloudProvider]) {
  override def toString: TaskId = {
    s"src-$srcId-sink-$sinkId-cluster-id-[$clusterId], last ts: [${lastOperationTs.toString}], status: [$lastStatus]"
  }
}

class CloudJobPipeline(taskId: TaskId,
                       runId: Long, srcId: Int,
                       @Deprecated sinkId: Int, clusterAuthConfig: BaseAuthConfig, adminApiClient: AdminApiClient,
                       listingClient: ListingAppClient,
                       appMessageProducer: ControlMessageProducer,
                       cloudProvider: CloudProvider, jobSender: JobSender, clusterTracker: ClusterTracker,
                       metricsHelper: MetricsHelper,
                       props: AppProps) (implicit ec: ExecutionContext)

  extends Pipeline(taskId, runId, srcId, sinkId, appMessageProducer, adminApiClient)
    with ConfigEnricher
    with WithLogging
    with StrictNexlaLogging {

  override val pipelineType: FlowType = FlowType.SPARK
  val MINIMAL_DEFAULT_FILES_AT_ONCE: Int = 5
  val FILES_IN_BATCH = "files.in.batch"

  private val howManyFilesAtOnce: Int = Option(ctx.dataSource.getSourceConfig.get(FILES_IN_BATCH))
    .map(_.toString).flatMap(maybeInt => Try(maybeInt.toInt).toOption).getOrElse(MINIMAL_DEFAULT_FILES_AT_ONCE)

  import ctx._
  val assignedCluster = new AtomicReference[ClusterAndLastOperationTs]()
  val clusterLookup = new ClusterLookup(props, adminApiClient, appMessageProducer, cloudProvider, assignedCluster)

  def isSuccess(status: String): Boolean = {
    status.equalsIgnoreCase("Completed") || // emr case
      status.equalsIgnoreCase("Succeeded") // databricks case
  }

  def handleNoHive(actualClusterId: String, promise: Promise[Done], pipelineStats: PipelineStats): Unit = {
    val adminApiServer = props.apiCredentialsServer

    val sentStepId = Try(
      jobSender.sendJob(actualClusterId, sinkId, String.valueOf(howManyFilesAtOnce), adminApiServer, runId)
    ).toOption

    if (sentStepId.isEmpty) {
      val exc = new IllegalArgumentException("attempt to send the job was not successful, removing the stored cluster")
      logger.warn("sent step id is empty", exc)
      clusterTracker.delete(ClusterAndLastOperationTs(
        ctx.sourceId,
        sinkId,
        actualClusterId,
        Option(LocalDateTime.now()),
        Utils.IN_USE,
        Option(cloudProvider)))
      promise.success(Done)
      return
    }

    pipelineStats.markSourceAsNonEmpty()
    val logMsg = "Spark Job launched on remote cluster"
    logger.info(logMsg)

    val msg = Utils.prepareFlowInsightsMsg(
      dataSource.getOrg.getId, logMsg,
      runId,
      new Resource(dataSource.getId, ResourceType.SOURCE),
      NexlaMonitoringLogSeverity.INFO)
    appMessageProducer.publishMonitoringLog(msg)

    val jobTerminalState = Await.result(jobSender.trackUntilTerminalState(sentStepId.get, actualClusterId), Duration.Inf)
    logger.info(s"${ctx.readLog} came back after tracking job terminal state")

    val logMsg2 = "Spark Job finished running on remote cluster"
    logger.info(logMsg2)

    val msg2 = Utils.prepareFlowInsightsMsg(
      dataSource.getOrg.getId, logMsg2,
      runId,
      new Resource(dataSource.getId, ResourceType.SOURCE),
      NexlaMonitoringLogSeverity.INFO)
    appMessageProducer.publishMonitoringLog(msg2)

    if (isSuccess(jobTerminalState.status)) {
      // the step above will take some time
      // then, metrics are calculated directly in the cloud and then sent back to Nexla via http-gateway module
      // finally, let's mark everything as "done" here
      pipelineStats.markSinkAsNonEmpty()
      promise.success(Done)
    } else {
      // todo: get actual stacktrace from cloud provider
      logger.error(s"${ctx.readLog} pipeline failed, finishing the promise not to hang")
      promise.success(Done)
    }
  }

  def handle(actualClusterId: String, resultFromListing: Option[ListedFile], promise: Promise[Done], pipelineStats: PipelineStats): Unit = {
    logger.info(s"${ctx.readLog} Got following result from listing app: [$resultFromListing], starting processing")
    if (resultFromListing.isEmpty) {
      logger.warn("Result is empty, stopping")
      return
    }
    val listedFile = resultFromListing.get
    val sourcePath = ctx.dataSource.getSourceConfig.get("path").toString

    val idAndPartitionToHandle = Utils.preparePath(ctx.dataSource.getConnectionType, sourcePath, listedFile)
    val adminApiServer = props.apiCredentialsServer
    val start = System.currentTimeMillis()
    Option(assignedCluster.get()).foreach { someCluster =>
      logger.info("Assigned cluster present, updating its status to IN_USE")
      clusterTracker.updateInTrackList(someCluster.copy(lastStatus = Utils.IN_USE))
    }
    val sentStepId = Try(
      jobSender.sendJob(actualClusterId, ctx.dataSinks.head._1, idAndPartitionToHandle, adminApiServer, runId)
    ).toOption
    val end = System.currentTimeMillis()
    metricsHelper.gaugeTaskAssignment(cloudProvider.cloudName, end - start)

    if (sentStepId.isEmpty) {
      val exc = new IllegalArgumentException("attempt to send the job was not successful")
      logger.warn("sent step id is empty", exc)
      clusterTracker.updateInTrackList(ClusterAndLastOperationTs(
        ctx.sourceId,
        ctx.dataSinks.head._1,
        actualClusterId,
        Option(LocalDateTime.now()),
        Utils.IDLE,
        Option(cloudProvider)))
      promise.success(Done)
      return
    }

    pipelineStats.markSourceAsNonEmpty()
    val beforeJobStart = System.currentTimeMillis()
    val logMsg = "Spark Job launched on remote cluster"
    logger.info(logMsg)

    val msg = Utils.prepareFlowInsightsMsg(
      dataSource.getOrg.getId, logMsg,
      runId,
      new Resource(dataSource.getId, ResourceType.SOURCE),
      NexlaMonitoringLogSeverity.INFO)
    appMessageProducer.publishMonitoringLog(msg)

    val jobTerminalState = Await.result(jobSender.trackUntilTerminalState(sentStepId.get, actualClusterId), Duration.Inf)
    val afterJobFinish = System.currentTimeMillis()
    metricsHelper.gaugeTaskRunTime(afterJobFinish - beforeJobStart)
    logger.info(s"${ctx.readLog} came back after tracking job terminal state")

    val logMsg2 = "Spark Job finished running on remote cluster"
    logger.info(logMsg2)

    val msg2 = Utils.prepareFlowInsightsMsg(
      dataSource.getOrg.getId, logMsg2,
      runId,
      new Resource(dataSource.getId, ResourceType.SOURCE),
      NexlaMonitoringLogSeverity.INFO)
    appMessageProducer.publishMonitoringLog(msg2)

    Option(assignedCluster.get()).foreach { a => clusterTracker.updateInTrackList(a.copy(lastStatus = Utils.IDLE))}
    if (isSuccess(jobTerminalState.status)) {
      // the step above will take some time
      // then, metrics are calculated directly in the cloud and then sent back to Nexla via http-gateway module
      // finally, let's mark everything as "done" here
      listingClient.setFileStatus(sourceId, resultFromListing.get.id, FileStatuses.Done, None, Option("Hive partition processed"))
      pipelineStats.markSinkAsNonEmpty()
      metricsHelper.incrementSuccessfulJobRuns()
      promise.success(Done)
    } else {
      // todo: get actual stacktrace from cloud provider
      logger.error(s"${ctx.readLog} pipeline failed, finishing the promise not to hang")
      metricsHelper.incrementFailedJobRuns()
      promise.success(Done)
    }
  }

  def startPipeline(pipelineStats: PipelineStats): PipelineRunContext = {
    MDC.put("taskId", taskId)
    val killSwitch = KillSwitches.shared("kill-switch_" + dataSource.getId)
    val newTasksKillSwitch = KillSwitches.shared("new-tasks_" + dataSource.getId)
    val actualClusterId = clusterLookup.getOrCreateCluster(ctx, clusterTracker)

    val clusterSecretManager = new ClusterSecretManager(props.decryptKey, cloudProvider.cloudName,
      ctx.dataSource.getConnectionType, actualClusterId, clusterAuthConfig)

    clusterSecretManager.initializeClusterSecrets(dataSource, ctx.dataSinks.head._2)

    val promise = Promise[Done]()
    val pipelineFut = promise.future

    if (dataSource.getConnectionType.isFile) {
      // ! direct accessing the source config map,
      // ! to use the constructor we would need more enrichment than is necessary for the use case
      val ageOut = Option(dataSource.getSourceConfig.get(FileSourceConnectorConfig.IGNORE_FILES_OLDER_THAN_MS))

      // hive, non-hive
      val responseFromListing = listingClient.listFiles(
        sourceId,
        Option.apply(List(FileStatuses.New, FileStatuses.Stopped)),
        createdAtFrom = ageOut.map(age => LocalDateTime.now(ZoneId.of("UTC"))
          .minusNanos(MILLISECONDS.toNanos(age.asInstanceOf[String].toLong))))

      val fileListingFuture = responseFromListing.map {
        resultFromListing =>
          if (resultFromListing.isDefined) {
            val listedFileOption = Option.apply(resultFromListing.get.head)
            val hivePartition = listedFileOption.exists { listedFile =>
              if (listedFile.metadata.nonEmpty) {
                listedFile.metadata.get.contains(NexlaConstants.HIVE_PARTITION)
              } else {
                false
              }
            }

            try {
              if (hivePartition) {
                val logMsg = "Apache Hive-compatible Spark pipeline detected, attempting to run Nexla Spark agent. Cluster will be spawned if necessary."
                logger.info(logMsg)
                handle(actualClusterId, listedFileOption, promise, pipelineStats)
                val msg = Utils.prepareFlowInsightsMsg(
                  dataSource.getOrg.getId, logMsg,
                  runId,
                  new Resource(dataSource.getId, ResourceType.SOURCE),
                  NexlaMonitoringLogSeverity.INFO)
                appMessageProducer.publishMonitoringLog(msg)
              } else {
                val logMsg = "Per-file (aka no Apache Hive) Spark pipeline detected, attempting to run Nexla Spark agent. Cluster will be spawned if necessary."
                logger.info(logMsg)

                val msg = Utils.prepareFlowInsightsMsg(
                  dataSource.getOrg.getId, logMsg,
                  runId,
                  new Resource(dataSource.getId, ResourceType.SOURCE),
                  NexlaMonitoringLogSeverity.INFO)
                appMessageProducer.publishMonitoringLog(msg)

                handleNoHive(actualClusterId, promise, pipelineStats)
                PipelineRunContext(killSwitch, newTasksKillSwitch, pipelineFut, ctx)
              }
            } catch {
              case e: Exception =>
                logger.error(s"${ctx.writeLog} Error during handling pipeline", e)
                metricsHelper.incrementFailedJobRuns()
                throw e
            }
          } else {
            logger.info(s"${ctx.readLog} No files found in response from listing, nothing to process, stopping the pipeline")
            metricsHelper.incrementEmptyJobRuns()
            promise.success(Done)
          }
      }

      metricsHelper.incrementActivePipelines()
      Await.result(fileListingFuture, Duration.Inf)
    } else if (dataSource.getConnectionType.equals(ConnectionType.DATABRICKS)) {
      try {
        handleNoHive(actualClusterId, promise, pipelineStats)
        PipelineRunContext(killSwitch, newTasksKillSwitch, pipelineFut, ctx)
      } catch {
        case e: Exception =>
          logger.error(s"${ctx.writeLog} Error during handling Databricks pipeline", e)
          metricsHelper.incrementFailedJobRuns()
          throw e
      }
    } else {
      throw new IllegalArgumentException(s"Only Databricks, file and Apache Hive sources are supported. Current source type: ${dataSource.getConnectionType}")
    }

    PipelineRunContext(killSwitch, newTasksKillSwitch, pipelineFut, ctx)
  }

  override def stop(): Future[Unit] = {
    if (assignedCluster.get() != null) {
      logger.debug("Putting cluster back to IDLE state, it will be stopped after TTL")
      clusterTracker.updateInTrackList(assignedCluster.get().copy(lastStatus = Utils.IDLE))
    } else {
      logger.debug("Assigned cluster is null, most probably we were using external cluster or nothing was spawned for managed")
    }
    metricsHelper.decrementCloudAPIConnections()
    metricsHelper.decrementActivePipelines()
    super.stop()
  }

}
