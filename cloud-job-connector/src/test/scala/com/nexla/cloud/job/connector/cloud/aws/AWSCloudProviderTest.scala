package com.nexla.cloud.job.connector.cloud.aws

import com.nexla.cloud.job.connector.MetricsHelper
import com.nexla.connector.config.rest.BaseAuthConfig
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.{any, anyLong, anyString}
import org.mockito.Mockito._
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite
import software.amazon.awssdk.services.emr.EmrClient
import software.amazon.awssdk.services.emr.model.{Cluster, ClusterState, ClusterStatus, DescribeClusterRequest, DescribeClusterResponse, HadoopStepConfig, ListStepsRequest, ListStepsResponse, RunJobFlowRequest, RunJobFlowResponse, StepState, StepStatus, StepSummary, TerminateJobFlowsRequest}
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import software.amazon.awssdk.services.secretsmanager.model.{GetSecretValueRequest, GetSecretValueResponse}

import scala.collection.JavaConverters._
import java.util

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class AWSCloudProviderTest extends AnyFunSuite {
  val logTag = "AWSCloudProviderTest"
  val baseAuthCfg: BaseAuthConfig = {
    val emptyMap = new util.HashMap[String, String]()
    new BaseAuthConfig(BaseAuthConfig.baseAuthConfigDef, emptyMap, 1)
  }

  val mockEmrClient: EmrClient = mock(classOf[EmrClient])
  val stubCfgMap: Map[String, String] = Map(
    "cloud.region" -> "example",
    "cloud.cluster.vm.subnet" -> "my-subnet",
    "cloud.role.with.cluster.access" -> "stub",
    "cloud.cluster.service.role" -> "stub2",
    "cloud.cluster.entity.role" -> "stub3",
    "cloud.jar.location" -> "s3://foo/bar/baz.jar"
  )

  test("cluster termination") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient)
    )
    val clusterName = "mock-cluster"
    val expectedReq = TerminateJobFlowsRequest.builder()
      .jobFlowIds(List(clusterName).asJava)

    awsCloudProvider.stopCluster(clusterName)
    verify(mockEmrClient).terminateJobFlows(expectedReq.build())
    verify(mockMetricsHelper).incrementClusterTerminates()
  }

  test("cluster creation") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val secrets = mock(classOf[SecretsManagerClient])
    val stubResult = GetSecretValueResponse.builder().secretString("stub").build()
    when(secrets.getSecretValue(ArgumentMatchers.any[GetSecretValueRequest]())).thenReturn(stubResult)
    val mockS3 = mock(classOf[S3Client])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient),
      Option(secrets),
      Option(mockS3)
    )

    val clusterStatus = ClusterStatus.builder()
      .state(ClusterState.WAITING).build()
    val cluster = Cluster.builder()
      .status(clusterStatus).build()
    val response = RunJobFlowResponse.builder()
      .jobFlowId("creating-cluster").build()
    val describeClusterResult = DescribeClusterResponse.builder()
      .cluster(cluster)

    when(mockEmrClient.runJobFlow(any[RunJobFlowRequest]())).thenReturn(response)
    when(mockEmrClient.describeCluster(any[DescribeClusterRequest]())).thenReturn(describeClusterResult.build())

    awsCloudProvider.createCluster()
    verify(mockEmrClient).runJobFlow(any[RunJobFlowRequest]())
    verify(mockMetricsHelper).incrementClusterSpawns()
    verify(mockMetricsHelper).gaugeClusterCreation(anyString(), anyLong())
  }

  test("check live cluster state") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient)
    )

    val clusterName = "live-cluster"
    val descrReq = DescribeClusterRequest.builder()
      .clusterId(clusterName).build()

    val clusterStatus = ClusterStatus.builder()
      .state(ClusterState.WAITING).build()
    val stepStatus = StepStatus.builder()
      .state(StepState.RUNNING).build()

    val hadoopStepConfig = HadoopStepConfig.builder()
      .args(List("9999", "a-partition", "env", "2131231").asJavaCollection).build()

    val cluster = Cluster.builder()
      .name(clusterName)
      .status(clusterStatus)
      .build()

    val result = DescribeClusterResponse
      .builder().cluster(cluster).build()

    val stepSummary = StepSummary.builder()
      .status(stepStatus)
      .name("mock-step")
      .config(hadoopStepConfig).build()

    val listStepResults = ListStepsResponse.builder()
      .steps(List(stepSummary).asJava).build()

    when(mockEmrClient.listSteps(any[ListStepsRequest]())).thenReturn(listStepResults)
    when(mockEmrClient.describeCluster(descrReq)).thenReturn(result)
    val checkResult = awsCloudProvider.checkExistingCluster(clusterName)
    verify(mockEmrClient, atLeast(1)).describeCluster(any[DescribeClusterRequest]())
    assert(checkResult.jobs.size == 1)
    assert(checkResult.jobs.head.status.equals("RUNNING"))
    assert(checkResult.jobs.head.sinkId.equals(9999))
    assert(checkResult.jobs.head.partitionInfo.equals(List("a-partition")))
    assert(checkResult.jobs.head.runId.equals(2131231L))
  }

  test("check dead cluster state") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val awsCloudProvider: AWSCloudProvider = AWSCloudProvider(mockMetricsHelper, logTag,
      baseAuthCfg,
      stubCfgMap,
      stubCfgMap,
      "stubKey",
      Option(mockEmrClient)
    )
    val clusterName = "dead-cluster"
    val clusterStatus = ClusterStatus.builder()
      .state(ClusterState.TERMINATED_WITH_ERRORS).build()

    val descrReq = DescribeClusterRequest.builder()
      .clusterId(clusterName).build()
    val cluster = Cluster.builder()
      .name(clusterName)
      .status(clusterStatus).build()
    val result = DescribeClusterResponse.builder()
      .cluster(cluster).build()

    when(mockEmrClient.describeCluster(descrReq)).thenReturn(result)
    val caught = intercept[IllegalArgumentException] {
      awsCloudProvider.checkExistingCluster(clusterName)
    }
    assert(caught.getMessage.toLowerCase.contains("unusable"))
  }

}
