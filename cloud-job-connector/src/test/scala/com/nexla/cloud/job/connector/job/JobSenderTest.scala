package com.nexla.cloud.job.connector.job


import com.google.common.hash.Hashing
import com.nexla.cloud.job.connector.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TestStuff}
import com.nexla.cloud.job.connector.cloud.aws.AWSCloudProvider
import com.nexla.cloud.job.connector.compat.PipelineContext
import com.nexla.connector.config.rest.BaseAuthConfig
import com.typesafe.scalalogging.LazyLogging
import org.mockito.ArgumentMatchers.{any, anyLong}
import org.mockito.Mockito.{mock, times, verify, when}
import org.mockito.{ArgumentMatchers, Mockito}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite
import software.amazon.awssdk.services.emr.EmrClient
import software.amazon.awssdk.services.emr.model.{DescribeClusterRequest, DescribeStepRequest, ListStepsRequest, StepState}
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.{HeadObjectRequest, HeadObjectResponse}
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import software.amazon.awssdk.services.secretsmanager.model.{ListSecretsRequest, ListSecretsResponse, SecretListEntry}

import java.io.File
import java.util.Collections
import java.util.concurrent.{ExecutorService, Executors}
import scala.collection.JavaConverters._
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext}

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class JobSenderTest extends AnyFunSuite with LazyLogging {

  val logTag = "JobSenderTest"
  val execService: ExecutorService = Executors.newSingleThreadExecutor()
  implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(execService)

  val ctx: PipelineContext = Mockito.mock(classOf[PipelineContext])
  val clusterName: String = "job-sender-unit-test"

  when(ctx.readLog).thenReturn("[unit-test][read-123]")
  when(ctx.writeLog).thenReturn("[unit-test][write-456]")

  test("get job state should fail after max retries are exceeded") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val jobId = "job-which-got-stuck"
    val mockS3: S3Client = mock(classOf[S3Client])
    val mockEmr: EmrClient = mock(classOf[EmrClient])
    val mockSecrets = mock(classOf[SecretsManagerClient])
    val stubSecr = SecretListEntry.builder().name("stub").build()
    val stubResult = ListSecretsResponse.builder().secretList(Collections.singletonList(stubSecr)).build()
    when(mockSecrets.listSecrets(ArgumentMatchers.any[ListSecretsRequest]())).thenReturn(stubResult)

    val cloudProvider = new AWSCloudProvider(mockMetricsHelper, logTag, mock(classOf[BaseAuthConfig]),
      TestStuff.getExampleSourceConfig.asScala.toMap, TestStuff.getExampleSourceConfig.asScala.toMap,
      Option(mockEmr),
      Option(mockSecrets),
      Option(mockS3),
      1, 5, "stub")
    when(mockEmr.describeCluster(ArgumentMatchers.any[DescribeClusterRequest]())).thenReturn(TestStuff.stubRawClusterResponse(clusterName))
    when(mockEmr.describeStep(ArgumentMatchers.any[DescribeStepRequest]())).thenReturn(TestStuff.stubRawDescribeStepResult(
      jobId, StepState.RUNNING, 1, "2", 3
    ))

    val jobSender: JobSender = JobSender(cloudProvider, ctx.readLog, mockMetricsHelper)

    assertThrows[com.github.rholder.retry.RetryException] {
      val future = jobSender.trackUntilTerminalState(jobId, clusterName)
      Await.result(future, Duration.Inf)
    }
    verify(mockMetricsHelper).gaugeTaskRunTime(anyLong())
  }

  test("internal retry count is reset") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val reusableJobId = "reusable-job-id"
    val mockS3: S3Client = mock(classOf[S3Client])
    val mockEmr: EmrClient = mock(classOf[EmrClient])
    val mockSecrets = mock(classOf[SecretsManagerClient])
    val stubSecr = SecretListEntry.builder().name("stub").build()
    val stubResult = ListSecretsResponse.builder().secretList(Collections.singletonList(stubSecr)).build()
    when(mockSecrets.listSecrets(ArgumentMatchers.any[ListSecretsRequest]())).thenReturn(stubResult)

    val cloudProvider = new AWSCloudProvider(mockMetricsHelper, logTag, mock(classOf[BaseAuthConfig]),
      TestStuff.getExampleSourceConfig.asScala.toMap,
      TestStuff.getExampleSourceConfig.asScala.toMap,
      Option(mockEmr), Option(mockSecrets), Option(mockS3),
      1, 5, "stub")
    when(mockEmr.describeCluster(ArgumentMatchers.any[DescribeClusterRequest]())).thenReturn(TestStuff.stubRawClusterResponse(clusterName))
    val jobSender: JobSender = JobSender(cloudProvider, ctx.readLog, mockMetricsHelper)
    // jobSender is created for a single cloud job pipeline.
    // let our job be running, then still running, then stopped - and then repeating the same behavior again.
    // theoretically it's impossible (the job id would be another one, we can not reuse it), but this is valid for AWS.
    // other providers may have another kind of behavior for this, this test makes sure our internal retry count is reset.
    when(mockEmr.describeStep(ArgumentMatchers.any[DescribeStepRequest]()))
      .thenReturn(TestStuff.stubRawDescribeStepResult(reusableJobId, StepState.RUNNING, 1,"2",3))
      .thenReturn(TestStuff.stubRawDescribeStepResult(reusableJobId, StepState.COMPLETED, 1,"2",3))
      .thenReturn(TestStuff.stubRawDescribeStepResult(reusableJobId, StepState.RUNNING, 1,"2",3))
      .thenReturn(TestStuff.stubRawDescribeStepResult(reusableJobId, StepState.COMPLETED, 1,"2",3))

    val futureFirst = jobSender.trackUntilTerminalState(reusableJobId, clusterName)
    val shouldBeOkFirstTime = Await.result(futureFirst, Duration.Inf)
    assert(shouldBeOkFirstTime.status.equalsIgnoreCase("COMPLETED"))

    val futureSecond = jobSender.trackUntilTerminalState(reusableJobId, clusterName)
    val shouldBeOkSecondTime = Await.result(futureSecond, Duration.Inf)
    assert(shouldBeOkSecondTime.status.equalsIgnoreCase("COMPLETED"))

    // 2 job sends, 2 times incremented
    verify(mockMetricsHelper, times(2)).gaugeTaskRunTime(anyLong())
  }

  test("get job state should get state after trying 2 times") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val expectedJobId = "job-id-2"
    val expectedStatus = "INTERRUPTED"
    val mockS3: S3Client = mock(classOf[S3Client])
    val mockSecrets = mock(classOf[SecretsManagerClient])
    val stubSecr = SecretListEntry.builder().name("stub").build()
    val stubResult = ListSecretsResponse.builder().secretList(Collections.singletonList(stubSecr)).build()
    when(mockSecrets.listSecrets(ArgumentMatchers.any[ListSecretsRequest]())).thenReturn(stubResult)
    val mockEmr: EmrClient = mock(classOf[EmrClient])
    val cloudProvider = new AWSCloudProvider(mockMetricsHelper, logTag, mock(classOf[BaseAuthConfig]),
      TestStuff.getExampleSourceConfig.asScala.toMap,
      TestStuff.getExampleSourceConfig.asScala.toMap,
      Option(mockEmr), Option(mockSecrets), Option(mockS3),
      1, 5, "stub")
    when(mockEmr.describeCluster(ArgumentMatchers.any[DescribeClusterRequest]())).thenReturn(TestStuff.stubRawClusterResponse(clusterName))
    val jobSender: JobSender = JobSender(cloudProvider, ctx.readLog, mockMetricsHelper)

    when(mockEmr.describeStep(ArgumentMatchers.any[DescribeStepRequest]()))
      .thenReturn(TestStuff.stubRawDescribeStepResult(expectedJobId, StepState.RUNNING, 1,"2",3))
      .thenReturn(TestStuff.stubRawDescribeStepResult(expectedJobId, StepState.RUNNING, 1,"2",3))
      .thenReturn(TestStuff.stubRawDescribeStepResult(expectedJobId, StepState.INTERRUPTED, 1,"2",3))

    val future = jobSender.trackUntilTerminalState(expectedJobId, clusterName)

    val result = Await.result(future, Duration.Inf)
    assert(result.jobId.equalsIgnoreCase(expectedJobId))
    assert(result.status.equalsIgnoreCase(expectedStatus))
    verify(mockMetricsHelper).gaugeTaskRunTime(anyLong())
  }

//  private def getMd5(file: File): String = {
//    val byteSource = com.google.common.io.Files.asByteSource(file)
//    val hc = byteSource.hash(Hashing.md5)
//    hc.toString.toLowerCase
//  }

  // disabled - works fine, but when running from maven, cannot find the local file. disabling for now.
//  test("job should be sent to the cloud") {
//    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
//    val sinkId = 2321
//    val partition = "a-partition"
//    val env = "unit-test"
//    val runId = 2L
//    val jobIdToReturn = "just-created-another-job-id"
//    val mockS3: S3Client = mock(classOf[S3Client])
//    val LOCAL_JAR_FILE_PATH = System.getProperty("user.dir") + "/cloud-job-connector/target/nexla-spark-agent.jar"
//    val md5Local = getMd5(new File(LOCAL_JAR_FILE_PATH))
//    when(mockS3.headObject(any[HeadObjectRequest]())).thenReturn(HeadObjectResponse.builder().eTag(md5Local).build())
//    val mockSecrets = mock(classOf[SecretsManagerClient])
//    val stubSecr = SecretListEntry.builder().name("stub").build()
//    val stubResult = ListSecretsResponse.builder().secretList(Collections.singletonList(stubSecr)).build()
//    when(mockSecrets.listSecrets(ArgumentMatchers.any[ListSecretsRequest]())).thenReturn(stubResult)
//    val mockEmr: EmrClient = mock(classOf[EmrClient])
//    val cloudProvider = new AWSCloudProvider(mockMetricsHelper, logTag, mock(classOf[BaseAuthConfig]),
//      TestStuff.getExampleSourceConfig.asScala.toMap,
//      TestStuff.getExampleSourceConfig.asScala.toMap,
//      Option(mockEmr), Option(mockSecrets), Option(mockS3),
//      1, 5, "stub")
//    when(mockEmr.describeCluster(ArgumentMatchers.any[DescribeClusterRequest]())).thenReturn(TestStuff.stubRawClusterResponse(clusterName))
//
//    val jobSender: JobSender = JobSender(cloudProvider, ctx.readLog, mockMetricsHelper)
//
//    when(mockEmr.listSteps(ArgumentMatchers.any[ListStepsRequest]())).thenReturn(
//      TestStuff.stubRawStepResponse(jobIdToReturn, sinkId, runId, partition)
//    )
//
//    val sendingResult = jobSender.sendJob(clusterName, sinkId, partition, env, runId)
//    assert(jobIdToReturn.equalsIgnoreCase(sendingResult))
//
//    verify(mockMetricsHelper).incrementTaskAssignments()
//  }

  test("attempt to get the status of job which doesn't exist") {
    val mockMetricsHelper: MetricsHelper = mock(classOf[MetricsHelper])
    val mockS3: S3Client = mock(classOf[S3Client])
    val mockEmr: EmrClient = mock(classOf[EmrClient])
    val mockSecrets = mock(classOf[SecretsManagerClient])
    val stubSecr = SecretListEntry.builder().name("stub").build()
    val stubResult = ListSecretsResponse.builder().secretList(Collections.singletonList(stubSecr)).build()
    when(mockSecrets.listSecrets(ArgumentMatchers.any[ListSecretsRequest]())).thenReturn(stubResult)
    val cloudProvider = new AWSCloudProvider(mockMetricsHelper, logTag, mock(classOf[BaseAuthConfig]),
      TestStuff.getExampleSourceConfig.asScala.toMap,
      TestStuff.getExampleSourceConfig.asScala.toMap,
      Option(mockEmr), Option(mockSecrets), Option(mockS3),
      1, 5, "stub")
    when(mockEmr.describeCluster(ArgumentMatchers.any[DescribeClusterRequest]())).thenReturn(TestStuff.stubRawClusterResponse(clusterName))
    val jobSender: JobSender = JobSender(cloudProvider, ctx.readLog, mockMetricsHelper)

    assertThrows[RuntimeException] {
      Await.result(jobSender.trackUntilTerminalState("where-is-my-job", clusterName), Duration.Inf)
    }

    verify(mockMetricsHelper).gaugeTaskRunTime(anyLong())
  }
}
