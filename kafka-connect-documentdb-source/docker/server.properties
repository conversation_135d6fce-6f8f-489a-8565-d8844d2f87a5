auto.create.topics.enable=true
broker.id=0
delete.topic.enable=true
log.cleaner.enable=true
log.dirs=/var/lib/kafka
log.retention.check.interval.ms=300000
log.retention.hours=168
log.segment.bytes=1073741824
num.io.threads=8
num.network.threads=3
num.partitions=1
num.recovery.threads.per.data.dir=1
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
socket.send.buffer.bytes=102400
zookeeper.connect=localhost:2181
zookeeper.connection.timeout.ms=6000