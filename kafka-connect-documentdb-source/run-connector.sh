#!/bin/sh

export KAFKA_DEBUG=y
export DEBUG_SUSPEND_FLAG=y

export PROJECT_PATH=$NEXLA_BACKEND_BASE/kafka-connect-documentdb-source
export CLASSPATH=$PROJECT_PATH/target/kafka-connect-documentdb-source-0.0.1-SNAPSHOT.jar

echo $NEXLA_BACKEND_BASE
echo $KAFKA_PATH

$KAFKA_PATH/bin/connect-standalone.sh \
    $PROJECT_PATH/example-connect-worker.properties \
    $PROJECT_PATH/example-connect-documentdb-source-timestamp.properties
