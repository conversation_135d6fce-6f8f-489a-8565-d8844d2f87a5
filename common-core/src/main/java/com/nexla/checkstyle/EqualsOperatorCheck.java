package com.nexla.checkstyle;

import com.puppycrawl.tools.checkstyle.api.AbstractCheck;
import com.puppycrawl.tools.checkstyle.api.DetailAST;
import com.puppycrawl.tools.checkstyle.api.TokenTypes;

public class EqualsOperatorCheck extends AbstractCheck {

  private static final String ERROR_MESSAGE =
      "Avoid using '==' for ConnectionType. Use '.equals()' instead.";

  // Variables to keep track of the current class and method
  private String currentClass = "";
  private String currentMethod = "";

  // Verbose logging flag
  private boolean verbose = false;

  public void setVerbose(boolean verbose) {
    this.verbose = verbose;
  }

  private void debugLog(String message) {
    if (verbose) {
      System.out.println(message);
    }
  }

  @Override
  public int[] getDefaultTokens() {
    return new int[] {
      TokenTypes.EQUAL, TokenTypes.NOT_EQUAL, TokenTypes.CLASS_DEF, TokenTypes.METHOD_DEF
    };
  }

  @Override
  public int[] getAcceptableTokens() {
    return new int[] {
      TokenTypes.EQUAL, TokenTypes.NOT_EQUAL, TokenTypes.CLASS_DEF, TokenTypes.METHOD_DEF
    };
  }

  @Override
  public int[] getRequiredTokens() {
    return new int[0];
  }

  @Override
  public void visitToken(DetailAST ast) {
    switch (ast.getType()) {
      case TokenTypes.CLASS_DEF:
        handleClassDef(ast);
        break;
      case TokenTypes.METHOD_DEF:
        handleMethodDef(ast);
        break;
      case TokenTypes.EQUAL:
      case TokenTypes.NOT_EQUAL:
        handleEqualityOperator(ast);
        break;
      default:
        // No action needed for other tokens
        break;
    }
  }

  private void handleClassDef(DetailAST ast) {
    DetailAST classNameAST = ast.findFirstToken(TokenTypes.IDENT);
    currentClass = classNameAST.getText();
    debugLog("Checking class: " + currentClass);
  }

  private void handleMethodDef(DetailAST ast) {
    DetailAST methodNameAST = ast.findFirstToken(TokenTypes.IDENT);
    currentMethod = methodNameAST.getText();
    debugLog("  Checking method: " + currentMethod);
  }

  private void handleEqualityOperator(DetailAST ast) {
    String operator = (ast.getType() == TokenTypes.EQUAL) ? "==" : "!=";
    debugLog("    Found operator: " + operator);

    DetailAST leftOperand = getLeftOperand(ast);
    DetailAST rightOperand = getRightOperand(ast);

    String leftVar = (leftOperand != null) ? leftOperand.getText() : "null";
    String rightVar = (rightOperand != null) ? rightOperand.getText() : "null";

    debugLog("      Left operand: " + leftVar);
    debugLog("      Right operand: " + rightVar);

    boolean leftIsConnectionType = isVariableOfType(leftOperand, "ConnectionType");
    boolean rightIsConnectionType = isVariableOfType(rightOperand, "ConnectionType");

    if (leftIsConnectionType || rightIsConnectionType) {
      log(ast.getLineNo(), ERROR_MESSAGE);
    }
  }

  private DetailAST getLeftOperand(DetailAST ast) {
    DetailAST firstChild = ast.getFirstChild();
    if (firstChild != null) {
      return firstChild;
    }
    return null;
  }

  private DetailAST getRightOperand(DetailAST ast) {
    DetailAST lastChild = ast.getLastChild();
    if (lastChild != null) {
      return lastChild;
    }
    return null;
  }

  @Override
  public void leaveToken(DetailAST ast) {
    switch (ast.getType()) {
      case TokenTypes.CLASS_DEF:
        debugLog("Finished checking class: " + currentClass);
        currentClass = "";
        break;
      case TokenTypes.METHOD_DEF:
        debugLog("  Finished checking method: " + currentMethod);
        currentMethod = "";
        break;
      default:
        // No action needed for other tokens
        break;
    }
  }

  private boolean isVariableOfType(DetailAST operand, String targetType) {
    if (operand == null) {
      return false;
    }

    int type = operand.getType();
    switch (type) {
      case TokenTypes.IDENT:
        // Simple variable, attempt to resolve its type
        return resolveTypeFromIdent(operand, targetType);
      case TokenTypes.METHOD_CALL:
        // Method call, attempt to infer return type
        return resolveTypeFromMethodCall(operand, targetType);
      case TokenTypes.DOT:
        // Field access, attempt to infer type
        return resolveTypeFromFieldAccess(operand, targetType);
      case TokenTypes.LITERAL_TRUE:
      case TokenTypes.LITERAL_FALSE:
      case TokenTypes.NUM_INT:
      case TokenTypes.NUM_FLOAT:
      case TokenTypes.NUM_DOUBLE:
      case TokenTypes.CHAR_LITERAL:
      case TokenTypes.STRING_LITERAL:
        // Literals are not of type ConnectionType
        return false;
      default:
        // Other types can be handled as needed
        return false;
    }
  }

  private DetailAST findVariableDeclaration(DetailAST operand) {
    DetailAST parent = operand.getParent();
    while (parent != null) {
      if (parent.getType() == TokenTypes.VARIABLE_DEF) {
        return parent;
      }
      if (parent.getType() == TokenTypes.CLASS_DEF) {
        // Search for field declarations within the class
        DetailAST variableDef = parent.findFirstToken(TokenTypes.VARIABLE_DEF);
        if (variableDef != null) {
          return variableDef;
        }
      }
      parent = parent.getParent();
    }
    return null;
  }

  private boolean resolveTypeFromIdent(DetailAST ident, String targetType) {
    DetailAST variableDeclaration = findVariableDeclaration(ident);
    if (variableDeclaration != null) {
      DetailAST typeAST = variableDeclaration.findFirstToken(TokenTypes.TYPE);
      if (typeAST != null) {
        String typeName = typeAST.getFirstChild().getText();
        debugLog("        Variable '" + ident.getText() + "' is of type: " + typeName);
        return targetType.equals(typeName);
      }
    } else {
      debugLog("        Variable declaration not found for: " + ident.getText());
    }
    return false;
  }

  private boolean resolveTypeFromMethodCall(DetailAST methodCall, String targetType) {
    DetailAST ident = methodCall.findFirstToken(TokenTypes.IDENT);
    if (ident != null) {
      String methodName = ident.getText();
      // Attempt to infer the return type based on method name
      // This is a heuristic and may need to be customized
      if (methodName.startsWith("get") && methodName.length() > 3) {
        String inferredType = methodName.substring(3); // e.g., getConnectionType -> ConnectionType
        debugLog("        Method '" + methodName + "' inferred return type: " + inferredType);
        return targetType.equals(inferredType);
      }
    }
    return false;
  }

  private boolean resolveTypeFromFieldAccess(DetailAST fieldAccess, String targetType) {
    DetailAST firstChild = fieldAccess.getFirstChild(); // e.g., ConnectionType.BIGQUERY
    DetailAST secondChild = firstChild.getNextSibling(); // The actual field
    if (firstChild != null && secondChild != null && firstChild.getType() == TokenTypes.IDENT) {
      String className = firstChild.getText();
      String fieldName = secondChild.getText();
      // Assuming the field is a constant, like an enum constant
      // Therefore, its type is the class name
      debugLog(
          "        Field '"
              + fieldName
              + "' of class '"
              + className
              + "' is of type: "
              + className);
      return targetType.equals(className);
    }
    return false;
  }
}
