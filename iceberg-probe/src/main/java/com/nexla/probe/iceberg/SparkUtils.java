package com.nexla.probe.iceberg;

import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import org.apache.spark.sql.SparkSession;

public class SparkUtils {
    private final NexlaLogger logger;
    private final SparkSession sparkSession;
    private final IcebergSinkConnectorConfig config;
    public SparkUtils(NexlaLogger logger, SparkSession sparkSession, IcebergSinkConnectorConfig config) {
        this.logger = logger;
        this.sparkSession = sparkSession;
        this.config = config;
    }

    public void execSparkSql(String sql) {
        if (config.logVerbose) {
            logger.info("Executing spark sql: '{}'", sql);
        }
        sparkSession.sql(sql);
    }

    public static String getS3Folder(String rootPath, String database, String tableName) {
        return String.format("%s%s/%s", rootPath, database, tableName);
    }
}
