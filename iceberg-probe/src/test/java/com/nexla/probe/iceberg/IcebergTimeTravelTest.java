package com.nexla.probe.iceberg;

import com.nexla.common.ConnectionType;
import com.nexla.common.probe.ProbeSampleResult;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.spark.sql.SparkSession;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;
import org.testcontainers.utility.MountableFile;

import java.io.InputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;

import static com.nexla.common.NexlaConstants.MONITOR_POLL_MS;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.METRICS_TOPIC;
import static com.nexla.connector.config.SinkConnectorConfig.*;
import static com.nexla.connector.config.file.AWSAuthConfig.SERVICE_ENDPOINT;
import static com.nexla.connector.config.file.FileSinkConnectorConfig.SPARK_SESSION_CONFIGS;
import static com.nexla.connector.config.file.S3Constants.*;
import static com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.TABLE_NAME;
import static com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig.*;
import static com.nexla.connector.properties.FileConfigAccessor.*;
import static com.nexla.probe.iceberg.LocalStackContainerAWSSDK2.Service.S3;

@Category(IntegrationTests.class)
public class IcebergTimeTravelTest {
    private static final String TEST_TABLE_NAME = "warcraft.playable_classes";
    static DockerImageName localstackImage = DockerImageName.parse("localstack/localstack:4.1.1");

    @ClassRule
    public static LocalStackContainerAWSSDK2 localstack = new LocalStackContainerAWSSDK2(localstackImage)
            .withServices(S3)
            .withCopyFileToContainer(MountableFile.forClasspathResource("warcraft", 755), "/warcraft");

    @SneakyThrows
    @BeforeClass
    public static void setup() {
        localstack.execInContainer("awslocal", "s3", "mb", "s3://nexla-data-lake");
        localstack.execInContainer("awslocal", "s3", "cp", "/warcraft", "s3://nexla-data-lake/warcraft", "--recursive");
    }

    @AfterClass
    public static void teardown() {
        SparkSession.active().close();
    }
    
    @SneakyThrows
    @Test
    public void shouldBeAbleToTimeTravelBySnapshotId() {
        SparkHadoopConnectorService connectorService = new SparkHadoopConnectorService();

        var connectorConfig = getSinkConnectorConfigProps();
        connectorConfig.put(AS_OF_SNAPSHOT_ID, "1418469552644939772");
        var fileConnectorConfig = new IcebergSourceConnectorConfig(connectorConfig);

        ProbeSampleResult read = connectorService.readSample(fileConnectorConfig, false);
        Assert.assertEquals(9, read.getData().size());
    }

    @SneakyThrows
    @Test
    public void shouldBeAbleToTimeTravelByTimestamp() {
        SparkHadoopConnectorService connectorService = new SparkHadoopConnectorService();

        var connectorConfig = getSinkConnectorConfigProps();
        connectorConfig.put(AS_OF_TIMESTAMP, "1702507049000");
        var fileConnectorConfig = new IcebergSourceConnectorConfig(connectorConfig);

        ProbeSampleResult read = connectorService.readSample(fileConnectorConfig, false);
        Assert.assertEquals(9, read.getData().size());
    }

    @SneakyThrows
    @Test
    public void shouldBeAbleToTimeTravelByBranch() {
        SparkHadoopConnectorService connectorService = new SparkHadoopConnectorService();

        var connectorConfig = getSinkConnectorConfigProps();
        connectorConfig.put(AS_OF_BRANCH, "Mists of Pandaria");
        var fileConnectorConfig = new IcebergSourceConnectorConfig(connectorConfig);

        ProbeSampleResult read = connectorService.readSample(fileConnectorConfig, false);
        Assert.assertEquals(11, read.getData().size());
    }

    @SneakyThrows
    @Test
    public void shouldBeAbleToTimeTravelByTag() {
        SparkHadoopConnectorService connectorService = new SparkHadoopConnectorService();

        var connectorConfig = getSinkConnectorConfigProps();
        connectorConfig.put(AS_OF_TAG, "Wrath of the Lich King");
        var fileConnectorConfig = new IcebergSourceConnectorConfig(connectorConfig);

        ProbeSampleResult read = connectorService.readSample(fileConnectorConfig, false);
        Assert.assertEquals(10, read.getData().size());
    }

    private Map<String, String> getSinkConnectorConfigProps() {
        return new HashMap<>() {{
            put(UNIT_TEST, "true");
            put(SINK_ID, "1");
            put(SOURCE_ID, "1");
            put(SOURCE_TYPE, ConnectionType.S3.name());
            put(CREDS_ENC, "1");
            put(CREDS_ENC_IV, "1");
            put(CREDENTIALS_DECRYPT_KEY, "1");
            put(LISTING_ENABLED, "false");
            put(SINK_TYPE, ConnectionType.S3.name());
            put(ACCESS_KEY_ID, localstack.getAccessKey());
            put(SECRET_KEY, localstack.getSecretKey());
            put(REGION, localstack.getRegion());
            put(SERVICE_ENDPOINT, localstack.getEndpointOverride(S3).toString());
            put(TABLE_NAME, TEST_TABLE_NAME);
            put(DATA_FORMAT, "parquet");
            put("iceberg.warehouse.dir", "nexla-data-lake");
            put(SPARK_SESSION_CONFIGS, "spark.driver.bindAddress=127.0.0.1");
            put(MAX_FILE_SIZE_MB, "0");
            put(FILE_NAME_PREFIX, "my-dataset");
            put(MONITOR_POLL_MS, "1");
        }};
    }
}
