# configuration of input ProbeService
input.bucket=echo-input.nexla.com
input.prefix=echo-pipeline
input.type=S3

# configuration of output ProbeService
output.bucket=echo-output.nexla.com
output.prefix=echo-pipeline
output.type=S3

# time in milliseconds between output bucket checks
test.resolution=2000
# time in milliseconds between test batches
test.interval=30000
# number of messages in each batch (file)
test.batch=5
# clear the input and output buckets before start the application
test.bucket.clear=true
# keep the statistics of the last X minutes
test.statistics.window=60
spring.jackson.date-format= yyyy-MM-dd HH:mm:ss
application=local
