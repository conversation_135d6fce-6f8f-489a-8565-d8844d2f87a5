package com.nexla.web.auth;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.ImmutableMap;
import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint;

public class CustomBasicAuthenticationEntryPoint extends BasicAuthenticationEntryPoint {

  public static final String AUTH_HEADER = "WWW-Authenticate";
  public final String HEADER_VALUE = "Basic realm=" + getRealmName();

  @Override
  public void commence(
      HttpServletRequest request,
      HttpServletResponse response,
      AuthenticationException authException)
      throws IOException {
    // Authentication failed, send error response.
    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
    response.addHeader(AUTH_HEADER, HEADER_VALUE);
    ImmutableMap<String, String> map =
        ImmutableMap.of("error", authException.getMessage(), "status", "401");
    PrintWriter writer = response.getWriter();
    writer.println(JsonUtils.toPrettyJsonString(map));
  }

  @Override
  public void afterPropertiesSet() {
    setRealmName(NexlaBasicAuth.REALM);
    super.afterPropertiesSet();
  }
}
