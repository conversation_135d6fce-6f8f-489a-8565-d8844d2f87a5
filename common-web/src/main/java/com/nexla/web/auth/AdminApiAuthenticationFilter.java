package com.nexla.web.auth;

import static java.util.Optional.ofNullable;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableList;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AuthResource;
import io.vavr.control.Either;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import javax.servlet.DispatcherType;
import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.filter.GenericFilterBean;

public abstract class AdminApiAuthenticationFilter extends GenericFilterBean {

  private static final Logger logger = LoggerFactory.getLogger(AdminApiAuthenticationFilter.class);
  public static final String API_KEY = "api_key";

  private final AdminApiClient adminApiClient;
  private final String username;
  private final String password;

  private final ConcurrentHashMap<String, Cache<AuthResource, Boolean>> authKeyCache =
      new ConcurrentHashMap<>();

  public AdminApiAuthenticationFilter(
      AdminApiClient adminApiClient, String username, String password) {
    this.adminApiClient = adminApiClient;
    this.username = username;
    this.password = password;
  }

  public abstract AuthResource getRequestedResource(HttpServletRequest requestUri);

  private Optional<String> buildHeaderFromApiKey(HttpServletRequest request) {
    List<NameValuePair> pairs =
        URLEncodedUtils.parse(request.getQueryString(), StandardCharsets.UTF_8);
    return pairs.stream()
        .filter(e -> API_KEY.equals(e.getName()))
        .findFirst()
        .map(NameValuePair::getValue)
        .map(e -> "Basic " + e);
  }

  @Override
  @SneakyThrows
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {

    HttpServletRequest httpRequest = (HttpServletRequest) request;
    String header =
        buildHeaderFromApiKey(httpRequest).orElse(httpRequest.getHeader("Authorization"));

    boolean errorPage = httpRequest.getDispatcherType() == DispatcherType.ERROR;
    if (!errorPage && header != null) {
      Cache<AuthResource, Boolean> cacheForKey = getCache(header);
      AuthResource authResourceKey = getRequestedResource(httpRequest);

      Optional<Boolean> authorizationStatus = ofNullable(cacheForKey.getIfPresent(authResourceKey));

      if (!authorizationStatus.isPresent() || authorizationStatus.get()) {
        boolean alreadyAuthenticated = authorizationStatus.orElse(false);
        if (alreadyAuthenticated) {
          grantAccess();
        } else {
          tryToAuthenticate(authResourceKey, header);
        }
      }
    }
    chain.doFilter(request, response);
  }

  private Cache<AuthResource, Boolean> getCache(String header) {
    return authKeyCache.computeIfAbsent(
        header,
        s ->
            CacheBuilder.newBuilder()
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build());
  }

  private void tryToAuthenticate(AuthResource authResource, String header) {
    Either<Exception, HttpStatus> result = getResourceAuthStatus(authResource, header);
    if (result.isRight()) {
      HttpStatus authStatus = result.get();
      if (authStatus.is2xxSuccessful()) {
        getCache(header).put(authResource, true);
        grantAccess();
      } else {
        // populating map with "false" =>
        // 1) within next N minutes it will reject requests
        // 2) after N minutes map entry will expire and another auth attempt might be made
        getCache(header).put(authResource, false);
        logger.error(
            "Response status {}, unable to authenticate on {}", authStatus.value(), authResource);
      }
    } else {
      getCache(header).put(authResource, false);
      logger.error("Exception, unable to authenticate on {}", authResource, result.getLeft());
      throw new RuntimeException(result.getLeft());
    }
  }

  private Either<Exception, HttpStatus> getResourceAuthStatus(
      AuthResource authResource, String header) {
    try {
      return Either.right(adminApiClient.resourceAuth(header, authResource));
    } catch (HttpStatusCodeException e) {
      logger.error("Unable to authenticate on {}", authResource, e);
      return Either.right(e.getStatusCode());
    } catch (Exception e) {
      logger.error("Unable to authenticate on {}", authResource, e);
      return Either.left(e);
    }
  }

  private void grantAccess() {
    List<GrantedAuthority> authorities = ImmutableList.of(new SimpleGrantedAuthority("ROLE_ADMIN"));
    UsernamePasswordAuthenticationToken authReq =
        new UsernamePasswordAuthenticationToken(username, password, authorities);
    SecurityContext sc = SecurityContextHolder.getContext();
    sc.setAuthentication(authReq);
  }
}
