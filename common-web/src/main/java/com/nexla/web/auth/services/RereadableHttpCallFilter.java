package com.nexla.web.auth.services;

import com.nexla.web.auth.ReusableRequestWrapper;
import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import org.springframework.web.filter.GenericFilterBean;

public class RereadableHttpCallFilter extends GenericFilterBean {

  @Override
  @SneakyThrows
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
    ReusableRequestWrapper httpRequest = new ReusableRequestWrapper((HttpServletRequest) request);
    chain.doFilter(httpRequest, response);
  }
}
