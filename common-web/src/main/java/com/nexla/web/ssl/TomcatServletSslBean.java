package com.nexla.web.ssl;

import com.nexla.common.NexlaSslContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.Ssl;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.stereotype.Component;

@Component
public class TomcatServletSslBean
    implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {

  private final NexlaSslContext appSslContext;

  public TomcatServletSslBean(@Autowired NexlaSslContext ctx) {
    this.appSslContext = ctx;
  }

  @Override
  public void customize(TomcatServletWebServerFactory tomcat) {
    Ssl tomcatSsl = tomcat.getSsl();
    if (tomcatSsl != null) {
      appSslContext
          .getServerKeystoreStore()
          .ifPresent(
              ctx -> {
                tomcatSsl.setKeyStore(ctx.getFile().getAbsolutePath());
                tomcatSsl.setKeyStorePassword(ctx.getPassword());
              });
    }
    //		I have noticed Tomcat was creating some component 40 seconds after app started
    //		probably we should not delete it for Spring apps, so commented
    //		tomcat.addContextLifecycleListeners((LifecycleListener) event -> {
    //			if (event.getType().equals(Lifecycle.PERIODIC_EVENT)) {
    //				appSslContext.ifPresent(x -> x.clean());
    //			}
    //		});
  }
}
