package com.nexla.web.service;

import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.API;
import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.DATABASE;
import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.FILE;
import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.REDIS;
import static com.nexla.common.NexlaConstants.ERROR_MESSAGE;
import static com.nexla.common.NexlaConstants.EXCEPTION_TRACE;
import static com.nexla.common.NexlaConstants.RESPONSE_BODY;
import static com.nexla.common.metrics.NexlaRawMetric.ERROR_COUNT;
import static com.nexla.common.metrics.NexlaRawMetric.NAME;
import static com.nexla.common.metrics.NexlaRawMetric.RECORDS;
import static com.nexla.common.metrics.NexlaRawMetric.SIZE;
import static com.nexla.web.service.DatadogMetricsService.addGlobalTags;
import static com.nexla.web.service.Tags.tag;
import static com.timgroup.statsd.Event.AlertType;
import static com.timgroup.statsd.Event.AlertType.SUCCESS;
import static java.util.Optional.ofNullable;

import com.nexla.admin.client.OwnerAndOrg;
import com.nexla.common.ConnectionType;
import com.nexla.common.ResourceType;
import com.nexla.common.metrics.NexlaRawMetric;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

public class MonitoringReceiverService {
  private static final Logger logger = LoggerFactory.getLogger(MonitoringReceiverService.class);

  private static final String API_PREFIX = "nexla";

  private static final String METRICS_ERROR_PREFIX = ".error_counts";

  private static final String SOURCES_PREFIX = ".sources";
  private static final String SOURCES_COUNTER_PREFIX = ".read_counter";
  private static final String SOURCES_METRICS_RECORD_PREFIX = ".read_records";
  private static final String SOURCES_METRICS_SIZE_PREFIX = ".read_bytes";

  private static final String SINKS_PREFIX = ".sinks";
  private static final String SINKS_COUNTER_PREFIX = ".write_counter";
  private static final String SINKS_METRICS_RECORD_PREFIX = ".write_records";
  private static final String SINKS_METRICS_SIZE_PREFIX = ".write_bytes";

  private static final String DATASET_PREFIX = ".datasets";
  private static final String DATASET_COUNTER_PREFIX = ".dataset_counter";
  private static final String DATASET_METRICS_RECORD_PREFIX = ".dataset_records";
  private static final String DATASET_METRICS_SIZE_PREFIX = ".dataset_bytes";

  private static final String SOURCES_COUNTER_NAME =
      API_PREFIX + SOURCES_PREFIX + SOURCES_COUNTER_PREFIX;
  private static final String SOURCES_METRICS_RECORD_NAME =
      API_PREFIX + SOURCES_PREFIX + SOURCES_METRICS_RECORD_PREFIX;
  private static final String SOURCES_METRICS_FILE_NAME =
      API_PREFIX + SOURCES_PREFIX + SOURCES_METRICS_SIZE_PREFIX;
  private static final String SOURCES_METRICS_ERROR_NAME =
      API_PREFIX + SOURCES_PREFIX + METRICS_ERROR_PREFIX;

  private static final String SINKS_COUNTER_NAME = API_PREFIX + SINKS_PREFIX + SINKS_COUNTER_PREFIX;
  private static final String SINKS_METRICS_RECORD_NAME =
      API_PREFIX + SINKS_PREFIX + SINKS_METRICS_RECORD_PREFIX;
  private static final String SINKS_METRICS_FILE_NAME =
      API_PREFIX + SINKS_PREFIX + SINKS_METRICS_SIZE_PREFIX;
  private static final String SINKS_METRICS_ERROR_NAME =
      API_PREFIX + SINKS_PREFIX + METRICS_ERROR_PREFIX;

  private static final String DATASET_COUNTER_NAME =
      API_PREFIX + DATASET_PREFIX + DATASET_COUNTER_PREFIX;
  private static final String DATASET_METRICS_RECORD_NAME =
      API_PREFIX + DATASET_PREFIX + DATASET_METRICS_RECORD_PREFIX;
  private static final String DATASET_METRICS_FILE_NAME =
      API_PREFIX + DATASET_PREFIX + DATASET_METRICS_SIZE_PREFIX;
  private static final String DATASET_METRICS_ERROR_NAME =
      API_PREFIX + DATASET_PREFIX + METRICS_ERROR_PREFIX;

  private final DatadogMetricsService datadogMetricsService;

  public MonitoringReceiverService(DatadogMetricsService datadogMetricsService) {
    this.datadogMetricsService = datadogMetricsService;
  }

  public void consumeMetric(
      NexlaRawMetric metric,
      Optional<ConnectionType> optionalConnectionType,
      OwnerAndOrg ownerAndOrg,
      String name,
      String format,
      String bucketName) {
    if (!enableMetricsService(metric.getResourceType(), optionalConnectionType)) {
      return;
    }

    ConnectionType connectionType = optionalConnectionType.orElse(null);
    final String error = (String) metric.getFields().get(EXCEPTION_TRACE);
    final AlertType status = getIngestionStatus(error);
    final String counterName;
    final String recordMetricName;
    final String fileMetricName;
    final String errorMetricName;
    final String sourceAction;

    List<String> tagsFormatted = new ArrayList<>();
    String fileName = "";
    // todo why cleanse the tags only for DATASET? safer to explicitly allowlist the tags we want
    Map<String, String> tags =
        metric.getResourceType() == ResourceType.DATASET
            ? Map.of(NAME, fileName)
            : metric.getTags();
    if (!CollectionUtils.isEmpty(tags)) {
      fileName = tags.get(NAME);
      for (Map.Entry<String, String> entry : tags.entrySet()) {
        tagsFormatted.add(tag(entry.getKey(), entry.getValue()));
      }
    }

    addGlobalTags(
        metric.getResourceType(),
        metric.getResourceId(),
        tagsFormatted,
        fileName,
        connectionType,
        ownerAndOrg.getOrg(),
        ownerAndOrg.getOwner(),
        name,
        format,
        bucketName,
        status.name());

    switch (metric.getResourceType()) {
      case SOURCE:
        counterName = SOURCES_COUNTER_NAME;
        recordMetricName = SOURCES_METRICS_RECORD_NAME;
        fileMetricName = SOURCES_METRICS_FILE_NAME;
        errorMetricName = SOURCES_METRICS_ERROR_NAME;
        sourceAction = "read";
        break;
      case SINK:
        counterName = SINKS_COUNTER_NAME;
        recordMetricName = SINKS_METRICS_RECORD_NAME;
        fileMetricName = SINKS_METRICS_FILE_NAME;
        errorMetricName = SINKS_METRICS_ERROR_NAME;
        sourceAction = "write";
        break;
      case DATASET:
        counterName = DATASET_COUNTER_NAME;
        recordMetricName = DATASET_METRICS_RECORD_NAME;
        fileMetricName = DATASET_METRICS_FILE_NAME;
        errorMetricName = DATASET_METRICS_ERROR_NAME;
        sourceAction = "dataset";
        break;
      default:
        logger.info("Invalid source type for metric {}", metric.getResourceType().name());
        return;
    }

    datadogMetricsService.sendCounterMetric(counterName, tagsFormatted);

    Long fileSize = toLong(metric.getFields().get(SIZE));
    Long records = toLong(metric.getFields().get(RECORDS));
    Long errorCount = ofNullable(toLong(metric.getFields().get(ERROR_COUNT))).orElse(1L);

    datadogMetricsService.incrementMetric(recordMetricName, tagsFormatted, records);
    datadogMetricsService.incrementMetric(fileMetricName, tagsFormatted, fileSize);
    if (status == AlertType.ERROR) {
      datadogMetricsService.incrementMetric(errorMetricName, tagsFormatted, errorCount);
    }

    String eventTitle = String.format("[%s] %s", status.name(), fileName);
    String connectionTypeCategory =
        (connectionType == null || connectionType.category == null)
            ? ""
            : connectionType.category.name();
    String eventText =
        getEventText(
            metric,
            sourceAction,
            error,
            fileName,
            fileSize,
            records,
            errorCount,
            connectionTypeCategory);
    datadogMetricsService.sendEvent(eventTitle, eventText, tagsFormatted, status);
  }

  private boolean enableMetricsService(
      ResourceType resourceType, Optional<ConnectionType> connectionType) {
    Optional<Boolean> aBoolean =
        connectionType.map(
            c ->
                (c.category == FILE
                    || c.category == API
                    || c.category == DATABASE
                    || c.category == REDIS));
    return resourceType == ResourceType.DATASET || aBoolean.orElse(Boolean.FALSE);
  }

  private AlertType getIngestionStatus(String error) {
    AlertType status = SUCCESS;
    if (error != null) {
      status = AlertType.ERROR;
    }
    return status;
  }

  private String getEventText(
      NexlaRawMetric metric,
      String sourceAction,
      String error,
      String filePath,
      Long fileSize,
      Long records,
      Long errorCounts,
      String sourceChannel) {
    String eventText;
    if (error != null) {
      String errorMessage = null;
      if (metric.getFields().get(ERROR_MESSAGE) != null) {
        errorMessage = (String) metric.getFields().get(ERROR_MESSAGE);
      }
      if (metric.getFields().get(RESPONSE_BODY) != null) {
        errorMessage =
            String.format(
                "%s responseBody= %100.100s", errorMessage, metric.getFields().get(RESPONSE_BODY));
      }

      eventText =
          String.format(
              "%s failed message= %200.200s, errorTrace= %400.400s, with size=%d bytes, "
                  + "records=%d and Error Counts=%d",
              sourceChannel, errorMessage, error, fileSize, records, errorCounts);
    } else {
      eventText =
          String.format(
              "%s %s %s with bytes=%d and records=%d",
              sourceChannel, filePath, sourceAction, fileSize, records);
    }
    return eventText;
  }
}
