package com.nexla.featureflagging;

import java.util.Map;

public interface NexlaFeatureFlagging {

  Boolean isOn(String featureName, Map<String, String> attributes);

  Boolean isOff(String featureName, Map<String, String> attributes);

  Boolean getFeatureValue(String featureName, Map<String, String> attributes, Boolean defaultValue);

  String getFeatureValue(String featureName, Map<String, String> attributes, String defaultValue);

  Float getFeatureValue(String featureName, Map<String, String> attributes, Float defaultValue);

  Integer getFeatureValue(String featureName, Map<String, String> attributes, Integer defaultValue);

  Double getFeatureValue(String featureName, Map<String, String> attributes, Double defaultValue);

  Object getFeatureValue(String featureName, Map<String, String> attributes, Object defaultValue);

  <ValueType> ValueType getFeatureValue(
      String featureName,
      Map<String, String> attributes,
      ValueType defaultValue,
      Class<ValueType> valueTypeClass);

  Boolean isFeatureEnabled(String featureName, Map<String, String> attributes);

  Boolean isOn(FeatureAttributes featureAttributes);

  Boolean isOff(FeatureAttributes featureAttributes);

  Boolean getFeatureValue(FeatureAttributes featureAttributes, Boolean defaultValue);

  String getFeatureValue(FeatureAttributes featureAttributes, String defaultValue);

  Float getFeatureValue(FeatureAttributes featureAttributes, Float defaultValue);

  Integer getFeatureValue(FeatureAttributes featureAttributes, Integer defaultValue);

  Double getFeatureValue(FeatureAttributes featureAttributes, Double defaultValue);

  Object getFeatureValue(FeatureAttributes featureAttributes, Object defaultValue);

  <ValueType> ValueType getFeatureValue(
      FeatureAttributes featureAttributes, ValueType defaultValue, Class<ValueType> valueTypeClass);

  Boolean isFeatureEnabled(FeatureAttributes featureAttributes);

  void close();
}
