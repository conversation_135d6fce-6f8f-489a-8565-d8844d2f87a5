package com.nexla.connector.sql.sink;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connect.common.cdc.DebeziumData;
import com.nexla.connect.common.cdc.OperationType;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connect.common.postponedFlush.PipelineProgressTracker;
import com.nexla.connector.ExtractedMessage;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.properties.SqlConfigAccessor;
import com.nexla.connector.sql.processor.FlushProcessor;
import com.nexla.connector.sql.processor.OpProcessorFactory;
import connect.data.Schema;
import connect.jdbc.sink.dialect.copy.CdcSinkCopyOperation;
import connect.jdbc.sink.warehouse.CdcDataWarehouseSink;
import connect.jdbc.util.CdcMode;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.connect.errors.RetriableException;
import org.quartz.CronExpression;

import java.time.ZoneId;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.properties.SqlConfigAccessor.TABLE;
import static com.nexla.probe.sql.SqlConnectorService.shouldRethrow;
import static java.util.stream.Collectors.toList;

public class CdcSinkTask extends JdbcSinkTask {

    private CdcMode cdcMode;

    @Override
    public void doStart() {
        super.doStart();
        checkCronProp();
        if (copyMode) {
            if (shouldUseNewCopyFlow()) {
                this.dataWarehouseSink = new CdcDataWarehouseSink(tableProcessor.getConnProvider(), tableProcessor.getDbDialect(), logger);
                this.flusher = new FlushProcessor(tableProcessor, config.sinkId, getExceptionHandler(), getMetricHandler(), lastProcessedMessageTs);
                PipelineProgressTracker ppt = pipelineProgressTracker.orElseGet(() -> new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger));
                this.postponedFlush = flusher.getPostponedFlush(this, ppt, dataWarehouseSink, lastProcessedMessageTs, logger);
            } else {
                this.copyOperation = new CdcSinkCopyOperation(tableProcessor.getConnProvider(), tableProcessor.getDbDialect(), getFileFormat(), logger);
            }
        }

        if (StringUtils.isNotBlank(this.config.table)) {
            cdcMode = CdcMode.SINGLE_TABLE;
        } else {
            cdcMode = CdcMode.MULTI_TABLE;
        }
    }

    private void checkCronProp() {
        logger.info("checking 'flush.cron' property: {} ...", this.config.flushCron);

        this.config.flushCron.ifPresentOrElse(c -> {
                    try {
                        CronExpression cron = new CronExpression(c);
                        cron.setTimeZone(TimeZone.getTimeZone(ZoneId.of("UTC")));
                    } catch (Exception e) {
                        String errMsg = String.format("Could not parse cron expression: %s", e.getMessage());
                        logger.error(errMsg, e);
                        throw new ConfigException(errMsg);
                    }
                },
                () -> {
                    throw new ConfigException("Property 'flush.cron' should not be empty");
                });

    }

    @Override
    protected void processBatch(StreamEx<NexlaMessageContext> messages, int streamSize) {
        if (this.config.mappingConfig.isEmpty()) {
            throw new ConfigException("CDC: Mapping Config should not be empty");
        }

        logger.info("CDC: Writing {} records to db={}", streamSize, this.config.authConfig.databaseName);
        final List<NexlaMessageContext> messageList = messages.toList();
        ExceptionHandler handler = getExceptionHandler();
        // in cdc 'multi_table' mode table name is empty
        String name = StringUtils.isNotBlank(this.config.table) ? this.config.table : this.config.authConfig.databaseName;
        try {
            if (this.copyMode) {
                copyRecords(messageList);
            } else {
                final RecordMetric recordMetric = new RecordMetric();
                // get original message because it was not mapped
                Collection<NexlaMessage> records = StreamEx.of(messageList).map(x -> x.original).toList();
                segregateRecords(records, recordMetric, handler);

                recordMetric.sentRecordsTotal.set(streamSize - recordMetric.errorRecords.get());
                sendQuarantineMessage(name, recordMetric.getQuarantineMessages());
                sendMetric(name, Optional.empty(), recordMetric.sentRecordsTotal.get(), recordMetric.sentBytesTotal.get(), recordMetric.errorRecords.get());
            }
        } catch (Exception e) {
            logger.error("Stopping CDC sink task {}.... ", this.config.sinkId, e);
            throw e;
        }
    }

    private void copyRecords(List<NexlaMessageContext> messageList) {
        if (messageList.isEmpty()) {
            return;
        }

        if (config.insertMode != UPSERT) {
            String msg = String.format("CDC Copy flow doesn't support %s operation. Only UPSERT operation is supported for the DB %s",
		            config.insertMode,  this.config.authConfig.databaseName);
            logger.error("{}", msg);
            throw new ConfigException(msg);
        }

        switch (this.cdcMode) {
            case SINGLE_TABLE:
                // do copy records for 'single_table' mode
                List<NexlaMessage> originals = StreamEx.of(messageList)
                        .map(nmc -> nmc.original)
                        .toList();
                Consumer<Void> method = (c) -> {
                    Function<NexlaMessageContext, NexlaMessage> stF = context -> context.original;
                    doCopyRecords(messageList, stF, this.config);
                };
                invokeMessageProcessing(method, originals, originals.size());
                break;
            case MULTI_TABLE:
                // do copy records for 'multi_table' mode
                // group by tableName and preserve message ordering
                Map<String, List<NexlaMessageContext>> grouped = StreamEx
                        .of(messageList)
                        .groupingBy(nmc -> nmc.original.getRawMessage().get(DebeziumConstants.NEXLA_OPERATION_TABLE).toString(),
                                LinkedHashMap::new,
                                Collectors.toList());

                for (Map.Entry<String, List<NexlaMessageContext>> entry : grouped.entrySet()) {

                    String table = entry.getKey();
                    List<NexlaMessageContext> groupedMessages = entry.getValue();
                    List<NexlaMessage> groupedOriginals = StreamEx.of(groupedMessages)
                            .map(nmc -> nmc.original)
                            .toList();

                    Consumer<Void> groupedMethod = (c) -> {
                        Optional<JdbcSinkConnectorConfig> enrichedOpt = enrichConfig(table, groupedOriginals);

                        if (enrichedOpt.isEmpty()) {
                            logger.info("CDC: JdbcSinkConnectorConfig is empty, nothing to process");
                            return;
                        }

                        JdbcSinkConnectorConfig enriched = enrichedOpt.get();

                        Function<NexlaMessageContext, NexlaMessage> mtF = context -> {
                            DebeziumData debeziumData = DebeziumData.newDebeziumData(context.original, enriched.sinkId);
                            return new NexlaMessage(debeziumData.getAfterData(), context.original.getNexlaMetaData());
                        };
                        doCopyRecords(groupedMessages, mtF, enriched);
                    };
                    invokeMessageProcessing(groupedMethod, groupedOriginals, groupedOriginals.size());
                }
                break;
        }
    }

    private void doCopyRecords(List<NexlaMessageContext> groupedMessages,
                               Function<NexlaMessageContext, NexlaMessage> f,
                               JdbcSinkConnectorConfig enriched) {

	    if (enriched.primaryKey.isEmpty()) {
		    String msg = String.format("CDC UPSERT mode: Primary Keys should not be empty for the table %s", enriched.table);
		    logger.error("{}", msg);
		    throw new ConfigException(msg);
	    }

        int streamSize = groupedMessages.size();
        groupedMessages = truncateIfNeeded(groupedMessages, enriched);

        // if TRUNCATE event is a last one, message list will be empty
        if (groupedMessages.isEmpty()) {
            logger.debug("CDC: message list is empty after check for TRUNCATE, nothing to process: {}", enriched.table);
            return;
        }

        tableProcessor.processTable(enriched, getAlterSupplier(), cdcMode);

        List<NexlaMessageContext> enrichedMessageList = StreamEx
                .of(groupedMessages)
                .filter(context -> StringUtils.isNotBlank(context.original
                        .getRawMessage()
                        .get(DebeziumConstants.NEXLA_OPERATION)
                        .toString()))
                .flatMap(context -> {
                    // check if deletion is allowed
                    boolean toDelete = enriched.mappingConfig.get().isReplayRowDeletions() && OperationType.toDelete(context.original
                            .getRawMessage()
                            .get(DebeziumConstants.NEXLA_OPERATION)
                            .toString());

                    ExtractedMessage extractedAfter = messageMapper.extractMessage(f.apply(context));
                    // adding 'nexla_op_delete' for further processing in warehouse to reply DELETE operation
                    extractedAfter
                            .getMapped()
                            .getRawMessage()
                            .put(DebeziumConstants.NEXLA_OPERATION_DELETE, toDelete);

                    return StreamEx.of(
                            new NexlaMessageContext(context.getOriginal(),
                                    extractedAfter.getMapped(),
                                    context.topicPartition,
                                    context.kafkaOffset)
                    );
                })
                .toList();

        logger.debug("CDC: before deduplication grouped Messages list size {}", enrichedMessageList.size());
        final List<NexlaMessageContext> dataStream = deduplicate(StreamEx.of(enrichedMessageList), enriched).collect(toList()); // Important. Do not filter records in any other way before they are written to CopyOperation buffer, because buffer keeps messages offset, and it should receive the offset of the last message in batch
        dataStream.stream()
                .filter(m -> m.getMapped() != null && m.getMapped().getNexlaMetaData() != null)
                .forEach(m -> runIdsToHeartbeat.add(m.getMapped().getNexlaMetaData().getRunId()));

        logger.debug("CDC: after deduplication data Stream size {}", dataStream.size());

        if (shouldUseNewCopyFlow()) {
            ((CdcDataWarehouseSink) dataWarehouseSink).writeData(enriched,
                    lastProcessedMessageTs,
                    getFileFormat(),
                    dataStream,
                    streamSize);
        } else {
            ((CdcSinkCopyOperation) copyOperation).writeToBuffer(enriched,
                    adminApiClient,
                    dataStream,
                    streamSize);
        }
    }

    private List<NexlaMessageContext> truncateIfNeeded(List<NexlaMessageContext> groupedMessages, JdbcSinkConnectorConfig enriched) {
        // check for TRUNCATE events
        int truncateIndex = -1;
        for (int i = groupedMessages.size() - 1; i >= 0; i--) {
            OperationType op = OperationType.asEnum(groupedMessages.get(i)
                    .original
                    .getRawMessage()
                    .get(DebeziumConstants.NEXLA_OPERATION)
                    .toString());

            if (OperationType.TRUNCATE.equals(op)) {
                truncateIndex = i;
                break;
            }
        }

        if (truncateIndex != -1) {
            // run TRUNCATE flow
            logger.info("CDC: TRUNCATE event at index [{}] out of [{}]: {}", truncateIndex, groupedMessages.size() - 1, enriched.table);
            if (shouldUseNewCopyFlow()) {
                ((CdcDataWarehouseSink) dataWarehouseSink).truncate(enriched, getFileFormat(), truncateIndex + 1);
                return new ArrayList<>(groupedMessages.subList(truncateIndex + 1, groupedMessages.size()));
            } else {
                logger.error("CDC: TRUNCATE is not supported in OLD COPY FLOW: {}", enriched.table);
            }
        }

        return groupedMessages;
    }

    /**
     * Before ALTER table need to:
     * - set cdcForceFlush flag to true
     * - do flush before table altering
     * - invalidate caches in CdcDataWarehouseSink and CdcSinkCopyOperation to get new schema further
     * - alter table
     *
     * @return Supplier to execute before alter table command
     */
    private Supplier<Boolean> getAlterSupplier() {
        return () -> {
            if (shouldUseNewCopyFlow()) {
                postponedFlush.setForceFlush(true);
            }

            boolean result = doFlush(postponedFlush.readyToFlush());

            if (dataWarehouseSink != null) {
                ((CdcDataWarehouseSink) dataWarehouseSink).invalidate();
            }

            if (copyOperation != null) {
                ((CdcSinkCopyOperation) copyOperation).invalidate();
            }

            return result;
        };
    }

    private void segregateRecords(Collection<NexlaMessage> records, RecordMetric recordMetric, ExceptionHandler handler) {
        Optional<NexlaMessage> first = records.stream().findFirst();
        if (first.isEmpty()) {
            return;
        }

        NexlaMessage nexlaMessage = first.get();
        OperationType op = OperationType.asEnum(nexlaMessage
                .getRawMessage()
                .get(DebeziumConstants.NEXLA_OPERATION)
                .toString());

        // get all same operations
        List<NexlaMessage> thisOpData = StreamEx.of(records)
                .takeWhile(r -> op.name().equals(r.getRawMessage().get(DebeziumConstants.NEXLA_OPERATION)))
                .toList();

        switch (this.cdcMode) {
            case SINGLE_TABLE:
                segregateRecordsSingle(thisOpData, op, recordMetric, handler);
                break;
            case MULTI_TABLE:
                segregateRecordsMulti(thisOpData, op, recordMetric, handler);
                break;
        }
        segregateRecords(StreamEx.of(records).skip(thisOpData.size()).toList(), recordMetric, handler);
    }

    /**
     * Segregate records based on operation type with preserving message ordering for 'single-table' mode
     * UPDATE operation example:
     * {
     * "id": 11,
     * "fullname": "John Doe",
     * "nexla_op": "UPDATE",
     * "before": {
     * "id": 11,
     * "fullname": "John Doe1 Updated"
     * }
     * }
     *
     * @param thisOpData   NexlaMessage filtered by operation
     * @param op           change event operation
     * @param recordMetric metrics
     * @param handler      ExceptionHandler
     */
    private void segregateRecordsSingle(Collection<NexlaMessage> thisOpData,
                                        OperationType op,
                                        RecordMetric recordMetric,
                                        ExceptionHandler handler) {
        Consumer<Void> method = (c) -> {
            Function<NexlaMessage, NexlaMessage> afterF = orig -> orig;
            Function<NexlaMessage, NexlaMessage> beforeF = orig -> new NexlaMessage((LinkedHashMap<String, Object>) orig.getRawMessage().get(DebeziumConstants.BEFORE_DATA), orig.getNexlaMetaData());
            Function<NexlaMessage, List<String>> keysF = orig -> this.config.primaryKey;

            doDBQuery(thisOpData, Optional.of(this.config), afterF, beforeF, keysF, op, recordMetric, handler);
        };
        invokeMessageProcessing(method, thisOpData, thisOpData.size());
    }

    /**
     * Segregate records based on operation type and table name with preserving message ordering for 'multi-table' mode
     * UPDATE operation example:
     * {
     * "nexla_op": "UPDATE",
     * "nexla_op_database": "customerdb",
     * "nexla_op_table": "customer",
     * "nexla_cdc_info": {
     * "primary_key": ["id"],
     * "before": {
     * "id": 1,
     * "fullname": "John Doe1 Updated"
     * },
     * "after": {
     * "id": 1,
     * "fullname": "John Doe1"
     * },
     * "schema": {
     * "id": "{INT32:null}",
     * "fullname": "{STRING:null}"
     * }
     * }
     * }
     *
     * @param thisOpData   NexlaMessage filtered by operation
     * @param op           change event operation
     * @param recordMetric metrics
     * @param handler      ExceptionHandler
     */
    private void segregateRecordsMulti(Collection<NexlaMessage> thisOpData,
                                       OperationType op,
                                       RecordMetric recordMetric,
                                       ExceptionHandler handler) {

        // group by tableName and preserve message ordering
        Map<String, List<NexlaMessage>> grouped = StreamEx.of(thisOpData)
                .groupingBy(nm -> nm.getRawMessage().get(DebeziumConstants.NEXLA_OPERATION_TABLE).toString(),
                        LinkedHashMap::new,
                        Collectors.toList());

        grouped.forEach((table, messages) -> {
            Consumer<Void> method = (c) -> {
                Optional<JdbcSinkConnectorConfig> enriched = enrichConfig(table, messages);

                int sinkId = this.config.sinkId;
                Function<NexlaMessage, NexlaMessage> afterF = orig -> new NexlaMessage(
                    DebeziumData.newDebeziumData(orig, sinkId).getAfterData(), orig.getNexlaMetaData());
                Function<NexlaMessage, NexlaMessage> beforeF = orig -> new NexlaMessage(DebeziumData.newDebeziumData(orig, sinkId).getBeforeData(), orig.getNexlaMetaData());
                Function<NexlaMessage, List<String>> keysF = orig -> DebeziumData.newDebeziumData(orig, sinkId).getPrimaryKey();

                doDBQuery(messages, enriched, afterF, beforeF, keysF, op, recordMetric, handler);
            };
            invokeMessageProcessing(method, messages, messages.size());
        });

    }

    private void doDBQuery(Collection<NexlaMessage> messages,
                           Optional<JdbcSinkConnectorConfig> enriched,
                           Function<NexlaMessage, NexlaMessage> afterF,
                           Function<NexlaMessage, NexlaMessage> beforeF,
                           Function<NexlaMessage, List<String>> keysF,
                           OperationType op,
                           RecordMetric recordMetric,
                           ExceptionHandler handler) {
        OpProcessorFactory.getOpProcessor(op, enriched, this.tableProcessor)
                .ifPresent(p -> {
                    // no need to process table because TRUNCATE msg has different schema
                    if (op.equals(OperationType.TRUNCATE)) {
                        p.process(messages, recordMetric, handler);
                        return;
                    }

                    // create or alter table if needed before messages processing
                    p.getTableProcessor().processTable(p.getConfig(), () -> true, cdcMode);

                    //prepare mapped messages for processing because it was not done in BaseSinkTask.put
                    List<NexlaMessage> mapped = StreamEx
                            .of(messages)
                            .flatMap(orig -> {
                                ExtractedMessage extractedAfter = this.messageMapper.extractMessage(afterF.apply(orig));

                                // for UPDATE operation without PK we need to preserve 'before' state
                                if (op.equals(OperationType.UPDATE) && p.getConfig().primaryKey.isEmpty()) {
                                    ExtractedMessage extractedBefore = this.messageMapper.extractMessage(beforeF.apply(orig));

                                    DebeziumData mappedDebezium = new DebeziumData(keysF.apply(orig),
                                            extractedBefore.getMapped().getRawMessage(),
                                            extractedAfter.getMapped().getRawMessage(),
                                            new LinkedHashMap<>());

                                    return StreamEx.of(new NexlaMessage(new LinkedHashMap<>(
                                            ImmutableMap.of(DebeziumConstants.NEXLA_CDC_INFO, mappedDebezium)), extractedAfter.getMapped().getNexlaMetaData()));
                                }

                                return StreamEx.of(extractedAfter.getMapped());
                            })
                            .toList();
                    p.process(mapped, recordMetric, handler);
                });
    }

    private void invokeMessageProcessing(Consumer<Void> method, Collection<NexlaMessage> originals, int streamSize) {

        try {
            method.accept(null);
        } catch (Exception e) {
            // TODO: improve exception handling and metrics reporting. Depending on the error place, the overall result will not be consistent
            if (shouldRethrow(e)) {
                throw new RetriableException(e);
            }
            logger.error("CDC: Error while writing records to sinkId={}, records={}", this.config.sinkId, streamSize, e);
            RecordMetric recordMetric = new RecordMetric();
            originals.forEach(m -> sendToQuarantine(m, e, recordMetric));

            String name = StringUtils.isNotBlank(this.config.table) ? this.config.table : this.config.authConfig.databaseName;
            sendMetric(name, Optional.empty(), 0L, 0L, recordMetric.errorRecords.get());

            ExceptionHandler handler = getExceptionHandler();
            handler.handleException(e, recordMetric.errorRecords.get(), name);
            handler.handleMonitoring(e);
            if (this.config.stopOnError) {
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * Enrich existing config with correct destination table name and mapping
     *
     * @param srcTable source table name
     * @param records  nexla messages to process
     * @return enriched jdbc sink config
     */
    private Optional<JdbcSinkConnectorConfig> enrichConfig(String srcTable, List<NexlaMessage> records) {

        if (records.isEmpty()) {
            logger.warn("NexlaMessages should not be empty");
            return Optional.empty();
        }

        if (this.config.mappingConfig.isEmpty()) {
            throw new ConfigException("CDC: Mapping Config should not be empty");
        }

        MappingConfig mappingConfig = this.config.mappingConfig.get();
        if (mappingConfig.getExcludeTables().contains(srcTable)) {
            logger.info("CDC: Table {} excluded from processing according to 'exclude_tables' property: {}", srcTable, mappingConfig.getExcludeTables());
            return Optional.empty();
        }

        String trgtTable = StringUtils.EMPTY;
        LinkedHashMap<String, Map<String, String>> mapping = Maps.newLinkedHashMap();
        List<String> enrichedPKs = new ArrayList<>();

        NexlaMessage last = records.get(records.size() - 1);
        DebeziumData debeziumData = Optional.ofNullable(last)
                .map(nm -> DebeziumData.newDebeziumData(nm, this.config.sinkId))
                .orElse(null);

        MappingConfig.TableMapping tableMapping = mappingConfig
                .getOverriddenMappings()
                .get(srcTable);

        if (tableMapping != null) {
            tableMapping.getColumns().forEach((k, v) -> {
                String targetColName = tableProcessor.getDbDialect().getTargetName(v.getName());
                mapping.put(k, Collections.singletonMap(targetColName, v.getType()));

                // set mapped PrimaryKey name
                if (debeziumData != null && debeziumData.getPrimaryKey() != null && debeziumData.getPrimaryKey().contains(k)) {
                    enrichedPKs.add(targetColName);
                }
            });
            trgtTable = tableMapping.getName();
        } else if (debeziumData != null) {
            debeziumData.getSchema().forEach((k, v) -> {
                v.forEach((literal, semantic) -> {
                            // use semantic type as DB type otherwise convert kafka literal type to DB type
                            String dbType = this.tableProcessor.getDbDialect().convertSemanticToDBType(semantic)
                                    .orElseGet(() -> {
                                        Schema.Type kafkaSchemaType = Schema.Type.valueOf(literal);
                                        return this.tableProcessor.getDbDialect().getDbType(kafkaSchemaType);
                                    });
                            String targetColName = tableProcessor.getDbDialect().getTargetName(k);
                            mapping.put(k, Collections.singletonMap(targetColName, dbType));

                            // set mapped PrimaryKey name
                            if (debeziumData.getPrimaryKey() != null && debeziumData.getPrimaryKey().contains(k)) {
                                enrichedPKs.add(targetColName);
                            }
                        }
                );
            });

            trgtTable = srcTable;
        }

        if (StringUtils.isBlank(trgtTable)) {
            logger.info("CDC: Skip Table {} according to 'overridden_mappings' property", srcTable);
            return Optional.empty();
        }

        this.config.mappingConfig.get().setMapping(mapping);

        Map<String, String> originals = this.config.originalsStrings();
        // clean up old PKs
        originals.remove(SqlConfigAccessor.PRIMARY_KEY);
        // set enriched table name
        trgtTable = tableProcessor.getFullTableName(mappingConfig, trgtTable);
        originals.put(TABLE, trgtTable);

        JdbcSinkConnectorConfig enriched = new JdbcSinkConnectorConfig(originals);
        enriched.mappingConfig.get().setMapping(mapping);
        enriched.primaryKey.addAll(enrichedPKs);
        return Optional.of(enriched);
    }
}
