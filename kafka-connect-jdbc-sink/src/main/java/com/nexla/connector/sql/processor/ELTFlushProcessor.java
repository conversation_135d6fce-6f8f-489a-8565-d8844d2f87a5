package com.nexla.connector.sql.processor;

import com.nexla.common.logging.TimeLogger;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.report.PipelineStatusReport;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.sink.JdbcSinkTask;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import connect.jdbc.util.ELTUtils;
import lombok.SneakyThrows;

import java.sql.Connection;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static java.util.Objects.nonNull;

public class ELTFlushProcessor extends FlushProcessor {

  private FlushResult flushResult;

  private AtomicBoolean recordsFlushed;

  public ELTFlushProcessor(TableProcessor tableProcessor,
                           int sinkId,
                           BaseSinkTask.ExceptionHandler exceptionHandler,
                           JdbcSinkTask.MetricHandler metricHandler,
                           AtomicLong lastProcessedMessageTs) {
    super(tableProcessor, sinkId, exceptionHandler, metricHandler, lastProcessedMessageTs);
    resetState();
  }

  // Note. When using JDBC connection in transaction mode, a SQL exception can lead to
  // loss of transaction context and uncommitted changes, even if the exception is caught in code.

  /**
   * Returns boolean flag that indicates if current pipeline is completely flushed
   */
  @SneakyThrows
  @Override
  public boolean flush(DataWarehouseSink dataWarehouseSink,
                       JdbcSinkConnectorConfig config,
                       PostponedFlush postponedFlush,
                       Optional<PipelineStatusReport> pipelineStatusReport,
                       ReadyToFlush readyToFlush) {

    // Writes to INSERT's destination table or UPSERT's temp table
    boolean readyToFlushBatch = dataWarehouseSink.getBufferSize() > 0;

    if (readyToFlushBatch || readyToFlush.flush) {
      pipelineStatusReport.ifPresent(status -> status.reportPipelineFlushStarted(100));
      Connection connection = tableProcessor.getConnProvider().get();
      try (TimeLogger ignored = new TimeLogger(logger, "flushUsingDataWarehouseSink")) {

        // Step 1. Flush batch (records are written to target table in INSERT mode OR to temp table in UPSERT mode)
        try (TimeLogger ignored1 = new TimeLogger(logger, "dataWarehouseSink::flushBatch")) {
          dataWarehouseSink.flushBatch(connection)
              .ifPresent(flushResult -> {
                recordsFlushed.set(true);
                updateFlushResult(flushResult);
              });
        }
        pipelineStatusReport.ifPresent(status -> status.flushProgress.addAndGet(50));

        // Step 2. Flush pipeline
        if (readyToFlush.flush) {

          // pipeline might be UPSERT but might only have inserts due to empty primary keys
          // double-checking the batch size before resetting the flag
          if (UPSERT == config.insertMode && dataWarehouseSink.getCurrentBatchSize() > 0) {
            // Reset flag to avoid false results in case flushPipeline silently fails
            recordsFlushed.set(false);

            try (TimeLogger ignored1 = new TimeLogger(logger, "dataWarehouseSink::flushPipeline")) {
              Optional<FlushResult> flushResult = dataWarehouseSink.flushPipeline(connection);
              if (flushResult.isPresent()) {
                updateFlushResult(flushResult.get());
                recordsFlushed.set(true);
              } else {
                // when flushBatchSize is present we might miss FLUSHED_SINK event because reportFlushed is false in the last flush
                // due to lag api returning false. In the subsequent doFlush call red to flush becomes true, so this condition was added
                // to send FLUSHED_SINK event in these cases.
                if (config.flushBatchSize.isPresent() && readyToFlush.reportFlushed && lastProcessedMessageTs.get() > 0) {
                  recordsFlushed.set(true);
                }
              }
            }
          }
        }

        pipelineStatusReport.ifPresent(status -> status.flushProgress.addAndGet(50));

        if (!tableProcessor.getDbDialect().isAutoCommit()) {
          connection.commit();
        }

        postponedFlush.resetFailures();

        boolean isFinalFlush = isUpsertAndAllRecordsHaveBeenFlushed(dataWarehouseSink, readyToFlush) ||
            isInsertAndAllRecordsHaveBeenFlushed(dataWarehouseSink, config, readyToFlush);

        if (isFinalFlush) {
          logger.info("M=flushUsingDataWarehouseSink, isFinalFlush=true");
        }

        if ((recordsFlushed.get() && INSERT == config.insertMode) ||
            (UPSERT == config.insertMode && isFinalFlush)) {
          metricHandler.handleMetrics(flushResult);
          resetState();
        }

        return isFinalFlush;
      } catch (Exception e) {
        postponedFlush.incrementFailures(e);
        if (!tableProcessor.getDbDialect().isAutoCommit()) {
          connection.rollback();
        }
        logger.error("M=flushUsingDataWarehouseSink, error while writing records to sinkId=" + config.sinkId, e);

        String name = config.authConfig.databaseName;

        var recordMetric = buildErrorMetric(dataWarehouseSink, e);
        metricHandler.handleMetric(name, Optional.empty(), 0L, 0L, recordMetric.errorRecords.get());
        exceptionHandler.handleException(e, recordMetric.errorRecords.get(), name);
        exceptionHandler.handleMonitoring(e);
      } finally {
        closeSilently(connection);
      }
    }
    return false;
  }

  private boolean isInsertAndAllRecordsHaveBeenFlushed(DataWarehouseSink dataWarehouseSink, JdbcSinkConnectorConfig config, ReadyToFlush readyToFlush) {
    return INSERT == config.insertMode && dataWarehouseSink.getBufferSize() == 0 && readyToFlush.reportFlushed;
  }

  private boolean isUpsertAndAllRecordsHaveBeenFlushed(DataWarehouseSink dataWarehouseSink, ReadyToFlush readyToFlush) {
    return recordsFlushed.get() && dataWarehouseSink.getCurrentBatchSize() == 0 && nonNull(flushResult) && readyToFlush.reportFlushed;
  }

  private void resetState() {
    this.recordsFlushed = new AtomicBoolean(false);
    this.flushResult = null;
  }

  private void updateFlushResult(FlushResult currentFlushResult) {
    if (Objects.nonNull(this.flushResult)) {
      this.flushResult = ELTUtils.mergeFlushResult(currentFlushResult, this.flushResult);
    } else {
      if (nonNull(currentFlushResult)){
        this.flushResult = currentFlushResult;
      }
    }
  }
}
