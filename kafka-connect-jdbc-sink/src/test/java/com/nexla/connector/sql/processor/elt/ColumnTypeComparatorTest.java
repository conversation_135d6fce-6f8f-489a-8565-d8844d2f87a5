package com.nexla.connector.sql.processor.elt;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class ColumnTypeComparatorTest {

  @Test
  void testEqualNumberTypes() {
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20, 0)", "NUMBER(20)"));
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20, NULL)", "NUMBER(20)"));
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20, 0)", "NUMBER(20, null)"));
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20, 0)", "NUMBER(20, 0)"));
  }

  @Test
  void testDifferentNumberTypes() {
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20, 2)", "NUMBER(20, 0)"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("NUMBER(10, 0)", "NUMBER(20, 0)"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20, 1)", "NUMBER(20)"));
  }

  @Test
  void testEqualVarcharTypes() {
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("VARCHAR(255)", "VARCHAR(255)"));
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("varchar(255)", "VARCHAR(255)"));
  }

  @Test
  void testDifferentVarcharTypes() {
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("VARCHAR(255)", "VARCHAR(500)"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("VARCHAR(255)", "TEXT"));
  }

  @Test
  void testTextAndOtherTypes() {
    assertTrue(ColumnTypeComparator.areColumnTypesEqual("TEXT", "TEXT"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("TEXT", "VARCHAR(255)"));
  }

  @Test
  void testNullAndEmptyTypes() {
    assertFalse(ColumnTypeComparator.areColumnTypesEqual(null, "NUMBER(20,0)"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20,0)", null));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("", "NUMBER(20,0)"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20,0)", ""));
  }

  @Test
  void testEdgeCases() {
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("NUMBER(20,0)", "BOOLEAN"));
    assertFalse(ColumnTypeComparator.areColumnTypesEqual("VARCHAR(255)", "CHAR(255)"));
  }
}