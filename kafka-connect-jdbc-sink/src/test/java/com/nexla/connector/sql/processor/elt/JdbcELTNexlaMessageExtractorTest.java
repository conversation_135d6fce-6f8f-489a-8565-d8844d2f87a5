package com.nexla.connector.sql.processor.elt;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.client.util.Lists;
import com.nexla.common.NexlaMessage;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import connect.data.Date;
import connect.data.SchemaBuilder;
import connect.data.Timestamp;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class JdbcELTNexlaMessageExtractorTest {

  private List<NexlaMessageContext> sampleMessages;

  @BeforeAll
  public void setUp() {
    var json = loadResourceAsString("/elt_nexla_messages.json");
    var nexlaMessages = JsonUtils.stringToType(json, new TypeReference<List<LinkedHashMap<String, Object>>>() {
    });
    var nexlaMessage1 = new NexlaMessage(nexlaMessages.get(0));
    var nexlaMessage2 = new NexlaMessage(nexlaMessages.get(1));
    var nexlaMessage3 = new NexlaMessage(nexlaMessages.get(2));
    var nexlaMessage4 = new NexlaMessage(nexlaMessages.get(3));
    var message1 = new NexlaMessageContext(nexlaMessage1, nexlaMessage1, new TopicPartition("test", 1), 1L);
    var message2 = new NexlaMessageContext(nexlaMessage2, nexlaMessage2, new TopicPartition("test", 1), 2L);
    var message3 = new NexlaMessageContext(nexlaMessage3, nexlaMessage3, new TopicPartition("test", 1), 3L);
    var message4 = new NexlaMessageContext(nexlaMessage4, nexlaMessage4, new TopicPartition("test", 1), 3L);
    this.sampleMessages = List.of(message1, message2, message3, message4);
  }

  @Test
  public void testExtractMappingConfigFromMetadata() {
    var result = JdbcELTNexlaMessageExtractor.extractPrimaryKeys(sampleMessages);

    var validKeys = result.getLeft();
    var invalidKeys = result.getRight();
    assertEquals(1, invalidKeys.size());
    assertEquals(sampleMessages.get(1), invalidKeys.get(List.of("")).get(0));

    assertEquals(2, validKeys.size());
    assertEquals(2, validKeys.get(List.of("id")).size());
    assertEquals(sampleMessages.get(2), validKeys.get(List.of("id")).get(0));
    assertEquals(1, validKeys.get(List.of()).size());
    assertEquals(sampleMessages.get(0), validKeys.get(List.of()).get(0));
  }


  @Test
  public void testExtractRecordMessage() {
    var result = JdbcELTNexlaMessageExtractor.extractNexlaRecord(sampleMessages);

    assertEquals(sampleMessages.size(), result.size());
    var messageContext1 = result.get(0);
    assertEquals(sampleMessages.get(0).getOriginal(), messageContext1.getOriginal());
    var mappedRawMessage1 = messageContext1.getMapped().getRawMessage();
    assertEquals(8, mappedRawMessage1.get("id"));
    assertEquals("test insert", mappedRawMessage1.get("int_string"));
    assertEquals("float string has csv delimiter; lets see if it gets escaped", mappedRawMessage1.get("float_string"));
    assertEquals("2", mappedRawMessage1.get("string_float"));
    assertEquals("boolean string has //\\ and 'single quotes'", mappedRawMessage1.get("boolean_string"));
    assertEquals("1", mappedRawMessage1.get("new_int_column"));

    var messageContext2 = result.get(1);
    assertEquals(sampleMessages.get(1).getOriginal(), messageContext2.getOriginal());
    var mappedRawMessage2 = messageContext2.getMapped().getRawMessage();
    assertEquals(8, mappedRawMessage2.get("id"));
    assertEquals(";d;f;f;f;f;f;f;", mappedRawMessage2.get("int_string"));
    assertEquals("\\g\\g\\g\\g\\g\\", mappedRawMessage2.get("float_string"));
    assertEquals(";;;f;f;f;f", mappedRawMessage2.get("string_float"));
    assertEquals("true", mappedRawMessage2.get("boolean_string"));
    assertEquals("1", mappedRawMessage2.get("new_int_column"));

    var messageContext3 = result.get(2);
    assertEquals(sampleMessages.get(2).getOriginal(), messageContext3.getOriginal());
    var mappedRawMessage3 = messageContext3.getMapped().getRawMessage();
    assertEquals(8, mappedRawMessage3.get("id"));
    assertEquals("same id insert 2", mappedRawMessage3.get("int_string"));
    assertEquals("same id insert 2", mappedRawMessage3.get("float_string"));
    assertEquals("2", mappedRawMessage3.get("string_float"));
    assertEquals("true", mappedRawMessage3.get("boolean_string"));
    assertEquals("1", mappedRawMessage3.get("new_int_column"));
  }

  @Test
  public void testExtractMappingConfigFromMetadataWhenNoMetadataIsPresent() {
    var mappingConfig = JdbcELTNexlaMessageExtractor.extractMappingConfigFromMetadata(
        List.of(sampleMessages.get(1), sampleMessages.get(2))
    );
    assertTrue(mappingConfig.isEmpty());
  }

  @Test
  public void testExtractMappingConfigFromMetadataWhenNoMessagesAreEmpty() {
    var mappingConfig = JdbcELTNexlaMessageExtractor.extractMappingConfigFromMetadata(Lists.newArrayList());
    assertTrue(mappingConfig.isEmpty());
  }

  @Test
  public void testExtractMappingConfigFromMetadataWhenMetadataIsPresent() {
    var optionalMappingConfig = JdbcELTNexlaMessageExtractor.extractMappingConfigFromMetadata(sampleMessages);
    assertTrue(optionalMappingConfig.isPresent());
    var mappingConfig = optionalMappingConfig.get();
    assertTrue(mappingConfig.getOverriddenMappings().containsKey("person"));
    assertTrue(mappingConfig.getOverriddenMappings().get("person").isPartialEnabled());
    var overriddenDbTypeMapping = mappingConfig.getOverriddenDbTypeMapping();
    assertNotNull(overriddenDbTypeMapping);
    assertEquals(6, overriddenDbTypeMapping.getGeneral().size());
    assertEquals(2, overriddenDbTypeMapping.getColumns().size());
    assertEquals(1, overriddenDbTypeMapping.getTables().size());
  }

  @Test
  public void testStripTimezoneFromMessageFields() {
    var schema = SchemaBuilder.struct()
        .name("dataset")
        .field("id", SchemaBuilder.int64().build())
        .field("timestamp_tz", Timestamp.SCHEMA)
        .field("timestamp", Timestamp.SCHEMA)
        .field("timestamp_ntz", Timestamp.SCHEMA)
        .field("date_field", Date.SCHEMA)
        .field("timestamp_null", Timestamp.SCHEMA)
        .build();

    var nexlaMessageContext = JdbcELTNexlaMessageExtractor.extractNexlaRecord(sampleMessages).get(3);

    var strippedMessageContext = JdbcELTNexlaMessageExtractor.stripTimezoneFromTimestampAttributes(List.of(nexlaMessageContext), schema);

    assertEquals(1, strippedMessageContext.size());
    var rawMessage = strippedMessageContext.get(0).getMapped().getRawMessage();

    assertEquals("2025-12-22T22:21:49.123", rawMessage.get("timestamp_tz"));
    assertEquals("2023-12-22T22:21:49", rawMessage.get("timestamp"));
    assertEquals("2024-12-22T22:21:49.123", rawMessage.get("timestamp_ntz"));
    assertEquals("2025-04-22", rawMessage.get("date_field"));
    assertNull(rawMessage.get("timestamp_null"));
  }

  @SneakyThrows
  private String loadResourceAsString(String name) {
    return new String(this.getClass().getResourceAsStream(name).readAllBytes(), StandardCharsets.UTF_8);
  }
}