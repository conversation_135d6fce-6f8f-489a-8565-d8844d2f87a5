[{"mode": "auto", "tracker_mode": "NONE", "overridden_mappings": {"person": {"partial_enabled": "true", "name": "person_overridden_mapping_partial", "columns": {"id": {"name": "ID_Person", "type": "VARCHAR(3000)"}, "name": {"name": "Name_Person", "type": "NUMBER"}}}, "company": {"name": "company_overridden_mapping_full", "columns": {"id": {"name": "ID_Company", "type": "NUMBER(18, 3)"}, "name": {"name": "Name_company", "type": "TEXT"}}}}, "overridden_db_type_mappings": {"tables": {"person": {"NULL": "VARCHAR(5000)"}}, "columns": {"id": "NUMBER(12,10)", "name": "BINARY", "latitude": "BOOLEAN"}, "general": {"NULL": "VARCHAR(1)", "INTEGER": "INT64", "FLOAT": "VARCHAR(12)", "JSON": "VARCHAR(13)", "BOOLEAN": "VARCHAR(3)", "STRING": "VARCHAR(2500)"}}}, {"mode": "auto", "tracker_mode": "NONE", "overridden_mappings": {"person": {"partial_enabled": "true", "name": "person_overridden_mapping_partial", "columns": {"id": {"name": "ID_Person", "type": "TEXT_ID_OM"}, "name": {"name": "Name_Person", "type": "TEXT_NAME_OM"}}}}, "overridden_db_type_mappings": {"tables": {"person": {"NULL": "TEXT_T"}}, "columns": {"id": "TEXT_ID", "name": "TEXT_NAME", "latitude": "TEXT_LAT"}, "general": {"NULL": "TEXT_G", "INTEGER": "TEXT_G", "FLOAT": "TEXT_G", "JSON": "TEXT_G", "BOOLEAN": "TEXT_G", "STRING": "TEXT_G"}}}, {"mode": "auto", "tracker_mode": "NONE", "overridden_db_type_mappings": {"all_data_types": "VARCHAR(7500)"}}, {"mode": "auto", "tracker_mode": "NONE", "overridden_db_type_mappings": {"general": {"NULL": "STRING", "INTEGER": "INT64", "FLOAT": "FLOAT64", "JSON": "JSON", "BOOLEAN": "BOOLEAN", "STRING": "INT64"}}}]