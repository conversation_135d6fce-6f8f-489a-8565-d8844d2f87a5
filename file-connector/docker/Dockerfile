# base image for kafka connectors
FROM confluentinc/cp-kafka-connect-base:6.2.0
MAINTAINER Avinash "<EMAIL>"

USER root
RUN yum update -y
RUN chmod 666 /etc/kafka/connect-log4j.properties

RUN yum -y install wget libstdc++ autoconf automake libtool autoconf-archive pkg-config gcc gcc-c++ make libjpeg-devel libpng-devel libtiff-devel zlib-devel

RUN wget https://github.com/DanBloomberg/leptonica/releases/download/1.84.1/leptonica-1.84.1.tar.gz
RUN tar -xvzf leptonica-1.84.1.tar.gz
RUN cd leptonica-1.84.1 && ./configure && make -j && make install && cd ..
RUN rm leptonica-1.84.1.tar.gz

RUN wget https://github.com/tesseract-ocr/tesseract/archive/refs/tags/5.3.4.tar.gz
RUN tar -xvzf 5.3.4.tar.gz
RUN cd tesseract-5.3.4 && ./autogen.sh && PKG_CONFIG_PATH=/usr/local/lib/pkgconfig LIBLEPT_HEADERSDIR=/usr/local/include ./configure --with-extra-includes=/usr/local/include --with-extra-libraries=/usr/local/lib && make && make install && ldconfig && cd ..
RUN rm -f 5.3.4.tar.gz

# Fetch tessdata
RUN wget https://github.com/tesseract-ocr/tessdata/archive/refs/tags/4.1.0.tar.gz
RUN rm -rf /usr/local/share/tessdata/*
RUN tar -xvzf 4.1.0.tar.gz
RUN mv tessdata-4.1.0/* /usr/local/share/tessdata
RUN rm 4.1.0.tar.gz

RUN tesseract --version

USER appuser

ENV LOG_DIR "/var/log/kafka"
ENV KAFKA_LOG_DIRS "/var/lib/kafka"

ENV COMPONENT=kafka-connect

ENV KAFKA_JMX_PORT "2000"

ENV NEXLA_LOG_DIR "/var/log/nexla"
RUN mkdir -p $NEXLA_LOG_DIR && chmod -R a+rw $LOG_DIR && chmod -R a+rw $NEXLA_LOG_DIR
VOLUME ${NEXLA_LOG_DIR}

EXPOSE 8083 2000
USER appuser

COPY /source-agent/target/*.jar /app/agent.jar
