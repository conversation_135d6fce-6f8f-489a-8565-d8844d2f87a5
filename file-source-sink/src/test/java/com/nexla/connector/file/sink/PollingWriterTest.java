package com.nexla.connector.file.sink;

import com.nexla.common.ConnectionType;
import com.nexla.common.exception.AuthFailException;
import com.nexla.common.exception.NexlaException;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connect.common.OffsetsCoordinationClient;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.file.FtpConstants;
import com.nexla.connector.file.sink.names.FileNaming;
import com.nexla.file.service.FileConnectorService;
import com.nexla.test.UnitTests;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.METRICS_TOPIC;
import static com.nexla.connector.config.SinkConnectorConfig.*;
import static com.nexla.connector.config.file.FtpConstants.PASSIVE_LOCAL;
import static com.nexla.connector.properties.FileConfigAccessor.MONITOR_POLL_MS;
import static com.nexla.connector.properties.FileConfigAccessor.*;
import static java.util.Optional.empty;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Category(UnitTests.class)
public class PollingWriterTest {

	private FileConnectorService probeService;
	private FileSinkConnectorConfig config;
	private NexlaLogger logger;
	private Optional<? extends OffsetsCoordinationClient> offsetsClient = empty();
	private PollingWriter pollingWriter;
	private FileWriter writer;

	@BeforeEach
	public void setup() throws IOException {
		probeService = mock(FileConnectorService.class);
		config = new FileSinkConnectorConfig(getSinkConnectorConfigProps());
		logger = mock(NexlaLogger.class);

		this.pollingWriter = new PollingWriter(probeService, config, offsetsClient, logger);

		File tempTarFile = File.createTempFile("origin",".data");
		tempTarFile.deleteOnExit();
		FileNaming fileNaming = new FileNaming("data", tempTarFile.getPath(), "json", "json", messageContext -> String.format("%012d", messageContext.kafkaOffset));
		writer = new FileWriter(fileNaming, Map.of(), Optional.empty(), "");
		NexlaMessageContext messageContext = new NexlaMessageContext(null, null, null, 1L);
		writer.createNexlaFileWriter(Optional.empty(), messageContext);
	}

	@Test
	public void uploadDataFile_withoutRetry_Test() {
		when(probeService.writeWithSuffixIfExists(any(), any(), any(), any()))
				.thenThrow( new RuntimeException("test", new AuthFailException(" - SFTP Credentials are invalid")));

		NexlaException thrown = Assertions.assertThrows(NexlaException.class, () -> {
			pollingWriter.uploadDataFile(writer, "", false);
		});
		verify(probeService, times(3)).writeWithSuffixIfExists(any(), any(), any(), any());

		Assertions.assertEquals(" - SFTP Credentials are invalid", thrown.getCause().getCause().getMessage());
	}

	@Test
	public void uploadDataFile_withRetry_Test() {
		when(probeService.writeWithSuffixIfExists(any(), any(), any(), any()))
				.thenThrow(new RuntimeException("test"));

		NexlaException thrown = Assertions.assertThrows(NexlaException.class, () -> {
			pollingWriter.uploadDataFile(writer, "", false);
		});
		verify(probeService, times(5)).writeWithSuffixIfExists(any(), any(), any(), any());

		Assertions.assertEquals("test", thrown.getMessage());
	}

	private Map<String, String> getSinkConnectorConfigProps() {
		return new HashMap<>() {{
			put(UNIT_TEST, "true");
			put(SINK_ID, "1");
			put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
			put(FtpConstants.USERNAME, "tests");
			put(FtpConstants.PASSWORD, "test");
			put(FtpConstants.PORT, "");
			put(CREDS_ENC, "1");
			put(CREDS_ENC_IV, "1");
			put(CREDENTIALS_DECRYPT_KEY, "1");
			put(LISTING_ENABLED, "false");
			put(SINK_TYPE, ConnectionType.FTP.name());
			put(MAPPING, "{ \"mode\": \"auto\", \"mapping\": { \"country.name\": \"country_name\", \"key3\": \"vvv3\", \"key1\": \"vvv1\" } }");
			put(DATA_FORMAT, "csv");
			put(PATH, "/upload");
			put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}/city={record.city}");
			put(MAX_FILE_SIZE_MB, "0");
			put(FILE_NAME_PREFIX, "my-dataset");
			put(MONITOR_POLL_MS, "1");
			put(TRACKER_ENCRYPTION_ENABLED, "true");
			put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
			put(METRICS_TOPIC, "");
		}};
	}

}
