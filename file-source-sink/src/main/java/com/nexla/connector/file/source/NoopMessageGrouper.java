package com.nexla.connector.file.source;

import com.google.common.collect.Maps;
import com.nexla.common.metrics.Metric;
import com.nexla.connector.config.file.GroupingProps;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

public class NoopMessageGrouper implements MessageGrouper {

	@Override
	public LinkedHashMap<String, Object> getGroupMessage(GroupingProps groupingProps, List<LinkedHashMap<String, Object>> groupedRawMessages) {
		return Maps.newLinkedHashMap();
	}

	@Override
	public List<NexlaMessageFile> groupRecords(GroupingProps groupingProps, List<NexlaMessageFile> records, ReadingContext ctx, Map<Integer, Metric> datasetMetrics) {
		return Collections.emptyList();
	}
}
