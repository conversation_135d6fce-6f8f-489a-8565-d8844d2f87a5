package com.nexla.connector.file.source;

import java.util.List;

public class NoopOffsetWriter implements FileSourceOffsetWriter {

	@Override
	public void markFileAsSkipped(List<NexlaMessageFile> records, ReadingContext ctx, Exception e) {
	}

	@Override
	public void markFileAsErroneous(List<NexlaMessageFile> records, ReadingContext ctx, Exception e) {
	}

	@Override
	public void writeEofOffset(ReadingContext ctx, boolean skip, boolean error) {
	}
}
