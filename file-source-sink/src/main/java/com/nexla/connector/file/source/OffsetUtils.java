package com.nexla.connector.file.source;

import com.nexla.common.NexlaFile;

import java.util.HashMap;
import java.util.Map;

import static com.nexla.connector.file.source.TransportFileReader.ERROR_KEY;
import static com.nexla.connector.file.source.TransportFileReader.SKIP_KEY;
import static com.nexla.connector.properties.FileConfigAccessor.OFFSET_POSITION_KEY;
import static com.nexla.connector.properties.FileConfigAccessor.PARTITION_KEY;
import static java.util.Collections.singletonMap;

public class OffsetUtils {

	public static final String EOF_KEY = "eof";
	public static final String HASH_KEY = "hash";
	public static final String LAST_MODIFIED_KEY = "lastModified";
	public static final String SIZE_KEY = "size";

	public static Map<String, Object> createPartitionMap(boolean listingEnabled, NexlaFile file) {
		return singletonMap(PARTITION_KEY, listingEnabled ? file.getId() : file.getFullPath());
	}

	public static Map<String, Object> createOffsetMap(
		NexlaFile nexlaFile,
		boolean eofReached,
		long messageNumber,
		boolean skip,
		boolean error
	) {
		Map<String, Object> offsetMap = new HashMap<>(7, 1);
		offsetMap.put(OFFSET_POSITION_KEY, messageNumber);
		offsetMap.put(HASH_KEY, nexlaFile.getMd5());
		offsetMap.put(EOF_KEY, eofReached);
		offsetMap.put(LAST_MODIFIED_KEY, nexlaFile.getLastModified());
		offsetMap.put(SIZE_KEY, nexlaFile.getSize());
		if (skip) {
			offsetMap.put(SKIP_KEY, true);
		}
		if (error) {
			offsetMap.put(ERROR_KEY, true);
		}
		return offsetMap;
	}

}
