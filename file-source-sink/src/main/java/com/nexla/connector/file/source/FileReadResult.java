package com.nexla.connector.file.source;

import com.nexla.common.NexlaMessage;
import com.nexla.common.metrics.Metric;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

public interface FileReadResult<T> {

	List<T> removeResult();

	boolean acceptsMoreData();

	void acceptMessage(
		SchemaContext schema,
		ReadingContext ctx,
		NexlaMessage dataItem,
		boolean eofReached,
		long messageNumber,
		Map<Integer, Metric> datasetMetrics);

	void onEof(ReadingContext ctx, boolean skip, boolean error);

	void onSuccess(ReadingContext ctx, boolean fileEofReached, Map<Integer, Metric> datasetMetrics);

	void onException(Exception e, ReadingContext ctx);
}
