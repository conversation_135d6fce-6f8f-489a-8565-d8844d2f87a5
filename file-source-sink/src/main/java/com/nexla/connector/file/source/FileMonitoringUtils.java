package com.nexla.connector.file.source;

import com.google.common.annotations.VisibleForTesting;
import com.nexla.common.NexlaFile;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.probe.ProbeControllerConstants;
import com.nexla.common.time.NexlaTimeUnit;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

import static com.nexla.common.FileUtils.checkWhiteBlackLists;
import static com.nexla.common.time.VarUtils.VarInfo;
import static com.nexla.common.time.VarUtils.replaceVars;
import static java.util.Collections.emptyMap;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

public class FileMonitoringUtils {

	public static Stream<NexlaFile> streamFiles(
		FileSourceConnectorConfig config,
		ConnectorService<?> probeService
	) {
		return config.timeVariables.isEmpty()
			? probeService.listBucketContents(config)
			: scanRecentFolders(config, probeService);
	}

	public static Stream<NexlaFile> scanRecentFolders(
		FileSourceConnectorConfig config,
		ConnectorService<?> probeService
	) {
		DateTime endDate = new DateTime(DateTimeZone.forID(config.timezone));

		NexlaTimeUnit minTimeUnit = config.timeVariables.stream()
			.map(NexlaTimeUnit::findByPattern)
			.max(Enum::compareTo)
			.get();

		DateTime startDate = minTimeUnit.incrementFunction.apply(endDate, -config.lookbackTime);

		return StreamEx
			.iterate(startDate, date -> minTimeUnit.incrementFunction.apply(date, 1))
			.takeWhile(date -> date.compareTo(endDate) <= 0)
			.flatMap(currDate -> {

				VarInfo varInfo = new VarInfo(config.path, config.timeVariables, emptyMap());
				String modifiedPath = replaceVars(varInfo, emptyMap(), currDate, config.dateTimePadding);
				FileSourceConnectorConfig copyConfig = (FileSourceConnectorConfig) config.clone();
				copyConfig.path = modifiedPath;

				return probeService.listBucketContents(copyConfig);
			});
	}

	@VisibleForTesting
	public static Map<String, NexlaFile> getFilesFromStorage(
		FileSourceConnectorConfig config,
		ConnectorService<?> probeService
	) {
		Stream<NexlaFile> nexlaFiles = streamFiles(config, probeService);
		return getNexlaFiles(nexlaFiles, config.whiteList, config.blackList, config.ignoreOlderThanMs, config.ignoreOlderThanGapMs)
			.toMap(NexlaFile::getFullPath, nexlaFile -> nexlaFile);
	}

	@VisibleForTesting
	public static StreamEx<NexlaFile> getFileStreamFromStorage(
		FileSourceConnectorConfig config,
		ConnectorService<?> probeService
	) {
		Stream<NexlaFile> nexlaFiles = streamFiles(config, probeService);
		return getNexlaFiles(nexlaFiles, config.whiteList, config.blackList, config.ignoreOlderThanMs, config.ignoreOlderThanGapMs);
	}

	public static StreamEx<NexlaFile> getNexlaFiles(
		Stream<NexlaFile> nexlaFiles,
		Set<String> whiteList,
		Set<String> blackList,
		Optional<Long> ignoreOlderThanMs,
		Optional<Long> ignoreOlderThanGapMs
	) {
		DateTime nowUTC = DateTimeUtils.nowUTC();
		Stream<NexlaFile> filteredFiles = nexlaFiles
			.filter(file -> matchesThresholds(file, nowUTC.getMillis(), ignoreOlderThanMs, ignoreOlderThanGapMs));

		if (isNotEmpty(whiteList) || isNotEmpty(blackList)) {
			filteredFiles = filteredFiles.filter(file -> checkWhiteBlackLists(whiteList, blackList, getFullPathForPathMatching(file), file.getNameSpace()));
		}
		return StreamEx.of(filteredFiles);
	}

	private static String getFullPathForPathMatching(NexlaFile file) {
		return file.getMetadata()
				.map(it -> it.get(ProbeControllerConstants.DISPLAY_PATH))
				.map(String::valueOf)
				.filter(StringUtils::isNotBlank)
				.orElse(file.getFullPath());
	}

	private static boolean matchesThresholds(
		NexlaFile file,
		long nowUTC,
		Optional<Long> ignoreOlderThanMsConf,
		Optional<Long> ignoreOlderThanGapMsConf
	) {
		Long lastModifiedTime = file.getLastModified();
		if (lastModifiedTime == null) {
			return true;
		}
		return (!ignoreOlderThanMsConf.isPresent() || lastModifiedTime >= ignoreOlderThanMsConf.get()) &&
			   (!ignoreOlderThanGapMsConf.isPresent() || lastModifiedTime >= nowUTC - ignoreOlderThanGapMsConf.get());
	}
}
