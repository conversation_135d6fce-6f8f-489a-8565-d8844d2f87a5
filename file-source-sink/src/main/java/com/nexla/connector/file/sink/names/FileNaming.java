package com.nexla.connector.file.sink.names;

import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import lombok.Data;
import lombok.Getter;

import java.util.Optional;
import java.util.function.Function;

@Data
public class FileNaming {

	private final String filenameBase;
	private final String path;
	private final String dataFormat;
	private final String extension;
	private final Function<NexlaMessageContext, String> fileNameSuffixGenerator;

	public String getIndexFileName(TopicPartition tp, long firstRecordOffset) {
		return String.format("%s-%s-%012d.index.json", filenameBase, partitionFormat(tp.partition), firstRecordOffset);
	}

	public static String partitionFormat(int partition) {
		return String.format("%05d", partition);
	}

	public String getDataFilePath(Optional<String> identifier, NexlaMessageContext messageContext) {
		String dataFileName = String.format("%s%s-%s.%s", filenameBase, identifier.orElse(""), fileNameSuffixGenerator.apply(messageContext), extension);
		return String.format("%s/%s", path, dataFileName);
	}

	public String getIndexFilePath(TopicPartition tp, long firstRecordOffset) {
		return String.format("%s/%s", path, this.getIndexFileName(tp, firstRecordOffset));
	}

	@Override
	public String toString() {
		return "FileNaming{" +
			   "filenameBase='" + filenameBase + '\'' +
			   ", path='" + path + '\'' +
			   ", dataFormat='" + dataFormat + '\'' +
			   ", extension='" + extension + '\'' +
			   '}';
	}
}
