package com.nexla.connector.file.sink;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.file.sink.names.FileNaming;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.writer.NexlaFileWriter;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.SneakyThrows;
import org.joda.time.DateTime;

import java.io.File;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.parse.ParserConfigs.Unstructured.HEADER_DATE_FORMAT;
import static com.nexla.common.parse.ParserConfigs.Unstructured.HEADER_TEMPLATE;
import static java.util.Optional.ofNullable;

@EqualsAndHashCode(of = "dataFilePath")
public class FileWriter {

	private final Map<String, Object> options;
	private final Optional<MappingConfig> mappingConfig;
	private final String parentLoggerPrefix;
	private final Map<TopicPartition, FileStats> statsMap = Maps.newHashMap();

	private NexlaFileWriter nexlaFileWriter;

	@Getter
	private DateTime lastModified = nowUTC();

	protected final FileNaming fileNaming;

	@Getter
	String dataFilePath;
	long rawBytes;
	long messageBytes;
	long numRecords;
	int bufferedWriterSize;

	public FileWriter(
			FileNaming fileNaming,
			Map<String, Object> options,
			Optional<MappingConfig> mappingConfig,
			String parentLoggerPrefix) {
		this(fileNaming, options, mappingConfig, parentLoggerPrefix, FileSinkConnectorConfig.DEFAULT_BUFFERED_WRITER_SIZE);
	}

	public FileWriter(
			FileNaming fileNaming,
			Map<String, Object> options,
			Optional<MappingConfig> mappingConfig,
			String parentLoggerPrefix,
			int bufferedWriterSize) {
		this.options = options;
		this.mappingConfig = mappingConfig;
		this.parentLoggerPrefix = parentLoggerPrefix;
		this.fileNaming = fileNaming;
		this.bufferedWriterSize = bufferedWriterSize;
	}

	void createNexlaFileWriter(Optional<String> identifier, NexlaMessageContext messageContext) {

		if (nexlaFileWriter == null) {
			this.dataFilePath = fileNaming.getDataFilePath(identifier, messageContext);

			new File(dataFilePath).getParentFile().mkdirs();

			this.nexlaFileWriter = NexlaFileWriter
					.forExt(fileNaming.getDataFormat())
					.options(getOptions())
					.withFileName(dataFilePath)
					.withLoggerPrefix(parentLoggerPrefix)
					.withBufferedWriterSize(bufferedWriterSize);
		}
	}

	private Map<String, Object> getOptions() {
		HashMap<String, Object> richOptions = new HashMap<>(options);

		mappingConfig.flatMap(x -> ofNullable(x.getHeaderTemplate())).ifPresent(t -> richOptions.put(HEADER_TEMPLATE, t));
		mappingConfig.flatMap(x -> ofNullable(x.getDateFormat())).ifPresent(t -> richOptions.put(HEADER_DATE_FORMAT, t));

		return richOptions;
	}

	void write(NexlaMessageContext recordOffset) {
		this.write(recordOffset, Optional.empty());
	}

	void write(NexlaMessageContext recordOffset, Optional<MappingConfig> mappingConfig) {
		NexlaMessage nexlaMessage = recordOffset.getMapped();
		LinkedHashMap<String, Object> rawMessage = ofNullable(nexlaMessage.getRawMessage()).orElseGet(LinkedHashMap::new);
		int bytesNum = nexlaFileWriter.append(rawMessage, mappingConfig);
		int messageBytesToWrite = calcBytes(nexlaMessage.toJsonString());

		updateStats(bytesNum, recordOffset, messageBytesToWrite);
	}

	private void updateStats(int rawBytesToWrite, NexlaMessageContext recordOffset, int messageBytesToWrite) {
		FileStats stats = getFileStats(recordOffset.getTopicPartition());

		if (stats.firstOffset == -1) {
			stats.firstOffset = recordOffset.getKafkaOffset();
		}

		stats.rawBytes += rawBytesToWrite;
		stats.lastOffset = recordOffset.getKafkaOffset();
		stats.numRecords++;
		rawBytes += rawBytesToWrite;
		messageBytes += messageBytesToWrite;
		numRecords++;

		if (numRecords % 10 == 0) {
			this.lastModified = nowUTC();
		}
	}

	public FileStats getFileStats(TopicPartition tp) {
		return statsMap.computeIfAbsent(tp, t -> new FileStats());
	}

	public boolean containsFileStats(TopicPartition tp) {
		return statsMap.containsKey(tp);
	}

	void delete() {
		deleteIfExists(dataFilePath);
	}

	private void deleteIfExists(String path) {
		new File(path).delete();
	}

	@SneakyThrows
	void close() {
		nexlaFileWriter.close();
	}

	/**
	 * Ensure that all buffers are flushed and calculate raw bytes from disk. Calls the finish
	 * callback on the writer. Some example use cases of the finish hook:
	 * <ul>
	 *   <li>JSON and XML writers in ENTIRE_FILE mode will write the footer template</li>
	 *   <li>Binary formats will finalize the temporary JSON file, write the binary file, and remove the temporary JSON file</li>
	 * </ul>
	 * @return Result of finish callback
	 */
	public boolean flushAndFinalizeStats() {
		nexlaFileWriter.flush();
		File file = new File(nexlaFileWriter.getOutputPath());
		if (file.exists() && file.length() > 0) {
			// set actual raw bytes from disk, otherwise keep the writer value
			this.rawBytes = new File(nexlaFileWriter.getOutputPath()).length();
		}

		return nexlaFileWriter.finish();
	}

	public boolean isClosed() {
		return nexlaFileWriter.isClosed();
	}

}
