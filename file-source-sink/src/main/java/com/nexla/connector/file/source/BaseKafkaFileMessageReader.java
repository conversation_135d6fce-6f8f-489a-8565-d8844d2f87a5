package com.nexla.connector.file.source;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.Metric;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import lombok.Data;
import lombok.Getter;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaMetaData.createSourceMetadata;
import static com.nexla.common.probe.ExceptionResolution.QUARANTINE;
import static java.util.Optional.ofNullable;

@Getter
public class BaseKafkaFileMessageReader implements FileReadResult<NexlaMessageFile> {

	private final FileSourceOffsetWriter offsetWriter;
	private final MessageGrouper messageGrouper;
	private final FileSourceContext fileSourceContext;
	private final NexlaLogger logger;
	private final ConnectorService probeService;
	private final FileSourceNotificationSender notificationSender;

	private List<NexlaMessageFile> records;

	public BaseKafkaFileMessageReader(
		FileSourceOffsetWriter offsetWriter,
		MessageGrouper messageGrouper,
		FileSourceContext fileSourceContext,
		NexlaLogger logger,
		ConnectorService probeService,
		FileSourceNotificationSender notificationSender
	) {
		this.offsetWriter = offsetWriter;
		this.messageGrouper = messageGrouper;
		this.fileSourceContext = fileSourceContext;
		this.logger = logger;
		this.probeService = probeService;
		this.notificationSender = notificationSender;
		newRecordsBuffer();
	}

	@Override
	public List<NexlaMessageFile> removeResult() {
		List<NexlaMessageFile> result = records;
		newRecordsBuffer();
		return result;
	}

	@Override
	public boolean acceptsMoreData() {
		return !fileSourceContext.config.batchEnabled || records.size() < fileSourceContext.config.batchRows;
	}

	@Override
	public void acceptMessage(
		SchemaContext schema,
		ReadingContext ctx,
		NexlaMessage message,
		boolean eof,
		long messageNumber,
		Map<Integer, Metric> datasetMetrics
	) {
		var metric = datasetMetrics.computeIfAbsent(schema.dataSetId, c -> new Metric());
		FileSourceConnectorConfig sourceCfg = fileSourceContext.config;

		var nexlaMetaData = createSourceMetadata(
			sourceCfg.sourceType, sourceCfg.sourceId, sourceCfg.version,
			ctx.nexlaFile, eof, messageNumber, schema.dataSetTopic, fileSourceContext.runId,
			message.getNexlaMetaData(), schema.dataSetId);

		message.setNexlaMetaData(nexlaMetaData);

		// if no grouping, count bytes from original records,
		// otherwise it will happen after grouping
		if (sourceCfg.groupingProps.isEmpty()) {
			metric.addSize(calcBytes(toJsonString(message)));
			metric.setRecords(metric.getRecords() + 1);
		}

		records.add(new NexlaMessageFile(message, ctx.nexlaFile, schema.dataSetId));
	}

	@Override
	public void onEof(ReadingContext ctx, boolean skip, boolean error) {
		// special case where there is no record that could pass offset with eof=true to kafka-connect
		// thus, we need to update offset directly
		if (records.isEmpty()) {
			logger.info("File={}: storing eof=true offset directly", ctx.nexlaFile.getFullPath());
			offsetWriter.writeEofOffset(ctx, skip, error);
		} else {
			// last message of file might be unparseable, it is required to set eof=true to the last available record
			NexlaMessageFile lastRecord = records.get(records.size() - 1);
			lastRecord.message.getNexlaMetaData().setEof(true);
		}
	}

	public void onSuccess(ReadingContext ctx, boolean fileEofReached, Map<Integer, Metric> datasetMetrics) {
		if (fileSourceContext.config.groupingProps.isPresent() && ctx.schema != null) {
			this.records = fileSourceContext.config.groupingProps
					.map(groupingProps -> ctx.schema != null ? messageGrouper.groupRecords(groupingProps, records, ctx, datasetMetrics) : records)
					.orElse(records);

			Map<Integer, List<NexlaMessageFile>> byDsId = StreamEx
					.of(records)
					.groupingBy(x -> x.dataSetId);


			EntryStream
					.of(datasetMetrics)
					.forKeyValue((k, v) ->
							v.setRecords(ofNullable(byDsId.get(k)).map(List::size).orElse(0)));
		}

		notificationSender.sendSuccessMessage(ctx, datasetMetrics, fileEofReached, ofNullable(fileSourceContext.runId));
	}

	public void onException(Exception e, ReadingContext ctx) {
		switch (probeService.analyzeException(e)) {
			case SKIP_FILE:
				offsetWriter.markFileAsSkipped(records, ctx, e);
				break;
			case RETRY:
				offsetWriter.markFileAsErroneous(records, ctx, e);
				break;
			case QUARANTINE:
				throw new IllegalArgumentException(QUARANTINE + " should not appear here");
		}
	}

	private void newRecordsBuffer() {
		this.records = new ArrayList<>(fileSourceContext.config.batchEnabled ? fileSourceContext.config.batchRows : 1000);
	}

}
