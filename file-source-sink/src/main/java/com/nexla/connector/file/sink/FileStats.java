package com.nexla.connector.file.sink;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class FileStats {

	long rawBytes = 0;

	long firstOffset = -1;
	long lastOffset = -1;

	long numRecords = 0;

	public FileStats(long rawBytes, long firstOffset, long lastOffset, long numRecords) {
		this.rawBytes = rawBytes;
		this.firstOffset = firstOffset;
		this.lastOffset = lastOffset;
		this.numRecords = numRecords;
	}

	@Override
	public String toString() {
		return "FileStats{" +
			   "rawBytes=" + rawBytes +
			   ", firstOffset=" + firstOffset +
			   ", lastOffset=" + lastOffset +
			   ", numRecords=" + numRecords +
			   '}';
	}
}
