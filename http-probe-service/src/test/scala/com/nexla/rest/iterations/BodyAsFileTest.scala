package com.nexla.rest.iterations

import com.nexla.admin.client.CodeContainer.CodeConfig
import com.nexla.admin.client.{AdminApiClient, CodeContainer, DataSource}
import com.nexla.common.logging.NexlaLogger
import com.nexla.connector.config.rest.{RestIterationConfig, RestSourceConnectorConfig}
import com.nexla.parser.PythonCustomParser
import com.nexla.test.UnitTests
import org.junit.experimental.categories.Category
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{mock, when}
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.slf4j.LoggerFactory

import java.util.{Collections, Optional}

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class BodyAsFileTest extends AnyFlatSpecLike with Matchers {
  private val mockAdminApiClient: AdminApiClient = mock(classOf[AdminApiClient])
  private val mockRestSourceConnectorConfig: RestSourceConnectorConfig = mock(classOf[RestSourceConnectorConfig])

  behavior of "BodyAsFileTest"

  "getParser" should "return empty if custom parser not enabled and can't detect" in {
    mockDataSourceWithCodeContainer()
    val bodyAsFile = new BodyAsFile(mockRestSourceConnectorConfig, createRestIterationConfig(), null,
      new NexlaLogger(LoggerFactory.getLogger("test")), Optional.of(mockAdminApiClient),
      false, Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty())
    val parser = bodyAsFile.getParser(Optional.empty(), Array[Byte](1, 2, 3))
    parser shouldEqual Optional.empty()
  }

  it should "return empty if no admin api client and can't detect" in {
    mockDataSourceWithCodeContainer()
    val bodyAsFile = new BodyAsFile(mockRestSourceConnectorConfig, createRestIterationConfig(), null,
      new NexlaLogger(LoggerFactory.getLogger("test")), Optional.empty(),
      true, Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty())
    val parser = bodyAsFile.getParser(Optional.empty(), Array[Byte](1, 2, 3))
    parser shouldEqual Optional.empty()
  }

  it should "return customer if enabled" in {
    mockDataSourceWithCodeContainer()
    val bodyAsFile = new BodyAsFile(mockRestSourceConnectorConfig, createRestIterationConfig(), null,
      new NexlaLogger(LoggerFactory.getLogger("test")), Optional.of(mockAdminApiClient),
      true, Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty())
    val parser = bodyAsFile.getParser(Optional.empty(), Array[Byte](1, 2, 3))
    parser.get.isInstanceOf[PythonCustomParser] shouldEqual true
  }

  it should "use detected parser even if custom parser enabled" in {
    mockDataSourceWithCodeContainer()
    val bodyAsFile = new BodyAsFile(mockRestSourceConnectorConfig, createRestIterationConfig(), null,
      new NexlaLogger(LoggerFactory.getLogger("test")), Optional.of(mockAdminApiClient),
      true, Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty())
    val parser = bodyAsFile.getParser(Optional.empty(), """{"a":1}""".getBytes)
    parser.get.isInstanceOf[PythonCustomParser] shouldEqual false
  }

  private def createRestIterationConfig() = {
    val config = new RestIterationConfig(Collections.emptyMap(), "", true, 1, 1, null)
    config.parseAsFileExtension = Optional.empty()
    config
  }

  private def mockDataSourceWithCodeContainer() = {
    val testDataSource = new DataSource()
    testDataSource.setId(1)
    testDataSource.setCodeContainerId(10)
    when(mockAdminApiClient.getCodeContainer(any())).thenReturn(new CodeContainer(1, "python",
      null, "code code code", null, Optional.empty[CodeConfig]()))
    when(mockAdminApiClient.getDataSource(any())).thenReturn(Optional.ofNullable(testDataSource))
  }
}