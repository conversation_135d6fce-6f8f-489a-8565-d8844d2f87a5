package com.nexla.apistreams.spec;

import com.google.common.collect.Maps;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.jupiter.api.Test;

import java.util.LinkedHashMap;
import java.util.Map;

import static com.nexla.connector.properties.RestConfigAccessor.RESPONSE_DATA_ADDITIONAL;
import static com.nexla.connector.properties.RestConfigAccessor.RESPONSE_DATA_PATH;
import static org.assertj.core.api.Assertions.assertThat;

class RestIterationConfigProcessorTest {

  @Test
  public void shouldReplacePlaceholdersWhenPresent() {
    Map<String, Object> originals = Maps.newHashMap();
    originals.put(RESPONSE_DATA_PATH, "https://api.example.com/{userId}/details");
    originals.put(RESPONSE_DATA_ADDITIONAL, "{\"nx_attributes\":{\"force\":\"{feed_spec.nx_attr_force}\",\"attributes\": \"{feed_spec.nx_attr_force_attrs}\"}}");

    Map<String, String> replacementMap = Maps.newHashMap();
    replacementMap.put("userId", "12345");
    replacementMap.put("feed_spec.nx_attr_force_attrs", "Id,Name,Type,BillingLatitude,Nicks_Account_Number__c,AccountNumber");
    replacementMap.put("feed_spec.nx_attr_force", "true");

    Map<String, Object> result = RestIterationConfigProcessor.replaceParamsForResponseParams(originals, replacementMap);

    assertThat(result.get(RESPONSE_DATA_PATH)).isEqualTo("https://api.example.com/12345/details");
    assertThat(result.get(RESPONSE_DATA_ADDITIONAL)).isEqualTo("{\"nx_attributes\":{\"force\":\"true\",\"attributes\": \"Id,Name,Type,BillingLatitude,Nicks_Account_Number__c,AccountNumber\"}}");
  }

  @Test
  public void shouldReplacePlaceholdersWhenPresentAsMap() {
    Map<String, Object> originals = Maps.newHashMap();
    Map<String, Object> additionalData = Maps.newLinkedHashMap();
    Map<String, Object> nexAttrs = Maps.newLinkedHashMap();
    nexAttrs.put("force", "{feed_spec.nx_attr_force}");
    nexAttrs.put("attributes", "{feed_spec.nx_attr_force_attrs}");
    additionalData.put("nx_attributes", nexAttrs);
    originals.put(RESPONSE_DATA_PATH, "https://api.example.com/{userId}/details");
    originals.put(RESPONSE_DATA_ADDITIONAL, additionalData);

    Map<String, String> replacementMap = Maps.newHashMap();
    replacementMap.put("userId", "12345");
    replacementMap.put("feed_spec.nx_attr_force_attrs", "Id,Name,Type,BillingLatitude,Nicks_Account_Number__c,AccountNumber");
    replacementMap.put("feed_spec.nx_attr_force", Boolean.TRUE.toString());

    Map<String, Object> result = RestIterationConfigProcessor.replaceParamsForResponseParams(originals, replacementMap);

    assertThat(result.get(RESPONSE_DATA_PATH)).isEqualTo("https://api.example.com/12345/details");
    assertThat(result.get(RESPONSE_DATA_ADDITIONAL)).isEqualTo("{nx_attributes={force=true, attributes=Id,Name,Type,BillingLatitude,Nicks_Account_Number__c,AccountNumber}}");
  }

  @Test
  public void shouldNotModifyOriginalsIfNoPlaceholdersMatch() {
    Map<String, Object> originals = Maps.newHashMap();
    originals.put(RESPONSE_DATA_PATH, "https://api.example.com/static/path");
    originals.put(RESPONSE_DATA_ADDITIONAL, "No placeholders here");

    Map<String, String> replacementMap = Maps.newHashMap();
    replacementMap.put("userId", "12345");

    Map<String, Object> result = RestIterationConfigProcessor.replaceParamsForResponseParams(originals, replacementMap);

    assertThat(result).isEqualTo(originals);
  }

  @Test
  public void shouldHandleMissingOptionalValues() {
    Map<String, Object> originals = Maps.newHashMap();

    Map<String, String> replacementMap = Maps.newHashMap();
    replacementMap.put("userId", "12345");

    Map<String, Object> result = RestIterationConfigProcessor.replaceParamsForResponseParams(originals, replacementMap);

    assertThat(result).isEmpty();
  }
}