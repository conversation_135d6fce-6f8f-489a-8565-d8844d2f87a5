package com.nexla.probe.http;

import com.nexla.common.interceptor.DecoderUtil;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.util.Objects;

import static com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity.ERROR;
import static com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity.INFO;
import static com.nexla.connect.common.DetailedFlowInsightsSender.RequestResponseDetailedMessages;

public class FlowInsightsRestTemplateInterceptor implements ClientHttpRequestInterceptor {

    private final boolean logVerbose;
    private final DetailedFlowInsightsSender flowInsightsSender;

    public FlowInsightsRestTemplateInterceptor(boolean logVerbose, DetailedFlowInsightsSender flowInsightsSender) {
        this.logVerbose = logVerbose;
        this.flowInsightsSender = flowInsightsSender;
    }

    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        if (!logVerbose) {
            return execution.execute(request, body);
        }

        String requestMessage = formatRequestMessage(request, body);
        long startTime = System.currentTimeMillis();
        try {
            ClientHttpResponse response = execution.execute(request, body);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String responseMessage = formatResponseMessage(response, request.getHeaders(), duration);
            RequestResponseDetailedMessages messages = new RequestResponseDetailedMessages(
                requestMessage,
                responseMessage,
                startTime,
                endTime,
                INFO,
                response.getStatusCode().is2xxSuccessful() ? INFO : ERROR
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                flowInsightsSender.sendSuccess(messages);
            } else {
                flowInsightsSender.sendError(messages);
            }
            return response;
        } catch (IOException e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String errorMessage = formatErrorMessage(request, e, duration);
            RequestResponseDetailedMessages messages = new RequestResponseDetailedMessages(
                requestMessage,
                errorMessage,
                startTime,
                endTime,
                INFO,
                ERROR
            );
            flowInsightsSender.sendError(messages);
            throw e;
        }
    }

    private String formatRequestMessage(HttpRequest request, byte[] body) {
        return "REQUEST || " +
            String.format("Internal IP: %s", CloudMetadataUtil.getInternalIp()) + " || " +
            String.format("External IP: %s", CloudMetadataUtil.getExternalIp()) + " || " +
            String.format("AWS Region: %s", CloudMetadataUtil.getAWSRegion()) + " || " +
            String.format("Method: %s", request.getMethod()) + " || " +
            String.format("URI: %s", request.getURI()) + " || " +
            String.format("Headers: %s", redactHeaders(request.getHeaders())) + " || " +
            String.format("Request body length: %s", body.length) + " || " +
            String.format("First " + flowInsightsSender.getBodySizeCap() + " symbols of request body: %s",
                    StringUtils.abbreviate(DecoderUtil.tryDecode(body), flowInsightsSender.getBodySizeCap()));
    }

    private String formatResponseMessage(ClientHttpResponse response, HttpHeaders requestHeaders, long executionTimeMs) throws IOException {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("RESPONSE took %.2fs", executionTimeMs / 1000.0)); sb.append(" || ");
        sb.append(String.format("Status code: %s", response.getStatusCode())); sb.append(" || ");
        sb.append(String.format("Status text: %s", response.getStatusText())); sb.append(" || ");
        HttpHeaders responseHeaders = response.getHeaders();
        requestHeaders.forEach((key, values) -> {
            if (responseHeaders.containsKey(key) && Objects.equals(responseHeaders.get(key), values)) {
                responseHeaders.remove(key);
            }
        });
        sb.append(String.format("Headers: %s", redactHeaders(responseHeaders))); sb.append(" || ");
        String content = DecoderUtil.tryDecode(StreamUtils.copyToByteArray(response.getBody()));
        sb.append(String.format("Response body length: %s", content.length())); sb.append(" || ");
        sb.append(String.format("First " + flowInsightsSender.getBodySizeCap() + " bytes of response body: %s",
                StringUtils.abbreviate(content, flowInsightsSender.getBodySizeCap())));
        return sb.toString();
    }

    private String formatErrorMessage(HttpRequest request, IOException e, long executionTimeMs) {
        return String.format("REQUEST FAILED took %.2fs || Method: %s || URI: %s || Error: %s",
            executionTimeMs / 1000.0,
            request.getMethod(),
            request.getURI(),
            e.getMessage());
    }

    private HttpHeaders redactHeaders(HttpHeaders originalHeaders) {
        HttpHeaders redactedHeaders = new HttpHeaders();
        originalHeaders.forEach((key, values) -> {
            if (key.equals("Authorization")) {
                redactedHeaders.add(key, "REDACTED");
            } else {
                redactedHeaders.addAll(key, values);
            }
        });
        return redactedHeaders;
    }
}
