package com.nexla.probe.http;

import com.nexla.client.ScriptEvalClient;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.io.SourceAwareInputStream;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.probe.ProbeSampleResult;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.common.probe.StringSampleResult;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.soap.SoapSourceConnectorConfig;
import com.nexla.parser.xml.AdditionalPathsParser;
import com.nexla.parser.xml.XmlParser;
import com.nexla.soap.SoapIteration;
import com.nexla.soap.SoapIterationChainBuilder;
import com.nexla.soap.SoapIterationContext;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.nexla.admin.client.oauth2.NexlaTokenProvider.sameToken;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.parse.ParserConfigs.CHARSET_DETECTION_CONFIDENCE_THRESHOLD;
import static com.nexla.common.parse.ParserConfigs.Unstructured.MODE_ENTIRE_FILE;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_MODE;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_XPATH;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.http.BaseRequestSender.createSenderPool;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

public class SoapConnectorService extends ConnectorService<RestAuthConfig> {

	private final ScriptEvalClient scriptEvalClient;
	private Logger logger;

	public SoapConnectorService(ScriptEvalClient scriptEvalClient) {
		this.logger = LoggerFactory.getLogger(SoapConnectorService.class);
		this.scriptEvalClient = scriptEvalClient;
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@Override
	public AuthResponse authenticate(RestAuthConfig auth) {
		try {
			RestHeaders headers = new RestHeaders(Collections.emptyMap(), ofNullable(auth.testContentType), auth.testAcceptHeader);
			HttpCallParameters cp = new HttpCallParameters(auth.testUrl, auth.skipUrlEncoding, auth.testMethod, auth.testBody, empty(), headers);
			// XXX: always does URL encoding
			createSender(auth, sameToken(), scriptEvalClient, false, false, empty())
				.auth(cp);

			return SUCCESS;

		} catch (Exception e) {
			logger.error("[creds-{}] Exception while authenticating", auth.getCredsId(), e);
			return authError(e);
		}
	}

	@SneakyThrows
	public static StreamEx<LinkedHashMap<String, Object>> readData(
		String responseDataPath,
		Integer charsetDetectionThreshold,
		byte[] responseBytes
	) {
		return new AdditionalPathsParser(new XmlParser())
			.option(XML_XPATH, responseDataPath)
			.option(XML_MODE, MODE_ENTIRE_FILE)
			.option(CHARSET_DETECTION_CONFIDENCE_THRESHOLD, charsetDetectionThreshold.toString())
			.parseMessages(() -> new SourceAwareInputStream<>(responseBytes, new ByteArrayInputStream(responseBytes)), true)
			.map(nexlaMessage -> nexlaMessage.get().getRawMessage());
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		return StreamEx.empty();
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig config) {
		return StreamEx.empty();
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig config) {
		return false;
	}

	@Override
	public ProbeSampleResult readSample(AbstractConfig c, boolean raw) {
		SoapSourceConnectorConfig config = (SoapSourceConnectorConfig) c;
		NexlaLogger logger = new NexlaLogger(this.logger, new NexlaLogKey(ResourceType.SOURCE, config.sourceId, Optional.empty()));

		NexlaPool<RequestSender> pool = createSenderPool(
			config.requestParallelismCount,
			// XXX: always does URL encoding
			() -> createSender(config.authConfig, sameToken(), scriptEvalClient, config.logVerbose, false, empty())
				.withLoggerPrefix(logger.getPrefix(), "[request]"));

		SoapIterationChainBuilder builder = new SoapIterationChainBuilder(pool);
		List<SoapIteration> restIterations = builder.buildIterations(config.restIterationConfig, logger);
		List<SoapIterationContext> callerContexts = builder.buildContexts(restIterations, nowUTC());
		SoapIterationContext lastContext = callerContexts.get(callerContexts.size() - 1);
		Stream<String> lines = lastContext.readFirst()
			.map(this::getStringStream)
			.orElse(Stream.empty());

		List<ProbeSampleResultEntry<String>> dataStream = StreamEx
			.of(lines)
			.map(ProbeSampleResultEntry::new)
			.toList();

		return new StringSampleResult(dataStream, empty(), true);
	}

	@SneakyThrows
	private Stream<String> getStringStream(byte[] responseBytes) {
		return new BufferedReader(new InputStreamReader(new ByteArrayInputStream(responseBytes))).lines();
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig config) {
		return StreamEx.of(new NexlaFile("api", 0L, "/", "", null, null, null));
	}
}