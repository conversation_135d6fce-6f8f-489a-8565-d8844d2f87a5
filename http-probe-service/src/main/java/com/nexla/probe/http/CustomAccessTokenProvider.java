package com.nexla.probe.http;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.connector.config.rest.RestAuthConfig;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.StreamingHttpOutputMessage;
import org.springframework.security.oauth2.client.resource.OAuth2ProtectedResourceDetails;
import org.springframework.security.oauth2.client.token.AccessTokenRequest;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsAccessTokenProvider;
import org.springframework.security.oauth2.common.AuthenticationScheme;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.util.OAuth2Utils;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RequestCallback;

import java.util.Arrays;
import java.util.Map;

import static com.nexla.connector.config.rest.RestAuthConfig.DEFAULT_TOKEN_TYPE;
import static com.nexla.connector.config.rest.RestAuthConfig.OAUTH2_TOKEN_THREE_LEGGED;

class CustomAccessTokenProvider extends ClientCredentialsAccessTokenProvider {

	private final RestAuthConfig authConfig;

	public CustomAccessTokenProvider(RestAuthConfig authConfig) {
		this.authConfig = authConfig;
	}

	@Override
	public OAuth2AccessToken obtainAccessToken(
		OAuth2ProtectedResourceDetails details,
		AccessTokenRequest request
	) {
		if (authConfig.tokenExchangeType.filter(OAUTH2_TOKEN_THREE_LEGGED::equals).isPresent()) {
			DefaultOAuth2AccessToken accessToken = new DefaultOAuth2AccessToken(authConfig.vendorAccessToken);
			accessToken.setTokenType(authConfig.oauth2TokenTypeOverride.orElse(DEFAULT_TOKEN_TYPE));
			return accessToken;
		}
		DefaultOAuth2AccessToken token = (DefaultOAuth2AccessToken) super.obtainAccessToken(details, request);
		// some APIs can return token_type, that they do not actually accept,
		// for instance, API can return token_type="BearerToken", but accepts requests with "Bearer"
		authConfig.oauth2TokenTypeOverride.ifPresent(token::setTokenType);
		return token;
	}

	@Override
	protected HttpMethod getHttpMethod() {
		return authConfig.oauth2AccessTokenMethod;
	}

	@Override
	protected RequestCallback getRequestCallback(
		OAuth2ProtectedResourceDetails resource,
		MultiValueMap<String, String> form, HttpHeaders headers
	) {
		//add headers if required
		authConfig.authHeaders.forEach(headers::set);

		return authConfig.authBody
				.map(ab -> processAuthBody(resource, form, headers, ab))
				.orElseGet(() -> super.getRequestCallback(resource, form, headers));
	}

	/**
	 * Add custom fields to the form or create RequestCallback with custom JSON body
	 *
	 * @param resource Details for an OAuth2 resource.
	 * @param form     OAuth2 form
	 * @param headers  request headers
	 * @param authBody auth body with custom fields
	 * @return callback that operates on a ClientHttpRequest.
	 */
	private RequestCallback processAuthBody(OAuth2ProtectedResourceDetails resource,
											MultiValueMap<String, String> form,
											HttpHeaders headers,
											String authBody) {

		return authConfig.oauth2ClientAuthScheme
				.filter(sch -> AuthenticationScheme.valueOf(sch).equals(AuthenticationScheme.form))
				.map(sch -> {
					// convert json to map to send additional params in form
					Map<String, Object> authBodyMap = JsonUtils.jsonToMap(authBody);

					//clean up everything related to the 'client_credentials' including 'scope:read write'
					String grantType = (String) authBodyMap.getOrDefault(OAuth2Utils.GRANT_TYPE, "");
					if(grantType.equals("password")) {
						logger.info("Clear FORM from previous 'client_credentials' GRANT_TYPE: " + form.toString());
						form.clear();
					}
					JsonUtils.jsonToMap(authBody)
							.forEach((k, v) -> form.set(k, v.toString()));
					logger.info("FORM was adjusted with new values from 'oauth2.auth.body' prop: " + form.toString());
					return super.getRequestCallback(resource, form, headers);
				})
				.orElseGet(() -> customAuthBody(authBody, headers));
	}

	private RequestCallback customAuthBody(String authBody, HttpHeaders headers) {
		return request -> {
			request.getHeaders().putAll(headers);
			request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
			request.getHeaders().setAccept(
					Arrays.asList(MediaType.APPLICATION_JSON, MediaType.APPLICATION_FORM_URLENCODED));

			byte[] bytes = authBody.getBytes();
			request.getHeaders().setContentLength(bytes.length);

			if (request instanceof StreamingHttpOutputMessage) {
				StreamingHttpOutputMessage streamingOutputMessage = (StreamingHttpOutputMessage) request;
				streamingOutputMessage.setBody(outputStream -> StreamUtils.copy(bytes, outputStream));
			} else {
				StreamUtils.copy(bytes, request.getBody());
			}
		};
	}
}
