package com.nexla.soap;

import com.google.common.collect.Lists;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.soap.SoapIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.soap.iterations.SoapStaticIteration;
import one.util.streamex.StreamEx;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Optional.empty;
import static java.util.Optional.of;

public class SoapIterationChainBuilder {

	private final NexlaPool<RequestSender> senderPool;
	private final SoapWsdlParser soapParser;

	public SoapIterationChainBuilder(NexlaPool<RequestSender> pool) {
		this.senderPool = pool;
		this.soapParser = new SoapWsdlParser(senderPool);
	}

	public List<SoapIterationContext> buildContexts(List<SoapIteration> restIterations, DateTime dateTime) {
		List<SoapIterationContext> contexts = Lists.newArrayList();
		SoapIteration lastElem = restIterations.get(restIterations.size() - 1);
		StreamEx.of(restIterations).foldLeft(Optional.<SoapIterationContext>empty(), (parentContext, elem) -> {

			Optional<SoapIterationOffset> offset = parentContext
				// initial state for child rest calls
				.map(e -> Optional.<SoapIterationOffset>empty())
				//initial state for root - parentMessageNumber here is not used as root has no parents
				.orElseGet(() -> of(elem.createStartOffset(0, dateTime)));

			SoapIterationContext callerContext = new SoapIterationContext(
				parentContext,
				elem,
				empty(),
				offset,
				0,
				elem == lastElem,
				dateTime);
			contexts.add(callerContext);
			return of(callerContext);
		});
		return contexts;
	}

	private List<SoapIterationContext> buildContextsFromOffsetMap(
		List<SoapIteration> restIterations,
		Map<String, Object> offsetMap,
		DateTime now
	) {
		List<SoapIterationContext> contexts = Lists.newArrayList();
		SoapIteration lastElem = restIterations.get(restIterations.size() - 1);
		StreamEx.of(restIterations).foldLeft(Optional.<SoapIterationContext>empty(), (prev, elem) -> {

			String offsetJson = offsetMap.get(elem.getCode()).toString();
			SoapIterationOffset callerOffset = elem.offsetFromJson(offsetJson);

			Optional<SoapIterationOffset> nextOffset = of(callerOffset).filter(o -> !o.isTerminal());
			SoapIterationContext newContext = new SoapIterationContext(
				prev,
				elem,
				empty(),
				nextOffset,
				callerOffset.getParentMessageNumber(),
				elem == lastElem,
				now
			);
			contexts.add(newContext);
			return of(newContext);
		});

		return contexts;
	}

	public List<SoapIteration> buildIterations(List<SoapIterationConfig> configList, NexlaLogger logger) {
		return StreamEx.of(configList)
			.map(config -> (SoapIteration) new SoapStaticIteration(config, senderPool, soapParser, logger))
			.toList();
	}

}
