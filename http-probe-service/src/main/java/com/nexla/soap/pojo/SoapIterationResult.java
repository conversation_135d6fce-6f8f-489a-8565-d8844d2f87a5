package com.nexla.soap.pojo;

import com.nexla.common.metrics.Metric;
import com.nexla.soap.SoapIterationOffset;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SoapIterationResult<T extends SoapIterationOffset> {

	private final List<SoapResultEntry> entries;
	private final T offset;
	private final Optional<T> nextOffset;
	private final Metric metric;
	private final String url;
	private final AtomicLong byteCounter = new AtomicLong();

	public SoapIterationResult(List<SoapResultEntry> entries, T offset, Optional<T> nextOffset, String url) {
		this.entries = entries;
		this.offset = offset;
		this.nextOffset = nextOffset;
		this.url = url;
		this.metric = new Metric();
	}
}