package com.nexla.soap;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.soap.SoapWsdlConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.pojo.UrlBody;
import com.nexla.soap.wsdl.WsdlBinding;
import com.nexla.soap.wsdl.WsdlDefinition;
import com.nexla.soap.wsdl.WsdlOperation;
import com.nexla.soap.wsdl.WsdlParameter;
import com.nexla.soap.wsdl.WsdlPort;
import com.nexla.soap.wsdl.WsdlService;
import com.predic8.wsdl.Definitions;
import com.predic8.wsdl.WSDLElement;
import com.predic8.wsdl.WSDLParser;
import com.predic8.wstool.creator.RequestCreator;
import com.predic8.wstool.creator.RequestTemplateCreator;
import com.predic8.wstool.creator.SOARequestCreator;
import groovy.xml.MarkupBuilder;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

public class SoapWsdlParser {

	private final Map<String, Definitions> definitions;
	private final WSDLParser parser;
	private final LoadingCache<String, Definitions> definitionsCache;

	@SneakyThrows
	public SoapWsdlParser(NexlaPool<RequestSender> senderPool) {
		this.parser = new WSDLParser();
		this.definitions = Maps.newHashMap();

		this.definitionsCache = CacheBuilder.newBuilder()
			.expireAfterWrite(30, TimeUnit.MINUTES)
			.build(new CacheLoader<String, Definitions>() {

				@Override
				public Definitions load(String wsdlPath) {
					return definitions.computeIfAbsent(wsdlPath, key -> {
						RestHeaders headers = new RestHeaders(Collections.emptyMap(), of(MediaType.APPLICATION_XML_VALUE), empty());
						// XXX: does URL encoding as expected.
						HttpCallParameters cp = new HttpCallParameters(wsdlPath, false, HttpMethod.GET, empty(), empty(), headers);
						String wsdl = senderPool.withPooledObject(pool -> pool.send(cp))
							.getBody()
							.map(String::new)
							.get();

						return parser.parse(IOUtils.toInputStream(wsdl, StandardCharsets.UTF_8));
					});
				}
			});
	}

	@SneakyThrows
	public WsdlDefinition readSoapWsdlDefinitions(String wsdlUrl) {
		Definitions defs = definitionsCache.get(wsdlUrl);

		Map<String, List<String>> serviceToPorts = StreamEx
			.of(defs.getServices())
			.toMap(
				WSDLElement::getName,
				ser -> StreamEx
					.of(ser.getPorts())
					.map(WSDLElement::getName)
					.toList());

		Map<String, String> portToLocation = StreamEx
			.of(defs.getServices())
			.flatMap(service -> StreamEx.of(service.getPorts()))
			.mapToEntry(WSDLElement::getName, port -> port.getAddress().getLocation())
			.toMap();

		Map<String, String> bindingToPortType = StreamEx
			.of(defs.getBindings())
			.filter(b -> b.getBinding().getClass().getName().contains("SOAPBinding"))
			.toMap(WSDLElement::getName, bin -> bin.getPortType().getName());

		Map<String, List<String>> portTypeToOperations = StreamEx
			.of(defs.getPortTypes())
			.toMap(WSDLElement::getName, ser -> StreamEx.of(ser.getOperations()).map(WSDLElement::getName).toList());

		Map<String, Map<String, List<WsdlParameter>>> bindingOpParams = EntryStream
			.of(bindingToPortType)
			.mapToValue((bind, portType) -> getOperationParameters(defs, bind, portType, portTypeToOperations))
			.toMap();

		List<WsdlService> services = EntryStream
			.of(serviceToPorts)
			.mapKeyValue((serviceName, ports) ->
				new WsdlService(
					serviceName,
					getWsdlPorts(portToLocation, ports),
					getWsdlBindings(bindingToPortType, portTypeToOperations, bindingOpParams)))
			.toList();

		return new WsdlDefinition(services);
	}

	private List<WsdlPort> getWsdlPorts(Map<String, String> portToLocation, List<String> p) {
		return p.stream()
			.map(port -> new WsdlPort(port, portToLocation.get(port)))
			.collect(Collectors.toList());
	}

	private List<WsdlBinding> getWsdlBindings(
		Map<String, String> bindingToPortType,
		Map<String, List<String>> portTypeToOperations,
		Map<String, Map<String, List<WsdlParameter>>> bindingOpParams
	) {
		return EntryStream.of(bindingToPortType)
			.mapKeyValue((b, pt) -> {
				List<WsdlOperation> ops = portTypeToOperations.get(pt)
					.stream()
					.map(op -> new WsdlOperation(op, bindingOpParams.get(b).get(op)))
					.collect(Collectors.toList());

				return new WsdlBinding(b, pt, ops);
			})
			.sorted(Comparator.comparing(WsdlBinding::getName))
			.toList();
	}

	private Map<String, List<WsdlParameter>> getOperationParameters(
		Definitions defs,
		String binding,
		String portType,
		Map<String, List<String>> portTypeToOperations
	) {
		Map<String, List<WsdlParameter>> map = StreamEx
			.of(portTypeToOperations.get(portType))
			.mapToEntry(
				opp -> opp,
				opp -> toWsdlParameter(defs, opp, binding, portType))
			.toMap();

		return new TreeMap<>(
			EntryStream.of(map)
				.mapValues(v -> StreamEx.of(v)
					.sorted(Comparator.comparing(WsdlParameter::getPath))
					.toList())
				.toMap());
	}

	private List<WsdlParameter> toWsdlParameter(Definitions defs, String operation, String bind, String portType) {
		StringWriter soapBody = new StringWriter();
		RequestTemplateCreator templateCreator = new RequestTemplateCreator();
		SOARequestCreator creator = new SOARequestCreator(defs, templateCreator, new MarkupBuilder(soapBody));
		creator.createRequest(portType, operation, bind);
		return EntryStream.of(templateCreator.elementsInfo)
			.mapKeyValue((k, v) ->
				new WsdlParameter(
					k,
					v.type,
					v.possibleValues.isEmpty() ? empty() : of(v.possibleValues),
					ofNullable(v.pattern),
					parseToInt(v.maxLength),
					parseToInt(v.maxDigits),
					parseToInt(v.fractionDigits),
					parseToInt(v.minLength),
					parseToInt(v.minInclusive),
					parseToInt(v.minExclisive),
					parseToInt(v.maxInclusive),
					parseToInt(v.maxExclusive),
					parseToInt(v.minOccurs),
					parseToInt(v.maxOccurs)
				)
			)
			.toList();
	}

	private Optional<Integer> parseToInt(String str) {
		if (!StringUtils.isNumeric(str)) {
			return empty();
		} else {
			return of(Integer.parseInt(str));
		}
	}

	@SneakyThrows
	public UrlBody getUrlBody(String wsdlUrl, SoapWsdlConfig soapConf, Map<String, String> formParams) {
		Definitions defs = definitionsCache.get(wsdlUrl);
		StringWriter soapBody = new StringWriter();
		SOARequestCreator creator = new SOARequestCreator(defs, new RequestCreator(), new MarkupBuilder(soapBody));
		creator.setFormParams(formParams);
		creator.createRequest(soapConf.getPortType(), soapConf.getOperation(), soapConf.getBinding());
		String url = defs.getServices()
			.stream()
			.filter(e -> e.getName().equals(soapConf.getService()))
			.findAny()
			.get()
			.getPorts()
			.stream()
			.filter(e -> e.getName().equals(soapConf.getServicePort()))
			.findAny()
			.get()
			.getAddress()
			.getLocation();

		return new UrlBody(url, soapBody.toString(), null);
	}

}