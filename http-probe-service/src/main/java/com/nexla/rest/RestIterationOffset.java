package com.nexla.rest;

import com.nexla.common.StreamUtils;
import lombok.Getter;
import lombok.SneakyThrows;
import org.joda.time.DateTime;

import java.beans.Transient;
import java.util.Optional;

import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.datetime.DateTimeUtils.validateTZ;
import static java.util.Optional.empty;
import static java.util.Optional.of;

@Getter
public class RestIterationOffset {

	public final static Optional<RestIterationOffset> NO_OFFSET_DATA = empty();
	public final static RestIterationOffset TERMINAL_OFFSET = new RestIterationOffset();

	private final DateTime dateTime;
	private final Integer parentMessageNumber;
	private final Integer messageNumber;
	private final Optional<Integer> pageSize;
	private final Optional<Boolean> isTerminal;

	public RestIterationOffset(Integer messageNumber, Optional<Integer> pageSize, Integer parentMessageNumber, DateTime dateTime) {
		this.messageNumber = messageNumber;
		this.pageSize = pageSize;
		this.parentMessageNumber = parentMessageNumber;
		this.dateTime = validateTZ(dateTime);
		this.isTerminal = empty();
	}

	private RestIterationOffset() {
		this.parentMessageNumber = -1;
		this.messageNumber = -1;
		this.pageSize = empty();
		this.dateTime = nowUTC();
		this.isTerminal = of(true);
	}

	@Transient
	public Boolean getRetryOnIncomplete() {
		return false;
	}

	@SneakyThrows
	public String toJson() {
		return StreamUtils.jsonUtil().toJsonString(this);
	}

	@SneakyThrows
	public static <T extends RestIterationOffset> T fromJson(String json, Class<T> valueType) {
		return StreamUtils.jsonUtil().stringToType(json, valueType);
	}

	@Transient
	public boolean isTerminal() {
		return isTerminal.orElse(false);
	}

}

