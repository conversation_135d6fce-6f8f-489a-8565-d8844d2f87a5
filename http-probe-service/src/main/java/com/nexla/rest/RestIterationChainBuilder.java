package com.nexla.rest;

import com.google.common.collect.Lists;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.admin.client.oauth2.RefreshingTokenProvider;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import com.nexla.connector.config.rest.IterationType;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.connector.config.rest.RestSourceConnectorConfig;
import com.nexla.connector.properties.RestConfigAccessor;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.rest.iterations.*;
import com.nexla.rest.iterations.graphql.GraphqlCursorIteration;
import com.nexla.rest.iterations.graphql.GraphqlPageIteration;
import com.nexla.rest.pojo.RestSourceError;
import one.util.streamex.StreamEx;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.rest.RestAuthConfig.VENDOR_REFRESH_TOKEN;
import static com.nexla.connector.properties.RestConfigAccessor.AUTH_TYPE;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.http.BaseRequestSender.createSenderPool;
import static java.util.Optional.empty;
import static java.util.Optional.of;

public class RestIterationChainBuilder {

	private final Map<IterationType, Function<RestIterationConfig, RestIteration>> restCallerFactoryMap;
	private final Optional<Runnable> heartbeatCallback;
	private final Optional<AdminApiClient> adminApiClient;
	private final boolean enableCustomParser;
	private final Optional<RestConnectorService> probeService;
	private final Optional<ControlMessageProducer> controlMessageProducer;
	private final Optional<Integer> dataSourceId;
	private final Optional<RestSourceError> restSourceError;
	private final RestSourceConnectorConfig restSourceConfig;
	private final Optional<DetailedFlowInsightsSender> flowInsightsSender;

	public RestIterationChainBuilder(
			NexlaLogger logger,
			boolean enableCustomParser,
			Optional<Runnable> heartbeatCallback,
			Optional<AdminApiClient> adminApiClient,
			Optional<RestConnectorService> probeService,
      Optional<ControlMessageProducer> controlMessageProducer,
			Optional<Integer> dataSourceId,
			Optional<RestSourceError> restSourceError,
			RestSourceConnectorConfig restSourceConfig,
			Optional<DetailedFlowInsightsSender> flowInsightsSender
		) {
		this.restCallerFactoryMap = getRestCallerFactoryMap(logger);
		this.heartbeatCallback = heartbeatCallback;
		this.adminApiClient = adminApiClient;
		this.enableCustomParser = enableCustomParser;
		this.probeService = probeService;
		this.controlMessageProducer = controlMessageProducer;
		this.dataSourceId = dataSourceId;
		this.restSourceError = restSourceError;
		this.restSourceConfig = restSourceConfig;
		this.flowInsightsSender = flowInsightsSender;
	}

	public List<RestIteration> buildIterations(List<RestIterationConfig> configList) {
		return StreamEx.of(configList)
			.map(config -> restCallerFactoryMap.get(config.iterationType).apply(config))
			.toList();
	}

	public List<RestIterationContext> buildContexts(List<RestIteration> restIterations, DateTime now) {
		List<RestIterationContext> contexts = Lists.newArrayList();
		RestIteration lastElem = restIterations.get(restIterations.size() - 1);

		StreamEx.of(restIterations).foldLeft(Optional.<RestIterationContext>empty(), (parentContext, restIteration) -> {

			Optional<RestIterationOffset> nextOffset = parentContext
				// initial state for child rest calls
				.map(e -> Optional.<RestIterationOffset>empty())
				//initial state for root - parentMessageNumber here is not used as root has no parents
				.orElseGet(() -> of(restIteration.createStartOffset(0, now)));

			RestIterationContext newContext = new RestIterationContext(
				parentContext,
				restIteration,
				empty(),
				nextOffset,
				0,
				restIteration == lastElem,
				now
			);
			contexts.add(newContext);
			return of(newContext);
		});
		return contexts;
	}

	private List<RestIterationContext> buildContextsFromOffsetMap(
		List<RestIteration> restIterations,
		Map<String, Object> offsetMap,
		DateTime now
	) {
		List<RestIterationContext> contexts = Lists.newArrayList();
		RestIteration lastElem = restIterations.get(restIterations.size() - 1);
		StreamEx.of(restIterations).foldLeft(Optional.<RestIterationContext>empty(), (parentContext, elem) -> {

			String offsetJson = offsetMap.get(elem.getCode()).toString();
			RestIterationOffset callerOffset = elem.offsetFromJson(offsetJson);

			Optional<RestIterationOffset> nextOffset = of(callerOffset).filter(o -> !o.isTerminal());
			RestIterationContext newContext = new RestIterationContext(
				parentContext,
				elem,
				empty(),
				nextOffset,
				callerOffset.getParentMessageNumber(),
				elem == lastElem,
				now
			);
			contexts.add(newContext);
			return of(newContext);
		});

		return contexts;
	}

	private Map<IterationType, Function<RestIterationConfig, RestIteration>> getRestCallerFactoryMap(NexlaLogger logger) {
		Map<IterationType, Function<RestIterationConfig, RestIteration>> map = new HashMap<>();
		map.put(IterationType.DATA_MAP_KEY_QUEUE, config -> new DataMapQueue(config, getPool(logger, config), logger));
		map.put(IterationType.PAGING_INCREMENTING, config -> new PagingIncrementing(config, getPool(logger, config), logger));
		map.put(IterationType.PAGING_INCREMENTING_OFFSET, config -> new PagingIncrementingOffset(config, getPool(logger, config), logger));
		map.put(IterationType.PAGING_NEXT_TOKEN, config -> new PagingNextToken(config, getPool(logger, config), logger));
		map.put(IterationType.PAGING_NEXT_URL, config -> new PagingNextUrl(config, getPool(logger, config), logger));
		map.put(IterationType.RESPONSE_ID_NUMBER, config -> new ResponseIdNumber(config, getPool(logger, config), logger));
		map.put(IterationType.RESPONSE_ID_STRING, config -> new ResponseIdString(config, getPool(logger, config), logger));
		map.put(IterationType.STATIC_URL, config -> new StaticUrl(config, getPool(logger, config), logger));
		map.put(IterationType.HORIZONTAL_ITERATION, config -> new HorizontalIteration(config, getPool(logger, config), logger));
		map.put(IterationType.EMIT_ONCE, config -> new EmitOnce(config, getPool(logger, config), logger));
		map.put(IterationType.LINK_HEADER, config -> new LinkHeaderIteration(config, getPool(logger, config), logger));
		map.put(IterationType.ASYNC_ITERATION, config -> new AsyncWaitIteration(config, getPool(logger, config), logger, heartbeatCallback));
		
		map.put(IterationType.BODY_AS_FILE_ITERATION, config -> new BodyAsFile(restSourceConfig, config, getPool(logger, config), logger, adminApiClient, enableCustomParser, probeService, controlMessageProducer, dataSourceId, restSourceError));
		map.put(IterationType.GRAPHQL_CURSOR_ITERATION, config -> new GraphqlCursorIteration(config, getPool(logger, config), logger));
		map.put(IterationType.GRAPHQL_PAGE_ITERATION, config -> new GraphqlPageIteration(config, getPool(logger, config), logger));

		map.put(IterationType.CODE_CONTAINER_ITERATION, config -> new CodeContainerIteration(config, getPool(logger, config), logger, adminApiClient));
		return map;
	}

	private NexlaPool<RequestSender> getPool(NexlaLogger logger, RestIterationConfig config) {
		ScriptEvalClient scriptEvalClient = new ScriptEvalClient(restSourceConfig.probeAppUrl, restSourceConfig.nexlaUsername,
				restSourceConfig.nexlaPassword, new RestTemplateBuilder().withSSL(restSourceConfig.nexlaSslConfig).withLogVerbose(restSourceConfig.logVerbose).build()
		);

		AdminApiClient apiClient = adminApiClient.get();
		Map<String, Object> restAuthOriginals = new HashMap<>(config.authConfig.originals());
		restAuthOriginals.remove(VENDOR_REFRESH_TOKEN);
		restAuthOriginals.put(AUTH_TYPE, RestConfigAccessor.AuthType.NONE);
		
		RestAuthConfig restAuthConfig = config.noAuthIteration
				? new RestAuthConfig(restAuthOriginals, -1)
				: config.dataCredentialsId
				.map(id -> {
					DataCredentials dataCredentials = apiClient.getDataCredentials(id).get();
					return new RestAuthConfig(getCreds(config.credentialsDecryptKey, dataCredentials.getCredentialsEnc(), dataCredentials.getCredentialsEncIv()), id);
				})
				.orElse(restSourceConfig.authConfig);

		RefreshingTokenProvider tokenProvider = new RefreshingTokenProvider(apiClient, config.credentialsDecryptKey);

		return createSenderPool(
				config.requestParallelismCount,
				() -> createSender(restAuthConfig, tokenProvider, scriptEvalClient, restSourceConfig.logVerbose, config.skipUrlEncoding, this.flowInsightsSender)
						.withLoggerPrefix(logger.getPrefix(), "[request]")
		);
	}
	
}
