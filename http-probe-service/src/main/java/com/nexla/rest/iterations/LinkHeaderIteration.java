package com.nexla.rest.iterations;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.LinkHeaderOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Optional.empty;

public class LinkHeaderIteration extends RestIteration<LinkHeaderOffset> {

	public static final String LINK_HEADER = "Link";

	public LinkHeaderIteration(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class getOffsetClass() {
		return LinkHeaderOffset.class;
	}

	@Override
	@SneakyThrows
	protected String getRequestUrl(LinkHeaderOffset prevCtx, String url) {
		return prevCtx.getNext().orElse(url);
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> unused,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		LinkHeaderOffset offset,
		String url
	) {
		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> {
				LinkHeaderOffset off = new LinkHeaderOffset(
					offsetOnPage,
					offset.getLast(),
					offset.getNext(),
					offset.getParentMessageNumber(),
					offset.getDateTime()
				);
				return new ResultEntry(data.getRawMessage(), offsetOnPage, url, off, headers.toSingleValueMap(), null);
			})
			.toList();

		List<String> linkHeader = headers.get(LINK_HEADER);
		Optional<LinkHeaderOffset> nextCallContext = nextOffset(offset, url, linkHeader);
		return new ParsedData(records, offset, nextCallContext);

	}

	private Optional<LinkHeaderOffset> nextOffset(LinkHeaderOffset offset, String url, List<String> linkHeader) {

		if (linkHeader == null) {
			return empty();
		}

		LinkHeaderParser parsedLink = new LinkHeaderParser(linkHeader.get(0));

		// if next points to the same url as was requested - stop
		boolean isLast = offset.getNext()
			.filter(next -> url.equals(parsedLink.getNext().orElse(null)))
			.isPresent();

		if (isLast || !parsedLink.getNext().isPresent()) {
			return empty();
		}

		return Optional.of(new LinkHeaderOffset(
			0,
			parsedLink.getLast(),
			parsedLink.getNext(),
			offset.getParentMessageNumber(),
			offset.getDateTime()
		));
	}

	@Override
	public LinkHeaderOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new LinkHeaderOffset(0, empty(), empty(), parentMessageNumber, dateTime);
	}

}