package com.nexla.rest.iterations.offsets;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.rest.RestIterationOffset;
import lombok.Getter;
import org.joda.time.DateTime;

import java.util.Optional;

import static java.util.Optional.empty;

@Getter
public class LinkHeaderOffset extends RestIterationOffset {

	private final Optional<String> last;
	private final Optional<String> next;

	public LinkHeaderOffset(
		@JsonProperty("messageNumber") Integer messageNumber,
		@JsonProperty("last") Optional<String> last,
		@JsonProperty("next") Optional<String> next,
		@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
		@JsonProperty("dateTime") DateTime dateTime
	) {
		super(messageNumber, empty(), parentMessageNumber, dateTime);
		this.next = next;
		this.last = last;
	}

}