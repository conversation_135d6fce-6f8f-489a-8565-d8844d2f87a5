package com.nexla.rest.iterations;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.Transform;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.CodeContainerOffset;
import com.nexla.rest.pojo.*;
import com.nexla.transform.*;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import scala.util.Either;
import scala.util.Left;
import scala.util.Right;

import java.util.*;
import java.util.stream.Collectors;

public class CodeContainerIteration extends RestIteration<CodeContainerOffset> {
    private final TransformService transformService;
    private final Object transformSpec;

    public CodeContainerIteration(
            RestIterationConfig config,
            NexlaPool<RequestSender> senderPool,
            NexlaLogger logger,
            Optional<AdminApiClient> adminApiClient
    ) {
        super(config, senderPool, logger);

        this.transformService = new TransformServiceImpl();
        Transform transform = adminApiClient.orElseThrow().getTransform(config.codeContainerId.orElseThrow());
        var transformCodeSpec = transform.getCode().get(0).getSpec();

        this.transformSpec = Collections.singletonList(new HashMap<>() {{
            put("operation", "nexla.custom");
            put("spec", new HashMap<>() {{
                put(Custom.SpecKey.LANGUAGE.toString(), transformCodeSpec.getLanguage());
                put(Custom.SpecKey.ENCODING.toString(), transformCodeSpec.getEncoding());
                put(Custom.SpecKey.SCRIPT.toString(), transformCodeSpec.getScript());
            }});
        }});
    }

    @Override
    protected Class getOffsetClass() { return CodeContainerOffset.class; }

    @Override
    public RestIterationResult<?> readRecords(Map<String, String> substituteMap, Optional<RestIterationResult> lastResult, CodeContainerOffset offset) {
        if (config.url != null) {
            return super.readRecords(substituteMap, offset);
        }

        // if no url and no data, something is wrong
        RestIterationResult<?> previousResult = lastResult.orElseThrow();

        List<ResultEntry> resultEntries = previousResult.getEntries();
        var inputs = resultEntries.stream()
                .map(ResultEntry::getDataMap)
                .collect(Collectors.toList());

        ParsedData<CodeContainerOffset> parsedData = doTransform(new Left<>(inputs), offset, null);
        return new RestIterationResult<>(parsedData.getEntries(), parsedData.getOffset(), parsedData.getNextOffset(), "");
    }

    private ParsedData<CodeContainerOffset> doTransform(Either<List<LinkedHashMap>,List<NexlaMessage>> inputs, CodeContainerOffset offset, HttpHeaders headers) {
        List<TransformerResult> transformResults;
        if (inputs.isLeft()) {
            transformResults = transformService.transform(inputs.left().get(), transformSpec);
        } else {
            transformResults = transformService.transform(inputs.right().get(), transformSpec);
        }

        List<ResultEntry> results = StreamUtils.zipWithIndices(transformResults.stream())
                .map(entry -> new ResultEntry<>(
                        new LinkedHashMap<>((Map<String, Object>) entry.getValue().getOutput()),
                        entry.getKey(),
                        null,
                        offset,
                        headers == null ? Collections.emptyMap() : headers.toSingleValueMap(),
                        null
                ))
                .collect(Collectors.toList());

        return new ParsedData<>(results, offset, Optional.empty());
    }

    @Override
    protected ParsedData<?> createCallResult(
            Optional<byte[]> responseBytes,
            HttpHeaders headers,
            EntryStream<Integer, NexlaMessage> recordStream,
            CodeContainerOffset prevCtx,
            String url
    ) {
        // used when the iteration has a url
        return doTransform(new Right<>(recordStream.map(Map.Entry::getValue).collect(Collectors.toList())), prevCtx, headers);
    }

    @Override
    protected CodeContainerOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
        return new CodeContainerOffset(0, Optional.empty(), parentMessageNumber, dateTime);
    }
}
