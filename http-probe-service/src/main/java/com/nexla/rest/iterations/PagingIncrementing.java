package com.nexla.rest.iterations;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.PagingIncrementingOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.ResultEntry;
import com.nexla.rest.pojo.UrlBody;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.google.common.base.Throwables.getRootCause;
import static com.nexla.common.StreamUtils.isInstance;
import static com.nexla.common.time.VarUtils.VarInfo;
import static java.util.Optional.*;

public class PagingIncrementing extends RestIteration<PagingIncrementingOffset> {

	private final ExecutorService executorService;
	private final Integer requestParallelismCount;

	public PagingIncrementing(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
		this.requestParallelismCount = config.requestParallelismCount;
		if (isParallelMode()) {
			this.executorService = Executors.newWorkStealingPool(config.requestParallelismCount);
		} else {
			this.executorService = null;
		}
	}

	@Override
	@SneakyThrows
	public Map<String, Object> getParameters(PagingIncrementingOffset offset) {
		Map<String, Object> params = Maps.newHashMap();
		if (config.paramIdMacro.isPresent()) {
			return params;
		}
		if (config.pageValueIsNumber) {
			params.put(config.paramId, offset.getPage());
		} else {
			params.put(config.paramId, offset.getPage().toString());
		}

		config.paramPageSize.ifPresent(paramPageSize ->{
			if (config.pageValueIsNumber) {
				params.put(paramPageSize, config.pageExpectedRows.get());
			} else {
				params.put(paramPageSize, config.pageExpectedRows.get().toString());
			}
		});

		return params;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordsStream,
		PagingIncrementingOffset offset,
		String url
	) {
		var recordsMap = recordsStream.toMap();
		List<ResultEntry<PagingIncrementingOffset>> result = EntryStream.of(recordsMap)
			.mapKeyValue((offsetOnPage, data) -> {
				PagingIncrementingOffset sourceOffset = createOffset(
					offset.getParentMessageNumber(),
					0,
					false,
					offset.getPage(),
					offsetOnPage,
					of(recordsMap.size()),
					offset.getDateTime()
				);
				return new ResultEntry<>(data.getRawMessage(), offsetOnPage, offset.getPage(), sourceOffset, headers.toSingleValueMap(), null);
			})
			.toList();

		List<ResultEntry<PagingIncrementingOffset>> resultEntries = result
			.stream()
			.skip(offset.getSkipMessages())
			.collect(Collectors.toList());

		Optional<PagingIncrementingOffset> nextCallContext = nextOffset(offset, result);

		return new ParsedData(resultEntries, offset, nextCallContext);
	}

	private Optional<PagingIncrementingOffset> nextOffset(PagingIncrementingOffset offset, List<ResultEntry<PagingIncrementingOffset>> result) {
		Integer ps = config.pageExpectedRows.orElse(result.size());
		if (ps > result.size()) {
			if (!offset.getRetryOnIncomplete()) {
				return of(retryPageOffset(offset, result.size()));
			} else {
				return empty();
			}
		} else {
			return result
				.stream()
				.skip(Math.max(result.size() - 1, 0))
				.findFirst()
				.map(o -> nextPageOffset(o.getSourceOffset()))
				.filter(this::filterMaxPageNumber);
		}
	}

	@Override
	@SneakyThrows
	public RestIterationResult readRecords(Map<String, String> substituteMap, PagingIncrementingOffset offset) {
		if (isParallelMode()) {
			return parallelExecution(substituteMap, offset);
		} else {
			return readResponseWrapped(substituteMap, offset);
		}
	}

	private boolean isParallelMode() {
		return requestParallelismCount > 1;
	}

	@Override
	public Class getOffsetClass() {
		return PagingIncrementingOffset.class;
	}

	@SneakyThrows
	private RestIterationResult parallelExecution(
		Map<String, String> substituteMap,
		PagingIncrementingOffset offset
	) {
		List<PagingIncrementingOffset> offsets = Lists.newArrayList();
		PagingIncrementingOffset offsetToAdd = offset;
		for (int i = 0; i < requestParallelismCount; i++) {
			offsets.add(offsetToAdd);
			offsetToAdd = nextPageOffset(offsetToAdd);
		}

		StreamEx<Callable<RestIterationResult>> callables = StreamEx.of(offsets)
			.filter(this::filterMaxPageNumber)
			.map(off -> () -> readResponseWrapped(substituteMap, off));

		List<Future<RestIterationResult>> collection = executorService.invokeAll(callables.collect(Collectors.toList()));
		return StreamEx.of(collection)
			.map(this::getResult)
			.reduce((res1, res2) -> res1.merge(res2, (Comparator<RestIterationResult<PagingIncrementingOffset>>) (o1, o2) -> {
				PagingIncrementingOffset off1 = o1.getOffset();
				PagingIncrementingOffset off2 = o2.getOffset();

				if (off1.getPage() > off2.getPage()) {
					return 1;
				}
				if (off1.getMessageNumber() > off2.getMessageNumber()) {
					return 1;
				}
				return -1;
			}))
			.get();
	}

	private boolean filterMaxPageNumber(PagingIncrementingOffset o) {
		return o.getPage() <= config.endPageTo.orElse(Long.MAX_VALUE);
	}

	@Override
	public Map<String, String> macroSubstitutionMap(PagingIncrementingOffset offset) {
		Map<String, String> substitutionMacroMap = new HashMap<>();
		if (config.paramIdMacro.isEmpty()) {
			return substitutionMacroMap;
		}
		substitutionMacroMap.put(config.paramIdMacro.get(), offset.getPage().toString());
		return substitutionMacroMap;
	}

	@SneakyThrows
	private RestIterationResult readResponseWrapped(
		Map<String, String> substituteMap,
		PagingIncrementingOffset offset
	) {
		UrlBody urlBody = getUrlBody(substituteMap, offset);
		try {
			HttpSenderResponse response = getSenderPool().withPooledObject(sender -> readResponse(substituteMap, sender, urlBody));
			return processResponse(offset, urlBody, response);
		} catch (Exception exc) {
			return StreamUtils
				.isInstance(ofNullable(exc.getCause()), HttpClientErrorException.class)
				.filter(e -> e.getStatusCode() == HttpStatus.NOT_FOUND)
				.map(e ->  new RestIterationResult(Collections.emptyList(), offset, Optional.empty(), urlBody.getUrl()))
				.orElseThrow(() -> exc);
		}
	}

	@SneakyThrows
	private RestIterationResult getResult(Future<RestIterationResult> resultFut) {
		try {
			return resultFut.get();
		} catch (Exception e) {
			logException(e, config.url.template, config.body.map(VarInfo::getTemplate));
			throw isInstance(ofNullable(getRootCause(e)), HttpStatusCodeException.class)
				.map(rootCause -> new ProbeRetriableException(config.url.template, e))
				.orElseThrow(() -> e);
		}
	}

	@Override
	public PagingIncrementingOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new PagingIncrementingOffset(
			config.startPageFrom,
			0,
			false,
			1, // start with message #1
			empty(),
			parentMessageNumber,
			dateTime
		);
	}

	private PagingIncrementingOffset createOffset(
		Integer parentMessNum,
		Integer skipMessages,
		Boolean retryOnce,
		Long currPage,
		Integer lastMessageNumber,
		Optional<Integer> pageSize,
		DateTime dateTime
	) {
		return new PagingIncrementingOffset(currPage, skipMessages, retryOnce, lastMessageNumber, pageSize, parentMessNum, dateTime);
	}

	private PagingIncrementingOffset retryPageOffset(PagingIncrementingOffset offset, int readMessages) {
		return createOffset(
			offset.getParentMessageNumber(),
			readMessages,
			true,
			offset.getPage(),
			offset.getMessageNumber(), // start with message #1
			offset.getPageSize(),
			offset.getDateTime());
	}

	private PagingIncrementingOffset nextPageOffset(PagingIncrementingOffset offset) {
		return createOffset(
			offset.getParentMessageNumber(),
			0,
			false,
			offset.getPage() + 1,
			1, // start with message #1
			offset.getPageSize(),
			offset.getDateTime());
	}

}
