package com.nexla.rest.iterations;

import lombok.Getter;

import java.util.Optional;

import static java.util.Optional.ofNullable;

@Getter
public class LinkHeaderParser {

	private static final String DELIM_LINKS = ",";
	private static final String DELIM_LINK_PARAM = ";";
	private static final String META_REL = "rel";
	private static final String META_LAST = "last";
	private static final String META_NEXT = "next";

	private final Optional<String> last;
	private final Optional<String> next;

	public LinkHeaderParser(String linkHeader) {
		String last = null;
		String next = null;
		String[] links = linkHeader.split(DELIM_LINKS);
		for (String link : links) {
			String[] segments = link.split(DELIM_LINK_PARAM);
			if (segments.length < 2) continue;

			String linkPart = segments[0].trim();

			if (!linkPart.startsWith("<") || !linkPart.endsWith(">")) continue;
			linkPart = linkPart.substring(1, linkPart.length() - 1);

			for (int i = 1; i < segments.length; i++) {
				String[] rel = segments[i].trim().split("=");
				if (rel.length < 2 || !META_REL.equals(rel[0]))
					continue;

				String relValue = rel[1];
				if (relValue.startsWith("\"") && relValue.endsWith("\"")) {
					relValue = relValue.substring(1, relValue.length() - 1);
				}

				switch (relValue) {
					case META_LAST:
						last = linkPart;
						break;
					case META_NEXT:
						next = linkPart;
						break;
				}
			}
		}
		this.last = ofNullable(last);
		this.next = ofNullable(next);
	}

}