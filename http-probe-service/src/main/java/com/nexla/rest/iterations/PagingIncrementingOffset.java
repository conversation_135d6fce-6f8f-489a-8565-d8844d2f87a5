package com.nexla.rest.iterations;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.PagingIncrementingOffsetOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.ResultEntry;
import com.nexla.rest.pojo.UrlBody;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

public class PagingIncrementingOffset extends RestIteration<PagingIncrementingOffsetOffset> {

	public PagingIncrementingOffset(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	@SneakyThrows
	public Map<String, Object> getParameters(PagingIncrementingOffsetOffset offset) {
		Map<String, Object> params = Maps.newHashMap();

		if (config.paramOffsetMacro.isPresent() || config.paramPageSizeMacro.isPresent()) {
			return params;
		}

		offset.getOffsetMessages().ifPresent(off -> {
			if (config.pageValueIsNumber) {
				config.paramOffset.ifPresent(param -> params.put(param, off));
			} else {
				config.paramOffset.ifPresent(param -> params.put(param, off.toString()));
			}
		});

		config.paramPageSize.ifPresent(paramPageSize -> {
			if (config.pageValueIsNumber) {
				params.put(paramPageSize, config.pageExpectedRows.get());
			} else {
				params.put(paramPageSize, config.pageExpectedRows.get().toString());
			}
		});

		return params;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> records,
		PagingIncrementingOffsetOffset offset,
		String url
	) {
		List<ResultEntry<PagingIncrementingOffsetOffset>> result = records
			.mapKeyValue((offsetOnPage, data) -> {
				PagingIncrementingOffsetOffset sourceOffset = createOffset(
					offset.getOffsetMessages(),
					offsetOnPage,
					offset.getPageSize(),
					offset.getParentMessageNumber(),
					offset.getDateTime()
				);
				return new ResultEntry<>(data.getRawMessage(), offsetOnPage, offset.getOffsetMessages(), sourceOffset, headers.toSingleValueMap(), null);
			})
			.toList();

		Optional<PagingIncrementingOffsetOffset> nextCallContext = nextOffset(result);

		return new ParsedData(result, offset, nextCallContext);
	}

	private Optional<PagingIncrementingOffsetOffset> nextOffset(List<ResultEntry<PagingIncrementingOffsetOffset>> result) {
		if (shouldStopIteration(result)) {
			logger.debug("Stopping REST iteration");
			return empty();
		}
		return result
			.stream()
			.skip(Math.max(result.size() - 1, 0))
			.findFirst()
			.map(o -> nextPageOffset(o.getSourceOffset()))
			.filter(o -> o.getOffsetMessages().orElse(0) <= config.endMessageOffset.orElse(Integer.MAX_VALUE));
	}

	@SuppressWarnings("SwitchStatementWithTooFewBranches")
	private boolean shouldStopIteration(List<ResultEntry<PagingIncrementingOffsetOffset>> records) {
		switch (config.iterationStopCondition.orElse("")) {
			case "norecords":
				return records.isEmpty();
			default:
				return records.size() < config.pageExpectedRows.orElse(1);
		}
	}

	@Override
	public Map<String, String> macroSubstitutionMap(PagingIncrementingOffsetOffset offset) {
		Map<String, String> substitutionMacroMap = new HashMap<>();
		if (config.paramOffsetMacro.isEmpty() || config.paramPageSizeMacro.isEmpty()) {
			return substitutionMacroMap;
		}
		config.paramOffsetMacro.ifPresent(offsetMacro -> substitutionMacroMap.put(offsetMacro,
			String.valueOf(offset.getOffsetMessages()
				.orElseThrow(() -> new IllegalArgumentException("When paramOffsetMacro is present," +
					" offsetMessages can't be empty")))));

		config.paramPageSizeMacro.ifPresent(pageSizeMacro -> substitutionMacroMap.put(pageSizeMacro,
			String.valueOf(offset.getPageSize()
				.orElseThrow(() -> new IllegalArgumentException("When paramPageSizeMacro is present," +
					" page size can't be empty")))));

		return substitutionMacroMap;
	}

	@Override
	@SneakyThrows
	public RestIterationResult readRecords(Map<String, String> substituteMap, PagingIncrementingOffsetOffset offset) {
		UrlBody urlBody = getUrlBody(substituteMap, offset);
		try {
			HttpSenderResponse response = getSenderPool().withPooledObject(sender -> readResponse(substituteMap, sender, urlBody));
			return processResponse(offset, urlBody, response);
		} catch (Exception exc) {
			return StreamUtils
				.isInstance(ofNullable(exc.getCause()), HttpClientErrorException.class)
				.filter(e -> e.getStatusCode() == HttpStatus.NOT_FOUND)
				.map(e -> new RestIterationResult(Collections.emptyList(), offset, Optional.empty(), urlBody.getUrl()))
				.orElseThrow(() -> exc);
		}
	}

	@Override
	public Class getOffsetClass() {
		return PagingIncrementingOffsetOffset.class;
	}

	@Override
	public PagingIncrementingOffsetOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new PagingIncrementingOffsetOffset(
			config.startMessageOffset,
			1,
			config.pageExpectedRows,
			parentMessageNumber,
			dateTime
		);
	}

	private PagingIncrementingOffsetOffset createOffset(
		Optional<Integer> offsetMessages,
		Integer messageNumber,
		Optional<Integer> pageSize,
		Integer parentMessageNumber,
		DateTime dateTime
	) {
		return new PagingIncrementingOffsetOffset(offsetMessages, messageNumber, pageSize, parentMessageNumber, dateTime);
	}

	private PagingIncrementingOffsetOffset nextPageOffset(PagingIncrementingOffsetOffset offset) {
		return createOffset(
			offset.getPageSize().map(pageSize -> pageSize + offset.getOffsetMessages().orElse(0)),
			1,
			offset.getPageSize(),
			offset.getParentMessageNumber(),
			offset.getDateTime()
		);
	}

}
