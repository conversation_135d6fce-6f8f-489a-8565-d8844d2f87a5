package com.nexla.rest;

import com.nexla.common.time.VarUtils;
import com.nexla.connector.config.rest.RestIterationConfig;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.http.client.utils.URIBuilder;
import org.joda.time.DateTime;

import java.net.URI;
import java.net.URL;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

import static com.nexla.common.time.VarUtils.replaceVars;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.joining;

@AllArgsConstructor
public class VarReplacer {

	public static final String QUERY_PLACEHOLDER = "__QUERY_PLACEHOLDER__";
	public static final String QUERY_DELIMITER = "&";
	public static final String DATE_VAR = "now";

	private final RestIterationConfig config;

	public String getUrl(Map<String, String> fullSubstituteMap) {
		String url = replaceVars(config.url, fullSubstituteMap);
		if (config.getReplaceQueryParams()) {
			return replaceQueryParams(fullSubstituteMap, url);
		} else {
			return url;
		}
	}

	public String getBody(Map<String, String> fullSubstituteMap) {
		return config.body
				.map(b -> replaceVars(b, fullSubstituteMap))
				.orElse(null);
	}

	public String getResponseDataAdditional(Map<String, String> fullSubstituteMap) {
		return config.responseDataAdditional
				.map(b -> replaceVars(b, fullSubstituteMap))
				.orElse(null);
	}

	public Map<String, String> getSubstituteMapWithDates(DateTime time) {
		Set<String> variables = StreamEx.of(config.url.variables)
			.append(config.body.map(x -> x.variables).orElse(Collections.emptySet()))
			.append(config.responseDataAdditional.map(x -> x.variables).orElse(Collections.emptySet()))
			.toSet();

		return VarUtils.getSubstitutionDates(config.dateTimeUnit, config.dateFormat, variables, time);
	}

	@SneakyThrows
	private String replaceQueryParams(Map<String, String> substituteMap, String url) {

		String query = new URL(url).getQuery();
		String queryVarsUrl = ofNullable(query)
				.map(q -> {
					String varQuery = StreamEx
							.of(q.split(QUERY_DELIMITER))
							.map(this::convertToVarString)
							.collect(joining(QUERY_DELIMITER));

					URI newUri = uriWithPlaceholdedQuery(url);
					return newUri.toString().replace(QUERY_PLACEHOLDER, varQuery);
				})
				.orElse(url);

		VarUtils.VarInfo varInfo = VarUtils.processStringWithVars(queryVarsUrl);
		return VarUtils.replaceVars(varInfo, substituteMap);
	}

	@SneakyThrows
	private URI uriWithPlaceholdedQuery(String prelimConversion) {
		URL url = new URL(prelimConversion);
		return new URIBuilder()
			.setScheme(url.getProtocol())
			.setHost(url.getHost())
			.setPort(url.getPort())
			.setPath(url.getPath())
			.setCustomQuery(QUERY_PLACEHOLDER)
			.build();
	}

	private String convertToVarString(String param) {
		String[] split = param.split("=");
		if (split.length == 1) {
			return split[0];
		}
		return split[0] + "={" + StreamEx.of(split).joining("=") + "}";
	}
}
