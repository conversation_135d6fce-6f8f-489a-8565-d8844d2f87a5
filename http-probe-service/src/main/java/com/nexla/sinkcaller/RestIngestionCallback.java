package com.nexla.sinkcaller;

import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.probe.http.RequestSender;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpMethod;

import java.util.Collections;
import java.util.Optional;

import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@AllArgsConstructor
public class RestIngestionCallback {

	private final String ingestUrl;
	private final NexlaPool<RequestSender> ingestSenderPool;

	public void ingest(Optional<String> newBody) {
		RestHeaders header = new RestHeaders(Collections.emptyMap(), ofNullable(APPLICATION_JSON_VALUE), empty());
		// XXX: does URL encoding as expected.
		HttpCallParameters cp = new HttpCallParameters(ingestUrl, false, HttpMethod.POST, newBody, empty(), header);
		ingestSenderPool.withPooledObject(sender -> sender.send(cp));
	}

}
