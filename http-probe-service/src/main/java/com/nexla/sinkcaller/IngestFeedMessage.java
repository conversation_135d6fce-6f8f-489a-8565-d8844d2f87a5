package com.nexla.sinkcaller;

import com.nexla.common.NexlaMessage;
import lombok.Data;
import java.util.List;
import java.util.Map;
import java.util.Optional;


public abstract class IngestFeedMessage <I, R> {
	protected final I input;
	protected final R request;
	protected Map<String, Object> response;

	public IngestFeedMessage(I input, R request, Map<String, Object> response) {
		this.input = input;
		this.request = request;
		this.response = response;
	}

	public abstract Optional<String> getNewBody();

	public abstract List<NexlaMessage> getNexlaMessageList();
}