package com.nexla.apistreams.spec;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connect.common.elt.ELTConstants;
import com.nexla.connector.config.api_streams.ApiStreamsSourceConnectorConfig;
import com.nexla.rest.pojo.RestIterationResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public abstract class BaseEltStream {
    protected Map<String, String> streamParams;
    protected String streamName;
    protected ApiStreamsSourceConnectorConfig rootConfig;
    protected Long runId;
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public BaseEltStream(Map<String, String> streamParams, String streamName, ApiStreamsSourceConnectorConfig rootConfig, Long runId) {
        this.streamParams = streamParams;
        this.streamName = streamName;
        this.rootConfig = rootConfig;
        this.runId = runId;
    }

    public abstract List<SourceRecordCreator> execute();

    public abstract Optional<RestIterationResult> getLastResult();
    
    public String getStreamName() {
        return this.streamName;
    }

    @SneakyThrows
    public LinkedHashMap<String, Object> toEltRecordData(LinkedHashMap<String, Object> originalRecordData,
                                                         String url,
                                                         String callerStreamName,
                                                         List<String> primaryKeys,
                                                         Optional<Object> metadata,
                                                         boolean singleSchemaMode) {
        ArrayNode primaryKeysJsonArr = OBJECT_MAPPER.valueToTree(primaryKeys);

        LinkedHashMap<String, Object> eltRecordData = new LinkedHashMap<>();
        eltRecordData.put(ELTConstants.NEXLA_OPERATION_OBJECT, callerStreamName);
        eltRecordData.put(ELTConstants.NEXLA_OPERATION_URL, url);
        eltRecordData.put(ELTConstants.NEXLA_OPERATION_PRIMARY_KEY, primaryKeysJsonArr);
        if (singleSchemaMode) {
            eltRecordData.put(ELTConstants.NEXLA_OPERATION_RECORD, new NexlaOpRecordWrapper(originalRecordData));
            eltRecordData.put(ELTConstants.NEXLA_OPERATION_METADATA, new NexlaOpMetadataWrapper(metadata.orElse(Map.of())));
        } else {
            eltRecordData.putAll(originalRecordData);
        }

        return eltRecordData;
    }

    /**
     * It is used to have a similar prop type (`unknown`) as CDC#nexla_cdc_info.
     * <p/>
     * We can get `unknown` type only if the prop value is not one of the following Java types:
     * Map | List | NullType | String | Integer | Long | BigInteger | Float | Double | BigDecimal | Boolean | Date | Integer | Long | BigInteger
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NexlaOpRecordWrapper {
        @JsonValue
        public LinkedHashMap<String, Object> nexlaOpRecord;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NexlaOpMetadataWrapper {
        @JsonValue
        public Object nexlaOpMetadata;
    }
}
