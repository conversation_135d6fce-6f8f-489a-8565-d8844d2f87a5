package com.nexla.apistreams.spec;

import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connector.config.api_streams.ApiStreamsSourceConnectorConfig;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.rest.RestIterationChainBuilder;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class DataSelfContainedEltStream extends BaseLineEltStream {
    
    private final List<String> primaryKeys;

    private final Optional<Object> metadata;
    
    public DataSelfContainedEltStream(Map<String, String> streamParams,
                                      RestIterationChainBuilder chainBuilder,
                                      List<RestIterationConfig> restIterationConfigs,
                                      String streamName,
                                      ApiStreamsSourceConnectorConfig rootConfig,
                                      Long runId,
                                      List<String> primaryKeys,
                                      Optional<Object> metadata) {
        super(streamParams, chainBuilder, restIterationConfigs, streamName, rootConfig, runId);
        this.primaryKeys = primaryKeys;
        this.metadata = metadata;
    }

    @Override
    public List<SourceRecordCreator> execute() {
        return super.execute(streamName, primaryKeys, metadata);
    }
}
