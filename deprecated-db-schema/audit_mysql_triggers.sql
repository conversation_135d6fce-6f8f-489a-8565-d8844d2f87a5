CREATE TABLE `audit_connector_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `state` varchar(20) NOT NULL,
  `message` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER connector_state_insert
  AFTER INSERT ON connector_state FOR EACH ROW
  INSERT INTO audit_connector_state(`action`, `audit_ts`, `resource_type`, `resource_id`, `state`, `message`)
        VALUES ('insert', current_timestamp(3), new.resource_type, new.resource_id, new.state, new.message);

CREATE TRIGGER connector_state_update
AFTER UPDATE ON connector_state FOR EACH ROW
BEGIN
  IF NEW.state <> OLD.state THEN
    INSERT INTO audit_connector_state(`action`, `audit_ts`, `resource_type`, `resource_id`, `state`, `message`)
    VALUES ('update', current_timestamp(3), NEW.resource_type, NEW.resource_id, NEW.state, NEW.message);
  END IF;
END

CREATE TRIGGER connector_state_delete
  AFTER DELETE ON connector_state FOR EACH ROW
  INSERT INTO audit_connector_state(`action`, `audit_ts`, `resource_type`, `resource_id`, `state`, `message`)
        VALUES ('delete', current_timestamp(3), old.resource_type, old.resource_id, old.state, old.message);


-

CREATE TABLE audit_topic_offset (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) NOT NULL,
  `partition_number` int(11) NOT NULL,
  `topic` varchar(300) NOT NULL,
  `topic_offset` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER topic_offset_insert
  AFTER INSERT ON topic_offset FOR EACH ROW
  INSERT INTO audit_topic_offset(`action`, `audit_ts`, `resource_type`, `resource_id`, `dataset_id`, `partition_number`, `topic`, `topic_offset`)
        VALUES ('insert', current_timestamp(3), new.resource_type, new.resource_id, new.dataset_id, new.partition_number, new.topic, new.topic_offset);

CREATE TRIGGER topic_offset_update
  AFTER UPDATE ON topic_offset FOR EACH ROW
  INSERT INTO audit_topic_offset(`action`, `audit_ts`, `resource_type`, `resource_id`, `dataset_id`, `partition_number`, `topic`, `topic_offset`)
        VALUES ('update', current_timestamp(3), new.resource_type, new.resource_id, new.dataset_id, new.partition_number, new.topic, new.topic_offset);

CREATE TRIGGER topic_offset_delete
  AFTER DELETE ON topic_offset FOR EACH ROW
  INSERT INTO audit_topic_offset(`action`, `audit_ts`, `resource_type`, `resource_id`, `dataset_id`, `partition_number`, `topic`, `topic_offset`)
        VALUES ('delete', current_timestamp(3), old.resource_type, old.resource_id, old.dataset_id, old.partition_number, old.topic, old.topic_offset);



CREATE TABLE `audit_fast_offset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `source_id` int(11) NOT NULL,
  `sink_id` int(11) NOT NULL,
  `offset_position` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER fast_offset_insert
  AFTER INSERT ON fast_offset FOR EACH ROW
  INSERT INTO audit_fast_offset(`action`, `audit_ts`, `source_id`, `sink_id`, `offset_position`)
        VALUES ('insert', current_timestamp(3), new.source_id, new.sink_id, new.offset_position);

CREATE TRIGGER fast_offset_update
  AFTER UPDATE ON fast_offset FOR EACH ROW
  INSERT INTO audit_fast_offset(`action`, `audit_ts`, `source_id`, `sink_id`, `offset_position`)
        VALUES ('update', current_timestamp(3), new.source_id, new.sink_id, new.offset_position);

CREATE TRIGGER fast_offset_delete
  AFTER DELETE ON fast_offset FOR EACH ROW
  INSERT INTO audit_fast_offset(`action`, `audit_ts`, `source_id`, `sink_id`, `offset_position`)
        VALUES ('delete', current_timestamp(3), old.source_id, old.sink_id, old.offset_position);



CREATE TABLE `audit_key_value` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `vendor_key` varchar(100) DEFAULT NULL,
  `vendor_value` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER key_value_insert
  AFTER INSERT ON key_value FOR EACH ROW
  INSERT INTO audit_key_value(`action`, `audit_ts`, `vendor_key`, `vendor_value`)
        VALUES ('insert', current_timestamp(3), new.vendor_key, new.vendor_value);

CREATE TRIGGER key_value_update
  AFTER UPDATE ON key_value FOR EACH ROW
  INSERT INTO audit_key_value(`action`, `audit_ts`, `vendor_key`, `vendor_value`)
        VALUES ('update', current_timestamp(3), new.vendor_key, new.vendor_value);

CREATE TRIGGER key_value_delete
  AFTER DELETE ON key_value FOR EACH ROW
  INSERT INTO audit_key_value(`action`, `audit_ts`, `vendor_key`, `vendor_value`)
        VALUES ('delete', current_timestamp(3), old.vendor_key, old.vendor_value);


CREATE TABLE `audit_sink_offset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `resource_id` int(11) NOT NULL,
  `offset_position` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER sink_offset_insert
  AFTER INSERT ON sink_offset FOR EACH ROW
  INSERT INTO audit_sink_offset(`action`, `audit_ts`, `resource_id`, `offset_position`)
        VALUES ('insert', current_timestamp(3), new.resource_id, new.offset_position);

CREATE TRIGGER sink_offset_update
  AFTER UPDATE ON sink_offset FOR EACH ROW
  INSERT INTO audit_sink_offset(`action`, `audit_ts`, `resource_id`, `offset_position`)
        VALUES ('update', current_timestamp(3), new.resource_id, new.offset_position);

CREATE TRIGGER sink_offset_delete
  AFTER DELETE ON sink_offset FOR EACH ROW
  INSERT INTO audit_sink_offset(`action`, `audit_ts`, `resource_id`, `offset_position`)
        VALUES ('delete', current_timestamp(3), old.resource_id, old.offset_position);


CREATE TABLE `audit_pipeline_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `task_id` varchar(256) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `max_instances` int(11) DEFAULT NULL,
  `dedicated` tinyint(1) DEFAULT NULL,
  `state` varchar(36) NOT NULL,
  `meta` text,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  `last_active_ts` timestamp(3) NULL DEFAULT NULL,
  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER pipeline_task_insert
  AFTER INSERT ON pipeline_task FOR EACH ROW
  INSERT INTO audit_pipeline_task(`action`, `audit_ts`, `task_id`, `task_type`, `max_instances`, `dedicated`, `state`, `meta`, `heartbeat_ts`, `last_active_ts`, `last_data_ts`)
        VALUES ('insert', current_timestamp(3), new.task_id, new.task_type, new.max_instances, new.dedicated, new.state, new.meta, new.heartbeat_ts, new.last_active_ts, new.last_data_ts);

DELIMITER $$

CREATE TRIGGER pipeline_task_update AFTER UPDATE ON pipeline_task
FOR EACH ROW
BEGIN
  IF (old.state <> new.state) THEN
      INSERT INTO audit_pipeline_task(`action`, `audit_ts`, `task_id`, `task_type`, `max_instances`, `dedicated`, `state`, `meta`, `heartbeat_ts`, `last_active_ts`, `last_data_ts`) VALUES ('update', current_timestamp(3), new.task_id, new.task_type, new.max_instances, new.dedicated, new.state, new.meta, new.heartbeat_ts, new.last_active_ts, new.last_data_ts);
  END IF;
END$$

DELIMITER ;

CREATE TRIGGER pipeline_task_delete
  AFTER DELETE ON pipeline_task FOR EACH ROW
  INSERT INTO audit_pipeline_task(`action`, `audit_ts`, `task_id`, `task_type`, `max_instances`, `dedicated`, `state`, `meta`, `heartbeat_ts`, `last_active_ts`, `last_data_ts`)
        VALUES ('delete', current_timestamp(3), old.task_id, old.task_type, old.max_instances, old.dedicated, old.state, old.meta, old.heartbeat_ts, old.last_active_ts, old.last_data_ts);

--
--CREATE TABLE `audit_pipeline_task_node` (
--  `id` bigint(20) NOT NULL AUTO_INCREMENT,
--  `action` varchar(10) NOT NULL,
--  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
--  `node_id` varchar(36) NOT NULL,
--  `task_id` varchar(36) NOT NULL,
--  `state` varchar(36) NOT NULL,
--  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
--  `last_data_ts` timestamp(3) NULL DEFAULT NULL,
--  PRIMARY KEY (`id`)
--) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--CREATE TRIGGER pipeline_task_node_insert
--  AFTER INSERT ON pipeline_task_node FOR EACH ROW
--  INSERT INTO audit_pipeline_task_node(`action`, `audit_ts`, `node_id`, `task_id`, `state`, `heartbeat_ts`, `last_data_ts`)
--        VALUES ('insert', current_timestamp(3), new.node_id, new.task_id, new.state, new.heartbeat_ts, new.last_data_ts);
--
--CREATE TRIGGER pipeline_task_node_update
--  AFTER UPDATE ON pipeline_task_node FOR EACH ROW
--  INSERT INTO audit_pipeline_task_node(`action`, `audit_ts`, `node_id`, `task_id`, `state`, `heartbeat_ts`, `last_data_ts`)
--        VALUES ('update', current_timestamp(3), new.node_id, new.task_id, new.state, new.heartbeat_ts, new.last_data_ts);
--
--CREATE TRIGGER pipeline_task_node_delete
--  AFTER DELETE ON pipeline_task_node FOR EACH ROW
--  INSERT INTO audit_pipeline_task_node(`action`, `audit_ts`, `node_id`, `task_id`, `state`, `heartbeat_ts`, `last_data_ts`)
--        VALUES ('delete', current_timestamp(3), old.node_id, old.task_id, old.state, old.heartbeat_ts, old.last_data_ts);


CREATE TABLE `audit_pipeline_node` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(10) NOT NULL,
  `audit_ts` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `node_id` varchar(36) NOT NULL,
  `task_type` varchar(36) NOT NULL,
  `ip` varchar(36) DEFAULT NULL,
  `pod_name` varchar(256) DEFAULT NULL,
  `dedicated` tinyint(1) DEFAULT NULL,
  `heartbeat_ts` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TRIGGER pipeline_node_insert
  AFTER INSERT ON pipeline_node FOR EACH ROW
  INSERT INTO audit_pipeline_node(`action`, `audit_ts`, `node_id`, `task_type`, `ip`, `pod_name`, `dedicated`, `heartbeat_ts`)
        VALUES ('insert', current_timestamp(3), new.node_id, new.task_type, new.ip, new.pod_name, new.dedicated, new.heartbeat_ts);

CREATE TRIGGER pipeline_node_update
  AFTER UPDATE ON pipeline_node FOR EACH ROW
  INSERT INTO audit_pipeline_node(`action`, `audit_ts`, `node_id`, `task_type`, `ip`, `pod_name`, `dedicated`, `heartbeat_ts`)
        VALUES ('update', current_timestamp(3), new.node_id, new.task_type, new.ip, new.pod_name, new.dedicated, new.heartbeat_ts);

CREATE TRIGGER pipeline_node_delete
  AFTER DELETE ON pipeline_node FOR EACH ROW
  INSERT INTO audit_pipeline_node(`action`, `audit_ts`, `node_id`, `task_type`, `ip`, `pod_name`, `dedicated`, `heartbeat_ts`)
        VALUES ('delete', current_timestamp(3), old.node_id, old.task_type, old.ip, old.pod_name, old.dedicated, old.heartbeat_ts);
