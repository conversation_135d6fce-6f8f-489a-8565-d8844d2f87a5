drop table pipeline_state;

DROP FUNCTION audit_transformation_delete();

DROP FUNCTION audit_transformation_task_delete();

DROP FUNCTION audit_transformation_task_upsert();

DROP FUNCTION audit_transformation_upsert();

CREATE TABLE pipeline_task
(
    task_id varchar(256) NOT NULL,
    task_type varchar(36) NOT NULL,
    max_instances int,
    dedicated boolean,
    state varchar(36) NOT NULL,
    meta text,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_active_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL,
    PRIMARY KEY (task_id)
);

CREATE TABLE pipeline_task_node
(
    node_id varchar(36) NOT NULL,
    task_id varchar(36) NOT NULL,
    state varchar(36) NOT NULL,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL,
    PRIMARY KEY (node_id, task_id)
);

CREATE TABLE pipeline_node
(
    node_id varchar(36) NOT NULL,
    task_type varchar(36) NOT NULL,
    ip varchar(36) null,
    pod_name varchar(256) null,
    dedicated boolean,
    heartbeat_ts TIMESTAMP(3) NULL,
    PRIMARY KEY (node_id)
);

-----------

CREATE TABLE IF NOT EXISTS audit_pipeline_task
(
    id bigserial PRIMARY KEY,
    "action" varchar(10) NOT NULL,
    audit_ts timestamp NOT NULL,
    task_id varchar(256) NOT NULL,
    task_type varchar(36) NOT NULL,
    max_instances int,
    dedicated boolean,
    state varchar(36) NOT NULL,
    meta text,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_active_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL
);

CREATE INDEX IF NOT EXISTS audit_pipeline_task_index ON audit_pipeline_task (audit_ts desc);

CREATE OR REPLACE FUNCTION audit_pipeline_task_upsert() RETURNS TRIGGER AS $target$
    BEGIN
       INSERT INTO audit_pipeline_task("action", audit_ts, task_id, task_type, max_instances, dedicated, state, meta, heartbeat_ts, last_active_ts, last_data_ts)
         VALUES (TG_OP, current_timestamp, new.task_id, new.task_type, new.max_instances, new.dedicated, new.state, new.meta, new.heartbeat_ts, new.last_active_ts, new.last_data_ts);
       RETURN NEW;
    END;
$target$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION audit_pipeline_task_delete() RETURNS TRIGGER AS $target$
    BEGIN
       INSERT INTO audit_pipeline_task("action", audit_ts, task_id, task_type, max_instances, dedicated, state, meta, heartbeat_ts, last_active_ts, last_data_ts)
         VALUES (TG_OP, current_timestamp, old.task_id, old.task_type, old.max_instances, old.dedicated, old.state, old.meta, old.heartbeat_ts, old.last_active_ts, old.last_data_ts);
       RETURN NEW;
    END;
$target$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS pipeline_task_upsert on pipeline_task;
DROP TRIGGER IF EXISTS pipeline_task_delete on pipeline_task;

create TRIGGER pipeline_task_upsert after INSERT or update ON pipeline_task FOR EACH ROW EXECUTE PROCEDURE audit_pipeline_task_upsert();
create TRIGGER pipeline_task_delete before delete ON pipeline_task FOR EACH ROW EXECUTE PROCEDURE audit_pipeline_task_delete();

-----------

CREATE TABLE IF NOT EXISTS audit_pipeline_task_node
(
    id bigserial PRIMARY KEY,
    "action" varchar(10) NOT NULL,
    audit_ts timestamp NOT NULL,
    node_id varchar(36) NOT NULL,
    task_id varchar(36) NOT NULL,
    state varchar(36) NOT NULL,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL
);

   CREATE INDEX IF NOT EXISTS audit_pipeline_task_node_index ON audit_pipeline_task_node (audit_ts desc);

CREATE OR REPLACE FUNCTION audit_pipeline_task_node_upsert() RETURNS TRIGGER AS $target$
    BEGIN
       INSERT INTO audit_pipeline_task_node("action", audit_ts, node_id, task_id, state, heartbeat_ts, last_data_ts)
         VALUES (TG_OP, current_timestamp, new.node_id, new.task_id, new.state, new.heartbeat_ts, new.last_data_ts);
       RETURN NEW;
    END;
$target$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION audit_pipeline_task_node_delete() RETURNS TRIGGER AS $target$
    BEGIN
       INSERT INTO audit_pipeline_task_node("action", audit_ts, node_id, task_id, state, heartbeat_ts, last_data_ts)
         VALUES (TG_OP, current_timestamp, old.node_id, old.task_id, old.state, old.heartbeat_ts, old.last_data_ts);
       RETURN OLD;
    END;
$target$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS pipeline_task_node_upsert on pipeline_task_node;
DROP TRIGGER IF EXISTS pipeline_task_node_delete on pipeline_task_node;

create TRIGGER pipeline_task_node_upsert after INSERT or update ON pipeline_task_node FOR EACH ROW EXECUTE PROCEDURE audit_pipeline_task_node_upsert();
create TRIGGER pipeline_task_node_delete before delete ON pipeline_task_node FOR EACH ROW EXECUTE PROCEDURE audit_pipeline_task_node_delete();

-----------

CREATE TABLE IF NOT EXISTS audit_pipeline_node
(
    id bigserial PRIMARY KEY,
    "action" varchar(10) NOT NULL,
    audit_ts timestamp NOT NULL,
    node_id varchar(36) NOT NULL,
    task_type varchar(36) NOT NULL,
    ip varchar(36) null,
    pod_name varchar(256) null,
    dedicated boolean,
    heartbeat_ts TIMESTAMP(3) NULL
);

CREATE INDEX IF NOT EXISTS audit_pipeline_node_index ON audit_pipeline_node (audit_ts desc);

CREATE OR REPLACE FUNCTION audit_pipeline_node_upsert() RETURNS TRIGGER AS $target$
    BEGIN
       INSERT INTO audit_pipeline_node("action", audit_ts, node_id, task_type, ip, pod_name, dedicated, heartbeat_ts)
         VALUES (TG_OP, current_timestamp, new.node_id, new.task_type, new.ip, new.pod_name, new.dedicated, new.heartbeat_ts);
       RETURN NEW;
    END;
$target$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION audit_pipeline_node_delete() RETURNS TRIGGER AS $target$
    BEGIN
       INSERT INTO audit_pipeline_node("action", audit_ts, node_id, task_type, ip, pod_name, dedicated, heartbeat_ts)
         VALUES (TG_OP, current_timestamp, old.node_id, old.task_type, old.ip, old.pod_name, old.dedicated, old.heartbeat_ts);
       RETURN OLD;
    END;
$target$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS pipeline_node_upsert on pipeline_node;
DROP TRIGGER IF EXISTS pipeline_node_delete on pipeline_node;

create TRIGGER pipeline_node_upsert after INSERT or update ON pipeline_node FOR EACH ROW EXECUTE PROCEDURE audit_pipeline_node_upsert();
create TRIGGER pipeline_node_delete before delete ON pipeline_node FOR EACH ROW EXECUTE PROCEDURE audit_pipeline_node_delete();

