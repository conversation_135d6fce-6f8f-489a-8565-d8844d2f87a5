ALTER TABLE file_source_state ADD COLUMN origin_node_id int(11);
ALTER TABLE file_source_state ADD COLUMN flow_node_id int(11);

ALTER TABLE file_sink_state ADD COLUMN origin_node_id int(11);
ALTER TABLE file_sink_state ADD COLUMN flow_node_id int(11);

ALTER TABLE error_status_metrics ADD COLUMN origin_node_id int(11);
ALTER TABLE error_status_metrics ADD COLUMN flow_node_id int(11);

ALTER TABLE data_monitor_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_monitor_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_monitor_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_monitor_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sinks_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sinks_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sources_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sources_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sets_tx_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sets_tx_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sinks_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sinks_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sources_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sources_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sets_tx_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sets_tx_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sets_tx_hourly ADD COLUMN parent_node_id int(11);
ALTER TABLE data_sets_tx_daily ADD COLUMN parent_node_id int(11);

ALTER TABLE data_sets_tx_hourly ADD COLUMN data_source_id int(11);
ALTER TABLE data_sets_tx_daily ADD COLUMN data_source_id int(11);

CREATE TABLE quarantine_topic_aggregation_result (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    resource_id int(11) NOT NULL,
    origin_node_id int(11) NOT NULL,
    flow_node_id int(11) NOT NULL,
    resource_type varchar(10) NOT NULL,
    topic varchar(300) NOT NULL,
    run_id BIGINT NOT NULL,
    aggregation_result text NOT NULL,
    created_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    last_modified timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`)
);

CREATE TABLE flow_metrics_daily (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    origin_node_id int(11) NOT NULL,
    org_id int(11) NOT NULL,
    owner_id int(11) NOT NULL,
    run_id bigint(20) NOT NULL,
    row_count bigint(20) NOT NULL,
    error_count bigint(20) NOT NULL,
    size_value bigint(20) NOT NULL,
    reporting_date timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    created_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`)
);