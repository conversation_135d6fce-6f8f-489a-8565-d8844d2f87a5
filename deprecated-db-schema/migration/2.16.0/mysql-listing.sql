CREATE TABLE `connector_sync`(
    `type`          VA<PERSON>HAR(32) NOT NULL,
    `connector_id`  VA<PERSON>HA<PERSON>(256) NOT NULL,
    `task_id`       VARCHAR(256) NOT NULL,
    `phase`         VARCHAR(32)  NOT NULL,
    `service_name`  VA<PERSON>HAR(70) NOT NULL,
    `pod_name`      VARCHAR(70) NOT NULL,
    `context`       TEXT DEFAULT NULL,
    `last_modified` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`type`, `connector_id`, `task_id`)
);

CREATE TABLE `dataset_trace` (
  `resource_id` int(11) NOT NULL,
  `run_id` bigint(20) NOT NULL,
  `traces` int(11) NOT NULL,
  `tx_state` varchar(36) NOT NULL,
  `tx_done_ts` TIMESTAMP(3) NULL,
  `updated_at` TIMESTAMP(3) NULL,
  `created_at` TIMESTAMP(3) NULL,
  PRIMARY KEY (`resource_id`, `run_id`)
);

CREATE TABLE `flink_job` (
     `job_id` varchar(100) NOT NULL,
     `resource_id` int(11) NOT NULL,
     `job_state` varchar(20) NOT NULL,
     `service_log` text DEFAULT NULL,
     `savepoint` text DEFAULT NULL,
     `records_sent` bigint NOT NULL DEFAULT 0,
     `bytes_sent` bigint NOT NULL DEFAULT 0,
     `error_count` bigint NOT NULL DEFAULT 0,
     `transform_data` text,
     `deleted` tinyint(1) NOT NULL DEFAULT '0',
     `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
     `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
     PRIMARY KEY (`job_id`)
);

CREATE TABLE `canary_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `application` VARCHAR(255) NOT NULL,
  `percentage` int(11),
  `org_id` int(11),
  `flow_id` int(11),
  `canary_value` VARCHAR(255),
  `rule_json` text,
  `start_time` timestamp(3) NULL DEFAULT NULL,
  `end_time` timestamp(3) NULL DEFAULT NULL,
  `disabled` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`)
);

ALTER TABLE pipeline_task ADD COLUMN `excluded` tinyint(1) NOT NULL DEFAULT '0';

ALTER TABLE pipeline_node MODIFY node_id VARCHAR(256);