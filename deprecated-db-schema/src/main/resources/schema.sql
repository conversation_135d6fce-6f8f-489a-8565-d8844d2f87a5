create table account_metrics_daily (
    id bigint auto_increment primary key,
    row_count bigint null,
    error_count bigint null,
    pipeline_count bigint null,
    size_value bigint null,
    org_id int null,
    owner_id int null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    updated_at timestamp(3) null,
    created_at timestamp(3) null
) charset=utf8;
---
create index account_metrics_daily_date on account_metrics_daily (reporting_date);
---
create index account_metrics_daily_org_id on account_metrics_daily (org_id, reporting_date);
---
create index account_metrics_daily_owner_id on account_metrics_daily (owner_id, reporting_date);
---
create table connector_state (
    resource_type varchar(10) not null,
    resource_id int not null,
    state varchar(20) not null,
    message text null,
    last_modified timestamp(3) default current_timestamp(3) not null,
    primary key (resource_type, resource_id)
);
---
create table custom_flow_daily (
    id bigint auto_increment primary key,
    resource_id int not null,
    run_id bigint null,
    row_count bigint null,
    error_count bigint null,
    pipeline_count bigint null,
    size_value bigint null,
    org_id int null,
    owner_id int null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    origin_node_id int null,
    flow_node_id int null
);
---
create index custom_flow_state_daily_date on custom_flow_daily (reporting_date);
---
create index custom_flow_state_daily_org_id on custom_flow_daily (org_id, reporting_date);
---
create index custom_flow_state_daily_owner_id on custom_flow_daily (owner_id, reporting_date);
---
create table custom_flow_hourly (
    id bigint auto_increment primary key,
    resource_id int not null,
    run_id bigint null,
    row_count bigint null,
    error_count bigint null,
    pipeline_count bigint null,
    size_value bigint null,
    org_id int null,
    owner_id int null,
    reporting_hour timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    origin_node_id int null,
    flow_node_id int null,
    constraint data_sources_hourly_id
        unique (resource_id, reporting_hour, run_id)
);
---
create index custom_flow_state_daily_date on custom_flow_hourly (reporting_hour);
---
create index custom_flow_state_daily_org_id on custom_flow_hourly (org_id, reporting_hour);
---
create index custom_flow_state_daily_owner_id on custom_flow_hourly (owner_id, reporting_hour);
---
create table custom_flow_state (
    id bigint auto_increment primary key,
    resource_id int null,
    name varchar(500) null,
    size_value bigint null,
    record_count bigint null,
    error_count bigint null,
    run_id bigint null,
    org_id int null,
    owner_id int null,
    aggregated tinyint(1) default 0 null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    origin_node_id int null,
    flow_node_id int null
);
---
create table data_monitor_daily (
    id bigint auto_increment primary key,
    data_volume_avg double null,
    data_volume_stdev double null,
    record_count_avg double null,
    record_count_stdev double null,
    resource_id int null,
    resource_type enum('SOURCE', 'SINK') collate utf8_unicode_ci not null,
    date_value date null,
    updated_at datetime null,
    created_at datetime null,
    origin_node_id int null,
    flow_node_id int null
) charset=utf8;
---
create index data_monitor_daily_resource_id on data_monitor_daily (resource_id);
---
create index data_monitor_daily_resource_type on data_monitor_daily (resource_type);
---
create table data_monitor_hourly (
    id bigint auto_increment primary key,
    data_volume_avg double null,
    data_volume_stdev double null,
    record_count_avg double null,
    record_count_stdev double null,
    resource_id int null,
    resource_type enum('SOURCE', 'SINK') collate utf8_unicode_ci not null,
    hour_of_day int null,
    updated_at datetime null,
    created_at datetime null,
    origin_node_id int null,
    flow_node_id int null
) charset=utf8;
---
create index data_monitor_hourly_resource_id on data_monitor_hourly (resource_id);
---
create index data_monitor_hourly_resource_type on data_monitor_hourly (resource_type);
---
create table data_monitor_notifications (
    id bigint auto_increment primary key,
    notification_setting_id int null,
    data_volume_avg double null,
    data_volume_stdev double null,
    record_count_avg double null,
    record_count_stdev double null,
    data_volume_total double null,
    record_count_total double null,
    resource_id int null,
    resource_type enum('ORG', 'USER', 'PIPELINE', 'SOURCE', 'PUB', 'SUB', 'DATASET', 'SINK') collate utf8_unicode_ci not null,
    last_exection_time timestamp(3) null,
    monitor_window bigint null,
    hour_of_day int null,
    monitor_date timestamp(3) null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    file_count_avg double null,
    file_count_stdev double null,
    file_count_total double null
) charset=utf8;
---
create index data_monitor_notifications_notification_setting_id on data_monitor_notifications (notification_setting_id);
---
create index data_monitor_notifications_resource_id on data_monitor_notifications (resource_id);
---
create index data_monitor_notifications_resource_type on data_monitor_notifications (resource_type);
---
create table data_pubs_daily (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    error_count bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    run_id bigint null,
    constraint data_pubs_daily_id
        unique (resource_id, reporting_date, data_set_id, run_id)
);
---
create index data_pubs_daily_date on data_pubs_daily (reporting_date);
---
create index data_pubs_daily_org_id on data_pubs_daily (org_id, reporting_date);
---
create index data_pubs_daily_owner_id on data_pubs_daily (owner_id, reporting_date);
---
create index run_id on data_pubs_daily (run_id);
---
create table data_pubs_hourly (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_hour timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    error_count bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    run_id bigint null,
    constraint data_pubs_hourly_id
        unique (resource_id, reporting_hour, data_set_id, run_id)
);
---
create index data_pubs_hourly_date on data_pubs_hourly (reporting_hour);
---
create index data_pubs_hourly_org_id on data_pubs_hourly (org_id, reporting_hour);
---
create index data_pubs_hourly_owner_id on data_pubs_hourly (owner_id, reporting_hour);
---
create index run_id on data_pubs_hourly (run_id);
---
create table data_sets_tx_daily (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    error_count bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    run_id bigint null,
    origin_node_id int null,
    flow_node_id int null,
    parent_node_id int null,
    data_source_id int null,
    constraint data_sets_daily_id
        unique (resource_id, reporting_date, data_set_id, run_id)
);
---
create index data_sets_daily_date on data_sets_tx_daily (reporting_date);
---
create index data_sets_daily_org_id on data_sets_tx_daily (org_id, reporting_date);
---
create index data_sets_daily_owner_id on data_sets_tx_daily (owner_id, reporting_date);
---
create table data_sets_tx_hourly (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_hour timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    error_count bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    run_id bigint null,
    origin_node_id int null,
    flow_node_id int null,
    parent_node_id int null,
    data_source_id int null,
    constraint data_sets_hourly_id
        unique (resource_id, reporting_hour, data_set_id, run_id)
);
---
create index data_sets_hourly_date on data_sets_tx_hourly (reporting_hour);
---
create index data_sets_hourly_org_id on data_sets_tx_hourly (org_id, reporting_hour);
---
create index data_sets_hourly_owner_id on data_sets_tx_hourly (owner_id, reporting_hour);
---
create table data_sinks_daily (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    error_count bigint default 0 null,
    run_id bigint null,
    max_last_written TIMESTAMP(3) NULL,
    origin_node_id int null,
    flow_node_id int null,
    constraint data_sinks_daily_id
        unique (resource_id, data_set_id, reporting_date, run_id)
);
---
create index data_set_id on data_sinks_daily (data_set_id);
---
create index data_set_id_reporting_date on data_sinks_daily (data_set_id, reporting_date);
---
create index data_sinks_daily_date on data_sinks_daily (reporting_date);
---
create index data_sinks_daily_org_id on data_sinks_daily (org_id, reporting_date);
---
create index data_sinks_daily_owner_id on data_sinks_daily (owner_id, reporting_date);
---
create index resource_id_reporting_date on data_sinks_daily (resource_id, reporting_date);
---
create index run_id on data_sinks_daily (run_id);
---
create table data_sinks_hourly (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_hour timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    error_count bigint default 0 null,
    run_id bigint null,
    max_last_written TIMESTAMP(3) NULL,
    origin_node_id int null,
    flow_node_id int null,
    constraint data_sinks_hourly_id
        unique (resource_id, data_set_id, reporting_hour, run_id)
);
---
create index data_set_id on data_sinks_hourly (data_set_id);
---
create index data_set_id_reporting_hour on data_sinks_hourly (data_set_id, reporting_hour);
---
create index data_sinks_hourly_date on data_sinks_hourly (reporting_hour);
---
create index data_sinks_hourly_org_id on data_sinks_hourly (org_id, reporting_hour);
---
create index data_sinks_hourly_owner_id on data_sinks_hourly (owner_id, reporting_hour);
---
create index resource_id_reporting_hour on data_sinks_hourly (resource_id, reporting_hour);
---
create index run_id on data_sinks_hourly (run_id);
---
create table data_sources_daily (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    error_count bigint null,
    name_count bigint default 0 null,
    run_id bigint null,
    origin_node_id int null,
    flow_node_id int null,
    constraint data_sources_daily_id
        unique (resource_id, data_set_id, reporting_date, run_id)
);
---
create index data_set_id on data_sources_daily (data_set_id);
---
create index data_set_id_reporting_date on data_sources_daily (data_set_id, reporting_date);
---
create index data_sources_daily_date on data_sources_daily (reporting_date);
---
create index data_sources_daily_org_id on data_sources_daily (org_id, reporting_date);
---
create index data_sources_daily_owner_id on data_sources_daily (owner_id, reporting_date);
---
create index resource_id_reporting_date on data_sources_daily (resource_id, reporting_date);
---
create index run_id on data_sources_daily (run_id);
---
create table data_sources_hourly (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_hour timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    error_count bigint null,
    name_count bigint default 0 null,
    run_id bigint null,
    origin_node_id int null,
    flow_node_id int null,
    constraint data_sources_hourly_id
        unique (resource_id, data_set_id, reporting_hour, run_id)
);
---
create index data_set_id on data_sources_hourly (data_set_id);
---
create index data_set_id_reporting_hour on data_sources_hourly (data_set_id, reporting_hour);
---
create index data_sources_hourly_date on data_sources_hourly (reporting_hour);
---
create index data_sources_hourly_org_id on data_sources_hourly (org_id, reporting_hour);
---
create index data_sources_hourly_owner_id on data_sources_hourly (owner_id, reporting_hour);
---
create index resource_id_reporting_hour on data_sources_hourly (resource_id, reporting_hour);
---
create index run_id on data_sources_hourly (run_id);
---
create table data_subs_daily (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_date timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    error_count bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    run_id bigint null,
    constraint data_subs_daily_id
        unique (resource_id, reporting_date, data_set_id, run_id)
);
---
create index data_subs_daily_date on data_subs_daily (reporting_date);
---
create index data_subs_daily_org_id on data_subs_daily (org_id, reporting_date);
---
create index data_subs_daily_owner_id on data_subs_daily (owner_id, reporting_date);
---
create index run_id on data_subs_daily (run_id);
---
create table data_subs_hourly (
    id bigint auto_increment primary key,
    resource_id int not null,
    reporting_hour timestamp(3) default current_timestamp(3) not null on update current_timestamp(3),
    row_count bigint null,
    data_volume_bytes bigint null,
    error_count bigint null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    org_id int null,
    owner_id int null,
    data_set_id int null,
    run_id bigint null,
    constraint data_subs_hourly_id
        unique (resource_id, reporting_hour, data_set_id, run_id)
);
---
create index data_subs_hourly_date on data_subs_hourly (reporting_hour);
---
create index data_subs_hourly_org_id on data_subs_hourly (org_id, reporting_hour);
---
create index data_subs_hourly_owner_id on data_subs_hourly (owner_id, reporting_hour);
---
create index run_id on data_subs_hourly (run_id);
---
create table error_status_metrics (
    id bigint auto_increment,
    resource_id int default 0 not null,
    resource_type varchar(10) null,
    row_count bigint null,
    error_count bigint null,
    size_value bigint null,
    status enum('ERROR', 'OK', 'WARNING') null,
    org_id int null,
    owner_id int null,
    updated_at timestamp(3) default current_timestamp(3) not null,
    created_at timestamp(3) default current_timestamp(3) not null,
    origin_node_id int null,
    flow_node_id int null,
    primary key (id, resource_id)
);
---
create table file_reingestion_request (
    id bigint auto_increment primary key,
    resource_id int not null,
    full_path varchar(514) not null,
    status varchar(20) not null,
    request_time timestamp(3) default current_timestamp(3) not null on update current_timestamp(3)
);
---
create index file_ingestion_request_resource_id_status_index on file_reingestion_request (resource_id, status);
---
create table file_sink_state (
    id bigint auto_increment primary key,
    name varchar(500) charset latin1 null,
    size_value bigint null,
    record_count bigint null,
    sink_id int not null,
    error varchar(4000) null,
    write_status int default 0 null,
    data_set_id int null,
    last_written timestamp(3) null,
    error_message varchar(4000) null,
    error_count bigint null,
    run_id bigint null,
    aggregated tinyint(1) default 0 null,
    metadata varchar(4000) null,
    origin_node_id int null,
    flow_node_id int null
) charset=utf8;
---
create index file_sink_state_dataset_index on file_sink_state (data_set_id);
---
create index file_sink_state_name_sink_id on file_sink_state (name, sink_id);
---
create index file_sink_state_sink_id_last_written on file_sink_state (sink_id, last_written);
---
create index last_written on file_sink_state (last_written);
---
create index run_id on file_sink_state (run_id);
---
create index sink_id on file_sink_state (sink_id);
---
create table file_sink_state_copy (
    id bigint default 0 not null,
    name varchar(500) null,
    size_value bigint null,
    record_count bigint null,
    sink_id int not null,
    error varchar(4000) charset utf8 null,
    write_status int default 0 null,
    data_set_id int null,
    last_written timestamp(3) null,
    error_message varchar(4000) charset utf8 null,
    error_count bigint null,
    run_id bigint null,
    aggregated tinyint(1) default 0 null
);
---
create index file_sink_state_copy_dataset_index on file_sink_state_copy (data_set_id);
---
create index file_sink_state_copy_name_sink_id on file_sink_state_copy (name, sink_id);
---
create index file_sink_state_copy_sink_id_last_written on file_sink_state_copy (sink_id, last_written);
---
create index file_sink_state_copy_sink_id_name_last_hash on file_sink_state_copy (sink_id, name);
---
create index file_sink_state_copy_sink_id_name_last_written on file_sink_state_copy (sink_id, name, last_written);
---
create index file_sink_state_copy_sink_name_id on file_sink_state_copy (sink_id, name);
---
create index last_written_copy on file_sink_state_copy (last_written);
---
create index run_id_copy on file_sink_state_copy (run_id);
---
create index sink_id_copy on file_sink_state_copy (sink_id);
---
create table file_source_state (
    id bigint auto_increment primary key,
    name varchar(500) charset latin1 null,
    size_value bigint null,
    record_count bigint null,
    data_source_id int not null,
    error varchar(4000) charset latin1 null,
    ingestion_status int default 0 null,
    data_set_id int null,
    last_modified timestamp null,
    last_ingested timestamp null,
    error_count int null,
    error_message varchar(4000) null,
    run_id bigint null,
    aggregated tinyint(1) default 0 null,
    metadata varchar(4000) null,
    origin_node_id int null,
    flow_node_id int null
) charset=utf8;
---
create index data_source_id on file_source_state (data_source_id);
---
create index file_source_state_data_source_id_last_ingested on file_source_state (data_source_id, last_ingested);
---
create index file_source_state_name_data_source_id on file_source_state (name, data_source_id);
---
create index file_state_dataset_index on file_source_state (data_set_id);
---
create index last_ingested on file_source_state (last_ingested);
---
create index last_modified on file_source_state (last_modified);
---
create index run_id on file_source_state (run_id);
---
create index file_source_state_group_fields on file_source_state (data_source_id, name, data_set_id);
---
create table file_source_state_temp (
    id bigint auto_increment primary key,
    name varchar(500) charset latin1 null,
    size_value bigint null,
    record_count bigint null,
    data_source_id int not null,
    error varchar(4000) charset latin1 null,
    ingestion_status int default 0 null,
    data_set_id int null,
    last_modified timestamp null,
    last_ingested timestamp null,
    error_count int null,
    error_message varchar(4000) null,
    run_id bigint null,
    aggregated tinyint(1) default 0 null
) charset=utf8;
---
create index data_source_id on file_source_state_temp (data_source_id);
---
create index file_source_state_data_source_id_last_ingested on file_source_state_temp (data_source_id, last_ingested);
---
create index file_source_state_name_data_source_id on file_source_state_temp (name, data_source_id);
---
create index file_state_dataset_index on file_source_state_temp (data_set_id);
---
create index last_ingested on file_source_state_temp (last_ingested);
---
create index last_modified on file_source_state_temp (last_modified);
---
create index run_id on file_source_state_temp (run_id);
---
create table quarantine_files (
    id bigint auto_increment primary key,
    resource_type varchar(10) not null,
    resource_id int not null,
    dataset_id int null,
    name varchar(500) not null,
    bucket varchar(100) null,
    size_value bigint null,
    record_count bigint null,
    data_credentials_id int null,
    cron_frequency varchar(255) not null,
    quarantine_setting_id int not null,
    org_id int null,
    owner_id int null,
    updated_at timestamp(3) null,
    created_at timestamp(3) null,
    system_quarantine tinyint default 0 null
);
---
create index quarantine_files_dataset_id on quarantine_files (dataset_id);
---
create index quarantine_files_org_id on quarantine_files (org_id);
---
create index quarantine_files_owner_id on quarantine_files (owner_id);
---
create index quarantine_files_resource on quarantine_files (resource_id, resource_type);
---
create index quarantine_files_resource_id on quarantine_files (resource_id);
---
create index quarantine_files_resource_type on quarantine_files (resource_type);
---
create table quarantine_topic_offset (
    resource_type varchar(10) not null,
    resource_id int not null,
    dataset_id int null,
    partition_number int not null,
    topic varchar(300) not null,
    offset bigint null,
    last_modified timestamp(3) default current_timestamp(3) not null,
    primary key (resource_type, resource_id, topic, partition_number)
);
---
create table script_config (
    id bigint auto_increment primary key,
    cron varchar(128) not null,
    script_github_path varchar(1024) not null,
    parameters text not null,
    description varchar(512) not null,
    status varchar(20) not null,
    source_id int null
);
---
create table topic_offset (
    resource_type varchar(10) not null,
    resource_id int not null,
    dataset_id int not null,
    partition_number int not null,
    topic varchar(300) not null,
    offset bigint null,
    last_modified timestamp(3) default current_timestamp(3) not null,
    primary key (resource_type, resource_id, dataset_id, topic, partition_number)
);
---
CREATE TABLE flow_metrics_daily (
    id bigint auto_increment primary key,
    origin_node_id int NOT NULL,
    org_id int NOT NULL,
    owner_id int NOT NULL,
    run_id bigint NOT NULL,
    row_count bigint NOT NULL,
    error_count bigint NOT NULL,
    size_value bigint NOT NULL,
    reporting_date timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    created_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)
) charset=utf8;
---
create index flow_metrics_daily_date on flow_metrics_daily (reporting_date);
---
create index flow_metrics_daily_org_id on flow_metrics_daily (org_id, reporting_date);
---
create index flow_metrics_daily_owner_id on flow_metrics_daily (owner_id, reporting_date);
---
CREATE TABLE dataset_statistics
(
    id bigint auto_increment PRIMARY KEY,
    dataset_id int,
    topic varchar(256),
    start_offsets text,
    end_offsets text,
    status varchar(32),
    submission_id varchar(32),
    statistics_json text,
    attempt int,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    started_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    finished_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    last_modified TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)
);