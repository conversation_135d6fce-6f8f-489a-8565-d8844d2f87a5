CREATE TABLE file_listing
(
  id bigserial PRIMARY KEY,
  resource_id int not null,
  connection_type varchar(20) not null,
  full_path varchar(512) not null,
  last_modified bigint null,
  hash varchar(100) null,
  file_size bigint null,
  link_to_original bigint null,
  created_at timestamp(3) not null,
  updated_at timestamp(3) not null,
  status varchar(20) not null,
  source varchar(20) not null,
  metadata text,
  last_message_offset bigint,
  message varchar(300),
  schema_detection_attempted boolean null,
  deleted boolean NOT NULL DEFAULT false,
  attempts_count int not null default '0',
  pod_name varchar(70) default null
);

CREATE UNIQUE INDEX uq_file_params_should_be_unique
  ON file_listing (resource_id, full_path, COALESCE(hash, '0'), COALESCE(last_modified, 0), COALESCE(file_size, 0))
  WHERE source = 'LISTING';

CREATE UNIQUE INDEX uq_only_one_file_in_progress_allowed
  ON file_listing (resource_id, full_path)
  WHERE status != 'DONE';

CREATE UNIQUE INDEX uq_only_one_original_file
  ON file_listing (resource_id, full_path)
  WHERE link_to_original IS NULL;

CREATE TABLE sink_offset
(
  resource_id int NOT NULL PRIMARY KEY,
  offset_position text,
  last_modified TIMESTAMP(3) NOT NULL
);

CREATE TABLE script_log
(
  id bigserial PRIMARY KEY,
  resource_type VARCHAR(10) NOT NULL,
  resource_id int NOT NULL,
  connection_type varchar(20) NOT NULL,
  start_time TIMESTAMP(3) NOT NULL,
  end_time TIMESTAMP(3) NULL,
  status varchar(20) NOT NULL,
  state TEXT
);

CREATE UNIQUE INDEX uq_script_log_resource_id_start_time
  ON script_log (resource_id, start_time);

CREATE TABLE topic_offset
(
    resource_type VARCHAR(10) NOT NULL,
    resource_id INT NOT NULL,
    dataset_id INT NOT NULL,
    partition_number INT NOT NULL,
    topic VARCHAR(300) NOT NULL,
    topic_offset BIGINT,
    last_modified TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (resource_type, resource_id, dataset_id, topic, partition_number)
);

CREATE TABLE connector_state
(
    resource_type VARCHAR(10) NOT NULL,
    resource_id INT NOT NULL,
    state VARCHAR(20) NOT NULL,
    message TEXT NULL,
    last_modified TIMESTAMP(3)  NOT NULL,
    PRIMARY KEY (resource_type, resource_id)
);

CREATE TABLE pipeline_run_state
(
    resource_type VARCHAR(10) NOT NULL,
    resource_id INT NOT NULL,
    run_id bigint,
    status varchar(32),
    last_modified TIMESTAMP(3) NOT NULL,
    trace_message boolean default false,
    PRIMARY KEY (resource_type, resource_id, run_id)
);

CREATE TABLE dataset_statistics
(
  id bigserial PRIMARY KEY,
  dataset_id int,
  topic varchar(256),
  start_offsets text,
  end_offsets text,
  status varchar(32),
  submission_id varchar(32),
  statistics_json text,
  attempt int,
  created_at TIMESTAMP(3) NOT NULL,
  started_at TIMESTAMP(3),
  finished_at TIMESTAMP(3),
  last_modified TIMESTAMP(3) NOT NULL
);

CREATE TABLE pipeline_state
(
    sink_id INT NOT NULL,
    node_id varchar(36) NULL,
    state VARCHAR(20) NOT NULL,
    message TEXT NULL,
    try_resume boolean null,
    last_heartbeat_ts TIMESTAMP(3) NOT NULL,
    last_data_ts TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (sink_id)
);

CREATE TABLE key_value
(
    vendor_key VARCHAR(100) NOT NULL,
    vendor_value TEXT NOT NULL,
    PRIMARY KEY (vendor_key)
);

CREATE TABLE fast_offset
(
  source_id int NOT NULL,
  sink_id int NOT NULL,
  offset_position text,
  last_modified TIMESTAMP(3) NOT NULL,
  PRIMARY KEY (source_id, sink_id)
);

CREATE TABLE pipeline_task
(
    task_id varchar(256) NOT NULL,
    task_type varchar(36) NOT NULL,
    max_instances int,
    dedicated boolean,
    state varchar(36) NOT NULL,
    meta text,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_active_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL,
    PRIMARY KEY (task_id)
);

CREATE TABLE pipeline_task_node
(
    node_id varchar(36) NOT NULL,
    task_id varchar(36) NOT NULL,
    run_id BIGINT NULL,
    state varchar(36) NOT NULL,
    read_state varchar(36) NULL,
    read_start_ts TIMESTAMP(3) NULL,
    read_done_ts TIMESTAMP(3) NULL,
    write_state varchar(36) NULL,
    write_start_ts TIMESTAMP(3) NULL,
    write_done_ts TIMESTAMP(3) NULL,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL,
    PRIMARY KEY (node_id, task_id)
);

-- Contains tasks that were running on node recently (during the last 5 minutes),
-- but now they are not assigned to that node anymore
CREATE TABLE pipeline_task_node_phantoms
(
    node_id varchar(36) NOT NULL,
    task_id varchar(36) NOT NULL,
    heartbeat_ts TIMESTAMP(3) NULL,
    last_data_ts TIMESTAMP(3) NULL,
    PRIMARY KEY (node_id, task_id)
);

CREATE TABLE pipeline_node
(
    node_id varchar(36) NOT NULL,
    task_type varchar(36) NOT NULL,
    ip varchar(36) null,
    pod_name varchar(256) null,
    dedicated boolean,
    decomission boolean default false,
    remove boolean default false,
    heartbeat_ts TIMESTAMP(3) NULL,
    tags varchar(36) default null,
    version varchar(16) default null,
    service_name varchar(64) default null,
    ctrl_transport boolean default false,
    PRIMARY KEY (node_id)
);

CREATE TABLE pipeline_task_run
(
    task_id varchar(256) NOT NULL,
    task_type varchar(36) NOT NULL,
    run_id BIGINT,
    read_state varchar(36) NOT NULL,
    read_start_ts TIMESTAMP(3),
    read_done_ts TIMESTAMP(3),
    write_state varchar(36) NOT NULL,
    write_start_ts TIMESTAMP(3),
    write_done_ts TIMESTAMP(3),
    PRIMARY KEY (task_id, run_id)
);