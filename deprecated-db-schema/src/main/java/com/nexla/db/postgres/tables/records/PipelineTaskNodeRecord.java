/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.PipelineTaskNode;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record12;
import org.jooq.Record2;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PipelineTaskNodeRecord extends UpdatableRecordImpl<PipelineTaskNodeRecord>
    implements Record12<
        String,
        String,
        Long,
        String,
        String,
        LocalDateTime,
        LocalDateTime,
        String,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>pipeline_task_node.node_id</code>. */
  public void setNodeId(String value) {
    set(0, value);
  }

  /** Getter for <code>pipeline_task_node.node_id</code>. */
  public String getNodeId() {
    return (String) get(0);
  }

  /** Setter for <code>pipeline_task_node.task_id</code>. */
  public void setTaskId(String value) {
    set(1, value);
  }

  /** Getter for <code>pipeline_task_node.task_id</code>. */
  public String getTaskId() {
    return (String) get(1);
  }

  /** Setter for <code>pipeline_task_node.run_id</code>. */
  public void setRunId(Long value) {
    set(2, value);
  }

  /** Getter for <code>pipeline_task_node.run_id</code>. */
  public Long getRunId() {
    return (Long) get(2);
  }

  /** Setter for <code>pipeline_task_node.state</code>. */
  public void setState(String value) {
    set(3, value);
  }

  /** Getter for <code>pipeline_task_node.state</code>. */
  public String getState() {
    return (String) get(3);
  }

  /** Setter for <code>pipeline_task_node.read_state</code>. */
  public void setReadState(String value) {
    set(4, value);
  }

  /** Getter for <code>pipeline_task_node.read_state</code>. */
  public String getReadState() {
    return (String) get(4);
  }

  /** Setter for <code>pipeline_task_node.read_start_ts</code>. */
  public void setReadStartTs(LocalDateTime value) {
    set(5, value);
  }

  /** Getter for <code>pipeline_task_node.read_start_ts</code>. */
  public LocalDateTime getReadStartTs() {
    return (LocalDateTime) get(5);
  }

  /** Setter for <code>pipeline_task_node.read_done_ts</code>. */
  public void setReadDoneTs(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>pipeline_task_node.read_done_ts</code>. */
  public LocalDateTime getReadDoneTs() {
    return (LocalDateTime) get(6);
  }

  /** Setter for <code>pipeline_task_node.write_state</code>. */
  public void setWriteState(String value) {
    set(7, value);
  }

  /** Getter for <code>pipeline_task_node.write_state</code>. */
  public String getWriteState() {
    return (String) get(7);
  }

  /** Setter for <code>pipeline_task_node.write_start_ts</code>. */
  public void setWriteStartTs(LocalDateTime value) {
    set(8, value);
  }

  /** Getter for <code>pipeline_task_node.write_start_ts</code>. */
  public LocalDateTime getWriteStartTs() {
    return (LocalDateTime) get(8);
  }

  /** Setter for <code>pipeline_task_node.write_done_ts</code>. */
  public void setWriteDoneTs(LocalDateTime value) {
    set(9, value);
  }

  /** Getter for <code>pipeline_task_node.write_done_ts</code>. */
  public LocalDateTime getWriteDoneTs() {
    return (LocalDateTime) get(9);
  }

  /** Setter for <code>pipeline_task_node.heartbeat_ts</code>. */
  public void setHeartbeatTs(LocalDateTime value) {
    set(10, value);
  }

  /** Getter for <code>pipeline_task_node.heartbeat_ts</code>. */
  public LocalDateTime getHeartbeatTs() {
    return (LocalDateTime) get(10);
  }

  /** Setter for <code>pipeline_task_node.last_data_ts</code>. */
  public void setLastDataTs(LocalDateTime value) {
    set(11, value);
  }

  /** Getter for <code>pipeline_task_node.last_data_ts</code>. */
  public LocalDateTime getLastDataTs() {
    return (LocalDateTime) get(11);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record2<String, String> key() {
    return (Record2) super.key();
  }

  // -------------------------------------------------------------------------
  // Record12 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row12<
          String,
          String,
          Long,
          String,
          String,
          LocalDateTime,
          LocalDateTime,
          String,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      fieldsRow() {
    return (Row12) super.fieldsRow();
  }

  @Override
  public Row12<
          String,
          String,
          Long,
          String,
          String,
          LocalDateTime,
          LocalDateTime,
          String,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      valuesRow() {
    return (Row12) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.NODE_ID;
  }

  @Override
  public Field<String> field2() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.TASK_ID;
  }

  @Override
  public Field<Long> field3() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.RUN_ID;
  }

  @Override
  public Field<String> field4() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.STATE;
  }

  @Override
  public Field<String> field5() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.READ_STATE;
  }

  @Override
  public Field<LocalDateTime> field6() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.READ_START_TS;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.READ_DONE_TS;
  }

  @Override
  public Field<String> field8() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.WRITE_STATE;
  }

  @Override
  public Field<LocalDateTime> field9() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.WRITE_START_TS;
  }

  @Override
  public Field<LocalDateTime> field10() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.WRITE_DONE_TS;
  }

  @Override
  public Field<LocalDateTime> field11() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.HEARTBEAT_TS;
  }

  @Override
  public Field<LocalDateTime> field12() {
    return PipelineTaskNode.PIPELINE_TASK_NODE.LAST_DATA_TS;
  }

  @Override
  public String component1() {
    return getNodeId();
  }

  @Override
  public String component2() {
    return getTaskId();
  }

  @Override
  public Long component3() {
    return getRunId();
  }

  @Override
  public String component4() {
    return getState();
  }

  @Override
  public String component5() {
    return getReadState();
  }

  @Override
  public LocalDateTime component6() {
    return getReadStartTs();
  }

  @Override
  public LocalDateTime component7() {
    return getReadDoneTs();
  }

  @Override
  public String component8() {
    return getWriteState();
  }

  @Override
  public LocalDateTime component9() {
    return getWriteStartTs();
  }

  @Override
  public LocalDateTime component10() {
    return getWriteDoneTs();
  }

  @Override
  public LocalDateTime component11() {
    return getHeartbeatTs();
  }

  @Override
  public LocalDateTime component12() {
    return getLastDataTs();
  }

  @Override
  public String value1() {
    return getNodeId();
  }

  @Override
  public String value2() {
    return getTaskId();
  }

  @Override
  public Long value3() {
    return getRunId();
  }

  @Override
  public String value4() {
    return getState();
  }

  @Override
  public String value5() {
    return getReadState();
  }

  @Override
  public LocalDateTime value6() {
    return getReadStartTs();
  }

  @Override
  public LocalDateTime value7() {
    return getReadDoneTs();
  }

  @Override
  public String value8() {
    return getWriteState();
  }

  @Override
  public LocalDateTime value9() {
    return getWriteStartTs();
  }

  @Override
  public LocalDateTime value10() {
    return getWriteDoneTs();
  }

  @Override
  public LocalDateTime value11() {
    return getHeartbeatTs();
  }

  @Override
  public LocalDateTime value12() {
    return getLastDataTs();
  }

  @Override
  public PipelineTaskNodeRecord value1(String value) {
    setNodeId(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value2(String value) {
    setTaskId(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value3(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value4(String value) {
    setState(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value5(String value) {
    setReadState(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value6(LocalDateTime value) {
    setReadStartTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value7(LocalDateTime value) {
    setReadDoneTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value8(String value) {
    setWriteState(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value9(LocalDateTime value) {
    setWriteStartTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value10(LocalDateTime value) {
    setWriteDoneTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value11(LocalDateTime value) {
    setHeartbeatTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord value12(LocalDateTime value) {
    setLastDataTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodeRecord values(
      String value1,
      String value2,
      Long value3,
      String value4,
      String value5,
      LocalDateTime value6,
      LocalDateTime value7,
      String value8,
      LocalDateTime value9,
      LocalDateTime value10,
      LocalDateTime value11,
      LocalDateTime value12) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached PipelineTaskNodeRecord */
  public PipelineTaskNodeRecord() {
    super(PipelineTaskNode.PIPELINE_TASK_NODE);
  }

  /** Create a detached, initialised PipelineTaskNodeRecord */
  public PipelineTaskNodeRecord(
      String nodeId,
      String taskId,
      Long runId,
      String state,
      String readState,
      LocalDateTime readStartTs,
      LocalDateTime readDoneTs,
      String writeState,
      LocalDateTime writeStartTs,
      LocalDateTime writeDoneTs,
      LocalDateTime heartbeatTs,
      LocalDateTime lastDataTs) {
    super(PipelineTaskNode.PIPELINE_TASK_NODE);

    setNodeId(nodeId);
    setTaskId(taskId);
    setRunId(runId);
    setState(state);
    setReadState(readState);
    setReadStartTs(readStartTs);
    setReadDoneTs(readDoneTs);
    setWriteState(writeState);
    setWriteStartTs(writeStartTs);
    setWriteDoneTs(writeDoneTs);
    setHeartbeatTs(heartbeatTs);
    setLastDataTs(lastDataTs);
  }
}
