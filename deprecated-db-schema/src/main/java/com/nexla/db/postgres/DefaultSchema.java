/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres;

import com.nexla.db.postgres.tables.AuditConnectorState;
import com.nexla.db.postgres.tables.AuditControl;
import com.nexla.db.postgres.tables.AuditControlJobscheduler;
import com.nexla.db.postgres.tables.AuditCoordination;
import com.nexla.db.postgres.tables.AuditFastOffset;
import com.nexla.db.postgres.tables.AuditKeyValue;
import com.nexla.db.postgres.tables.AuditSinkOffset;
import com.nexla.db.postgres.tables.AuditTopicOffset;
import com.nexla.db.postgres.tables.CanaryRules;
import com.nexla.db.postgres.tables.CloudEnvironment;
import com.nexla.db.postgres.tables.ConnectorState;
import com.nexla.db.postgres.tables.ConnectorSync;
import com.nexla.db.postgres.tables.DatasetStatistics;
import com.nexla.db.postgres.tables.DatasetTrace;
import com.nexla.db.postgres.tables.FastOffset;
import com.nexla.db.postgres.tables.FileListing;
import com.nexla.db.postgres.tables.FlinkJob;
import com.nexla.db.postgres.tables.KeyValue;
import com.nexla.db.postgres.tables.PipelineNode;
import com.nexla.db.postgres.tables.PipelineRunState;
import com.nexla.db.postgres.tables.PipelineTask;
import com.nexla.db.postgres.tables.PipelineTaskNode;
import com.nexla.db.postgres.tables.PipelineTaskNodePhantoms;
import com.nexla.db.postgres.tables.PipelineTaskRun;
import com.nexla.db.postgres.tables.PodPool;
import com.nexla.db.postgres.tables.RuntimeStatus;
import com.nexla.db.postgres.tables.ScriptLog;
import com.nexla.db.postgres.tables.Semaphore;
import com.nexla.db.postgres.tables.SinkOffset;
import com.nexla.db.postgres.tables.TopicOffset;
import java.util.Arrays;
import java.util.List;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DefaultSchema extends SchemaImpl {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>DEFAULT_SCHEMA</code> */
  public static final DefaultSchema DEFAULT_SCHEMA = new DefaultSchema();

  /** The table <code>audit_connector_state</code>. */
  public final AuditConnectorState AUDIT_CONNECTOR_STATE =
      AuditConnectorState.AUDIT_CONNECTOR_STATE;

  /** The table <code>audit_control</code>. */
  public final AuditControl AUDIT_CONTROL = AuditControl.AUDIT_CONTROL;

  /** The table <code>audit_control_jobscheduler</code>. */
  public final AuditControlJobscheduler AUDIT_CONTROL_JOBSCHEDULER =
      AuditControlJobscheduler.AUDIT_CONTROL_JOBSCHEDULER;

  /** The table <code>audit_coordination</code>. */
  public final AuditCoordination AUDIT_COORDINATION = AuditCoordination.AUDIT_COORDINATION;

  /** The table <code>audit_fast_offset</code>. */
  public final AuditFastOffset AUDIT_FAST_OFFSET = AuditFastOffset.AUDIT_FAST_OFFSET;

  /** The table <code>audit_key_value</code>. */
  public final AuditKeyValue AUDIT_KEY_VALUE = AuditKeyValue.AUDIT_KEY_VALUE;

  /** The table <code>audit_sink_offset</code>. */
  public final AuditSinkOffset AUDIT_SINK_OFFSET = AuditSinkOffset.AUDIT_SINK_OFFSET;

  /** The table <code>audit_topic_offset</code>. */
  public final AuditTopicOffset AUDIT_TOPIC_OFFSET = AuditTopicOffset.AUDIT_TOPIC_OFFSET;

  /** The table <code>canary_rules</code>. */
  public final CanaryRules CANARY_RULES = CanaryRules.CANARY_RULES;

  /** The table <code>cloud_environment</code>. */
  public final CloudEnvironment CLOUD_ENVIRONMENT = CloudEnvironment.CLOUD_ENVIRONMENT;

  /** The table <code>connector_state</code>. */
  public final ConnectorState CONNECTOR_STATE = ConnectorState.CONNECTOR_STATE;

  /** The table <code>connector_sync</code>. */
  public final ConnectorSync CONNECTOR_SYNC = ConnectorSync.CONNECTOR_SYNC;

  /** The table <code>dataset_statistics</code>. */
  public final DatasetStatistics DATASET_STATISTICS = DatasetStatistics.DATASET_STATISTICS;

  /** The table <code>dataset_trace</code>. */
  public final DatasetTrace DATASET_TRACE = DatasetTrace.DATASET_TRACE;

  /** The table <code>fast_offset</code>. */
  public final FastOffset FAST_OFFSET = FastOffset.FAST_OFFSET;

  /** The table <code>file_listing</code>. */
  public final FileListing FILE_LISTING = FileListing.FILE_LISTING;

  /** The table <code>flink_job</code>. */
  public final FlinkJob FLINK_JOB = FlinkJob.FLINK_JOB;

  /** The table <code>key_value</code>. */
  public final KeyValue KEY_VALUE = KeyValue.KEY_VALUE;

  /** The table <code>license</code>. */

  /** The table <code>pipeline_node</code>. */
  public final PipelineNode PIPELINE_NODE = PipelineNode.PIPELINE_NODE;

  /** The table <code>pipeline_run_state</code>. */
  public final PipelineRunState PIPELINE_RUN_STATE = PipelineRunState.PIPELINE_RUN_STATE;

  /** The table <code>pipeline_task</code>. */
  public final PipelineTask PIPELINE_TASK = PipelineTask.PIPELINE_TASK;

  /** The table <code>pipeline_task_node</code>. */
  public final PipelineTaskNode PIPELINE_TASK_NODE = PipelineTaskNode.PIPELINE_TASK_NODE;

  /** The table <code>pipeline_task_node_phantoms</code>. */
  public final PipelineTaskNodePhantoms PIPELINE_TASK_NODE_PHANTOMS =
      PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS;

  /** The table <code>pipeline_task_run</code>. */
  public final PipelineTaskRun PIPELINE_TASK_RUN = PipelineTaskRun.PIPELINE_TASK_RUN;

  /** The table <code>pod_pool</code>. */
  public final PodPool POD_POOL = PodPool.POD_POOL;

  /** The table <code>runtime_status</code>. */
  public final RuntimeStatus RUNTIME_STATUS = RuntimeStatus.RUNTIME_STATUS;

  /** The table <code>script_log</code>. */
  public final ScriptLog SCRIPT_LOG = ScriptLog.SCRIPT_LOG;

  /** The table <code>semaphore</code>. */
  public final Semaphore SEMAPHORE = Semaphore.SEMAPHORE;

  /** The table <code>sink_offset</code>. */
  public final SinkOffset SINK_OFFSET = SinkOffset.SINK_OFFSET;

  /** The table <code>topic_offset</code>. */
  public final TopicOffset TOPIC_OFFSET = TopicOffset.TOPIC_OFFSET;

  /** No further instances allowed */
  private DefaultSchema() {
    super("", null);
  }

  @Override
  public Catalog getCatalog() {
    return DefaultCatalog.DEFAULT_CATALOG;
  }

  @Override
  public final List<Table<?>> getTables() {
    return Arrays.<Table<?>>asList(
        AuditConnectorState.AUDIT_CONNECTOR_STATE,
        AuditControl.AUDIT_CONTROL,
        AuditControlJobscheduler.AUDIT_CONTROL_JOBSCHEDULER,
        AuditCoordination.AUDIT_COORDINATION,
        AuditFastOffset.AUDIT_FAST_OFFSET,
        AuditKeyValue.AUDIT_KEY_VALUE,
        AuditSinkOffset.AUDIT_SINK_OFFSET,
        AuditTopicOffset.AUDIT_TOPIC_OFFSET,
        CanaryRules.CANARY_RULES,
        CloudEnvironment.CLOUD_ENVIRONMENT,
        ConnectorState.CONNECTOR_STATE,
        ConnectorSync.CONNECTOR_SYNC,
        DatasetTrace.DATASET_TRACE,
        FlinkJob.FLINK_JOB,
        KeyValue.KEY_VALUE,
        PipelineNode.PIPELINE_NODE,
        PipelineRunState.PIPELINE_RUN_STATE,
        PipelineTask.PIPELINE_TASK,
        PipelineTaskNode.PIPELINE_TASK_NODE,
        PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS,
        PipelineTaskRun.PIPELINE_TASK_RUN,
        PodPool.POD_POOL,
        RuntimeStatus.RUNTIME_STATUS,
        ScriptLog.SCRIPT_LOG,
        Semaphore.SEMAPHORE,
        SinkOffset.SINK_OFFSET,
        TopicOffset.TOPIC_OFFSET);
  }
}
