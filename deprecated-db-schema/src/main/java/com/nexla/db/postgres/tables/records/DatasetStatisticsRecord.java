/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.DatasetStatistics;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DatasetStatisticsRecord extends UpdatableRecordImpl<DatasetStatisticsRecord>
    implements Record13<
        Long,
        Integer,
        String,
        String,
        String,
        String,
        String,
        String,
        Integer,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>dataset_statistics.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>dataset_statistics.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>dataset_statistics.dataset_id</code>. */
  public void setDatasetId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>dataset_statistics.dataset_id</code>. */
  public Integer getDatasetId() {
    return (Integer) get(1);
  }

  /** Setter for <code>dataset_statistics.topic</code>. */
  public void setTopic(String value) {
    set(2, value);
  }

  /** Getter for <code>dataset_statistics.topic</code>. */
  public String getTopic() {
    return (String) get(2);
  }

  /** Setter for <code>dataset_statistics.start_offsets</code>. */
  public void setStartOffsets(String value) {
    set(3, value);
  }

  /** Getter for <code>dataset_statistics.start_offsets</code>. */
  public String getStartOffsets() {
    return (String) get(3);
  }

  /** Setter for <code>dataset_statistics.end_offsets</code>. */
  public void setEndOffsets(String value) {
    set(4, value);
  }

  /** Getter for <code>dataset_statistics.end_offsets</code>. */
  public String getEndOffsets() {
    return (String) get(4);
  }

  /** Setter for <code>dataset_statistics.status</code>. */
  public void setStatus(String value) {
    set(5, value);
  }

  /** Getter for <code>dataset_statistics.status</code>. */
  public String getStatus() {
    return (String) get(5);
  }

  /** Setter for <code>dataset_statistics.submission_id</code>. */
  public void setSubmissionId(String value) {
    set(6, value);
  }

  /** Getter for <code>dataset_statistics.submission_id</code>. */
  public String getSubmissionId() {
    return (String) get(6);
  }

  /** Setter for <code>dataset_statistics.statistics_json</code>. */
  public void setStatisticsJson(String value) {
    set(7, value);
  }

  /** Getter for <code>dataset_statistics.statistics_json</code>. */
  public String getStatisticsJson() {
    return (String) get(7);
  }

  /** Setter for <code>dataset_statistics.attempt</code>. */
  public void setAttempt(Integer value) {
    set(8, value);
  }

  /** Getter for <code>dataset_statistics.attempt</code>. */
  public Integer getAttempt() {
    return (Integer) get(8);
  }

  /** Setter for <code>dataset_statistics.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(9, value);
  }

  /** Getter for <code>dataset_statistics.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(9);
  }

  /** Setter for <code>dataset_statistics.started_at</code>. */
  public void setStartedAt(LocalDateTime value) {
    set(10, value);
  }

  /** Getter for <code>dataset_statistics.started_at</code>. */
  public LocalDateTime getStartedAt() {
    return (LocalDateTime) get(10);
  }

  /** Setter for <code>dataset_statistics.finished_at</code>. */
  public void setFinishedAt(LocalDateTime value) {
    set(11, value);
  }

  /** Getter for <code>dataset_statistics.finished_at</code>. */
  public LocalDateTime getFinishedAt() {
    return (LocalDateTime) get(11);
  }

  /** Setter for <code>dataset_statistics.last_modified</code>. */
  public void setLastModified(LocalDateTime value) {
    set(12, value);
  }

  /** Getter for <code>dataset_statistics.last_modified</code>. */
  public LocalDateTime getLastModified() {
    return (LocalDateTime) get(12);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record13 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row13<
          Long,
          Integer,
          String,
          String,
          String,
          String,
          String,
          String,
          Integer,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      fieldsRow() {
    return (Row13) super.fieldsRow();
  }

  @Override
  public Row13<
          Long,
          Integer,
          String,
          String,
          String,
          String,
          String,
          String,
          Integer,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      valuesRow() {
    return (Row13) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return DatasetStatistics.DATASET_STATISTICS.ID;
  }

  @Override
  public Field<Integer> field2() {
    return DatasetStatistics.DATASET_STATISTICS.DATASET_ID;
  }

  @Override
  public Field<String> field3() {
    return DatasetStatistics.DATASET_STATISTICS.TOPIC;
  }

  @Override
  public Field<String> field4() {
    return DatasetStatistics.DATASET_STATISTICS.START_OFFSETS;
  }

  @Override
  public Field<String> field5() {
    return DatasetStatistics.DATASET_STATISTICS.END_OFFSETS;
  }

  @Override
  public Field<String> field6() {
    return DatasetStatistics.DATASET_STATISTICS.STATUS;
  }

  @Override
  public Field<String> field7() {
    return DatasetStatistics.DATASET_STATISTICS.SUBMISSION_ID;
  }

  @Override
  public Field<String> field8() {
    return DatasetStatistics.DATASET_STATISTICS.STATISTICS_JSON;
  }

  @Override
  public Field<Integer> field9() {
    return DatasetStatistics.DATASET_STATISTICS.ATTEMPT;
  }

  @Override
  public Field<LocalDateTime> field10() {
    return DatasetStatistics.DATASET_STATISTICS.CREATED_AT;
  }

  @Override
  public Field<LocalDateTime> field11() {
    return DatasetStatistics.DATASET_STATISTICS.STARTED_AT;
  }

  @Override
  public Field<LocalDateTime> field12() {
    return DatasetStatistics.DATASET_STATISTICS.FINISHED_AT;
  }

  @Override
  public Field<LocalDateTime> field13() {
    return DatasetStatistics.DATASET_STATISTICS.LAST_MODIFIED;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public Integer component2() {
    return getDatasetId();
  }

  @Override
  public String component3() {
    return getTopic();
  }

  @Override
  public String component4() {
    return getStartOffsets();
  }

  @Override
  public String component5() {
    return getEndOffsets();
  }

  @Override
  public String component6() {
    return getStatus();
  }

  @Override
  public String component7() {
    return getSubmissionId();
  }

  @Override
  public String component8() {
    return getStatisticsJson();
  }

  @Override
  public Integer component9() {
    return getAttempt();
  }

  @Override
  public LocalDateTime component10() {
    return getCreatedAt();
  }

  @Override
  public LocalDateTime component11() {
    return getStartedAt();
  }

  @Override
  public LocalDateTime component12() {
    return getFinishedAt();
  }

  @Override
  public LocalDateTime component13() {
    return getLastModified();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public Integer value2() {
    return getDatasetId();
  }

  @Override
  public String value3() {
    return getTopic();
  }

  @Override
  public String value4() {
    return getStartOffsets();
  }

  @Override
  public String value5() {
    return getEndOffsets();
  }

  @Override
  public String value6() {
    return getStatus();
  }

  @Override
  public String value7() {
    return getSubmissionId();
  }

  @Override
  public String value8() {
    return getStatisticsJson();
  }

  @Override
  public Integer value9() {
    return getAttempt();
  }

  @Override
  public LocalDateTime value10() {
    return getCreatedAt();
  }

  @Override
  public LocalDateTime value11() {
    return getStartedAt();
  }

  @Override
  public LocalDateTime value12() {
    return getFinishedAt();
  }

  @Override
  public LocalDateTime value13() {
    return getLastModified();
  }

  @Override
  public DatasetStatisticsRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value2(Integer value) {
    setDatasetId(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value3(String value) {
    setTopic(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value4(String value) {
    setStartOffsets(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value5(String value) {
    setEndOffsets(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value6(String value) {
    setStatus(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value7(String value) {
    setSubmissionId(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value8(String value) {
    setStatisticsJson(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value9(Integer value) {
    setAttempt(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value10(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value11(LocalDateTime value) {
    setStartedAt(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value12(LocalDateTime value) {
    setFinishedAt(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord value13(LocalDateTime value) {
    setLastModified(value);
    return this;
  }

  @Override
  public DatasetStatisticsRecord values(
      Long value1,
      Integer value2,
      String value3,
      String value4,
      String value5,
      String value6,
      String value7,
      String value8,
      Integer value9,
      LocalDateTime value10,
      LocalDateTime value11,
      LocalDateTime value12,
      LocalDateTime value13) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached DatasetStatisticsRecord */
  public DatasetStatisticsRecord() {
    super(DatasetStatistics.DATASET_STATISTICS);
  }

  /** Create a detached, initialised DatasetStatisticsRecord */
  public DatasetStatisticsRecord(
      Long id,
      Integer datasetId,
      String topic,
      String startOffsets,
      String endOffsets,
      String status,
      String submissionId,
      String statisticsJson,
      Integer attempt,
      LocalDateTime createdAt,
      LocalDateTime startedAt,
      LocalDateTime finishedAt,
      LocalDateTime lastModified) {
    super(DatasetStatistics.DATASET_STATISTICS);

    setId(id);
    setDatasetId(datasetId);
    setTopic(topic);
    setStartOffsets(startOffsets);
    setEndOffsets(endOffsets);
    setStatus(status);
    setSubmissionId(submissionId);
    setStatisticsJson(statisticsJson);
    setAttempt(attempt);
    setCreatedAt(createdAt);
    setStartedAt(startedAt);
    setFinishedAt(finishedAt);
    setLastModified(lastModified);
  }
}
