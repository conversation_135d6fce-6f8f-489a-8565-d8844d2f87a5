/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.SinkOffset;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record3;
import org.jooq.Row3;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class SinkOffsetRecord extends UpdatableRecordImpl<SinkOffsetRecord>
    implements Record3<Integer, String, LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>sink_offset.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(0, value);
  }

  /** Getter for <code>sink_offset.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(0);
  }

  /** Setter for <code>sink_offset.offset_position</code>. */
  public void setOffsetPosition(String value) {
    set(1, value);
  }

  /** Getter for <code>sink_offset.offset_position</code>. */
  public String getOffsetPosition() {
    return (String) get(1);
  }

  /** Setter for <code>sink_offset.last_modified</code>. */
  public void setLastModified(LocalDateTime value) {
    set(2, value);
  }

  /** Getter for <code>sink_offset.last_modified</code>. */
  public LocalDateTime getLastModified() {
    return (LocalDateTime) get(2);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Integer> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record3 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row3<Integer, String, LocalDateTime> fieldsRow() {
    return (Row3) super.fieldsRow();
  }

  @Override
  public Row3<Integer, String, LocalDateTime> valuesRow() {
    return (Row3) super.valuesRow();
  }

  @Override
  public Field<Integer> field1() {
    return SinkOffset.SINK_OFFSET.RESOURCE_ID;
  }

  @Override
  public Field<String> field2() {
    return SinkOffset.SINK_OFFSET.OFFSET_POSITION;
  }

  @Override
  public Field<LocalDateTime> field3() {
    return SinkOffset.SINK_OFFSET.LAST_MODIFIED;
  }

  @Override
  public Integer component1() {
    return getResourceId();
  }

  @Override
  public String component2() {
    return getOffsetPosition();
  }

  @Override
  public LocalDateTime component3() {
    return getLastModified();
  }

  @Override
  public Integer value1() {
    return getResourceId();
  }

  @Override
  public String value2() {
    return getOffsetPosition();
  }

  @Override
  public LocalDateTime value3() {
    return getLastModified();
  }

  @Override
  public SinkOffsetRecord value1(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public SinkOffsetRecord value2(String value) {
    setOffsetPosition(value);
    return this;
  }

  @Override
  public SinkOffsetRecord value3(LocalDateTime value) {
    setLastModified(value);
    return this;
  }

  @Override
  public SinkOffsetRecord values(Integer value1, String value2, LocalDateTime value3) {
    value1(value1);
    value2(value2);
    value3(value3);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached SinkOffsetRecord */
  public SinkOffsetRecord() {
    super(SinkOffset.SINK_OFFSET);
  }

  /** Create a detached, initialised SinkOffsetRecord */
  public SinkOffsetRecord(Integer resourceId, String offsetPosition, LocalDateTime lastModified) {
    super(SinkOffset.SINK_OFFSET);

    setResourceId(resourceId);
    setOffsetPosition(offsetPosition);
    setLastModified(lastModified);
  }
}
