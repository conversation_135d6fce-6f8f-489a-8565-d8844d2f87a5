/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.SinkOffsetRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row3;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class SinkOffset extends TableImpl<SinkOffsetRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>sink_offset</code> */
  public static final SinkOffset SINK_OFFSET = new SinkOffset();

  /** The class holding records for this type */
  @Override
  public Class<SinkOffsetRecord> getRecordType() {
    return SinkOffsetRecord.class;
  }

  /** The column <code>sink_offset.resource_id</code>. */
  public final TableField<SinkOffsetRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>sink_offset.offset_position</code>. */
  public final TableField<SinkOffsetRecord, String> OFFSET_POSITION =
      createField(DSL.name("offset_position"), SQLDataType.CLOB, this, "");

  /** The column <code>sink_offset.last_modified</code>. */
  public final TableField<SinkOffsetRecord, LocalDateTime> LAST_MODIFIED =
      createField(
          DSL.name("last_modified"), SQLDataType.LOCALDATETIME(3).nullable(false), this, "");

  private SinkOffset(Name alias, Table<SinkOffsetRecord> aliased) {
    this(alias, aliased, null);
  }

  private SinkOffset(Name alias, Table<SinkOffsetRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>sink_offset</code> table reference */
  public SinkOffset(String alias) {
    this(DSL.name(alias), SINK_OFFSET);
  }

  /** Create an aliased <code>sink_offset</code> table reference */
  public SinkOffset(Name alias) {
    this(alias, SINK_OFFSET);
  }

  /** Create a <code>sink_offset</code> table reference */
  public SinkOffset() {
    this(DSL.name("sink_offset"), null);
  }

  public <O extends Record> SinkOffset(Table<O> child, ForeignKey<O, SinkOffsetRecord> key) {
    super(child, key, SINK_OFFSET);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<SinkOffsetRecord> getPrimaryKey() {
    return Keys.SINK_OFFSET_PKEY;
  }

  @Override
  public List<UniqueKey<SinkOffsetRecord>> getKeys() {
    return Arrays.<UniqueKey<SinkOffsetRecord>>asList(Keys.SINK_OFFSET_PKEY);
  }

  @Override
  public SinkOffset as(String alias) {
    return new SinkOffset(DSL.name(alias), this);
  }

  @Override
  public SinkOffset as(Name alias) {
    return new SinkOffset(alias, this);
  }

  /** Rename this table */
  @Override
  public SinkOffset rename(String name) {
    return new SinkOffset(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public SinkOffset rename(Name name) {
    return new SinkOffset(name, null);
  }

  // -------------------------------------------------------------------------
  // Row3 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row3<Integer, String, LocalDateTime> fieldsRow() {
    return (Row3) super.fieldsRow();
  }
}
