/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.PipelineRunState;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record3;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PipelineRunStateRecord extends UpdatableRecordImpl<PipelineRunStateRecord>
    implements Record8<String, Integer, Long, String, LocalDateTime, Boolean, String, Boolean> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>pipeline_run_state.resource_type</code>. */
  public void setResourceType(String value) {
    set(0, value);
  }

  /** Getter for <code>pipeline_run_state.resource_type</code>. */
  public String getResourceType() {
    return (String) get(0);
  }

  /** Setter for <code>pipeline_run_state.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>pipeline_run_state.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(1);
  }

  /** Setter for <code>pipeline_run_state.run_id</code>. */
  public void setRunId(Long value) {
    set(2, value);
  }

  /** Getter for <code>pipeline_run_state.run_id</code>. */
  public Long getRunId() {
    return (Long) get(2);
  }

  /** Setter for <code>pipeline_run_state.status</code>. */
  public void setStatus(String value) {
    set(3, value);
  }

  /** Getter for <code>pipeline_run_state.status</code>. */
  public String getStatus() {
    return (String) get(3);
  }

  /** Setter for <code>pipeline_run_state.last_modified</code>. */
  public void setLastModified(LocalDateTime value) {
    set(4, value);
  }

  /** Getter for <code>pipeline_run_state.last_modified</code>. */
  public LocalDateTime getLastModified() {
    return (LocalDateTime) get(4);
  }

  /** Setter for <code>pipeline_run_state.trace_message</code>. */
  public void setTraceMessage(Boolean value) {
    set(5, value);
  }

  /** Getter for <code>pipeline_run_state.trace_message</code>. */
  public Boolean getTraceMessage() {
    return (Boolean) get(5);
  }

  /** Setter for <code>pipeline_run_state.runtime</code>. */
  public void setRuntime(String value) {
    set(6, value);
  }

  /** Getter for <code>pipeline_run_state.runtime</code>. */
  public String getRuntime() {
    return (String) get(6);
  }

  /** Setter for <code>pipeline_run_state.done_on_timeout</code>. */
  public void setDoneOnTimeout(Boolean value) {
    set(7, value);
  }

  /** Getter for <code>pipeline_run_state.done_on_timeout</code>. */
  public Boolean getDoneOnTimeout() {
    return (Boolean) get(7);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record3<String, Integer, Long> key() {
    return (Record3) super.key();
  }

  // -------------------------------------------------------------------------
  // Record8 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row8<String, Integer, Long, String, LocalDateTime, Boolean, String, Boolean> fieldsRow() {
    return (Row8) super.fieldsRow();
  }

  @Override
  public Row8<String, Integer, Long, String, LocalDateTime, Boolean, String, Boolean> valuesRow() {
    return (Row8) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return PipelineRunState.PIPELINE_RUN_STATE.RESOURCE_TYPE;
  }

  @Override
  public Field<Integer> field2() {
    return PipelineRunState.PIPELINE_RUN_STATE.RESOURCE_ID;
  }

  @Override
  public Field<Long> field3() {
    return PipelineRunState.PIPELINE_RUN_STATE.RUN_ID;
  }

  @Override
  public Field<String> field4() {
    return PipelineRunState.PIPELINE_RUN_STATE.STATUS;
  }

  @Override
  public Field<LocalDateTime> field5() {
    return PipelineRunState.PIPELINE_RUN_STATE.LAST_MODIFIED;
  }

  @Override
  public Field<Boolean> field6() {
    return PipelineRunState.PIPELINE_RUN_STATE.TRACE_MESSAGE;
  }

  @Override
  public Field<String> field7() {
    return PipelineRunState.PIPELINE_RUN_STATE.RUNTIME;
  }

  @Override
  public Field<Boolean> field8() {
    return PipelineRunState.PIPELINE_RUN_STATE.DONE_ON_TIMEOUT;
  }

  @Override
  public String component1() {
    return getResourceType();
  }

  @Override
  public Integer component2() {
    return getResourceId();
  }

  @Override
  public Long component3() {
    return getRunId();
  }

  @Override
  public String component4() {
    return getStatus();
  }

  @Override
  public LocalDateTime component5() {
    return getLastModified();
  }

  @Override
  public Boolean component6() {
    return getTraceMessage();
  }

  @Override
  public String component7() {
    return getRuntime();
  }

  @Override
  public Boolean component8() {
    return getDoneOnTimeout();
  }

  @Override
  public String value1() {
    return getResourceType();
  }

  @Override
  public Integer value2() {
    return getResourceId();
  }

  @Override
  public Long value3() {
    return getRunId();
  }

  @Override
  public String value4() {
    return getStatus();
  }

  @Override
  public LocalDateTime value5() {
    return getLastModified();
  }

  @Override
  public Boolean value6() {
    return getTraceMessage();
  }

  @Override
  public String value7() {
    return getRuntime();
  }

  @Override
  public Boolean value8() {
    return getDoneOnTimeout();
  }

  @Override
  public PipelineRunStateRecord value1(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value2(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value3(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value4(String value) {
    setStatus(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value5(LocalDateTime value) {
    setLastModified(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value6(Boolean value) {
    setTraceMessage(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value7(String value) {
    setRuntime(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord value8(Boolean value) {
    setDoneOnTimeout(value);
    return this;
  }

  @Override
  public PipelineRunStateRecord values(
      String value1,
      Integer value2,
      Long value3,
      String value4,
      LocalDateTime value5,
      Boolean value6,
      String value7,
      Boolean value8) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached PipelineRunStateRecord */
  public PipelineRunStateRecord() {
    super(PipelineRunState.PIPELINE_RUN_STATE);
  }

  /** Create a detached, initialised PipelineRunStateRecord */
  public PipelineRunStateRecord(
      String resourceType,
      Integer resourceId,
      Long runId,
      String status,
      LocalDateTime lastModified,
      Boolean traceMessage,
      String runtime,
      Boolean doneOnTimeout) {
    super(PipelineRunState.PIPELINE_RUN_STATE);

    setResourceType(resourceType);
    setResourceId(resourceId);
    setRunId(runId);
    setStatus(status);
    setLastModified(lastModified);
    setTraceMessage(traceMessage);
    setRuntime(runtime);
    setDoneOnTimeout(doneOnTimeout);
  }
}
