/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.PodPoolRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PodPool extends TableImpl<PodPoolRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>pod_pool</code> */
  public static final PodPool POD_POOL = new PodPool();

  /** The class holding records for this type */
  @Override
  public Class<PodPoolRecord> getRecordType() {
    return PodPoolRecord.class;
  }

  /** The column <code>pod_pool.pooled_service_name</code>. */
  public final TableField<PodPoolRecord, String> POOLED_SERVICE_NAME =
      createField(
          DSL.name("pooled_service_name"), SQLDataType.VARCHAR(128).nullable(false), this, "");

  /** The column <code>pod_pool.docker_image</code>. */
  public final TableField<PodPoolRecord, String> DOCKER_IMAGE =
      createField(DSL.name("docker_image"), SQLDataType.VARCHAR(128).nullable(false), this, "");

  /** The column <code>pod_pool.nexla_service_name</code>. */
  public final TableField<PodPoolRecord, String> NEXLA_SERVICE_NAME =
      createField(DSL.name("nexla_service_name"), SQLDataType.VARCHAR(128), this, "");

  /** The column <code>pod_pool.status</code>. */
  public final TableField<PodPoolRecord, String> STATUS =
      createField(
          DSL.name("status"),
          SQLDataType.VARCHAR(32)
              .nullable(false)
              .defaultValue(DSL.inline("PHANTOM", SQLDataType.VARCHAR)),
          this,
          "");

  /** The column <code>pod_pool.last_modified_ts</code>. */
  public final TableField<PodPoolRecord, LocalDateTime> LAST_MODIFIED_TS =
      createField(
          DSL.name("last_modified_ts"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("CURRENT_TIMESTAMP(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>pod_pool.enabled</code>. */
  public final TableField<PodPoolRecord, Boolean> ENABLED =
      createField(
          DSL.name("enabled"),
          SQLDataType.BOOLEAN.defaultValue(DSL.inline("1", SQLDataType.BOOLEAN)),
          this,
          "");

  private PodPool(Name alias, Table<PodPoolRecord> aliased) {
    this(alias, aliased, null);
  }

  private PodPool(Name alias, Table<PodPoolRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>pod_pool</code> table reference */
  public PodPool(String alias) {
    this(DSL.name(alias), POD_POOL);
  }

  /** Create an aliased <code>pod_pool</code> table reference */
  public PodPool(Name alias) {
    this(alias, POD_POOL);
  }

  /** Create a <code>pod_pool</code> table reference */
  public PodPool() {
    this(DSL.name("pod_pool"), null);
  }

  public <O extends Record> PodPool(Table<O> child, ForeignKey<O, PodPoolRecord> key) {
    super(child, key, POD_POOL);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<PodPoolRecord> getPrimaryKey() {
    return Keys.KEY_POD_POOL_PRIMARY;
  }

  @Override
  public List<UniqueKey<PodPoolRecord>> getKeys() {
    return Arrays.<UniqueKey<PodPoolRecord>>asList(Keys.KEY_POD_POOL_PRIMARY);
  }

  @Override
  public PodPool as(String alias) {
    return new PodPool(DSL.name(alias), this);
  }

  @Override
  public PodPool as(Name alias) {
    return new PodPool(alias, this);
  }

  /** Rename this table */
  @Override
  public PodPool rename(String name) {
    return new PodPool(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public PodPool rename(Name name) {
    return new PodPool(name, null);
  }

  // -------------------------------------------------------------------------
  // Row6 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row6<String, String, String, String, LocalDateTime, Boolean> fieldsRow() {
    return (Row6) super.fieldsRow();
  }
}
