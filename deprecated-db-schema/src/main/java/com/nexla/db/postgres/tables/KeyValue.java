/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.KeyValueRecord;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class KeyValue extends TableImpl<KeyValueRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>key_value</code> */
  public static final KeyValue KEY_VALUE = new KeyValue();

  /** The class holding records for this type */
  @Override
  public Class<KeyValueRecord> getRecordType() {
    return KeyValueRecord.class;
  }

  /** The column <code>key_value.vendor_key</code>. */
  public final TableField<KeyValueRecord, String> VENDOR_KEY =
      createField(DSL.name("vendor_key"), SQLDataType.VARCHAR(100).nullable(false), this, "");

  /** The column <code>key_value.vendor_value</code>. */
  public final TableField<KeyValueRecord, String> VENDOR_VALUE =
      createField(DSL.name("vendor_value"), SQLDataType.CLOB.nullable(false), this, "");

  private KeyValue(Name alias, Table<KeyValueRecord> aliased) {
    this(alias, aliased, null);
  }

  private KeyValue(Name alias, Table<KeyValueRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>key_value</code> table reference */
  public KeyValue(String alias) {
    this(DSL.name(alias), KEY_VALUE);
  }

  /** Create an aliased <code>key_value</code> table reference */
  public KeyValue(Name alias) {
    this(alias, KEY_VALUE);
  }

  /** Create a <code>key_value</code> table reference */
  public KeyValue() {
    this(DSL.name("key_value"), null);
  }

  public <O extends Record> KeyValue(Table<O> child, ForeignKey<O, KeyValueRecord> key) {
    super(child, key, KEY_VALUE);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<KeyValueRecord> getPrimaryKey() {
    return Keys.KEY_VALUE_PKEY;
  }

  @Override
  public List<UniqueKey<KeyValueRecord>> getKeys() {
    return Arrays.<UniqueKey<KeyValueRecord>>asList(Keys.KEY_VALUE_PKEY);
  }

  @Override
  public KeyValue as(String alias) {
    return new KeyValue(DSL.name(alias), this);
  }

  @Override
  public KeyValue as(Name alias) {
    return new KeyValue(alias, this);
  }

  /** Rename this table */
  @Override
  public KeyValue rename(String name) {
    return new KeyValue(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public KeyValue rename(Name name) {
    return new KeyValue(name, null);
  }

  // -------------------------------------------------------------------------
  // Row2 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row2<String, String> fieldsRow() {
    return (Row2) super.fieldsRow();
  }
}
