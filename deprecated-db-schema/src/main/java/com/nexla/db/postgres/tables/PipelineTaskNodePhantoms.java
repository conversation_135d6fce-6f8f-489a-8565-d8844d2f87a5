/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.PipelineTaskNodePhantomsRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PipelineTaskNodePhantoms extends TableImpl<PipelineTaskNodePhantomsRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>pipeline_task_node_phantoms</code> */
  public static final PipelineTaskNodePhantoms PIPELINE_TASK_NODE_PHANTOMS =
      new PipelineTaskNodePhantoms();

  /** The class holding records for this type */
  @Override
  public Class<PipelineTaskNodePhantomsRecord> getRecordType() {
    return PipelineTaskNodePhantomsRecord.class;
  }

  /** The column <code>pipeline_task_node_phantoms.node_id</code>. */
  public final TableField<PipelineTaskNodePhantomsRecord, String> NODE_ID =
      createField(DSL.name("node_id"), SQLDataType.VARCHAR(36).nullable(false), this, "");

  /** The column <code>pipeline_task_node_phantoms.task_id</code>. */
  public final TableField<PipelineTaskNodePhantomsRecord, String> TASK_ID =
      createField(DSL.name("task_id"), SQLDataType.VARCHAR(36).nullable(false), this, "");

  /** The column <code>pipeline_task_node_phantoms.heartbeat_ts</code>. */
  public final TableField<PipelineTaskNodePhantomsRecord, LocalDateTime> HEARTBEAT_TS =
      createField(DSL.name("heartbeat_ts"), SQLDataType.LOCALDATETIME(3), this, "");

  /** The column <code>pipeline_task_node_phantoms.last_data_ts</code>. */
  public final TableField<PipelineTaskNodePhantomsRecord, LocalDateTime> LAST_DATA_TS =
      createField(DSL.name("last_data_ts"), SQLDataType.LOCALDATETIME(3), this, "");

  private PipelineTaskNodePhantoms(Name alias, Table<PipelineTaskNodePhantomsRecord> aliased) {
    this(alias, aliased, null);
  }

  private PipelineTaskNodePhantoms(
      Name alias, Table<PipelineTaskNodePhantomsRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>pipeline_task_node_phantoms</code> table reference */
  public PipelineTaskNodePhantoms(String alias) {
    this(DSL.name(alias), PIPELINE_TASK_NODE_PHANTOMS);
  }

  /** Create an aliased <code>pipeline_task_node_phantoms</code> table reference */
  public PipelineTaskNodePhantoms(Name alias) {
    this(alias, PIPELINE_TASK_NODE_PHANTOMS);
  }

  /** Create a <code>pipeline_task_node_phantoms</code> table reference */
  public PipelineTaskNodePhantoms() {
    this(DSL.name("pipeline_task_node_phantoms"), null);
  }

  public <O extends Record> PipelineTaskNodePhantoms(
      Table<O> child, ForeignKey<O, PipelineTaskNodePhantomsRecord> key) {
    super(child, key, PIPELINE_TASK_NODE_PHANTOMS);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<PipelineTaskNodePhantomsRecord> getPrimaryKey() {
    return Keys.PIPELINE_TASK_NODE_PHANTOMS_PKEY;
  }

  @Override
  public List<UniqueKey<PipelineTaskNodePhantomsRecord>> getKeys() {
    return Arrays.<UniqueKey<PipelineTaskNodePhantomsRecord>>asList(
        Keys.PIPELINE_TASK_NODE_PHANTOMS_PKEY);
  }

  @Override
  public PipelineTaskNodePhantoms as(String alias) {
    return new PipelineTaskNodePhantoms(DSL.name(alias), this);
  }

  @Override
  public PipelineTaskNodePhantoms as(Name alias) {
    return new PipelineTaskNodePhantoms(alias, this);
  }

  /** Rename this table */
  @Override
  public PipelineTaskNodePhantoms rename(String name) {
    return new PipelineTaskNodePhantoms(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public PipelineTaskNodePhantoms rename(Name name) {
    return new PipelineTaskNodePhantoms(name, null);
  }

  // -------------------------------------------------------------------------
  // Row4 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row4<String, String, LocalDateTime, LocalDateTime> fieldsRow() {
    return (Row4) super.fieldsRow();
  }
}
