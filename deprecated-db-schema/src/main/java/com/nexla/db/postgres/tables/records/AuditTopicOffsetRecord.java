/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.AuditTopicOffset;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class AuditTopicOffsetRecord extends UpdatableRecordImpl<AuditTopicOffsetRecord>
    implements Record9<
        Long, String, LocalDateTime, String, Integer, Integer, Integer, String, Long> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>audit_topic_offset.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>audit_topic_offset.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>audit_topic_offset.action</code>. */
  public void setAction(String value) {
    set(1, value);
  }

  /** Getter for <code>audit_topic_offset.action</code>. */
  public String getAction() {
    return (String) get(1);
  }

  /** Setter for <code>audit_topic_offset.audit_ts</code>. */
  public void setAuditTs(LocalDateTime value) {
    set(2, value);
  }

  /** Getter for <code>audit_topic_offset.audit_ts</code>. */
  public LocalDateTime getAuditTs() {
    return (LocalDateTime) get(2);
  }

  /** Setter for <code>audit_topic_offset.resource_type</code>. */
  public void setResourceType(String value) {
    set(3, value);
  }

  /** Getter for <code>audit_topic_offset.resource_type</code>. */
  public String getResourceType() {
    return (String) get(3);
  }

  /** Setter for <code>audit_topic_offset.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(4, value);
  }

  /** Getter for <code>audit_topic_offset.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(4);
  }

  /** Setter for <code>audit_topic_offset.dataset_id</code>. */
  public void setDatasetId(Integer value) {
    set(5, value);
  }

  /** Getter for <code>audit_topic_offset.dataset_id</code>. */
  public Integer getDatasetId() {
    return (Integer) get(5);
  }

  /** Setter for <code>audit_topic_offset.partition_number</code>. */
  public void setPartitionNumber(Integer value) {
    set(6, value);
  }

  /** Getter for <code>audit_topic_offset.partition_number</code>. */
  public Integer getPartitionNumber() {
    return (Integer) get(6);
  }

  /** Setter for <code>audit_topic_offset.topic</code>. */
  public void setTopic(String value) {
    set(7, value);
  }

  /** Getter for <code>audit_topic_offset.topic</code>. */
  public String getTopic() {
    return (String) get(7);
  }

  /** Setter for <code>audit_topic_offset.topic_offset</code>. */
  public void setTopicOffset(Long value) {
    set(8, value);
  }

  /** Getter for <code>audit_topic_offset.topic_offset</code>. */
  public Long getTopicOffset() {
    return (Long) get(8);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record9 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row9<Long, String, LocalDateTime, String, Integer, Integer, Integer, String, Long>
      fieldsRow() {
    return (Row9) super.fieldsRow();
  }

  @Override
  public Row9<Long, String, LocalDateTime, String, Integer, Integer, Integer, String, Long>
      valuesRow() {
    return (Row9) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.ID;
  }

  @Override
  public Field<String> field2() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.ACTION;
  }

  @Override
  public Field<LocalDateTime> field3() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.AUDIT_TS;
  }

  @Override
  public Field<String> field4() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.RESOURCE_TYPE;
  }

  @Override
  public Field<Integer> field5() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.RESOURCE_ID;
  }

  @Override
  public Field<Integer> field6() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.DATASET_ID;
  }

  @Override
  public Field<Integer> field7() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.PARTITION_NUMBER;
  }

  @Override
  public Field<String> field8() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.TOPIC;
  }

  @Override
  public Field<Long> field9() {
    return AuditTopicOffset.AUDIT_TOPIC_OFFSET.TOPIC_OFFSET;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public String component2() {
    return getAction();
  }

  @Override
  public LocalDateTime component3() {
    return getAuditTs();
  }

  @Override
  public String component4() {
    return getResourceType();
  }

  @Override
  public Integer component5() {
    return getResourceId();
  }

  @Override
  public Integer component6() {
    return getDatasetId();
  }

  @Override
  public Integer component7() {
    return getPartitionNumber();
  }

  @Override
  public String component8() {
    return getTopic();
  }

  @Override
  public Long component9() {
    return getTopicOffset();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public String value2() {
    return getAction();
  }

  @Override
  public LocalDateTime value3() {
    return getAuditTs();
  }

  @Override
  public String value4() {
    return getResourceType();
  }

  @Override
  public Integer value5() {
    return getResourceId();
  }

  @Override
  public Integer value6() {
    return getDatasetId();
  }

  @Override
  public Integer value7() {
    return getPartitionNumber();
  }

  @Override
  public String value8() {
    return getTopic();
  }

  @Override
  public Long value9() {
    return getTopicOffset();
  }

  @Override
  public AuditTopicOffsetRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value2(String value) {
    setAction(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value3(LocalDateTime value) {
    setAuditTs(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value4(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value5(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value6(Integer value) {
    setDatasetId(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value7(Integer value) {
    setPartitionNumber(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value8(String value) {
    setTopic(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord value9(Long value) {
    setTopicOffset(value);
    return this;
  }

  @Override
  public AuditTopicOffsetRecord values(
      Long value1,
      String value2,
      LocalDateTime value3,
      String value4,
      Integer value5,
      Integer value6,
      Integer value7,
      String value8,
      Long value9) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached AuditTopicOffsetRecord */
  public AuditTopicOffsetRecord() {
    super(AuditTopicOffset.AUDIT_TOPIC_OFFSET);
  }

  /** Create a detached, initialised AuditTopicOffsetRecord */
  public AuditTopicOffsetRecord(
      Long id,
      String action,
      LocalDateTime auditTs,
      String resourceType,
      Integer resourceId,
      Integer datasetId,
      Integer partitionNumber,
      String topic,
      Long topicOffset) {
    super(AuditTopicOffset.AUDIT_TOPIC_OFFSET);

    setId(id);
    setAction(action);
    setAuditTs(auditTs);
    setResourceType(resourceType);
    setResourceId(resourceId);
    setDatasetId(datasetId);
    setPartitionNumber(partitionNumber);
    setTopic(topic);
    setTopicOffset(topicOffset);
  }
}
