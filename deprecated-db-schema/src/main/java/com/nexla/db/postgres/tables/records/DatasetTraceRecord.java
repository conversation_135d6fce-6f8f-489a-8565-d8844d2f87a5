/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.DatasetTrace;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DatasetTraceRecord extends UpdatableRecordImpl<DatasetTraceRecord>
    implements Record7<
        Integer, Long, Integer, String, LocalDateTime, LocalDateTime, LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>dataset_trace.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(0, value);
  }

  /** Getter for <code>dataset_trace.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(0);
  }

  /** Setter for <code>dataset_trace.run_id</code>. */
  public void setRunId(Long value) {
    set(1, value);
  }

  /** Getter for <code>dataset_trace.run_id</code>. */
  public Long getRunId() {
    return (Long) get(1);
  }

  /** Setter for <code>dataset_trace.traces</code>. */
  public void setTraces(Integer value) {
    set(2, value);
  }

  /** Getter for <code>dataset_trace.traces</code>. */
  public Integer getTraces() {
    return (Integer) get(2);
  }

  /** Setter for <code>dataset_trace.tx_state</code>. */
  public void setTxState(String value) {
    set(3, value);
  }

  /** Getter for <code>dataset_trace.tx_state</code>. */
  public String getTxState() {
    return (String) get(3);
  }

  /** Setter for <code>dataset_trace.tx_done_ts</code>. */
  public void setTxDoneTs(LocalDateTime value) {
    set(4, value);
  }

  /** Getter for <code>dataset_trace.tx_done_ts</code>. */
  public LocalDateTime getTxDoneTs() {
    return (LocalDateTime) get(4);
  }

  /** Setter for <code>dataset_trace.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(5, value);
  }

  /** Getter for <code>dataset_trace.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(5);
  }

  /** Setter for <code>dataset_trace.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>dataset_trace.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(6);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record2<Integer, Long> key() {
    return (Record2) super.key();
  }

  // -------------------------------------------------------------------------
  // Record7 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row7<Integer, Long, Integer, String, LocalDateTime, LocalDateTime, LocalDateTime>
      fieldsRow() {
    return (Row7) super.fieldsRow();
  }

  @Override
  public Row7<Integer, Long, Integer, String, LocalDateTime, LocalDateTime, LocalDateTime>
      valuesRow() {
    return (Row7) super.valuesRow();
  }

  @Override
  public Field<Integer> field1() {
    return DatasetTrace.DATASET_TRACE.RESOURCE_ID;
  }

  @Override
  public Field<Long> field2() {
    return DatasetTrace.DATASET_TRACE.RUN_ID;
  }

  @Override
  public Field<Integer> field3() {
    return DatasetTrace.DATASET_TRACE.TRACES;
  }

  @Override
  public Field<String> field4() {
    return DatasetTrace.DATASET_TRACE.TX_STATE;
  }

  @Override
  public Field<LocalDateTime> field5() {
    return DatasetTrace.DATASET_TRACE.TX_DONE_TS;
  }

  @Override
  public Field<LocalDateTime> field6() {
    return DatasetTrace.DATASET_TRACE.UPDATED_AT;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return DatasetTrace.DATASET_TRACE.CREATED_AT;
  }

  @Override
  public Integer component1() {
    return getResourceId();
  }

  @Override
  public Long component2() {
    return getRunId();
  }

  @Override
  public Integer component3() {
    return getTraces();
  }

  @Override
  public String component4() {
    return getTxState();
  }

  @Override
  public LocalDateTime component5() {
    return getTxDoneTs();
  }

  @Override
  public LocalDateTime component6() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime component7() {
    return getCreatedAt();
  }

  @Override
  public Integer value1() {
    return getResourceId();
  }

  @Override
  public Long value2() {
    return getRunId();
  }

  @Override
  public Integer value3() {
    return getTraces();
  }

  @Override
  public String value4() {
    return getTxState();
  }

  @Override
  public LocalDateTime value5() {
    return getTxDoneTs();
  }

  @Override
  public LocalDateTime value6() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime value7() {
    return getCreatedAt();
  }

  @Override
  public DatasetTraceRecord value1(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public DatasetTraceRecord value2(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public DatasetTraceRecord value3(Integer value) {
    setTraces(value);
    return this;
  }

  @Override
  public DatasetTraceRecord value4(String value) {
    setTxState(value);
    return this;
  }

  @Override
  public DatasetTraceRecord value5(LocalDateTime value) {
    setTxDoneTs(value);
    return this;
  }

  @Override
  public DatasetTraceRecord value6(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public DatasetTraceRecord value7(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public DatasetTraceRecord values(
      Integer value1,
      Long value2,
      Integer value3,
      String value4,
      LocalDateTime value5,
      LocalDateTime value6,
      LocalDateTime value7) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached DatasetTraceRecord */
  public DatasetTraceRecord() {
    super(DatasetTrace.DATASET_TRACE);
  }

  /** Create a detached, initialised DatasetTraceRecord */
  public DatasetTraceRecord(
      Integer resourceId,
      Long runId,
      Integer traces,
      String txState,
      LocalDateTime txDoneTs,
      LocalDateTime updatedAt,
      LocalDateTime createdAt) {
    super(DatasetTrace.DATASET_TRACE);

    setResourceId(resourceId);
    setRunId(runId);
    setTraces(traces);
    setTxState(txState);
    setTxDoneTs(txDoneTs);
    setUpdatedAt(updatedAt);
    setCreatedAt(createdAt);
  }
}
