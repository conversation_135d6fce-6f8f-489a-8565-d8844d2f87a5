/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.PipelineNode;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PipelineNodeRecord extends UpdatableRecordImpl<PipelineNodeRecord>
    implements Record11<
        String,
        String,
        String,
        String,
        Boolean,
        Boolean,
        Boolean,
        LocalDateTime,
        String,
        String,
        String> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>pipeline_node.node_id</code>. */
  public void setNodeId(String value) {
    set(0, value);
  }

  /** Getter for <code>pipeline_node.node_id</code>. */
  public String getNodeId() {
    return (String) get(0);
  }

  /** Setter for <code>pipeline_node.task_type</code>. */
  public void setTaskType(String value) {
    set(1, value);
  }

  /** Getter for <code>pipeline_node.task_type</code>. */
  public String getTaskType() {
    return (String) get(1);
  }

  /** Setter for <code>pipeline_node.ip</code>. */
  public void setIp(String value) {
    set(2, value);
  }

  /** Getter for <code>pipeline_node.ip</code>. */
  public String getIp() {
    return (String) get(2);
  }

  /** Setter for <code>pipeline_node.pod_name</code>. */
  public void setPodName(String value) {
    set(3, value);
  }

  /** Getter for <code>pipeline_node.pod_name</code>. */
  public String getPodName() {
    return (String) get(3);
  }

  /** Setter for <code>pipeline_node.dedicated</code>. */
  public void setDedicated(Boolean value) {
    set(4, value);
  }

  /** Getter for <code>pipeline_node.dedicated</code>. */
  public Boolean getDedicated() {
    return (Boolean) get(4);
  }

  /** Setter for <code>pipeline_node.decomission</code>. */
  public void setDecomission(Boolean value) {
    set(5, value);
  }

  /** Getter for <code>pipeline_node.decomission</code>. */
  public Boolean getDecomission() {
    return (Boolean) get(5);
  }

  /** Setter for <code>pipeline_node.remove</code>. */
  public void setRemove(Boolean value) {
    set(6, value);
  }

  /** Getter for <code>pipeline_node.remove</code>. */
  public Boolean getRemove() {
    return (Boolean) get(6);
  }

  /** Setter for <code>pipeline_node.heartbeat_ts</code>. */
  public void setHeartbeatTs(LocalDateTime value) {
    set(7, value);
  }

  /** Getter for <code>pipeline_node.heartbeat_ts</code>. */
  public LocalDateTime getHeartbeatTs() {
    return (LocalDateTime) get(7);
  }

  /** Setter for <code>pipeline_node.tags</code>. */
  public void setTags(String value) {
    set(8, value);
  }

  /** Getter for <code>pipeline_node.tags</code>. */
  public String getTags() {
    return (String) get(8);
  }

  /** Setter for <code>pipeline_node.version</code>. */
  public void setVersion(String value) {
    set(9, value);
  }

  /** Getter for <code>pipeline_node.version</code>. */
  public String getVersion() {
    return (String) get(9);
  }

  /** Setter for <code>pipeline_node.service_name</code>. */
  public void setServiceName(String value) {
    set(10, value);
  }

  /** Getter for <code>pipeline_node.service_name</code>. */
  public String getServiceName() {
    return (String) get(10);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<String> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record11 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row11<
          String,
          String,
          String,
          String,
          Boolean,
          Boolean,
          Boolean,
          LocalDateTime,
          String,
          String,
          String>
      fieldsRow() {
    return (Row11) super.fieldsRow();
  }

  @Override
  public Row11<
          String,
          String,
          String,
          String,
          Boolean,
          Boolean,
          Boolean,
          LocalDateTime,
          String,
          String,
          String>
      valuesRow() {
    return (Row11) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return PipelineNode.PIPELINE_NODE.NODE_ID;
  }

  @Override
  public Field<String> field2() {
    return PipelineNode.PIPELINE_NODE.TASK_TYPE;
  }

  @Override
  public Field<String> field3() {
    return PipelineNode.PIPELINE_NODE.IP;
  }

  @Override
  public Field<String> field4() {
    return PipelineNode.PIPELINE_NODE.POD_NAME;
  }

  @Override
  public Field<Boolean> field5() {
    return PipelineNode.PIPELINE_NODE.DEDICATED;
  }

  @Override
  public Field<Boolean> field6() {
    return PipelineNode.PIPELINE_NODE.DECOMISSION;
  }

  @Override
  public Field<Boolean> field7() {
    return PipelineNode.PIPELINE_NODE.REMOVE;
  }

  @Override
  public Field<LocalDateTime> field8() {
    return PipelineNode.PIPELINE_NODE.HEARTBEAT_TS;
  }

  @Override
  public Field<String> field9() {
    return PipelineNode.PIPELINE_NODE.TAGS;
  }

  @Override
  public Field<String> field10() {
    return PipelineNode.PIPELINE_NODE.VERSION;
  }

  @Override
  public Field<String> field11() {
    return PipelineNode.PIPELINE_NODE.SERVICE_NAME;
  }

  @Override
  public String component1() {
    return getNodeId();
  }

  @Override
  public String component2() {
    return getTaskType();
  }

  @Override
  public String component3() {
    return getIp();
  }

  @Override
  public String component4() {
    return getPodName();
  }

  @Override
  public Boolean component5() {
    return getDedicated();
  }

  @Override
  public Boolean component6() {
    return getDecomission();
  }

  @Override
  public Boolean component7() {
    return getRemove();
  }

  @Override
  public LocalDateTime component8() {
    return getHeartbeatTs();
  }

  @Override
  public String component9() {
    return getTags();
  }

  @Override
  public String component10() {
    return getVersion();
  }

  @Override
  public String component11() {
    return getServiceName();
  }

  @Override
  public String value1() {
    return getNodeId();
  }

  @Override
  public String value2() {
    return getTaskType();
  }

  @Override
  public String value3() {
    return getIp();
  }

  @Override
  public String value4() {
    return getPodName();
  }

  @Override
  public Boolean value5() {
    return getDedicated();
  }

  @Override
  public Boolean value6() {
    return getDecomission();
  }

  @Override
  public Boolean value7() {
    return getRemove();
  }

  @Override
  public LocalDateTime value8() {
    return getHeartbeatTs();
  }

  @Override
  public String value9() {
    return getTags();
  }

  @Override
  public String value10() {
    return getVersion();
  }

  @Override
  public String value11() {
    return getServiceName();
  }

  @Override
  public PipelineNodeRecord value1(String value) {
    setNodeId(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value2(String value) {
    setTaskType(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value3(String value) {
    setIp(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value4(String value) {
    setPodName(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value5(Boolean value) {
    setDedicated(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value6(Boolean value) {
    setDecomission(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value7(Boolean value) {
    setRemove(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value8(LocalDateTime value) {
    setHeartbeatTs(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value9(String value) {
    setTags(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value10(String value) {
    setVersion(value);
    return this;
  }

  @Override
  public PipelineNodeRecord value11(String value) {
    setServiceName(value);
    return this;
  }

  @Override
  public PipelineNodeRecord values(
      String value1,
      String value2,
      String value3,
      String value4,
      Boolean value5,
      Boolean value6,
      Boolean value7,
      LocalDateTime value8,
      String value9,
      String value10,
      String value11) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached PipelineNodeRecord */
  public PipelineNodeRecord() {
    super(PipelineNode.PIPELINE_NODE);
  }

  /** Create a detached, initialised PipelineNodeRecord */
  public PipelineNodeRecord(
      String nodeId,
      String taskType,
      String ip,
      String podName,
      Boolean dedicated,
      Boolean decomission,
      Boolean remove,
      LocalDateTime heartbeatTs,
      String tags,
      String version,
      String serviceName) {
    super(PipelineNode.PIPELINE_NODE);

    setNodeId(nodeId);
    setTaskType(taskType);
    setIp(ip);
    setPodName(podName);
    setDedicated(dedicated);
    setDecomission(decomission);
    setRemove(remove);
    setHeartbeatTs(heartbeatTs);
    setTags(tags);
    setVersion(version);
    setServiceName(serviceName);
  }
}
