/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.AuditControl;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class AuditControlRecord extends UpdatableRecordImpl<AuditControlRecord>
    implements Record9<
        Long, String, String, String, Integer, String, LocalDateTime, String, LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>audit_control.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>audit_control.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>audit_control.message_id</code>. */
  public void setMessageId(String value) {
    set(1, value);
  }

  /** Getter for <code>audit_control.message_id</code>. */
  public String getMessageId() {
    return (String) get(1);
  }

  /** Setter for <code>audit_control.resource_type</code>. */
  public void setResourceType(String value) {
    set(2, value);
  }

  /** Getter for <code>audit_control.resource_type</code>. */
  public String getResourceType() {
    return (String) get(2);
  }

  /** Setter for <code>audit_control.event_type</code>. */
  public void setEventType(String value) {
    set(3, value);
  }

  /** Getter for <code>audit_control.event_type</code>. */
  public String getEventType() {
    return (String) get(3);
  }

  /** Setter for <code>audit_control.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(4, value);
  }

  /** Getter for <code>audit_control.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(4);
  }

  /** Setter for <code>audit_control.origin</code>. */
  public void setOrigin(String value) {
    set(5, value);
  }

  /** Getter for <code>audit_control.origin</code>. */
  public String getOrigin() {
    return (String) get(5);
  }

  /** Setter for <code>audit_control.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>audit_control.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(6);
  }

  /** Setter for <code>audit_control.body</code>. */
  public void setBody(String value) {
    set(7, value);
  }

  /** Getter for <code>audit_control.body</code>. */
  public String getBody() {
    return (String) get(7);
  }

  /** Setter for <code>audit_control.audit_ts</code>. */
  public void setAuditTs(LocalDateTime value) {
    set(8, value);
  }

  /** Getter for <code>audit_control.audit_ts</code>. */
  public LocalDateTime getAuditTs() {
    return (LocalDateTime) get(8);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record2<Long, LocalDateTime> key() {
    return (Record2) super.key();
  }

  // -------------------------------------------------------------------------
  // Record9 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row9<Long, String, String, String, Integer, String, LocalDateTime, String, LocalDateTime>
      fieldsRow() {
    return (Row9) super.fieldsRow();
  }

  @Override
  public Row9<Long, String, String, String, Integer, String, LocalDateTime, String, LocalDateTime>
      valuesRow() {
    return (Row9) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return AuditControl.AUDIT_CONTROL.ID;
  }

  @Override
  public Field<String> field2() {
    return AuditControl.AUDIT_CONTROL.MESSAGE_ID;
  }

  @Override
  public Field<String> field3() {
    return AuditControl.AUDIT_CONTROL.RESOURCE_TYPE;
  }

  @Override
  public Field<String> field4() {
    return AuditControl.AUDIT_CONTROL.EVENT_TYPE;
  }

  @Override
  public Field<Integer> field5() {
    return AuditControl.AUDIT_CONTROL.RESOURCE_ID;
  }

  @Override
  public Field<String> field6() {
    return AuditControl.AUDIT_CONTROL.ORIGIN;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return AuditControl.AUDIT_CONTROL.CREATED_AT;
  }

  @Override
  public Field<String> field8() {
    return AuditControl.AUDIT_CONTROL.BODY;
  }

  @Override
  public Field<LocalDateTime> field9() {
    return AuditControl.AUDIT_CONTROL.AUDIT_TS;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public String component2() {
    return getMessageId();
  }

  @Override
  public String component3() {
    return getResourceType();
  }

  @Override
  public String component4() {
    return getEventType();
  }

  @Override
  public Integer component5() {
    return getResourceId();
  }

  @Override
  public String component6() {
    return getOrigin();
  }

  @Override
  public LocalDateTime component7() {
    return getCreatedAt();
  }

  @Override
  public String component8() {
    return getBody();
  }

  @Override
  public LocalDateTime component9() {
    return getAuditTs();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public String value2() {
    return getMessageId();
  }

  @Override
  public String value3() {
    return getResourceType();
  }

  @Override
  public String value4() {
    return getEventType();
  }

  @Override
  public Integer value5() {
    return getResourceId();
  }

  @Override
  public String value6() {
    return getOrigin();
  }

  @Override
  public LocalDateTime value7() {
    return getCreatedAt();
  }

  @Override
  public String value8() {
    return getBody();
  }

  @Override
  public LocalDateTime value9() {
    return getAuditTs();
  }

  @Override
  public AuditControlRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public AuditControlRecord value2(String value) {
    setMessageId(value);
    return this;
  }

  @Override
  public AuditControlRecord value3(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public AuditControlRecord value4(String value) {
    setEventType(value);
    return this;
  }

  @Override
  public AuditControlRecord value5(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public AuditControlRecord value6(String value) {
    setOrigin(value);
    return this;
  }

  @Override
  public AuditControlRecord value7(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public AuditControlRecord value8(String value) {
    setBody(value);
    return this;
  }

  @Override
  public AuditControlRecord value9(LocalDateTime value) {
    setAuditTs(value);
    return this;
  }

  @Override
  public AuditControlRecord values(
      Long value1,
      String value2,
      String value3,
      String value4,
      Integer value5,
      String value6,
      LocalDateTime value7,
      String value8,
      LocalDateTime value9) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached AuditControlRecord */
  public AuditControlRecord() {
    super(AuditControl.AUDIT_CONTROL);
  }

  /** Create a detached, initialised AuditControlRecord */
  public AuditControlRecord(
      Long id,
      String messageId,
      String resourceType,
      String eventType,
      Integer resourceId,
      String origin,
      LocalDateTime createdAt,
      String body,
      LocalDateTime auditTs) {
    super(AuditControl.AUDIT_CONTROL);

    setId(id);
    setMessageId(messageId);
    setResourceType(resourceType);
    setEventType(eventType);
    setResourceId(resourceId);
    setOrigin(origin);
    setCreatedAt(createdAt);
    setBody(body);
    setAuditTs(auditTs);
  }
}
