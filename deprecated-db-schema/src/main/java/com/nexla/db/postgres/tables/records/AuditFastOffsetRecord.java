/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.AuditFastOffset;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class AuditFastOffsetRecord extends UpdatableRecordImpl<AuditFastOffsetRecord>
    implements Record6<Long, String, LocalDateTime, Integer, Integer, String> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>audit_fast_offset.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>audit_fast_offset.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>audit_fast_offset.action</code>. */
  public void setAction(String value) {
    set(1, value);
  }

  /** Getter for <code>audit_fast_offset.action</code>. */
  public String getAction() {
    return (String) get(1);
  }

  /** Setter for <code>audit_fast_offset.audit_ts</code>. */
  public void setAuditTs(LocalDateTime value) {
    set(2, value);
  }

  /** Getter for <code>audit_fast_offset.audit_ts</code>. */
  public LocalDateTime getAuditTs() {
    return (LocalDateTime) get(2);
  }

  /** Setter for <code>audit_fast_offset.source_id</code>. */
  public void setSourceId(Integer value) {
    set(3, value);
  }

  /** Getter for <code>audit_fast_offset.source_id</code>. */
  public Integer getSourceId() {
    return (Integer) get(3);
  }

  /** Setter for <code>audit_fast_offset.sink_id</code>. */
  public void setSinkId(Integer value) {
    set(4, value);
  }

  /** Getter for <code>audit_fast_offset.sink_id</code>. */
  public Integer getSinkId() {
    return (Integer) get(4);
  }

  /** Setter for <code>audit_fast_offset.offset_position</code>. */
  public void setOffsetPosition(String value) {
    set(5, value);
  }

  /** Getter for <code>audit_fast_offset.offset_position</code>. */
  public String getOffsetPosition() {
    return (String) get(5);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record6 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row6<Long, String, LocalDateTime, Integer, Integer, String> fieldsRow() {
    return (Row6) super.fieldsRow();
  }

  @Override
  public Row6<Long, String, LocalDateTime, Integer, Integer, String> valuesRow() {
    return (Row6) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return AuditFastOffset.AUDIT_FAST_OFFSET.ID;
  }

  @Override
  public Field<String> field2() {
    return AuditFastOffset.AUDIT_FAST_OFFSET.ACTION;
  }

  @Override
  public Field<LocalDateTime> field3() {
    return AuditFastOffset.AUDIT_FAST_OFFSET.AUDIT_TS;
  }

  @Override
  public Field<Integer> field4() {
    return AuditFastOffset.AUDIT_FAST_OFFSET.SOURCE_ID;
  }

  @Override
  public Field<Integer> field5() {
    return AuditFastOffset.AUDIT_FAST_OFFSET.SINK_ID;
  }

  @Override
  public Field<String> field6() {
    return AuditFastOffset.AUDIT_FAST_OFFSET.OFFSET_POSITION;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public String component2() {
    return getAction();
  }

  @Override
  public LocalDateTime component3() {
    return getAuditTs();
  }

  @Override
  public Integer component4() {
    return getSourceId();
  }

  @Override
  public Integer component5() {
    return getSinkId();
  }

  @Override
  public String component6() {
    return getOffsetPosition();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public String value2() {
    return getAction();
  }

  @Override
  public LocalDateTime value3() {
    return getAuditTs();
  }

  @Override
  public Integer value4() {
    return getSourceId();
  }

  @Override
  public Integer value5() {
    return getSinkId();
  }

  @Override
  public String value6() {
    return getOffsetPosition();
  }

  @Override
  public AuditFastOffsetRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public AuditFastOffsetRecord value2(String value) {
    setAction(value);
    return this;
  }

  @Override
  public AuditFastOffsetRecord value3(LocalDateTime value) {
    setAuditTs(value);
    return this;
  }

  @Override
  public AuditFastOffsetRecord value4(Integer value) {
    setSourceId(value);
    return this;
  }

  @Override
  public AuditFastOffsetRecord value5(Integer value) {
    setSinkId(value);
    return this;
  }

  @Override
  public AuditFastOffsetRecord value6(String value) {
    setOffsetPosition(value);
    return this;
  }

  @Override
  public AuditFastOffsetRecord values(
      Long value1,
      String value2,
      LocalDateTime value3,
      Integer value4,
      Integer value5,
      String value6) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached AuditFastOffsetRecord */
  public AuditFastOffsetRecord() {
    super(AuditFastOffset.AUDIT_FAST_OFFSET);
  }

  /** Create a detached, initialised AuditFastOffsetRecord */
  public AuditFastOffsetRecord(
      Long id,
      String action,
      LocalDateTime auditTs,
      Integer sourceId,
      Integer sinkId,
      String offsetPosition) {
    super(AuditFastOffset.AUDIT_FAST_OFFSET);

    setId(id);
    setAction(action);
    setAuditTs(auditTs);
    setSourceId(sourceId);
    setSinkId(sinkId);
    setOffsetPosition(offsetPosition);
  }
}
