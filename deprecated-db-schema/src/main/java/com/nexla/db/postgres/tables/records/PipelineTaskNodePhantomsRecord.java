/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.PipelineTaskNodePhantoms;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PipelineTaskNodePhantomsRecord
    extends UpdatableRecordImpl<PipelineTaskNodePhantomsRecord>
    implements Record4<String, String, LocalDateTime, LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>pipeline_task_node_phantoms.node_id</code>. */
  public void setNodeId(String value) {
    set(0, value);
  }

  /** Getter for <code>pipeline_task_node_phantoms.node_id</code>. */
  public String getNodeId() {
    return (String) get(0);
  }

  /** Setter for <code>pipeline_task_node_phantoms.task_id</code>. */
  public void setTaskId(String value) {
    set(1, value);
  }

  /** Getter for <code>pipeline_task_node_phantoms.task_id</code>. */
  public String getTaskId() {
    return (String) get(1);
  }

  /** Setter for <code>pipeline_task_node_phantoms.heartbeat_ts</code>. */
  public void setHeartbeatTs(LocalDateTime value) {
    set(2, value);
  }

  /** Getter for <code>pipeline_task_node_phantoms.heartbeat_ts</code>. */
  public LocalDateTime getHeartbeatTs() {
    return (LocalDateTime) get(2);
  }

  /** Setter for <code>pipeline_task_node_phantoms.last_data_ts</code>. */
  public void setLastDataTs(LocalDateTime value) {
    set(3, value);
  }

  /** Getter for <code>pipeline_task_node_phantoms.last_data_ts</code>. */
  public LocalDateTime getLastDataTs() {
    return (LocalDateTime) get(3);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record2<String, String> key() {
    return (Record2) super.key();
  }

  // -------------------------------------------------------------------------
  // Record4 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row4<String, String, LocalDateTime, LocalDateTime> fieldsRow() {
    return (Row4) super.fieldsRow();
  }

  @Override
  public Row4<String, String, LocalDateTime, LocalDateTime> valuesRow() {
    return (Row4) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS.NODE_ID;
  }

  @Override
  public Field<String> field2() {
    return PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS.TASK_ID;
  }

  @Override
  public Field<LocalDateTime> field3() {
    return PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS.HEARTBEAT_TS;
  }

  @Override
  public Field<LocalDateTime> field4() {
    return PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS.LAST_DATA_TS;
  }

  @Override
  public String component1() {
    return getNodeId();
  }

  @Override
  public String component2() {
    return getTaskId();
  }

  @Override
  public LocalDateTime component3() {
    return getHeartbeatTs();
  }

  @Override
  public LocalDateTime component4() {
    return getLastDataTs();
  }

  @Override
  public String value1() {
    return getNodeId();
  }

  @Override
  public String value2() {
    return getTaskId();
  }

  @Override
  public LocalDateTime value3() {
    return getHeartbeatTs();
  }

  @Override
  public LocalDateTime value4() {
    return getLastDataTs();
  }

  @Override
  public PipelineTaskNodePhantomsRecord value1(String value) {
    setNodeId(value);
    return this;
  }

  @Override
  public PipelineTaskNodePhantomsRecord value2(String value) {
    setTaskId(value);
    return this;
  }

  @Override
  public PipelineTaskNodePhantomsRecord value3(LocalDateTime value) {
    setHeartbeatTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodePhantomsRecord value4(LocalDateTime value) {
    setLastDataTs(value);
    return this;
  }

  @Override
  public PipelineTaskNodePhantomsRecord values(
      String value1, String value2, LocalDateTime value3, LocalDateTime value4) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached PipelineTaskNodePhantomsRecord */
  public PipelineTaskNodePhantomsRecord() {
    super(PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS);
  }

  /** Create a detached, initialised PipelineTaskNodePhantomsRecord */
  public PipelineTaskNodePhantomsRecord(
      String nodeId, String taskId, LocalDateTime heartbeatTs, LocalDateTime lastDataTs) {
    super(PipelineTaskNodePhantoms.PIPELINE_TASK_NODE_PHANTOMS);

    setNodeId(nodeId);
    setTaskId(taskId);
    setHeartbeatTs(heartbeatTs);
    setLastDataTs(lastDataTs);
  }
}
