/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.enums.ErrorStatusMetricsStatus;
import com.nexla.db.monitoring.schema.tables.ErrorStatusMetrics;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record13;
import org.jooq.Record2;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class ErrorStatusMetricsRecord extends UpdatableRecordImpl<ErrorStatusMetricsRecord>
    implements Record13<
        Long,
        Integer,
        String,
        Long,
        Long,
        Long,
        ErrorStatusMetricsStatus,
        Integer,
        Integer,
        LocalDateTime,
        LocalDateTime,
        Integer,
        Integer> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>error_status_metrics.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>error_status_metrics.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>error_status_metrics.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>error_status_metrics.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(1);
  }

  /** Setter for <code>error_status_metrics.resource_type</code>. */
  public void setResourceType(String value) {
    set(2, value);
  }

  /** Getter for <code>error_status_metrics.resource_type</code>. */
  public String getResourceType() {
    return (String) get(2);
  }

  /** Setter for <code>error_status_metrics.row_count</code>. */
  public void setRowCount(Long value) {
    set(3, value);
  }

  /** Getter for <code>error_status_metrics.row_count</code>. */
  public Long getRowCount() {
    return (Long) get(3);
  }

  /** Setter for <code>error_status_metrics.error_count</code>. */
  public void setErrorCount(Long value) {
    set(4, value);
  }

  /** Getter for <code>error_status_metrics.error_count</code>. */
  public Long getErrorCount() {
    return (Long) get(4);
  }

  /** Setter for <code>error_status_metrics.size_value</code>. */
  public void setSizeValue(Long value) {
    set(5, value);
  }

  /** Getter for <code>error_status_metrics.size_value</code>. */
  public Long getSizeValue() {
    return (Long) get(5);
  }

  /** Setter for <code>error_status_metrics.status</code>. */
  public void setStatus(ErrorStatusMetricsStatus value) {
    set(6, value);
  }

  /** Getter for <code>error_status_metrics.status</code>. */
  public ErrorStatusMetricsStatus getStatus() {
    return (ErrorStatusMetricsStatus) get(6);
  }

  /** Setter for <code>error_status_metrics.org_id</code>. */
  public void setOrgId(Integer value) {
    set(7, value);
  }

  /** Getter for <code>error_status_metrics.org_id</code>. */
  public Integer getOrgId() {
    return (Integer) get(7);
  }

  /** Setter for <code>error_status_metrics.owner_id</code>. */
  public void setOwnerId(Integer value) {
    set(8, value);
  }

  /** Getter for <code>error_status_metrics.owner_id</code>. */
  public Integer getOwnerId() {
    return (Integer) get(8);
  }

  /** Setter for <code>error_status_metrics.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(9, value);
  }

  /** Getter for <code>error_status_metrics.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(9);
  }

  /** Setter for <code>error_status_metrics.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(10, value);
  }

  /** Getter for <code>error_status_metrics.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(10);
  }

  /** Setter for <code>error_status_metrics.origin_node_id</code>. */
  public void setOriginNodeId(Integer value) {
    set(11, value);
  }

  /** Getter for <code>error_status_metrics.origin_node_id</code>. */
  public Integer getOriginNodeId() {
    return (Integer) get(11);
  }

  /** Setter for <code>error_status_metrics.flow_node_id</code>. */
  public void setFlowNodeId(Integer value) {
    set(12, value);
  }

  /** Getter for <code>error_status_metrics.flow_node_id</code>. */
  public Integer getFlowNodeId() {
    return (Integer) get(12);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record2<Long, Integer> key() {
    return (Record2) super.key();
  }

  // -------------------------------------------------------------------------
  // Record13 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row13<
          Long,
          Integer,
          String,
          Long,
          Long,
          Long,
          ErrorStatusMetricsStatus,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer>
      fieldsRow() {
    return (Row13) super.fieldsRow();
  }

  @Override
  public Row13<
          Long,
          Integer,
          String,
          Long,
          Long,
          Long,
          ErrorStatusMetricsStatus,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer>
      valuesRow() {
    return (Row13) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.ID;
  }

  @Override
  public Field<Integer> field2() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.RESOURCE_ID;
  }

  @Override
  public Field<String> field3() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.RESOURCE_TYPE;
  }

  @Override
  public Field<Long> field4() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.ROW_COUNT;
  }

  @Override
  public Field<Long> field5() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.ERROR_COUNT;
  }

  @Override
  public Field<Long> field6() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.SIZE_VALUE;
  }

  @Override
  public Field<ErrorStatusMetricsStatus> field7() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.STATUS;
  }

  @Override
  public Field<Integer> field8() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.ORG_ID;
  }

  @Override
  public Field<Integer> field9() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.OWNER_ID;
  }

  @Override
  public Field<LocalDateTime> field10() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.UPDATED_AT;
  }

  @Override
  public Field<LocalDateTime> field11() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.CREATED_AT;
  }

  @Override
  public Field<Integer> field12() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.ORIGIN_NODE_ID;
  }

  @Override
  public Field<Integer> field13() {
    return ErrorStatusMetrics.ERROR_STATUS_METRICS.FLOW_NODE_ID;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public Integer component2() {
    return getResourceId();
  }

  @Override
  public String component3() {
    return getResourceType();
  }

  @Override
  public Long component4() {
    return getRowCount();
  }

  @Override
  public Long component5() {
    return getErrorCount();
  }

  @Override
  public Long component6() {
    return getSizeValue();
  }

  @Override
  public ErrorStatusMetricsStatus component7() {
    return getStatus();
  }

  @Override
  public Integer component8() {
    return getOrgId();
  }

  @Override
  public Integer component9() {
    return getOwnerId();
  }

  @Override
  public LocalDateTime component10() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime component11() {
    return getCreatedAt();
  }

  @Override
  public Integer component12() {
    return getOriginNodeId();
  }

  @Override
  public Integer component13() {
    return getFlowNodeId();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public Integer value2() {
    return getResourceId();
  }

  @Override
  public String value3() {
    return getResourceType();
  }

  @Override
  public Long value4() {
    return getRowCount();
  }

  @Override
  public Long value5() {
    return getErrorCount();
  }

  @Override
  public Long value6() {
    return getSizeValue();
  }

  @Override
  public ErrorStatusMetricsStatus value7() {
    return getStatus();
  }

  @Override
  public Integer value8() {
    return getOrgId();
  }

  @Override
  public Integer value9() {
    return getOwnerId();
  }

  @Override
  public LocalDateTime value10() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime value11() {
    return getCreatedAt();
  }

  @Override
  public Integer value12() {
    return getOriginNodeId();
  }

  @Override
  public Integer value13() {
    return getFlowNodeId();
  }

  @Override
  public ErrorStatusMetricsRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value2(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value3(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value4(Long value) {
    setRowCount(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value5(Long value) {
    setErrorCount(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value6(Long value) {
    setSizeValue(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value7(ErrorStatusMetricsStatus value) {
    setStatus(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value8(Integer value) {
    setOrgId(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value9(Integer value) {
    setOwnerId(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value10(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value11(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value12(Integer value) {
    setOriginNodeId(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord value13(Integer value) {
    setFlowNodeId(value);
    return this;
  }

  @Override
  public ErrorStatusMetricsRecord values(
      Long value1,
      Integer value2,
      String value3,
      Long value4,
      Long value5,
      Long value6,
      ErrorStatusMetricsStatus value7,
      Integer value8,
      Integer value9,
      LocalDateTime value10,
      LocalDateTime value11,
      Integer value12,
      Integer value13) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached ErrorStatusMetricsRecord */
  public ErrorStatusMetricsRecord() {
    super(ErrorStatusMetrics.ERROR_STATUS_METRICS);
  }

  /** Create a detached, initialised ErrorStatusMetricsRecord */
  public ErrorStatusMetricsRecord(
      Long id,
      Integer resourceId,
      String resourceType,
      Long rowCount,
      Long errorCount,
      Long sizeValue,
      ErrorStatusMetricsStatus status,
      Integer orgId,
      Integer ownerId,
      LocalDateTime updatedAt,
      LocalDateTime createdAt,
      Integer originNodeId,
      Integer flowNodeId) {
    super(ErrorStatusMetrics.ERROR_STATUS_METRICS);

    setId(id);
    setResourceId(resourceId);
    setResourceType(resourceType);
    setRowCount(rowCount);
    setErrorCount(errorCount);
    setSizeValue(sizeValue);
    setStatus(status);
    setOrgId(orgId);
    setOwnerId(ownerId);
    setUpdatedAt(updatedAt);
    setCreatedAt(createdAt);
    setOriginNodeId(originNodeId);
    setFlowNodeId(flowNodeId);
  }
}
