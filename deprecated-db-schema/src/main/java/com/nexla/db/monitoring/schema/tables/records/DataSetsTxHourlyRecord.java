/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.DataSetsTxHourly;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DataSetsTxHourlyRecord extends UpdatableRecordImpl<DataSetsTxHourlyRecord>
    implements Record16<
        Long,
        Integer,
        LocalDateTime,
        Long,
        Long,
        Long,
        LocalDateTime,
        LocalDateTime,
        Integer,
        Integer,
        Integer,
        Long,
        Integer,
        Integer,
        Integer,
        Integer> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>data_sets_tx_hourly.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>data_sets_tx_hourly.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>data_sets_tx_hourly.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>data_sets_tx_hourly.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(1);
  }

  /** Setter for <code>data_sets_tx_hourly.reporting_hour</code>. */
  public void setReportingHour(LocalDateTime value) {
    set(2, value);
  }

  /** Getter for <code>data_sets_tx_hourly.reporting_hour</code>. */
  public LocalDateTime getReportingHour() {
    return (LocalDateTime) get(2);
  }

  /** Setter for <code>data_sets_tx_hourly.row_count</code>. */
  public void setRowCount(Long value) {
    set(3, value);
  }

  /** Getter for <code>data_sets_tx_hourly.row_count</code>. */
  public Long getRowCount() {
    return (Long) get(3);
  }

  /** Setter for <code>data_sets_tx_hourly.data_volume_bytes</code>. */
  public void setDataVolumeBytes(Long value) {
    set(4, value);
  }

  /** Getter for <code>data_sets_tx_hourly.data_volume_bytes</code>. */
  public Long getDataVolumeBytes() {
    return (Long) get(4);
  }

  /** Setter for <code>data_sets_tx_hourly.error_count</code>. */
  public void setErrorCount(Long value) {
    set(5, value);
  }

  /** Getter for <code>data_sets_tx_hourly.error_count</code>. */
  public Long getErrorCount() {
    return (Long) get(5);
  }

  /** Setter for <code>data_sets_tx_hourly.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>data_sets_tx_hourly.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(6);
  }

  /** Setter for <code>data_sets_tx_hourly.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(7, value);
  }

  /** Getter for <code>data_sets_tx_hourly.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(7);
  }

  /** Setter for <code>data_sets_tx_hourly.org_id</code>. */
  public void setOrgId(Integer value) {
    set(8, value);
  }

  /** Getter for <code>data_sets_tx_hourly.org_id</code>. */
  public Integer getOrgId() {
    return (Integer) get(8);
  }

  /** Setter for <code>data_sets_tx_hourly.owner_id</code>. */
  public void setOwnerId(Integer value) {
    set(9, value);
  }

  /** Getter for <code>data_sets_tx_hourly.owner_id</code>. */
  public Integer getOwnerId() {
    return (Integer) get(9);
  }

  /** Setter for <code>data_sets_tx_hourly.data_set_id</code>. */
  public void setDataSetId(Integer value) {
    set(10, value);
  }

  /** Getter for <code>data_sets_tx_hourly.data_set_id</code>. */
  public Integer getDataSetId() {
    return (Integer) get(10);
  }

  /** Setter for <code>data_sets_tx_hourly.run_id</code>. */
  public void setRunId(Long value) {
    set(11, value);
  }

  /** Getter for <code>data_sets_tx_hourly.run_id</code>. */
  public Long getRunId() {
    return (Long) get(11);
  }

  /** Setter for <code>data_sets_tx_hourly.origin_node_id</code>. */
  public void setOriginNodeId(Integer value) {
    set(12, value);
  }

  /** Getter for <code>data_sets_tx_hourly.origin_node_id</code>. */
  public Integer getOriginNodeId() {
    return (Integer) get(12);
  }

  /** Setter for <code>data_sets_tx_hourly.flow_node_id</code>. */
  public void setFlowNodeId(Integer value) {
    set(13, value);
  }

  /** Getter for <code>data_sets_tx_hourly.flow_node_id</code>. */
  public Integer getFlowNodeId() {
    return (Integer) get(13);
  }

  /** Setter for <code>data_sets_tx_hourly.parent_node_id</code>. */
  public void setParentNodeId(Integer value) {
    set(14, value);
  }

  /** Getter for <code>data_sets_tx_hourly.parent_node_id</code>. */
  public Integer getParentNodeId() {
    return (Integer) get(14);
  }

  /** Setter for <code>data_sets_tx_hourly.data_source_id</code>. */
  public void setDataSourceId(Integer value) {
    set(15, value);
  }

  /** Getter for <code>data_sets_tx_hourly.data_source_id</code>. */
  public Integer getDataSourceId() {
    return (Integer) get(15);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record16 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row16<
          Long,
          Integer,
          LocalDateTime,
          Long,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer,
          Integer,
          Long,
          Integer,
          Integer,
          Integer,
          Integer>
      fieldsRow() {
    return (Row16) super.fieldsRow();
  }

  @Override
  public Row16<
          Long,
          Integer,
          LocalDateTime,
          Long,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer,
          Integer,
          Long,
          Integer,
          Integer,
          Integer,
          Integer>
      valuesRow() {
    return (Row16) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.ID;
  }

  @Override
  public Field<Integer> field2() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.RESOURCE_ID;
  }

  @Override
  public Field<LocalDateTime> field3() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.REPORTING_HOUR;
  }

  @Override
  public Field<Long> field4() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.ROW_COUNT;
  }

  @Override
  public Field<Long> field5() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.DATA_VOLUME_BYTES;
  }

  @Override
  public Field<Long> field6() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.ERROR_COUNT;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.UPDATED_AT;
  }

  @Override
  public Field<LocalDateTime> field8() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.CREATED_AT;
  }

  @Override
  public Field<Integer> field9() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.ORG_ID;
  }

  @Override
  public Field<Integer> field10() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.OWNER_ID;
  }

  @Override
  public Field<Integer> field11() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.DATA_SET_ID;
  }

  @Override
  public Field<Long> field12() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.RUN_ID;
  }

  @Override
  public Field<Integer> field13() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.ORIGIN_NODE_ID;
  }

  @Override
  public Field<Integer> field14() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.FLOW_NODE_ID;
  }

  @Override
  public Field<Integer> field15() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.PARENT_NODE_ID;
  }

  @Override
  public Field<Integer> field16() {
    return DataSetsTxHourly.DATA_SETS_TX_HOURLY.DATA_SOURCE_ID;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public Integer component2() {
    return getResourceId();
  }

  @Override
  public LocalDateTime component3() {
    return getReportingHour();
  }

  @Override
  public Long component4() {
    return getRowCount();
  }

  @Override
  public Long component5() {
    return getDataVolumeBytes();
  }

  @Override
  public Long component6() {
    return getErrorCount();
  }

  @Override
  public LocalDateTime component7() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime component8() {
    return getCreatedAt();
  }

  @Override
  public Integer component9() {
    return getOrgId();
  }

  @Override
  public Integer component10() {
    return getOwnerId();
  }

  @Override
  public Integer component11() {
    return getDataSetId();
  }

  @Override
  public Long component12() {
    return getRunId();
  }

  @Override
  public Integer component13() {
    return getOriginNodeId();
  }

  @Override
  public Integer component14() {
    return getFlowNodeId();
  }

  @Override
  public Integer component15() {
    return getParentNodeId();
  }

  @Override
  public Integer component16() {
    return getDataSourceId();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public Integer value2() {
    return getResourceId();
  }

  @Override
  public LocalDateTime value3() {
    return getReportingHour();
  }

  @Override
  public Long value4() {
    return getRowCount();
  }

  @Override
  public Long value5() {
    return getDataVolumeBytes();
  }

  @Override
  public Long value6() {
    return getErrorCount();
  }

  @Override
  public LocalDateTime value7() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime value8() {
    return getCreatedAt();
  }

  @Override
  public Integer value9() {
    return getOrgId();
  }

  @Override
  public Integer value10() {
    return getOwnerId();
  }

  @Override
  public Integer value11() {
    return getDataSetId();
  }

  @Override
  public Long value12() {
    return getRunId();
  }

  @Override
  public Integer value13() {
    return getOriginNodeId();
  }

  @Override
  public Integer value14() {
    return getFlowNodeId();
  }

  @Override
  public Integer value15() {
    return getParentNodeId();
  }

  @Override
  public Integer value16() {
    return getDataSourceId();
  }

  @Override
  public DataSetsTxHourlyRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value2(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value3(LocalDateTime value) {
    setReportingHour(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value4(Long value) {
    setRowCount(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value5(Long value) {
    setDataVolumeBytes(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value6(Long value) {
    setErrorCount(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value7(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value8(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value9(Integer value) {
    setOrgId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value10(Integer value) {
    setOwnerId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value11(Integer value) {
    setDataSetId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value12(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value13(Integer value) {
    setOriginNodeId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value14(Integer value) {
    setFlowNodeId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value15(Integer value) {
    setParentNodeId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord value16(Integer value) {
    setDataSourceId(value);
    return this;
  }

  @Override
  public DataSetsTxHourlyRecord values(
      Long value1,
      Integer value2,
      LocalDateTime value3,
      Long value4,
      Long value5,
      Long value6,
      LocalDateTime value7,
      LocalDateTime value8,
      Integer value9,
      Integer value10,
      Integer value11,
      Long value12,
      Integer value13,
      Integer value14,
      Integer value15,
      Integer value16) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    value14(value14);
    value15(value15);
    value16(value16);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached DataSetsTxHourlyRecord */
  public DataSetsTxHourlyRecord() {
    super(DataSetsTxHourly.DATA_SETS_TX_HOURLY);
  }

  /** Create a detached, initialised DataSetsTxHourlyRecord */
  public DataSetsTxHourlyRecord(
      Long id,
      Integer resourceId,
      LocalDateTime reportingHour,
      Long rowCount,
      Long dataVolumeBytes,
      Long errorCount,
      LocalDateTime updatedAt,
      LocalDateTime createdAt,
      Integer orgId,
      Integer ownerId,
      Integer dataSetId,
      Long runId,
      Integer originNodeId,
      Integer flowNodeId,
      Integer parentNodeId,
      Integer dataSourceId) {
    super(DataSetsTxHourly.DATA_SETS_TX_HOURLY);

    setId(id);
    setResourceId(resourceId);
    setReportingHour(reportingHour);
    setRowCount(rowCount);
    setDataVolumeBytes(dataVolumeBytes);
    setErrorCount(errorCount);
    setUpdatedAt(updatedAt);
    setCreatedAt(createdAt);
    setOrgId(orgId);
    setOwnerId(ownerId);
    setDataSetId(dataSetId);
    setRunId(runId);
    setOriginNodeId(originNodeId);
    setFlowNodeId(flowNodeId);
    setParentNodeId(parentNodeId);
    setDataSourceId(dataSourceId);
  }
}
