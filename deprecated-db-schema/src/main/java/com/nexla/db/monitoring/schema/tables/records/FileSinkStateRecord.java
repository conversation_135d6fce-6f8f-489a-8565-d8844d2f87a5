/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.FileSinkState;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class FileSinkStateRecord extends UpdatableRecordImpl<FileSinkStateRecord>
    implements Record16<
        Long,
        String,
        Long,
        Long,
        Integer,
        String,
        Integer,
        Integer,
        LocalDateTime,
        String,
        Long,
        Long,
        Byte,
        String,
        Integer,
        Integer> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>file_sink_state.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>file_sink_state.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>file_sink_state.name</code>. */
  public void setName(String value) {
    set(1, value);
  }

  /** Getter for <code>file_sink_state.name</code>. */
  public String getName() {
    return (String) get(1);
  }

  /** Setter for <code>file_sink_state.size_value</code>. */
  public void setSizeValue(Long value) {
    set(2, value);
  }

  /** Getter for <code>file_sink_state.size_value</code>. */
  public Long getSizeValue() {
    return (Long) get(2);
  }

  /** Setter for <code>file_sink_state.record_count</code>. */
  public void setRecordCount(Long value) {
    set(3, value);
  }

  /** Getter for <code>file_sink_state.record_count</code>. */
  public Long getRecordCount() {
    return (Long) get(3);
  }

  /** Setter for <code>file_sink_state.sink_id</code>. */
  public void setSinkId(Integer value) {
    set(4, value);
  }

  /** Getter for <code>file_sink_state.sink_id</code>. */
  public Integer getSinkId() {
    return (Integer) get(4);
  }

  /** Setter for <code>file_sink_state.error</code>. */
  public void setError(String value) {
    set(5, value);
  }

  /** Getter for <code>file_sink_state.error</code>. */
  public String getError() {
    return (String) get(5);
  }

  /** Setter for <code>file_sink_state.write_status</code>. */
  public void setWriteStatus(Integer value) {
    set(6, value);
  }

  /** Getter for <code>file_sink_state.write_status</code>. */
  public Integer getWriteStatus() {
    return (Integer) get(6);
  }

  /** Setter for <code>file_sink_state.data_set_id</code>. */
  public void setDataSetId(Integer value) {
    set(7, value);
  }

  /** Getter for <code>file_sink_state.data_set_id</code>. */
  public Integer getDataSetId() {
    return (Integer) get(7);
  }

  /** Setter for <code>file_sink_state.last_written</code>. */
  public void setLastWritten(LocalDateTime value) {
    set(8, value);
  }

  /** Getter for <code>file_sink_state.last_written</code>. */
  public LocalDateTime getLastWritten() {
    return (LocalDateTime) get(8);
  }

  /** Setter for <code>file_sink_state.error_message</code>. */
  public void setErrorMessage(String value) {
    set(9, value);
  }

  /** Getter for <code>file_sink_state.error_message</code>. */
  public String getErrorMessage() {
    return (String) get(9);
  }

  /** Setter for <code>file_sink_state.error_count</code>. */
  public void setErrorCount(Long value) {
    set(10, value);
  }

  /** Getter for <code>file_sink_state.error_count</code>. */
  public Long getErrorCount() {
    return (Long) get(10);
  }

  /** Setter for <code>file_sink_state.run_id</code>. */
  public void setRunId(Long value) {
    set(11, value);
  }

  /** Getter for <code>file_sink_state.run_id</code>. */
  public Long getRunId() {
    return (Long) get(11);
  }

  /** Setter for <code>file_sink_state.aggregated</code>. */
  public void setAggregated(Byte value) {
    set(12, value);
  }

  /** Getter for <code>file_sink_state.aggregated</code>. */
  public Byte getAggregated() {
    return (Byte) get(12);
  }

  /** Setter for <code>file_sink_state.metadata</code>. */
  public void setMetadata(String value) {
    set(13, value);
  }

  /** Getter for <code>file_sink_state.metadata</code>. */
  public String getMetadata() {
    return (String) get(13);
  }

  /** Setter for <code>file_sink_state.origin_node_id</code>. */
  public void setOriginNodeId(Integer value) {
    set(14, value);
  }

  /** Getter for <code>file_sink_state.origin_node_id</code>. */
  public Integer getOriginNodeId() {
    return (Integer) get(14);
  }

  /** Setter for <code>file_sink_state.flow_node_id</code>. */
  public void setFlowNodeId(Integer value) {
    set(15, value);
  }

  /** Getter for <code>file_sink_state.flow_node_id</code>. */
  public Integer getFlowNodeId() {
    return (Integer) get(15);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record16 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row16<
          Long,
          String,
          Long,
          Long,
          Integer,
          String,
          Integer,
          Integer,
          LocalDateTime,
          String,
          Long,
          Long,
          Byte,
          String,
          Integer,
          Integer>
      fieldsRow() {
    return (Row16) super.fieldsRow();
  }

  @Override
  public Row16<
          Long,
          String,
          Long,
          Long,
          Integer,
          String,
          Integer,
          Integer,
          LocalDateTime,
          String,
          Long,
          Long,
          Byte,
          String,
          Integer,
          Integer>
      valuesRow() {
    return (Row16) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return FileSinkState.FILE_SINK_STATE.ID;
  }

  @Override
  public Field<String> field2() {
    return FileSinkState.FILE_SINK_STATE.NAME;
  }

  @Override
  public Field<Long> field3() {
    return FileSinkState.FILE_SINK_STATE.SIZE_VALUE;
  }

  @Override
  public Field<Long> field4() {
    return FileSinkState.FILE_SINK_STATE.RECORD_COUNT;
  }

  @Override
  public Field<Integer> field5() {
    return FileSinkState.FILE_SINK_STATE.SINK_ID;
  }

  @Override
  public Field<String> field6() {
    return FileSinkState.FILE_SINK_STATE.ERROR;
  }

  @Override
  public Field<Integer> field7() {
    return FileSinkState.FILE_SINK_STATE.WRITE_STATUS;
  }

  @Override
  public Field<Integer> field8() {
    return FileSinkState.FILE_SINK_STATE.DATA_SET_ID;
  }

  @Override
  public Field<LocalDateTime> field9() {
    return FileSinkState.FILE_SINK_STATE.LAST_WRITTEN;
  }

  @Override
  public Field<String> field10() {
    return FileSinkState.FILE_SINK_STATE.ERROR_MESSAGE;
  }

  @Override
  public Field<Long> field11() {
    return FileSinkState.FILE_SINK_STATE.ERROR_COUNT;
  }

  @Override
  public Field<Long> field12() {
    return FileSinkState.FILE_SINK_STATE.RUN_ID;
  }

  @Override
  public Field<Byte> field13() {
    return FileSinkState.FILE_SINK_STATE.AGGREGATED;
  }

  @Override
  public Field<String> field14() {
    return FileSinkState.FILE_SINK_STATE.METADATA;
  }

  @Override
  public Field<Integer> field15() {
    return FileSinkState.FILE_SINK_STATE.ORIGIN_NODE_ID;
  }

  @Override
  public Field<Integer> field16() {
    return FileSinkState.FILE_SINK_STATE.FLOW_NODE_ID;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public String component2() {
    return getName();
  }

  @Override
  public Long component3() {
    return getSizeValue();
  }

  @Override
  public Long component4() {
    return getRecordCount();
  }

  @Override
  public Integer component5() {
    return getSinkId();
  }

  @Override
  public String component6() {
    return getError();
  }

  @Override
  public Integer component7() {
    return getWriteStatus();
  }

  @Override
  public Integer component8() {
    return getDataSetId();
  }

  @Override
  public LocalDateTime component9() {
    return getLastWritten();
  }

  @Override
  public String component10() {
    return getErrorMessage();
  }

  @Override
  public Long component11() {
    return getErrorCount();
  }

  @Override
  public Long component12() {
    return getRunId();
  }

  @Override
  public Byte component13() {
    return getAggregated();
  }

  @Override
  public String component14() {
    return getMetadata();
  }

  @Override
  public Integer component15() {
    return getOriginNodeId();
  }

  @Override
  public Integer component16() {
    return getFlowNodeId();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public String value2() {
    return getName();
  }

  @Override
  public Long value3() {
    return getSizeValue();
  }

  @Override
  public Long value4() {
    return getRecordCount();
  }

  @Override
  public Integer value5() {
    return getSinkId();
  }

  @Override
  public String value6() {
    return getError();
  }

  @Override
  public Integer value7() {
    return getWriteStatus();
  }

  @Override
  public Integer value8() {
    return getDataSetId();
  }

  @Override
  public LocalDateTime value9() {
    return getLastWritten();
  }

  @Override
  public String value10() {
    return getErrorMessage();
  }

  @Override
  public Long value11() {
    return getErrorCount();
  }

  @Override
  public Long value12() {
    return getRunId();
  }

  @Override
  public Byte value13() {
    return getAggregated();
  }

  @Override
  public String value14() {
    return getMetadata();
  }

  @Override
  public Integer value15() {
    return getOriginNodeId();
  }

  @Override
  public Integer value16() {
    return getFlowNodeId();
  }

  @Override
  public FileSinkStateRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value2(String value) {
    setName(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value3(Long value) {
    setSizeValue(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value4(Long value) {
    setRecordCount(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value5(Integer value) {
    setSinkId(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value6(String value) {
    setError(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value7(Integer value) {
    setWriteStatus(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value8(Integer value) {
    setDataSetId(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value9(LocalDateTime value) {
    setLastWritten(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value10(String value) {
    setErrorMessage(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value11(Long value) {
    setErrorCount(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value12(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value13(Byte value) {
    setAggregated(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value14(String value) {
    setMetadata(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value15(Integer value) {
    setOriginNodeId(value);
    return this;
  }

  @Override
  public FileSinkStateRecord value16(Integer value) {
    setFlowNodeId(value);
    return this;
  }

  @Override
  public FileSinkStateRecord values(
      Long value1,
      String value2,
      Long value3,
      Long value4,
      Integer value5,
      String value6,
      Integer value7,
      Integer value8,
      LocalDateTime value9,
      String value10,
      Long value11,
      Long value12,
      Byte value13,
      String value14,
      Integer value15,
      Integer value16) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    value14(value14);
    value15(value15);
    value16(value16);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached FileSinkStateRecord */
  public FileSinkStateRecord() {
    super(FileSinkState.FILE_SINK_STATE);
  }

  /** Create a detached, initialised FileSinkStateRecord */
  public FileSinkStateRecord(
      Long id,
      String name,
      Long sizeValue,
      Long recordCount,
      Integer sinkId,
      String error,
      Integer writeStatus,
      Integer dataSetId,
      LocalDateTime lastWritten,
      String errorMessage,
      Long errorCount,
      Long runId,
      Byte aggregated,
      String metadata,
      Integer originNodeId,
      Integer flowNodeId) {
    super(FileSinkState.FILE_SINK_STATE);

    setId(id);
    setName(name);
    setSizeValue(sizeValue);
    setRecordCount(recordCount);
    setSinkId(sinkId);
    setError(error);
    setWriteStatus(writeStatus);
    setDataSetId(dataSetId);
    setLastWritten(lastWritten);
    setErrorMessage(errorMessage);
    setErrorCount(errorCount);
    setRunId(runId);
    setAggregated(aggregated);
    setMetadata(metadata);
    setOriginNodeId(originNodeId);
    setFlowNodeId(flowNodeId);
  }
}
