/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Indexes;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.enums.DataMonitorNotificationsResourceType;
import com.nexla.db.monitoring.schema.tables.records.DataMonitorNotificationsRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row19;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DataMonitorNotifications extends TableImpl<DataMonitorNotificationsRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>data_monitor_notifications</code> */
  public static final DataMonitorNotifications DATA_MONITOR_NOTIFICATIONS =
      new DataMonitorNotifications();

  /** The class holding records for this type */
  @Override
  public Class<DataMonitorNotificationsRecord> getRecordType() {
    return DataMonitorNotificationsRecord.class;
  }

  /** The column <code>data_monitor_notifications.id</code>. */
  public final TableField<DataMonitorNotificationsRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>data_monitor_notifications.notification_setting_id</code>. */
  public final TableField<DataMonitorNotificationsRecord, Integer> NOTIFICATION_SETTING_ID =
      createField(
          DSL.name("notification_setting_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>data_monitor_notifications.data_volume_avg</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> DATA_VOLUME_AVG =
      createField(
          DSL.name("data_volume_avg"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.data_volume_stdev</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> DATA_VOLUME_STDEV =
      createField(
          DSL.name("data_volume_stdev"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.record_count_avg</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> RECORD_COUNT_AVG =
      createField(
          DSL.name("record_count_avg"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.record_count_stdev</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> RECORD_COUNT_STDEV =
      createField(
          DSL.name("record_count_stdev"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.data_volume_total</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> DATA_VOLUME_TOTAL =
      createField(
          DSL.name("data_volume_total"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.record_count_total</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> RECORD_COUNT_TOTAL =
      createField(
          DSL.name("record_count_total"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.resource_id</code>. */
  public final TableField<DataMonitorNotificationsRecord, Integer> RESOURCE_ID =
      createField(
          DSL.name("resource_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>data_monitor_notifications.resource_type</code>. */
  public final TableField<DataMonitorNotificationsRecord, DataMonitorNotificationsResourceType>
      RESOURCE_TYPE =
          createField(
              DSL.name("resource_type"),
              SQLDataType.VARCHAR(8)
                  .nullable(false)
                  .asEnumDataType(
                      com.nexla.db.monitoring.schema.enums.DataMonitorNotificationsResourceType
                          .class),
              this,
              "");

  /** The column <code>data_monitor_notifications.last_exection_time</code>. */
  public final TableField<DataMonitorNotificationsRecord, LocalDateTime> LAST_EXECTION_TIME =
      createField(
          DSL.name("last_exection_time"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_monitor_notifications.monitor_window</code>. */
  public final TableField<DataMonitorNotificationsRecord, Long> MONITOR_WINDOW =
      createField(
          DSL.name("monitor_window"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>data_monitor_notifications.hour_of_day</code>. */
  public final TableField<DataMonitorNotificationsRecord, Integer> HOUR_OF_DAY =
      createField(
          DSL.name("hour_of_day"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>data_monitor_notifications.monitor_date</code>. */
  public final TableField<DataMonitorNotificationsRecord, LocalDateTime> MONITOR_DATE =
      createField(
          DSL.name("monitor_date"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_monitor_notifications.updated_at</code>. */
  public final TableField<DataMonitorNotificationsRecord, LocalDateTime> UPDATED_AT =
      createField(
          DSL.name("updated_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_monitor_notifications.created_at</code>. */
  public final TableField<DataMonitorNotificationsRecord, LocalDateTime> CREATED_AT =
      createField(
          DSL.name("created_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_monitor_notifications.file_count_avg</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> FILE_COUNT_AVG =
      createField(
          DSL.name("file_count_avg"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.file_count_stdev</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> FILE_COUNT_STDEV =
      createField(
          DSL.name("file_count_stdev"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  /** The column <code>data_monitor_notifications.file_count_total</code>. */
  public final TableField<DataMonitorNotificationsRecord, Double> FILE_COUNT_TOTAL =
      createField(
          DSL.name("file_count_total"),
          SQLDataType.DOUBLE.defaultValue(DSL.inline("NULL", SQLDataType.DOUBLE)),
          this,
          "");

  private DataMonitorNotifications(Name alias, Table<DataMonitorNotificationsRecord> aliased) {
    this(alias, aliased, null);
  }

  private DataMonitorNotifications(
      Name alias, Table<DataMonitorNotificationsRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>data_monitor_notifications</code> table reference */
  public DataMonitorNotifications(String alias) {
    this(DSL.name(alias), DATA_MONITOR_NOTIFICATIONS);
  }

  /** Create an aliased <code>data_monitor_notifications</code> table reference */
  public DataMonitorNotifications(Name alias) {
    this(alias, DATA_MONITOR_NOTIFICATIONS);
  }

  /** Create a <code>data_monitor_notifications</code> table reference */
  public DataMonitorNotifications() {
    this(DSL.name("data_monitor_notifications"), null);
  }

  public <O extends Record> DataMonitorNotifications(
      Table<O> child, ForeignKey<O, DataMonitorNotificationsRecord> key) {
    super(child, key, DATA_MONITOR_NOTIFICATIONS);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public List<Index> getIndexes() {
    return Arrays.<Index>asList(
        Indexes.DATA_MONITOR_NOTIFICATIONS_DATA_MONITOR_NOTIFICATIONS_NOTIFICATION_SETTING_ID,
        Indexes.DATA_MONITOR_NOTIFICATIONS_DATA_MONITOR_NOTIFICATIONS_RESOURCE_ID,
        Indexes.DATA_MONITOR_NOTIFICATIONS_DATA_MONITOR_NOTIFICATIONS_RESOURCE_TYPE);
  }

  @Override
  public Identity<DataMonitorNotificationsRecord, Long> getIdentity() {
    return (Identity<DataMonitorNotificationsRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<DataMonitorNotificationsRecord> getPrimaryKey() {
    return Keys.KEY_DATA_MONITOR_NOTIFICATIONS_PRIMARY;
  }

  @Override
  public List<UniqueKey<DataMonitorNotificationsRecord>> getKeys() {
    return Arrays.<UniqueKey<DataMonitorNotificationsRecord>>asList(
        Keys.KEY_DATA_MONITOR_NOTIFICATIONS_PRIMARY);
  }

  @Override
  public DataMonitorNotifications as(String alias) {
    return new DataMonitorNotifications(DSL.name(alias), this);
  }

  @Override
  public DataMonitorNotifications as(Name alias) {
    return new DataMonitorNotifications(alias, this);
  }

  /** Rename this table */
  @Override
  public DataMonitorNotifications rename(String name) {
    return new DataMonitorNotifications(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public DataMonitorNotifications rename(Name name) {
    return new DataMonitorNotifications(name, null);
  }

  // -------------------------------------------------------------------------
  // Row19 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row19<
          Long,
          Integer,
          Double,
          Double,
          Double,
          Double,
          Double,
          Double,
          Integer,
          DataMonitorNotificationsResourceType,
          LocalDateTime,
          Long,
          Integer,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          Double,
          Double,
          Double>
      fieldsRow() {
    return (Row19) super.fieldsRow();
  }
}
