/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.enums.DataMonitorNotificationsResourceType;
import com.nexla.db.monitoring.schema.tables.DataMonitorNotifications;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record19;
import org.jooq.Row19;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DataMonitorNotificationsRecord
    extends UpdatableRecordImpl<DataMonitorNotificationsRecord>
    implements Record19<
        Long,
        Integer,
        Double,
        Double,
        Double,
        Double,
        Double,
        Double,
        Integer,
        DataMonitorNotificationsResourceType,
        LocalDateTime,
        Long,
        Integer,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime,
        Double,
        Double,
        Double> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>data_monitor_notifications.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>data_monitor_notifications.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>data_monitor_notifications.notification_setting_id</code>. */
  public void setNotificationSettingId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>data_monitor_notifications.notification_setting_id</code>. */
  public Integer getNotificationSettingId() {
    return (Integer) get(1);
  }

  /** Setter for <code>data_monitor_notifications.data_volume_avg</code>. */
  public void setDataVolumeAvg(Double value) {
    set(2, value);
  }

  /** Getter for <code>data_monitor_notifications.data_volume_avg</code>. */
  public Double getDataVolumeAvg() {
    return (Double) get(2);
  }

  /** Setter for <code>data_monitor_notifications.data_volume_stdev</code>. */
  public void setDataVolumeStdev(Double value) {
    set(3, value);
  }

  /** Getter for <code>data_monitor_notifications.data_volume_stdev</code>. */
  public Double getDataVolumeStdev() {
    return (Double) get(3);
  }

  /** Setter for <code>data_monitor_notifications.record_count_avg</code>. */
  public void setRecordCountAvg(Double value) {
    set(4, value);
  }

  /** Getter for <code>data_monitor_notifications.record_count_avg</code>. */
  public Double getRecordCountAvg() {
    return (Double) get(4);
  }

  /** Setter for <code>data_monitor_notifications.record_count_stdev</code>. */
  public void setRecordCountStdev(Double value) {
    set(5, value);
  }

  /** Getter for <code>data_monitor_notifications.record_count_stdev</code>. */
  public Double getRecordCountStdev() {
    return (Double) get(5);
  }

  /** Setter for <code>data_monitor_notifications.data_volume_total</code>. */
  public void setDataVolumeTotal(Double value) {
    set(6, value);
  }

  /** Getter for <code>data_monitor_notifications.data_volume_total</code>. */
  public Double getDataVolumeTotal() {
    return (Double) get(6);
  }

  /** Setter for <code>data_monitor_notifications.record_count_total</code>. */
  public void setRecordCountTotal(Double value) {
    set(7, value);
  }

  /** Getter for <code>data_monitor_notifications.record_count_total</code>. */
  public Double getRecordCountTotal() {
    return (Double) get(7);
  }

  /** Setter for <code>data_monitor_notifications.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(8, value);
  }

  /** Getter for <code>data_monitor_notifications.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(8);
  }

  /** Setter for <code>data_monitor_notifications.resource_type</code>. */
  public void setResourceType(DataMonitorNotificationsResourceType value) {
    set(9, value);
  }

  /** Getter for <code>data_monitor_notifications.resource_type</code>. */
  public DataMonitorNotificationsResourceType getResourceType() {
    return (DataMonitorNotificationsResourceType) get(9);
  }

  /** Setter for <code>data_monitor_notifications.last_exection_time</code>. */
  public void setLastExectionTime(LocalDateTime value) {
    set(10, value);
  }

  /** Getter for <code>data_monitor_notifications.last_exection_time</code>. */
  public LocalDateTime getLastExectionTime() {
    return (LocalDateTime) get(10);
  }

  /** Setter for <code>data_monitor_notifications.monitor_window</code>. */
  public void setMonitorWindow(Long value) {
    set(11, value);
  }

  /** Getter for <code>data_monitor_notifications.monitor_window</code>. */
  public Long getMonitorWindow() {
    return (Long) get(11);
  }

  /** Setter for <code>data_monitor_notifications.hour_of_day</code>. */
  public void setHourOfDay(Integer value) {
    set(12, value);
  }

  /** Getter for <code>data_monitor_notifications.hour_of_day</code>. */
  public Integer getHourOfDay() {
    return (Integer) get(12);
  }

  /** Setter for <code>data_monitor_notifications.monitor_date</code>. */
  public void setMonitorDate(LocalDateTime value) {
    set(13, value);
  }

  /** Getter for <code>data_monitor_notifications.monitor_date</code>. */
  public LocalDateTime getMonitorDate() {
    return (LocalDateTime) get(13);
  }

  /** Setter for <code>data_monitor_notifications.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(14, value);
  }

  /** Getter for <code>data_monitor_notifications.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(14);
  }

  /** Setter for <code>data_monitor_notifications.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(15, value);
  }

  /** Getter for <code>data_monitor_notifications.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(15);
  }

  /** Setter for <code>data_monitor_notifications.file_count_avg</code>. */
  public void setFileCountAvg(Double value) {
    set(16, value);
  }

  /** Getter for <code>data_monitor_notifications.file_count_avg</code>. */
  public Double getFileCountAvg() {
    return (Double) get(16);
  }

  /** Setter for <code>data_monitor_notifications.file_count_stdev</code>. */
  public void setFileCountStdev(Double value) {
    set(17, value);
  }

  /** Getter for <code>data_monitor_notifications.file_count_stdev</code>. */
  public Double getFileCountStdev() {
    return (Double) get(17);
  }

  /** Setter for <code>data_monitor_notifications.file_count_total</code>. */
  public void setFileCountTotal(Double value) {
    set(18, value);
  }

  /** Getter for <code>data_monitor_notifications.file_count_total</code>. */
  public Double getFileCountTotal() {
    return (Double) get(18);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record19 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row19<
          Long,
          Integer,
          Double,
          Double,
          Double,
          Double,
          Double,
          Double,
          Integer,
          DataMonitorNotificationsResourceType,
          LocalDateTime,
          Long,
          Integer,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          Double,
          Double,
          Double>
      fieldsRow() {
    return (Row19) super.fieldsRow();
  }

  @Override
  public Row19<
          Long,
          Integer,
          Double,
          Double,
          Double,
          Double,
          Double,
          Double,
          Integer,
          DataMonitorNotificationsResourceType,
          LocalDateTime,
          Long,
          Integer,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          Double,
          Double,
          Double>
      valuesRow() {
    return (Row19) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.ID;
  }

  @Override
  public Field<Integer> field2() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.NOTIFICATION_SETTING_ID;
  }

  @Override
  public Field<Double> field3() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.DATA_VOLUME_AVG;
  }

  @Override
  public Field<Double> field4() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.DATA_VOLUME_STDEV;
  }

  @Override
  public Field<Double> field5() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.RECORD_COUNT_AVG;
  }

  @Override
  public Field<Double> field6() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.RECORD_COUNT_STDEV;
  }

  @Override
  public Field<Double> field7() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.DATA_VOLUME_TOTAL;
  }

  @Override
  public Field<Double> field8() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.RECORD_COUNT_TOTAL;
  }

  @Override
  public Field<Integer> field9() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.RESOURCE_ID;
  }

  @Override
  public Field<DataMonitorNotificationsResourceType> field10() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.RESOURCE_TYPE;
  }

  @Override
  public Field<LocalDateTime> field11() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.LAST_EXECTION_TIME;
  }

  @Override
  public Field<Long> field12() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.MONITOR_WINDOW;
  }

  @Override
  public Field<Integer> field13() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.HOUR_OF_DAY;
  }

  @Override
  public Field<LocalDateTime> field14() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.MONITOR_DATE;
  }

  @Override
  public Field<LocalDateTime> field15() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.UPDATED_AT;
  }

  @Override
  public Field<LocalDateTime> field16() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.CREATED_AT;
  }

  @Override
  public Field<Double> field17() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.FILE_COUNT_AVG;
  }

  @Override
  public Field<Double> field18() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.FILE_COUNT_STDEV;
  }

  @Override
  public Field<Double> field19() {
    return DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS.FILE_COUNT_TOTAL;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public Integer component2() {
    return getNotificationSettingId();
  }

  @Override
  public Double component3() {
    return getDataVolumeAvg();
  }

  @Override
  public Double component4() {
    return getDataVolumeStdev();
  }

  @Override
  public Double component5() {
    return getRecordCountAvg();
  }

  @Override
  public Double component6() {
    return getRecordCountStdev();
  }

  @Override
  public Double component7() {
    return getDataVolumeTotal();
  }

  @Override
  public Double component8() {
    return getRecordCountTotal();
  }

  @Override
  public Integer component9() {
    return getResourceId();
  }

  @Override
  public DataMonitorNotificationsResourceType component10() {
    return getResourceType();
  }

  @Override
  public LocalDateTime component11() {
    return getLastExectionTime();
  }

  @Override
  public Long component12() {
    return getMonitorWindow();
  }

  @Override
  public Integer component13() {
    return getHourOfDay();
  }

  @Override
  public LocalDateTime component14() {
    return getMonitorDate();
  }

  @Override
  public LocalDateTime component15() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime component16() {
    return getCreatedAt();
  }

  @Override
  public Double component17() {
    return getFileCountAvg();
  }

  @Override
  public Double component18() {
    return getFileCountStdev();
  }

  @Override
  public Double component19() {
    return getFileCountTotal();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public Integer value2() {
    return getNotificationSettingId();
  }

  @Override
  public Double value3() {
    return getDataVolumeAvg();
  }

  @Override
  public Double value4() {
    return getDataVolumeStdev();
  }

  @Override
  public Double value5() {
    return getRecordCountAvg();
  }

  @Override
  public Double value6() {
    return getRecordCountStdev();
  }

  @Override
  public Double value7() {
    return getDataVolumeTotal();
  }

  @Override
  public Double value8() {
    return getRecordCountTotal();
  }

  @Override
  public Integer value9() {
    return getResourceId();
  }

  @Override
  public DataMonitorNotificationsResourceType value10() {
    return getResourceType();
  }

  @Override
  public LocalDateTime value11() {
    return getLastExectionTime();
  }

  @Override
  public Long value12() {
    return getMonitorWindow();
  }

  @Override
  public Integer value13() {
    return getHourOfDay();
  }

  @Override
  public LocalDateTime value14() {
    return getMonitorDate();
  }

  @Override
  public LocalDateTime value15() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime value16() {
    return getCreatedAt();
  }

  @Override
  public Double value17() {
    return getFileCountAvg();
  }

  @Override
  public Double value18() {
    return getFileCountStdev();
  }

  @Override
  public Double value19() {
    return getFileCountTotal();
  }

  @Override
  public DataMonitorNotificationsRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value2(Integer value) {
    setNotificationSettingId(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value3(Double value) {
    setDataVolumeAvg(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value4(Double value) {
    setDataVolumeStdev(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value5(Double value) {
    setRecordCountAvg(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value6(Double value) {
    setRecordCountStdev(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value7(Double value) {
    setDataVolumeTotal(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value8(Double value) {
    setRecordCountTotal(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value9(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value10(DataMonitorNotificationsResourceType value) {
    setResourceType(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value11(LocalDateTime value) {
    setLastExectionTime(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value12(Long value) {
    setMonitorWindow(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value13(Integer value) {
    setHourOfDay(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value14(LocalDateTime value) {
    setMonitorDate(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value15(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value16(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value17(Double value) {
    setFileCountAvg(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value18(Double value) {
    setFileCountStdev(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord value19(Double value) {
    setFileCountTotal(value);
    return this;
  }

  @Override
  public DataMonitorNotificationsRecord values(
      Long value1,
      Integer value2,
      Double value3,
      Double value4,
      Double value5,
      Double value6,
      Double value7,
      Double value8,
      Integer value9,
      DataMonitorNotificationsResourceType value10,
      LocalDateTime value11,
      Long value12,
      Integer value13,
      LocalDateTime value14,
      LocalDateTime value15,
      LocalDateTime value16,
      Double value17,
      Double value18,
      Double value19) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    value14(value14);
    value15(value15);
    value16(value16);
    value17(value17);
    value18(value18);
    value19(value19);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached DataMonitorNotificationsRecord */
  public DataMonitorNotificationsRecord() {
    super(DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS);
  }

  /** Create a detached, initialised DataMonitorNotificationsRecord */
  public DataMonitorNotificationsRecord(
      Long id,
      Integer notificationSettingId,
      Double dataVolumeAvg,
      Double dataVolumeStdev,
      Double recordCountAvg,
      Double recordCountStdev,
      Double dataVolumeTotal,
      Double recordCountTotal,
      Integer resourceId,
      DataMonitorNotificationsResourceType resourceType,
      LocalDateTime lastExectionTime,
      Long monitorWindow,
      Integer hourOfDay,
      LocalDateTime monitorDate,
      LocalDateTime updatedAt,
      LocalDateTime createdAt,
      Double fileCountAvg,
      Double fileCountStdev,
      Double fileCountTotal) {
    super(DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS);

    setId(id);
    setNotificationSettingId(notificationSettingId);
    setDataVolumeAvg(dataVolumeAvg);
    setDataVolumeStdev(dataVolumeStdev);
    setRecordCountAvg(recordCountAvg);
    setRecordCountStdev(recordCountStdev);
    setDataVolumeTotal(dataVolumeTotal);
    setRecordCountTotal(recordCountTotal);
    setResourceId(resourceId);
    setResourceType(resourceType);
    setLastExectionTime(lastExectionTime);
    setMonitorWindow(monitorWindow);
    setHourOfDay(hourOfDay);
    setMonitorDate(monitorDate);
    setUpdatedAt(updatedAt);
    setCreatedAt(createdAt);
    setFileCountAvg(fileCountAvg);
    setFileCountStdev(fileCountStdev);
    setFileCountTotal(fileCountTotal);
  }
}
