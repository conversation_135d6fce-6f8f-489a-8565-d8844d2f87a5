/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.enums.ErrorStatusMetricsStatus;
import com.nexla.db.monitoring.schema.tables.records.ErrorStatusMetricsRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row13;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class ErrorStatusMetrics extends TableImpl<ErrorStatusMetricsRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>error_status_metrics</code> */
  public static final ErrorStatusMetrics ERROR_STATUS_METRICS = new ErrorStatusMetrics();

  /** The class holding records for this type */
  @Override
  public Class<ErrorStatusMetricsRecord> getRecordType() {
    return ErrorStatusMetricsRecord.class;
  }

  /** The column <code>error_status_metrics.id</code>. */
  public final TableField<ErrorStatusMetricsRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>error_status_metrics.resource_id</code>. */
  public final TableField<ErrorStatusMetricsRecord, Integer> RESOURCE_ID =
      createField(
          DSL.name("resource_id"),
          SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>error_status_metrics.resource_type</code>. */
  public final TableField<ErrorStatusMetricsRecord, String> RESOURCE_TYPE =
      createField(
          DSL.name("resource_type"),
          SQLDataType.VARCHAR(10).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)),
          this,
          "");

  /** The column <code>error_status_metrics.row_count</code>. */
  public final TableField<ErrorStatusMetricsRecord, Long> ROW_COUNT =
      createField(
          DSL.name("row_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>error_status_metrics.error_count</code>. */
  public final TableField<ErrorStatusMetricsRecord, Long> ERROR_COUNT =
      createField(
          DSL.name("error_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>error_status_metrics.size_value</code>. */
  public final TableField<ErrorStatusMetricsRecord, Long> SIZE_VALUE =
      createField(
          DSL.name("size_value"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>error_status_metrics.status</code>. */
  public final TableField<ErrorStatusMetricsRecord, ErrorStatusMetricsStatus> STATUS =
      createField(
          DSL.name("status"),
          SQLDataType.VARCHAR(7)
              .defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR))
              .asEnumDataType(com.nexla.db.monitoring.schema.enums.ErrorStatusMetricsStatus.class),
          this,
          "");

  /** The column <code>error_status_metrics.org_id</code>. */
  public final TableField<ErrorStatusMetricsRecord, Integer> ORG_ID =
      createField(
          DSL.name("org_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>error_status_metrics.owner_id</code>. */
  public final TableField<ErrorStatusMetricsRecord, Integer> OWNER_ID =
      createField(
          DSL.name("owner_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>error_status_metrics.updated_at</code>. */
  public final TableField<ErrorStatusMetricsRecord, LocalDateTime> UPDATED_AT =
      createField(
          DSL.name("updated_at"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("current_timestamp(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>error_status_metrics.created_at</code>. */
  public final TableField<ErrorStatusMetricsRecord, LocalDateTime> CREATED_AT =
      createField(
          DSL.name("created_at"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("current_timestamp(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>error_status_metrics.origin_node_id</code>. */
  public final TableField<ErrorStatusMetricsRecord, Integer> ORIGIN_NODE_ID =
      createField(DSL.name("origin_node_id"), SQLDataType.INTEGER, this, "");

  /** The column <code>error_status_metrics.flow_node_id</code>. */
  public final TableField<ErrorStatusMetricsRecord, Integer> FLOW_NODE_ID =
      createField(DSL.name("flow_node_id"), SQLDataType.INTEGER, this, "");

  private ErrorStatusMetrics(Name alias, Table<ErrorStatusMetricsRecord> aliased) {
    this(alias, aliased, null);
  }

  private ErrorStatusMetrics(
      Name alias, Table<ErrorStatusMetricsRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>error_status_metrics</code> table reference */
  public ErrorStatusMetrics(String alias) {
    this(DSL.name(alias), ERROR_STATUS_METRICS);
  }

  /** Create an aliased <code>error_status_metrics</code> table reference */
  public ErrorStatusMetrics(Name alias) {
    this(alias, ERROR_STATUS_METRICS);
  }

  /** Create a <code>error_status_metrics</code> table reference */
  public ErrorStatusMetrics() {
    this(DSL.name("error_status_metrics"), null);
  }

  public <O extends Record> ErrorStatusMetrics(
      Table<O> child, ForeignKey<O, ErrorStatusMetricsRecord> key) {
    super(child, key, ERROR_STATUS_METRICS);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public Identity<ErrorStatusMetricsRecord, Long> getIdentity() {
    return (Identity<ErrorStatusMetricsRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<ErrorStatusMetricsRecord> getPrimaryKey() {
    return Keys.KEY_ERROR_STATUS_METRICS_PRIMARY;
  }

  @Override
  public List<UniqueKey<ErrorStatusMetricsRecord>> getKeys() {
    return Arrays.<UniqueKey<ErrorStatusMetricsRecord>>asList(
        Keys.KEY_ERROR_STATUS_METRICS_PRIMARY);
  }

  @Override
  public ErrorStatusMetrics as(String alias) {
    return new ErrorStatusMetrics(DSL.name(alias), this);
  }

  @Override
  public ErrorStatusMetrics as(Name alias) {
    return new ErrorStatusMetrics(alias, this);
  }

  /** Rename this table */
  @Override
  public ErrorStatusMetrics rename(String name) {
    return new ErrorStatusMetrics(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public ErrorStatusMetrics rename(Name name) {
    return new ErrorStatusMetrics(name, null);
  }

  // -------------------------------------------------------------------------
  // Row13 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row13<
          Long,
          Integer,
          String,
          Long,
          Long,
          Long,
          ErrorStatusMetricsStatus,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer>
      fieldsRow() {
    return (Row13) super.fieldsRow();
  }
}
