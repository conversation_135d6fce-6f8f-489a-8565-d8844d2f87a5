/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Indexes;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.tables.records.CustomFlowHourlyRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row12;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class CustomFlowHourly extends TableImpl<CustomFlowHourlyRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>custom_flow_hourly</code> */
  public static final CustomFlowHourly CUSTOM_FLOW_HOURLY = new CustomFlowHourly();

  /** The class holding records for this type */
  @Override
  public Class<CustomFlowHourlyRecord> getRecordType() {
    return CustomFlowHourlyRecord.class;
  }

  /** The column <code>custom_flow_hourly.id</code>. */
  public final TableField<CustomFlowHourlyRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>custom_flow_hourly.resource_id</code>. */
  public final TableField<CustomFlowHourlyRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>custom_flow_hourly.run_id</code>. */
  public final TableField<CustomFlowHourlyRecord, Long> RUN_ID =
      createField(
          DSL.name("run_id"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>custom_flow_hourly.row_count</code>. */
  public final TableField<CustomFlowHourlyRecord, Long> ROW_COUNT =
      createField(
          DSL.name("row_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>custom_flow_hourly.error_count</code>. */
  public final TableField<CustomFlowHourlyRecord, Long> ERROR_COUNT =
      createField(
          DSL.name("error_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>custom_flow_hourly.pipeline_count</code>. */
  public final TableField<CustomFlowHourlyRecord, Long> PIPELINE_COUNT =
      createField(
          DSL.name("pipeline_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>custom_flow_hourly.size_value</code>. */
  public final TableField<CustomFlowHourlyRecord, Long> SIZE_VALUE =
      createField(
          DSL.name("size_value"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>custom_flow_hourly.org_id</code>. */
  public final TableField<CustomFlowHourlyRecord, Integer> ORG_ID =
      createField(
          DSL.name("org_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>custom_flow_hourly.owner_id</code>. */
  public final TableField<CustomFlowHourlyRecord, Integer> OWNER_ID =
      createField(
          DSL.name("owner_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>custom_flow_hourly.reporting_hour</code>. */
  public final TableField<CustomFlowHourlyRecord, LocalDateTime> REPORTING_HOUR =
      createField(
          DSL.name("reporting_hour"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("current_timestamp(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>custom_flow_hourly.updated_at</code>. */
  public final TableField<CustomFlowHourlyRecord, LocalDateTime> UPDATED_AT =
      createField(DSL.name("updated_at"), SQLDataType.LOCALDATETIME(3), this, "");

  /** The column <code>custom_flow_hourly.created_at</code>. */
  public final TableField<CustomFlowHourlyRecord, LocalDateTime> CREATED_AT =
      createField(
          DSL.name("created_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  private CustomFlowHourly(Name alias, Table<CustomFlowHourlyRecord> aliased) {
    this(alias, aliased, null);
  }

  private CustomFlowHourly(
      Name alias, Table<CustomFlowHourlyRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>custom_flow_hourly</code> table reference */
  public CustomFlowHourly(String alias) {
    this(DSL.name(alias), CUSTOM_FLOW_HOURLY);
  }

  /** Create an aliased <code>custom_flow_hourly</code> table reference */
  public CustomFlowHourly(Name alias) {
    this(alias, CUSTOM_FLOW_HOURLY);
  }

  /** Create a <code>custom_flow_hourly</code> table reference */
  public CustomFlowHourly() {
    this(DSL.name("custom_flow_hourly"), null);
  }

  public <O extends Record> CustomFlowHourly(
      Table<O> child, ForeignKey<O, CustomFlowHourlyRecord> key) {
    super(child, key, CUSTOM_FLOW_HOURLY);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public List<Index> getIndexes() {
    return Arrays.<Index>asList(
        Indexes.CUSTOM_FLOW_HOURLY_CUSTOM_FLOW_STATE_DAILY_DATE,
        Indexes.CUSTOM_FLOW_HOURLY_CUSTOM_FLOW_STATE_DAILY_ORG_ID,
        Indexes.CUSTOM_FLOW_HOURLY_CUSTOM_FLOW_STATE_DAILY_OWNER_ID);
  }

  @Override
  public Identity<CustomFlowHourlyRecord, Long> getIdentity() {
    return (Identity<CustomFlowHourlyRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<CustomFlowHourlyRecord> getPrimaryKey() {
    return Keys.KEY_CUSTOM_FLOW_HOURLY_PRIMARY;
  }

  @Override
  public List<UniqueKey<CustomFlowHourlyRecord>> getKeys() {
    return Arrays.<UniqueKey<CustomFlowHourlyRecord>>asList(
        Keys.KEY_CUSTOM_FLOW_HOURLY_PRIMARY, Keys.KEY_CUSTOM_FLOW_HOURLY_DATA_SOURCES_HOURLY_ID);
  }

  @Override
  public CustomFlowHourly as(String alias) {
    return new CustomFlowHourly(DSL.name(alias), this);
  }

  @Override
  public CustomFlowHourly as(Name alias) {
    return new CustomFlowHourly(alias, this);
  }

  /** Rename this table */
  @Override
  public CustomFlowHourly rename(String name) {
    return new CustomFlowHourly(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public CustomFlowHourly rename(Name name) {
    return new CustomFlowHourly(name, null);
  }

  // -------------------------------------------------------------------------
  // Row12 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row12<
          Long,
          Integer,
          Long,
          Long,
          Long,
          Long,
          Long,
          Integer,
          Integer,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      fieldsRow() {
    return (Row12) super.fieldsRow();
  }
}
