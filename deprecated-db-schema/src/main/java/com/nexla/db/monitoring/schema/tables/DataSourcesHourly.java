/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Indexes;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.tables.records.DataSourcesHourlyRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DataSourcesHourly extends TableImpl<DataSourcesHourlyRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>data_sources_hourly</code> */
  public static final DataSourcesHourly DATA_SOURCES_HOURLY = new DataSourcesHourly();

  /** The class holding records for this type */
  @Override
  public Class<DataSourcesHourlyRecord> getRecordType() {
    return DataSourcesHourlyRecord.class;
  }

  /** The column <code>data_sources_hourly.id</code>. */
  public final TableField<DataSourcesHourlyRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>data_sources_hourly.resource_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>data_sources_hourly.reporting_hour</code>. */
  public final TableField<DataSourcesHourlyRecord, LocalDateTime> REPORTING_HOUR =
      createField(
          DSL.name("reporting_hour"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("current_timestamp(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_sources_hourly.row_count</code>. */
  public final TableField<DataSourcesHourlyRecord, Long> ROW_COUNT =
      createField(
          DSL.name("row_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>data_sources_hourly.data_volume_bytes</code>. */
  public final TableField<DataSourcesHourlyRecord, Long> DATA_VOLUME_BYTES =
      createField(
          DSL.name("data_volume_bytes"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>data_sources_hourly.updated_at</code>. */
  public final TableField<DataSourcesHourlyRecord, LocalDateTime> UPDATED_AT =
      createField(
          DSL.name("updated_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_sources_hourly.created_at</code>. */
  public final TableField<DataSourcesHourlyRecord, LocalDateTime> CREATED_AT =
      createField(
          DSL.name("created_at"),
          SQLDataType.LOCALDATETIME(3).defaultValue(DSL.inline("NULL", SQLDataType.LOCALDATETIME)),
          this,
          "");

  /** The column <code>data_sources_hourly.org_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Integer> ORG_ID =
      createField(
          DSL.name("org_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>data_sources_hourly.owner_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Integer> OWNER_ID =
      createField(
          DSL.name("owner_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>data_sources_hourly.data_set_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Integer> DATA_SET_ID =
      createField(
          DSL.name("data_set_id"),
          SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)),
          this,
          "");

  /** The column <code>data_sources_hourly.error_count</code>. */
  public final TableField<DataSourcesHourlyRecord, Long> ERROR_COUNT =
      createField(
          DSL.name("error_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>data_sources_hourly.name_count</code>. */
  public final TableField<DataSourcesHourlyRecord, Long> NAME_COUNT =
      createField(
          DSL.name("name_count"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("0", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>data_sources_hourly.run_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Long> RUN_ID =
      createField(
          DSL.name("run_id"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>data_sources_hourly.origin_node_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Integer> ORIGIN_NODE_ID =
      createField(DSL.name("origin_node_id"), SQLDataType.INTEGER, this, "");

  /** The column <code>data_sources_hourly.flow_node_id</code>. */
  public final TableField<DataSourcesHourlyRecord, Integer> FLOW_NODE_ID =
      createField(DSL.name("flow_node_id"), SQLDataType.INTEGER, this, "");

  private DataSourcesHourly(Name alias, Table<DataSourcesHourlyRecord> aliased) {
    this(alias, aliased, null);
  }

  private DataSourcesHourly(
      Name alias, Table<DataSourcesHourlyRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>data_sources_hourly</code> table reference */
  public DataSourcesHourly(String alias) {
    this(DSL.name(alias), DATA_SOURCES_HOURLY);
  }

  /** Create an aliased <code>data_sources_hourly</code> table reference */
  public DataSourcesHourly(Name alias) {
    this(alias, DATA_SOURCES_HOURLY);
  }

  /** Create a <code>data_sources_hourly</code> table reference */
  public DataSourcesHourly() {
    this(DSL.name("data_sources_hourly"), null);
  }

  public <O extends Record> DataSourcesHourly(
      Table<O> child, ForeignKey<O, DataSourcesHourlyRecord> key) {
    super(child, key, DATA_SOURCES_HOURLY);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public List<Index> getIndexes() {
    return Arrays.<Index>asList(
        Indexes.DATA_SOURCES_HOURLY_DATA_SET_ID,
        Indexes.DATA_SOURCES_HOURLY_DATA_SET_ID_REPORTING_HOUR,
        Indexes.DATA_SOURCES_HOURLY_DATA_SOURCES_HOURLY_DATE,
        Indexes.DATA_SOURCES_HOURLY_DATA_SOURCES_HOURLY_ORG_ID,
        Indexes.DATA_SOURCES_HOURLY_DATA_SOURCES_HOURLY_OWNER_ID,
        Indexes.DATA_SOURCES_HOURLY_RESOURCE_ID_REPORTING_HOUR,
        Indexes.DATA_SOURCES_HOURLY_RUN_ID);
  }

  @Override
  public Identity<DataSourcesHourlyRecord, Long> getIdentity() {
    return (Identity<DataSourcesHourlyRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<DataSourcesHourlyRecord> getPrimaryKey() {
    return Keys.KEY_DATA_SOURCES_HOURLY_PRIMARY;
  }

  @Override
  public List<UniqueKey<DataSourcesHourlyRecord>> getKeys() {
    return Arrays.<UniqueKey<DataSourcesHourlyRecord>>asList(
        Keys.KEY_DATA_SOURCES_HOURLY_PRIMARY, Keys.KEY_DATA_SOURCES_HOURLY_DATA_SOURCES_HOURLY_ID);
  }

  @Override
  public DataSourcesHourly as(String alias) {
    return new DataSourcesHourly(DSL.name(alias), this);
  }

  @Override
  public DataSourcesHourly as(Name alias) {
    return new DataSourcesHourly(alias, this);
  }

  /** Rename this table */
  @Override
  public DataSourcesHourly rename(String name) {
    return new DataSourcesHourly(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public DataSourcesHourly rename(Name name) {
    return new DataSourcesHourly(name, null);
  }

  // -------------------------------------------------------------------------
  // Row15 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row15<
          Long,
          Integer,
          LocalDateTime,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer,
          Integer,
          Long,
          Long,
          Long,
          Integer,
          Integer>
      fieldsRow() {
    return (Row15) super.fieldsRow();
  }
}
