/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.FlowMetricsDaily;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class FlowMetricsDailyRecord extends UpdatableRecordImpl<FlowMetricsDailyRecord>
    implements Record11<
        Long,
        Integer,
        Integer,
        Integer,
        Long,
        Long,
        Long,
        Long,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime> {

  private static final long serialVersionUID = -1605452422;

  /** Setter for <code>flow_metrics_daily.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>flow_metrics_daily.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>flow_metrics_daily.origin_node_id</code>. */
  public void setOriginNodeId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>flow_metrics_daily.origin_node_id</code>. */
  public Integer getOriginNodeId() {
    return (Integer) get(1);
  }

  /** Setter for <code>flow_metrics_daily.org_id</code>. */
  public void setOrgId(Integer value) {
    set(2, value);
  }

  /** Getter for <code>flow_metrics_daily.org_id</code>. */
  public Integer getOrgId() {
    return (Integer) get(2);
  }

  /** Setter for <code>flow_metrics_daily.owner_id</code>. */
  public void setOwnerId(Integer value) {
    set(3, value);
  }

  /** Getter for <code>flow_metrics_daily.owner_id</code>. */
  public Integer getOwnerId() {
    return (Integer) get(3);
  }

  /** Setter for <code>flow_metrics_daily.run_id</code>. */
  public void setRunId(Long value) {
    set(4, value);
  }

  /** Getter for <code>flow_metrics_daily.run_id</code>. */
  public Long getRunId() {
    return (Long) get(4);
  }

  /** Setter for <code>flow_metrics_daily.row_count</code>. */
  public void setRowCount(Long value) {
    set(5, value);
  }

  /** Getter for <code>flow_metrics_daily.row_count</code>. */
  public Long getRowCount() {
    return (Long) get(5);
  }

  /** Setter for <code>flow_metrics_daily.error_count</code>. */
  public void setErrorCount(Long value) {
    set(6, value);
  }

  /** Getter for <code>flow_metrics_daily.error_count</code>. */
  public Long getErrorCount() {
    return (Long) get(6);
  }

  /** Setter for <code>flow_metrics_daily.size_value</code>. */
  public void setSizeValue(Long value) {
    set(7, value);
  }

  /** Getter for <code>flow_metrics_daily.size_value</code>. */
  public Long getSizeValue() {
    return (Long) get(7);
  }

  /** Setter for <code>flow_metrics_daily.reporting_date</code>. */
  public void setReportingDate(LocalDateTime value) {
    set(8, value);
  }

  /** Getter for <code>flow_metrics_daily.reporting_date</code>. */
  public LocalDateTime getReportingDate() {
    return (LocalDateTime) get(8);
  }

  /** Setter for <code>flow_metrics_daily.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(9, value);
  }

  /** Getter for <code>flow_metrics_daily.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(9);
  }

  /** Setter for <code>flow_metrics_daily.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(10, value);
  }

  /** Getter for <code>flow_metrics_daily.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(10);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record11 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row11<
          Long,
          Integer,
          Integer,
          Integer,
          Long,
          Long,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      fieldsRow() {
    return (Row11) super.fieldsRow();
  }

  @Override
  public Row11<
          Long,
          Integer,
          Integer,
          Integer,
          Long,
          Long,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime>
      valuesRow() {
    return (Row11) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.ID;
  }

  @Override
  public Field<Integer> field2() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.ORIGIN_NODE_ID;
  }

  @Override
  public Field<Integer> field3() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.ORG_ID;
  }

  @Override
  public Field<Integer> field4() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.OWNER_ID;
  }

  @Override
  public Field<Long> field5() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.RUN_ID;
  }

  @Override
  public Field<Long> field6() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.ROW_COUNT;
  }

  @Override
  public Field<Long> field7() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.ERROR_COUNT;
  }

  @Override
  public Field<Long> field8() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.SIZE_VALUE;
  }

  @Override
  public Field<LocalDateTime> field9() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.REPORTING_DATE;
  }

  @Override
  public Field<LocalDateTime> field10() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.CREATED_AT;
  }

  @Override
  public Field<LocalDateTime> field11() {
    return FlowMetricsDaily.FLOW_METRICS_DAILY.UPDATED_AT;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public Integer component2() {
    return getOriginNodeId();
  }

  @Override
  public Integer component3() {
    return getOrgId();
  }

  @Override
  public Integer component4() {
    return getOwnerId();
  }

  @Override
  public Long component5() {
    return getRunId();
  }

  @Override
  public Long component6() {
    return getRowCount();
  }

  @Override
  public Long component7() {
    return getErrorCount();
  }

  @Override
  public Long component8() {
    return getSizeValue();
  }

  @Override
  public LocalDateTime component9() {
    return getReportingDate();
  }

  @Override
  public LocalDateTime component10() {
    return getCreatedAt();
  }

  @Override
  public LocalDateTime component11() {
    return getUpdatedAt();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public Integer value2() {
    return getOriginNodeId();
  }

  @Override
  public Integer value3() {
    return getOrgId();
  }

  @Override
  public Integer value4() {
    return getOwnerId();
  }

  @Override
  public Long value5() {
    return getRunId();
  }

  @Override
  public Long value6() {
    return getRowCount();
  }

  @Override
  public Long value7() {
    return getErrorCount();
  }

  @Override
  public Long value8() {
    return getSizeValue();
  }

  @Override
  public LocalDateTime value9() {
    return getReportingDate();
  }

  @Override
  public LocalDateTime value10() {
    return getCreatedAt();
  }

  @Override
  public LocalDateTime value11() {
    return getUpdatedAt();
  }

  @Override
  public FlowMetricsDailyRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value2(Integer value) {
    setOriginNodeId(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value3(Integer value) {
    setOrgId(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value4(Integer value) {
    setOwnerId(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value5(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value6(Long value) {
    setRowCount(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value7(Long value) {
    setErrorCount(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value8(Long value) {
    setSizeValue(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value9(LocalDateTime value) {
    setReportingDate(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value10(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord value11(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public FlowMetricsDailyRecord values(
      Long value1,
      Integer value2,
      Integer value3,
      Integer value4,
      Long value5,
      Long value6,
      Long value7,
      Long value8,
      LocalDateTime value9,
      LocalDateTime value10,
      LocalDateTime value11) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached FlowMetricsDailyRecord */
  public FlowMetricsDailyRecord() {
    super(FlowMetricsDaily.FLOW_METRICS_DAILY);
  }

  /** Create a detached, initialised FlowMetricsDailyRecord */
  public FlowMetricsDailyRecord(
      Long id,
      Integer originNodeId,
      Integer orgId,
      Integer ownerId,
      Long runId,
      Long rowCount,
      Long errorCount,
      Long sizeValue,
      LocalDateTime reportingDate,
      LocalDateTime createdAt,
      LocalDateTime updatedAt) {
    super(FlowMetricsDaily.FLOW_METRICS_DAILY);

    setId(id);
    setOriginNodeId(originNodeId);
    setOrgId(orgId);
    setOwnerId(ownerId);
    setRunId(runId);
    setRowCount(rowCount);
    setErrorCount(errorCount);
    setSizeValue(sizeValue);
    setReportingDate(reportingDate);
    setCreatedAt(createdAt);
    setUpdatedAt(updatedAt);
  }
}
