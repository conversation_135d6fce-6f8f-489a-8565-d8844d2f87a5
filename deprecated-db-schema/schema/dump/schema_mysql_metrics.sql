CREATE DATABASE metrics;
USE metrics;
-- created based on PROD env metrics DB
-- some tables are skipped because we don't need them in java yet
-- marchex_sinks_sources, DATA_SUBS_HOURLY, DATA_SUBS_DAILY, DATA_PUBS_HOURLY, DATA_PUBS_DAILY

CREATE TABLE `account_metrics_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `row_count` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `pipeline_count` bigint(20) DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `reporting_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  <PERSON>IMARY KEY (`id`),
  <PERSON><PERSON>Y `account_metrics_daily_date` (`reporting_date`),
  <PERSON><PERSON>Y `account_metrics_daily_org_id` (`org_id`,`reporting_date`),
  KEY `account_metrics_daily_owner_id` (`owner_id`,`reporting_date`)
);

CREATE TABLE `connector_state` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `state` varchar(20) NOT NULL,
  `message` text,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`resource_type`,`resource_id`)
);

CREATE TABLE `custom_flow_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  `row_count` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `pipeline_count` bigint(20) DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `reporting_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `custom_flow_state_daily_date` (`reporting_date`),
  KEY `custom_flow_state_daily_org_id` (`org_id`,`reporting_date`),
  KEY `custom_flow_state_daily_owner_id` (`owner_id`,`reporting_date`)
);

CREATE TABLE `custom_flow_hourly` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  `row_count` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `pipeline_count` bigint(20) DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `reporting_hour` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sources_hourly_id` (`resource_id`,`reporting_hour`,`run_id`),
  KEY `custom_flow_state_daily_date` (`reporting_hour`),
  KEY `custom_flow_state_daily_org_id` (`org_id`,`reporting_hour`),
  KEY `custom_flow_state_daily_owner_id` (`owner_id`,`reporting_hour`)
);

CREATE TABLE `custom_flow_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) DEFAULT NULL,
  `name` varchar(500) DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `record_count` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `aggregated` tinyint(1) DEFAULT '0',
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE `data_monitor_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `data_volume_avg` double DEFAULT NULL,
  `data_volume_stdev` double DEFAULT NULL,
  `record_count_avg` double DEFAULT NULL,
  `record_count_stdev` double DEFAULT NULL,
  `resource_id` int(11) DEFAULT NULL,
  `resource_type` enum('SOURCE','SINK') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `date_value` date DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_monitor_daily_resource_id` (`resource_id`),
  KEY `data_monitor_daily_resource_type` (`resource_type`)
);

CREATE TABLE `data_monitor_hourly` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `data_volume_avg` double DEFAULT NULL,
  `data_volume_stdev` double DEFAULT NULL,
  `record_count_avg` double DEFAULT NULL,
  `record_count_stdev` double DEFAULT NULL,
  `resource_id` int(11) DEFAULT NULL,
  `resource_type` enum('SOURCE','SINK') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `hour_of_day` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_monitor_hourly_resource_id` (`resource_id`),
  KEY `data_monitor_hourly_resource_type` (`resource_type`)
);

CREATE TABLE `data_monitor_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `notification_setting_id` int(11) DEFAULT NULL,
  `data_volume_avg` double DEFAULT NULL,
  `data_volume_stdev` double DEFAULT NULL,
  `record_count_avg` double DEFAULT NULL,
  `record_count_stdev` double DEFAULT NULL,
  `data_volume_total` double DEFAULT NULL,
  `record_count_total` double DEFAULT NULL,
  `resource_id` int(11) DEFAULT NULL,
  `resource_type` enum('ORG','USER','PIPELINE','SOURCE','PUB','SUB','DATASET','SINK') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `last_exection_time` timestamp(3) NULL DEFAULT NULL,
  `monitor_window` bigint(20) DEFAULT NULL,
  `hour_of_day` int(11) DEFAULT NULL,
  `monitor_date` timestamp(3) NULL DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `file_count_avg` double DEFAULT NULL,
  `file_count_stdev` double DEFAULT NULL,
  `file_count_total` double DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_monitor_notifications_resource_id` (`resource_id`),
  KEY `data_monitor_notifications_resource_type` (`resource_type`),
  KEY `data_monitor_notifications_notification_setting_id` (`notification_setting_id`)
);

-- CREATE TABLE `data_pubs_daily` (
--  `id` bigint(20) NOT NULL AUTO_INCREMENT,
--  `resource_id` int(11) NOT NULL,
--  `reporting_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
--  `row_count` bigint(20) DEFAULT NULL,
--  `data_volume_bytes` bigint(20) DEFAULT NULL,
--  `error_count` bigint(20) DEFAULT NULL,
--  `updated_at` timestamp(3) NULL DEFAULT NULL,
--  `created_at` timestamp(3) NULL DEFAULT NULL,
--  `org_id` int(11) DEFAULT NULL,
--  `owner_id` int(11) DEFAULT NULL,
--  `data_set_id` int(11) DEFAULT NULL,
--  `run_id` bigint(20) DEFAULT NULL,
--  PRIMARY KEY (`id`),
--  UNIQUE KEY `data_pubs_daily_id` (`resource_id`,`reporting_date`,`data_set_id`,`run_id`),
--  KEY `data_pubs_daily_date` (`reporting_date`),
--  KEY `data_pubs_daily_org_id` (`org_id`,`reporting_date`),
--  KEY `data_pubs_daily_owner_id` (`owner_id`,`reporting_date`),
--  KEY `run_id` (`run_id`)
-- );
--
-- CREATE TABLE `data_pubs_hourly` (
--  `id` bigint(20) NOT NULL AUTO_INCREMENT,
--  `resource_id` int(11) NOT NULL,
--  `reporting_hour` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
--  `row_count` bigint(20) DEFAULT NULL,
--  `data_volume_bytes` bigint(20) DEFAULT NULL,
--  `error_count` bigint(20) DEFAULT NULL,
--  `updated_at` timestamp(3) NULL DEFAULT NULL,
--  `created_at` timestamp(3) NULL DEFAULT NULL,
--  `org_id` int(11) DEFAULT NULL,
--  `owner_id` int(11) DEFAULT NULL,
--  `data_set_id` int(11) DEFAULT NULL,
--  `run_id` bigint(20) DEFAULT NULL,
--  PRIMARY KEY (`id`),
--  UNIQUE KEY `data_pubs_hourly_id` (`resource_id`,`reporting_hour`,`data_set_id`,`run_id`),
--  KEY `data_pubs_hourly_date` (`reporting_hour`),
--  KEY `data_pubs_hourly_org_id` (`org_id`,`reporting_hour`),
--  KEY `data_pubs_hourly_owner_id` (`owner_id`,`reporting_hour`),
--  KEY `run_id` (`run_id`)
-- );

CREATE TABLE `data_sets_tx_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `reporting_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `row_count` bigint(20) DEFAULT NULL,
  `data_volume_bytes` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `data_set_id` int(11) DEFAULT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sets_daily_id` (`resource_id`,`reporting_date`,`data_set_id`,`run_id`),
  KEY `data_sets_daily_date` (`reporting_date`),
  KEY `data_sets_daily_org_id` (`org_id`,`reporting_date`),
  KEY `data_sets_daily_owner_id` (`owner_id`,`reporting_date`)
);

CREATE TABLE `data_sets_tx_hourly` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `reporting_hour` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `row_count` bigint(20) DEFAULT NULL,
  `data_volume_bytes` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `data_set_id` int(11) DEFAULT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sets_hourly_id` (`resource_id`,`reporting_hour`,`data_set_id`,`run_id`),
  KEY `data_sets_hourly_date` (`reporting_hour`),
  KEY `data_sets_hourly_org_id` (`org_id`,`reporting_hour`),
  KEY `data_sets_hourly_owner_id` (`owner_id`,`reporting_hour`)
);

CREATE TABLE `data_sinks_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `reporting_date` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
  `row_count` bigint(20) DEFAULT NULL,
  `data_volume_bytes` bigint(20) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `data_set_id` int(11) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT '0',
  `run_id` bigint(20) DEFAULT NULL,
  `max_last_written` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sinks_daily_id` (`resource_id`,`data_set_id`,`reporting_date`,`run_id`),
  KEY `data_sinks_daily_date` (`reporting_date`),
  KEY `data_sinks_daily_org_id` (`org_id`,`reporting_date`),
  KEY `data_sinks_daily_owner_id` (`owner_id`,`reporting_date`),
  KEY `data_set_id` (`data_set_id`),
  KEY `data_set_id_reporting_date` (`data_set_id`,`reporting_date`),
  KEY `resource_id_reporting_date` (`resource_id`,`reporting_date`),
  KEY `run_id` (`run_id`)
);

CREATE TABLE `data_sinks_hourly` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `reporting_hour` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
  `row_count` bigint(20) DEFAULT NULL,
  `data_volume_bytes` bigint(20) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `data_set_id` int(11) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT '0',
  `run_id` bigint(20) DEFAULT NULL,
  `max_last_written` timestamp(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sinks_hourly_id` (`resource_id`,`data_set_id`,`reporting_hour`,`run_id`),
  KEY `data_sinks_hourly_date` (`reporting_hour`),
  KEY `data_sinks_hourly_org_id` (`org_id`,`reporting_hour`),
  KEY `data_sinks_hourly_owner_id` (`owner_id`,`reporting_hour`),
  KEY `data_set_id` (`data_set_id`),
  KEY `data_set_id_reporting_hour` (`data_set_id`,`reporting_hour`),
  KEY `resource_id_reporting_hour` (`resource_id`,`reporting_hour`),
  KEY `run_id` (`run_id`)
);

CREATE TABLE `data_sources_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `reporting_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `row_count` bigint(20) DEFAULT NULL,
  `data_volume_bytes` bigint(20) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `data_set_id` int(11) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `name_count` bigint(20) DEFAULT '0',
  `run_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sources_daily_id` (`resource_id`,`data_set_id`,`reporting_date`,`run_id`),
  KEY `data_sources_daily_date` (`reporting_date`),
  KEY `data_sources_daily_org_id` (`org_id`,`reporting_date`),
  KEY `data_sources_daily_owner_id` (`owner_id`,`reporting_date`),
  KEY `data_set_id` (`data_set_id`),
  KEY `data_set_id_reporting_date` (`data_set_id`,`reporting_date`),
  KEY `resource_id_reporting_date` (`resource_id`,`reporting_date`),
  KEY `run_id` (`run_id`)
);

CREATE TABLE `data_sources_hourly` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `reporting_hour` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `row_count` bigint(20) DEFAULT NULL,
  `data_volume_bytes` bigint(20) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `data_set_id` int(11) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `name_count` bigint(20) DEFAULT '0',
  `run_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_sources_hourly_id` (`resource_id`,`data_set_id`,`reporting_hour`,`run_id`),
  KEY `data_sources_hourly_date` (`reporting_hour`),
  KEY `data_sources_hourly_org_id` (`org_id`,`reporting_hour`),
  KEY `data_sources_hourly_owner_id` (`owner_id`,`reporting_hour`),
  KEY `data_set_id` (`data_set_id`),
  KEY `data_set_id_reporting_hour` (`data_set_id`,`reporting_hour`),
  KEY `resource_id_reporting_hour` (`resource_id`,`reporting_hour`),
  KEY `run_id` (`run_id`)
);

-- CREATE TABLE `data_subs_daily` (
--  `id` bigint(20) NOT NULL AUTO_INCREMENT,
--  `resource_id` int(11) NOT NULL,
--  `reporting_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
--  `row_count` bigint(20) DEFAULT NULL,
--  `data_volume_bytes` bigint(20) DEFAULT NULL,
--  `error_count` bigint(20) DEFAULT NULL,
--  `updated_at` timestamp(3) NULL DEFAULT NULL,
--  `created_at` timestamp(3) NULL DEFAULT NULL,
--  `org_id` int(11) DEFAULT NULL,
--  `owner_id` int(11) DEFAULT NULL,
--  `data_set_id` int(11) DEFAULT NULL,
--  `run_id` bigint(20) DEFAULT NULL,
--  PRIMARY KEY (`id`),
--  UNIQUE KEY `data_subs_daily_id` (`resource_id`,`reporting_date`,`data_set_id`,`run_id`),
--  KEY `data_subs_daily_date` (`reporting_date`),
--  KEY `data_subs_daily_org_id` (`org_id`,`reporting_date`),
--  KEY `data_subs_daily_owner_id` (`owner_id`,`reporting_date`),
--  KEY `run_id` (`run_id`)
-- );

-- CREATE TABLE `data_subs_hourly` (
--  `id` bigint(20) NOT NULL AUTO_INCREMENT,
--  `resource_id` int(11) NOT NULL,
--  `reporting_hour` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
--  `row_count` bigint(20) DEFAULT NULL,
--  `data_volume_bytes` bigint(20) DEFAULT NULL,
--  `error_count` bigint(20) DEFAULT NULL,
--  `updated_at` timestamp(3) NULL DEFAULT NULL,
--  `created_at` timestamp(3) NULL DEFAULT NULL,
--  `org_id` int(11) DEFAULT NULL,
--  `owner_id` int(11) DEFAULT NULL,
--  `data_set_id` int(11) DEFAULT NULL,
--  `run_id` bigint(20) DEFAULT NULL,
--  PRIMARY KEY (`id`),
--  UNIQUE KEY `data_subs_hourly_id` (`resource_id`,`reporting_hour`,`data_set_id`,`run_id`),
--  KEY `data_subs_hourly_date` (`reporting_hour`),
--  KEY `data_subs_hourly_org_id` (`org_id`,`reporting_hour`),
--  KEY `data_subs_hourly_owner_id` (`owner_id`,`reporting_hour`),
--  KEY `run_id` (`run_id`)
-- );

CREATE TABLE `error_status_metrics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL DEFAULT '0',
  `resource_type` varchar(10) DEFAULT NULL,
  `row_count` bigint(20) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `status` enum('ERROR','OK','WARNING') DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `updated_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`,`resource_id`)
);

CREATE TABLE `file_reingestion_request` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` int(11) NOT NULL,
  `full_path` varchar(514) NOT NULL,
  `status` varchar(20) NOT NULL,
  `request_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `file_ingestion_request_resource_id_status_index` (`resource_id`,`status`)
);

CREATE TABLE `file_sink_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET latin1 DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `record_count` bigint(20) DEFAULT NULL,
  `sink_id` int(11) NOT NULL,
  `error` varchar(4000) DEFAULT NULL,
  `write_status` int(11) DEFAULT '0',
  `data_set_id` int(11) DEFAULT NULL,
  `last_written` timestamp(3) NULL DEFAULT NULL,
  `error_message` varchar(4000) DEFAULT NULL,
  `error_count` bigint(20) DEFAULT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  `aggregated` tinyint(1) DEFAULT '0',
  `metadata` varchar(4000) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `file_sink_state_dataset_index` (`data_set_id`),
  KEY `sink_id` (`sink_id`),
  KEY `last_written` (`last_written`),
  KEY `file_sink_state_name_sink_id` (`name`,`sink_id`),
  KEY `file_sink_state_sink_id_last_written` (`sink_id`,`last_written`),
  KEY `run_id` (`run_id`)
);

CREATE TABLE `file_source_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET latin1 DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `record_count` bigint(20) DEFAULT NULL,
  `data_source_id` int(11) NOT NULL,
  `error` varchar(4000) CHARACTER SET latin1 DEFAULT NULL,
  `ingestion_status` int(11) DEFAULT '0',
  `data_set_id` int(11) DEFAULT NULL,
  `last_modified` timestamp NULL DEFAULT NULL,
  `last_ingested` timestamp NULL DEFAULT NULL,
  `error_count` int(11) DEFAULT NULL,
  `error_message` varchar(4000) DEFAULT NULL,
  `run_id` bigint(20) DEFAULT NULL,
  `aggregated` tinyint(1) DEFAULT '0',
  `metadata` varchar(4000) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `file_state_dataset_index` (`data_set_id`),
  KEY `data_source_id` (`data_source_id`),
  KEY `last_ingested` (`last_ingested`),
  KEY `last_modified` (`last_modified`),
  KEY `file_source_state_name_data_source_id` (`name`,`data_source_id`),
  KEY `file_source_state_data_source_id_last_ingested` (`data_source_id`,`last_ingested`),
  KEY `run_id` (`run_id`)
);

-- CREATE TABLE `marchex_sinks_sources` (
--  `sink` int(11) DEFAULT NULL,
--  `source` int(11) DEFAULT NULL
-- );

CREATE TABLE `quarantine_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) DEFAULT NULL,
  `name` varchar(500) NOT NULL,
  `bucket` varchar(100) DEFAULT NULL,
  `size_value` bigint(20) DEFAULT NULL,
  `record_count` bigint(20) DEFAULT NULL,
  `data_credentials_id` int(11) DEFAULT NULL,
  `cron_frequency` varchar(255) DEFAULT NULL,
  `quarantine_setting_id` int(11) NOT NULL,
  `org_id` int(11) DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `updated_at` timestamp(3) NULL DEFAULT NULL,
  `created_at` timestamp(3) NULL DEFAULT NULL,
  `system_quarantine` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `quarantine_files_resource_id` (`resource_id`),
  KEY `quarantine_files_resource_type` (`resource_type`),
  KEY `quarantine_files_resource` (`resource_id`,`resource_type`),
  KEY `quarantine_files_dataset_id` (`dataset_id`),
  KEY `quarantine_files_org_id` (`org_id`),
  KEY `quarantine_files_owner_id` (`owner_id`)
);

CREATE TABLE `quarantine_topic_offset` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) DEFAULT NULL,
  `partition_number` int(11) NOT NULL,
  `topic` varchar(300) NOT NULL,
  `offset` bigint(20) DEFAULT NULL,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`resource_type`,`resource_id`,`topic`,`partition_number`)
);

CREATE TABLE `script_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cron` varchar(128) NOT NULL,
  `script_github_path` varchar(1024) NOT NULL,
  `parameters` text NOT NULL,
  `description` varchar(512) NOT NULL,
  `status` varchar(20) NOT NULL,
  `source_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE `topic_offset` (
  `resource_type` varchar(10) NOT NULL,
  `resource_id` int(11) NOT NULL,
  `dataset_id` int(11) NOT NULL,
  `partition_number` int(11) NOT NULL,
  `topic` varchar(300) NOT NULL,
  `offset` bigint(20) DEFAULT NULL,
  `last_modified` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`resource_type`,`resource_id`,`dataset_id`,`topic`,`partition_number`)
);

ALTER TABLE file_source_state ADD COLUMN origin_node_id int(11);
ALTER TABLE file_source_state ADD COLUMN flow_node_id int(11);

ALTER TABLE file_sink_state ADD COLUMN origin_node_id int(11);
ALTER TABLE file_sink_state ADD COLUMN flow_node_id int(11);

ALTER TABLE error_status_metrics ADD COLUMN origin_node_id int(11);
ALTER TABLE error_status_metrics ADD COLUMN flow_node_id int(11);

ALTER TABLE data_monitor_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_monitor_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_monitor_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_monitor_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sinks_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sinks_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sources_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sources_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sets_tx_hourly ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sets_tx_hourly ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sinks_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sinks_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sources_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sources_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sets_tx_daily ADD COLUMN origin_node_id int(11);
ALTER TABLE data_sets_tx_daily ADD COLUMN flow_node_id int(11);

ALTER TABLE data_sets_tx_hourly ADD COLUMN parent_node_id int(11);
ALTER TABLE data_sets_tx_daily ADD COLUMN parent_node_id int(11);

CREATE TABLE quarantine_topic_aggregation_result (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    resource_id int(11) NOT NULL,
    origin_node_id int(11) NOT NULL,
    flow_node_id int(11) NOT NULL,
    resource_type varchar(10) NOT NULL,
    topic varchar(300) NOT NULL,
    run_id BIGINT NOT NULL,
    aggregation_result text NOT NULL,
    created_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    last_modified timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`)
);

CREATE TABLE flow_metrics_daily (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    origin_node_id int(11) NOT NULL,
    org_id int(11) NOT NULL,
    owner_id int(11) NOT NULL,
    run_id bigint(20) NOT NULL,
    row_count bigint(20) NOT NULL,
    error_count bigint(20) NOT NULL,
    size_value bigint(20) NOT NULL,
    reporting_date timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    created_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`)
);