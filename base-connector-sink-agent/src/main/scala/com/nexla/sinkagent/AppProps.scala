package com.nexla.sinkagent

import com.nexla.admin.client.config.EnrichedConfig.EnrichSinkParams
import com.nexla.admin.client.config.SinkConfigUtils
import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config._

import scala.compat.java8.OptionConverters._

class AppProps(val config: NexlaAppConfig)
  extends NexlaClusterApplication
    with NexlaSslConfig
    with NexlaAdminApi
    with KafkaProperties
    with NexlaEndpoints
    with Vault
    with AwsCredentials
    with SecretNames{

  val nodeTags = config.getOptString("node.tags")
    .map(_.split(",").toSeq)
    .getOrElse(Seq())

  val dedicatedTaskId = config.getOptString("dedicated.task.id").map(_.toInt)
  val taskType = config.getOptString("task.type")
    .orElse(config.getOptString("default.task.type"))

  val receiveTasksCron = config.getString("receive.tasks.cron")
  val credentialsSource = config.getStore.getType

  val vaultHost: Option[String] = vault.map(_.host)
  val vaultToken: Option[String] = vault.map(_.token)

  val sinkParams = new EnrichSinkParams(
    credentialsSource,
    vaultHost.asJava,
    vaultToken.asJava,
    credentialsAwsRegion.asJava,
    credentialsAwsAccessKey.asJava,
    credentialsAwsSecretKey.asJava,
    secretNames.asJava)
}
