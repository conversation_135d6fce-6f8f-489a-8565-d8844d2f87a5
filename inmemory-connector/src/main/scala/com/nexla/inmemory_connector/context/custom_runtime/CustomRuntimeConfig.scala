package com.nexla.inmemory_connector.context.custom_runtime

import com.nexla.admin.client.DataCredentials
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient.PrivatePackages
import spray.json.JsValue

sealed trait CustomRuntimeConfig

object CustomRuntimeConfig {
  case object NoCustomRuntimeConfig extends CustomRuntimeConfig

  case class RayRuntimeConfig(customCode: String, driverFunction: String, packages: List[String], privatePackages: Option[PrivatePackages], sink: String, source: String, destS3Credentials: MezzanineAuthConfig, sourceS3Credentials: MezzanineAuthConfig, extraData: Option[JsValue]) extends CustomRuntimeConfig

  case class MezzanineAuthConfig(bucketWithPrefix: String, authConfig: AWSAuthConfig, dataCredentials: DataCredentials)
}

