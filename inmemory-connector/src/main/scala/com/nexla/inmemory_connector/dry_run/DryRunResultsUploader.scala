package com.nexla.inmemory_connector.dry_run

import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.{DeleteObjectRequest, GetObjectRequest, ListObjectsV2Request, PutObjectRequest}
import com.nexla.common.StreamUtils
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.compat.DirectoryTransformationMessage
import com.nexla.inmemory_connector.context.Context.DryRunContext
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.probe.s3.S3ConnectorService
import com.nexla.sc.util.{Async, StrictNexlaLogging}
import org.apache.commons.io.FilenameUtils

import java.io.File
import java.nio.charset.StandardCharsets
import java.nio.file.{Files, StandardOpenOption}
import java.util
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.iterableAsScalaIterableConverter
import scala.util.{Failure, Success, Try}

class DryRunResultsUploader(dryRunContext: DryRunContext)(implicit ec: ExecutionContext) extends StrictNexlaLogging {
  private val taskEc = Async.ioExecutorContext

  private val mezzanineS3Client: S3Client = S3ConnectorService.createS3ClientFromCreds(dryRunContext.mezzanineCredentials.authConfig, null)

  private val ResultDirectory = s"${dryRunContext.dryRunUploadPath}/${dryRunContext.dryRunIdentifier}"

  private def listFile(directoryPath: String): Future[String] = Future {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(directoryPath, false)
    val request = ListObjectsV2Request.builder()
      .bucket(bucketPrefix.bucket)
      .prefix(bucketPrefix.prefix)
      .build()

    val result = mezzanineS3Client.listObjectsV2(request)
    val fileNames = result.contents().asScala.map(_.key()).toList.map(p => s"${bucketPrefix.bucket}/$p")

    fileNames match {
      case Nil =>
        logger.warn(s"No probe output files found in $directoryPath")
        throw new Exception(s"No probe output files found in $directoryPath")
      case file1 :: Nil => file1
      case file1 :: _ =>
        logger.warn(s"More than one file found in $directoryPath: $fileNames. Giving just the first one")
        file1
    }
  }(taskEc)

  private def downloadFile(filePath: String): Future[File] = Future {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(filePath, false)
    val tempFile = File.createTempFile("dry-run-sink", null)
    val getObjectRequest = GetObjectRequest.builder()
      .bucket(bucketPrefix.bucket)
      .key(bucketPrefix.prefix)
      .build()

    mezzanineS3Client.getObject(getObjectRequest, tempFile.toPath)
    tempFile
  }(taskEc)

  private def uploadFile(outputFilePath: String, localFile: File): Future[Unit] = Future {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(outputFilePath, false)
    val putObjectRequest = PutObjectRequest.builder()
      .bucket(bucketPrefix.bucket)
      .key(bucketPrefix.prefix)
      .build()

    mezzanineS3Client.putObject(putObjectRequest, RequestBody.fromFile(localFile))
    logger.info(s"Status file uploaded to $outputFilePath")
    ()
  }(taskEc)

  private def replicateProbeResultFile(fileTransformationMessage: DirectoryTransformationMessage): Future[String] = for {
    fileToReplicatePath <- listFile(fileTransformationMessage.fullDirectoryPath)
    outputFilePath = s"$ResultDirectory/${dryRunContext.dryRunIdentifier}.${FilenameUtils.getExtension(fileToReplicatePath)}"
    localFile <- downloadFile(fileToReplicatePath).tap(_ => logger.info(s"File $fileToReplicatePath downloaded successfully")).tapError(logger.error(s"Failed to download file $fileToReplicatePath", _))
    _ <- uploadFile(outputFilePath, localFile).tap(_ => logger.info(s"File $fileToReplicatePath uploaded successfully to $outputFilePath")).tapError(logger.error(s"Failed to upload file $fileToReplicatePath to $outputFilePath", _))
  } yield outputFilePath

  def cleanupResultFiles(): Future[Unit] = Future {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(ResultDirectory, false)

    val request = ListObjectsV2Request.builder()
      .bucket(bucketPrefix.bucket)
      .prefix(bucketPrefix.prefix)
      .build()

    val result = mezzanineS3Client.listObjectsV2(request)
    val objectsForDeletion = result.contents().asScala

    objectsForDeletion.foreach { obj =>
      val deleteObjectRequest = DeleteObjectRequest.builder()
        .bucket(bucketPrefix.bucket)
        .key(obj.key())
        .build()
      mezzanineS3Client.deleteObject(deleteObjectRequest)
      logger.info(s"Cleanup probe result file: ${obj.key()} (bucket: ${bucketPrefix.bucket})")
    }
  }(taskEc).tap(_ => logger.info(s"Successfully cleaned probe result files in $ResultDirectory")).tapError(logger.error(s"Failed to cleanup probe result files in $ResultDirectory", _))

  def uploadResults(result: Try[DirectoryTransformationMessage]): Future[Unit] = {
    val statusFilePath = s"$ResultDirectory/status"
    val probeFileUploadEff = result match {
      case Failure(exception) => Future.failed(exception)
      case Success(value) => replicateProbeResultFile(value).tap(path => logger.info(s"Probe file successfully replicated to $path")).tapError(logger.error(s"Failed to replicate probe file", _))
    }

    probeFileUploadEff
      .transformWith {
        case Failure(exception) => Future.successful(prepareLocalStatusFile("", Some(exception.getMessage)))
        case Success(outputFilePath) => Future.successful(prepareLocalStatusFile(outputFilePath, None))
      }
      .flatMap { file =>
        uploadFile(statusFilePath, file)
      }
  }


  private def prepareLocalStatusFile(outputFilePath: String, errorMessage: Option[String]): File = {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(outputFilePath, false)
    val statusFileContent = new util.HashMap[String, String]()
    statusFileContent.put("result.bucket", bucketPrefix.bucket)
    statusFileContent.put("result.path", bucketPrefix.prefix)
    errorMessage.foreach { error => statusFileContent.put("error", error) }

    val file = Files.createTempFile("status", ".tmp")
    Files.write(file, StreamUtils.jsonUtil().toJsonString(statusFileContent).getBytes(StandardCharsets.UTF_8), StandardOpenOption.WRITE)
    logger.info(s"Status file content: $statusFileContent")

    file.toFile
  }

}


