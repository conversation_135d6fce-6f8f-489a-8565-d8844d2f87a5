package com.nexla.inmemory_connector.pipeline.byo

import akka.NotUsed
import akka.stream.scaladsl.Flow
import cats.implicits.toTraverseOps
import com.nexla.common.ResourceType
import com.nexla.inmemory_connector.compat.{BasicMessage, DirectoryTransformationMessage, JustOffset, RecordWithOffset}
import com.nexla.inmemory_connector.monitoring.MetricsSender
import com.typesafe.scalalogging.StrictLogging

import scala.concurrent.{ExecutionContext, Future}

class ByoMetricSender(metricsSender: MetricsSender)(implicit ec: ExecutionContext) extends StrictLogging {
  val sendingMetricsFlow: Flow[BasicMessage, BasicMessage, NotUsed] = Flow[BasicMessage].mapAsync(1) {
    case r: RecordWithOffset => Future.successful(r)
    case r: JustOffset => Future.successful(r)
    case r: DirectoryTransformationMessage => r.offsets.traverse(fo =>
      metricsSender.publishMetric(fo.sourceId, ResourceType.SOURCE, fo.datasetId, 0, fo.fileSize, fo.filePath, Option(fo.eof), fo.maybeAdaptiveFlowTask)
    ).recoverWith { case e =>
      logger.error("Unable to publish metrics", e)
      Future.unit
    }.map(_ => r)
  }
}
