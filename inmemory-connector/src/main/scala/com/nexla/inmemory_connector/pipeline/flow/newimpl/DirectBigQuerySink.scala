package com.nexla.inmemory_connector.pipeline.flow.newimpl

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.Flow
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.client.flownode.FlowNodeDatasink
import com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials
import com.nexla.connector.config.BaseConnectorConfig.FAST_MODE
import com.nexla.connector.config.SinkConnectorConfig
import com.nexla.connector.config.big_query.BigQuerySinkConnectorConfig
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat
import com.nexla.connector.push.sink.BigQuerySinkTask
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.ReplicationSourceLocal
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryControlMessageProducer
import com.nexla.inmemory_connector.pipeline.storage.EmptySinkTaskContext
import com.nexla.inmemory_connector.state.ResourceState
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{Async, StrictNexlaLogging, WithLogging}
import connect.jdbc.sink.dialect.copy.filewriter.{DataFileWriter, JsonDataFileWriter}

import java.io.File
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class DirectBigQuerySink(dataSink: FlowNodeDatasink,
                         adminApiClient: AdminApiClient,
                         val ctx: PipelineContext,
                         val listingClient: ListingAppClient,
                         val coordinationClient: CoordinationAppClient,
                         val controlMessageProducer: InMemoryControlMessageProducer,
                         val offsetSaver: OffsetSaver,
                         props: AppProps,
                        )
                        (implicit val ec: ExecutionContext,
                         val system: ActorSystem,
                         val mat: Materializer)
  extends com.nexla.inmemory_connector.pipeline.flow.newimpl.DirectSink
    with StrictNexlaLogging
    with WithLogging
    with ConfigEnricher {
  private val taskEC: ExecutionContext = Async.ioExecutorContext

  val sinkParallelism = 1
  val writeLog = s"[sink][${dataSink.getConnectorType.toString}-${dataSink.getId}]"
  val usualDataSinkWithCfg = adminApiClient.getDataSink(dataSink.getId).get()
  val configMap = fullDataSinkConfig(props.config, usualDataSinkWithCfg, props.enrichSinkParams, BigQuerySinkConnectorConfig.configDef())
  enrichWithDataCredentials(adminApiClient, configMap)

  override val config: SinkConnectorConfig = new BigQuerySinkConnectorConfig(configMap)

  def filesSinkFlow(resourceState: ResourceState): Flow[ReplicationSourceLocal, ReplicationSourceLocal, Future[Unit]] = {
    val inputFlow = Flow[ReplicationSourceLocal]

    val task = new BigQuerySinkTask() {

      override protected def saveErrorFile(localTempFile: File): Unit = {
        // do not save file locally
      }

    }
    task.initialize(new EmptySinkTaskContext)
    task.setRunId(ctx.runId)

    configMap.put(FAST_MODE, "true")
    task.start(configMap, "fast-bigquery-sink")
    resourceState.markAsStarted()

    inputFlow
      .mapAsync(sinkParallelism) { local =>
        Future {
          local
            .files
            .flatMap(_.file)
            .foreach(task.uploadFileFastConnector)

          local
        }(taskEC)
      }
      .watchTermination()((notUsed, eventualDone) => {
        eventualDone.onComplete {
          case Success(_) =>
            logger.info(s"$writeLog sink task stop with success")
            resourceState.markAsDone()
            task.stop()
          case Failure(ex) =>
            logger.info(s"$writeLog sink task stop with failure", ex)
            resourceState.markAsFailed()
            task.stop()
        }
        Future.unit
      })
  }

  def dataFileWriter(): DataFileWriter = {
    val usualDataSinkWithCfg = adminApiClient.getDataSink(dataSink.getId).get()
    val resultConfig = fullDataSinkConfig(props.config, usualDataSinkWithCfg, props.enrichSinkParams, BigQuerySinkConnectorConfig.configDef())
    enrichWithDataCredentials(adminApiClient, resultConfig)
    val cfg = new BigQuerySinkConnectorConfig(resultConfig)
    // TODO: add support for BigQuery CSV upload and test it with DWH
    val fileFormat = cfg.fileFormat.orElse(WarehouseCopyFileFormat.JSON)
    new JsonDataFileWriter(fileFormat)
  }

}
