<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
    <logger name="org.apache.kafka" level="INFO" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>
    <logger name="org.apache.kafka.connect" level="INFO" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>
    <logger name="org.reflections" level="ERROR" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>
    <logger name="com.joestelmach.natty" level="WARN" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>
</configuration>