package com.nexla.parser.pb;

import com.nexla.common.NexlaMessage;
import com.nexla.common.parse.NexlaParser;
import com.nexla.common.parse.ParserConfigs;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Protobuf parser.
 */
public class ProtobufParser extends NexlaParser {
    private byte[] fileSetDescriptor;
    private String rootType;

    public ProtobufParser option(String key, String value) {
        super.option(key, value);
        switch (key) {
            case ParserConfigs.Protobuf.PROTO:
                // TODO support schema proto files
                break;
            case ParserConfigs.Protobuf.FILE_SET_DESCRIPTOR:
                this.fileSetDescriptor = Base64.getDecoder().decode(value);
                break;
            case ParserConfigs.Protobuf.ROOT_TYPE:
                this.rootType = value;
                break;
        }
        return this;
    }

    @SneakyThrows
    @Override
    public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream is) {
        // order matters, ParseAsUnknownFieldSet can parse everything, so it is last, as a fallback.
        List<ParsingStrategy> delegates = Stream
                .of(
                        maybeDynamicMessageParser(),
                        Optional.of(new ParseAsStruct()),
                        Optional.of(new ParseAsUnknownFieldSet())
                )
                .filter(Optional::isPresent).map(Optional::get)
                .collect(Collectors.toList());

        byte[] message = is.readAllBytes();
        for (ParsingStrategy delegate : delegates) {
            Optional<Stream<NexlaMessage>> parsed = delegate.parse(message);
            if (parsed.isPresent()) {
                return StreamEx.of(parsed.get())
                        .map(Optional::of);
            }
        }

        return StreamEx.empty();
    }

    private Optional<ParsingStrategy> maybeDynamicMessageParser() {
        if (fileSetDescriptor == null || rootType == null) {
            return Optional.empty();
        }

        return Optional.of(new ParseAsDynamicMessage(fileSetDescriptor, rootType));
    }

}
