package com.nexla.parser.pdf.strategy.parse;

import com.nexla.parser.pdf.strategy.parse.util.TextPositions;
import lombok.Builder;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.apache.pdfbox.util.Matrix;
import org.apache.pdfbox.util.Vector;

import java.awt.*;
import java.awt.geom.RectangularShape;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
public class TextCollector {
    private PDDocument document;
    private RectangularShape area;
    private RectangularShape below;
    private Integer pageNumber;

    public void iterate(BiConsumer<String, List<TextPosition>> consumer) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper() {
            @Override
            protected void showGlyph(Matrix textRenderingMatrix, PDFont font, int code, String unicode, Vector displacement) throws IOException {
                if (unicode == null || unicode.isEmpty()) {
                    return;
                }

                super.showGlyph(textRenderingMatrix, font, code, unicode, displacement);
            }

            @Override
            protected void writeString(String text, List<TextPosition> textPositions) {
                if (pageNumber != null && pageNumber != this.getCurrentPageNo() - 1) {
                    return;
                }

                PDRectangle cropBox = getCurrentPage().getCropBox();
                textPositions = textPositions.stream()
                        .filter(tp -> {
                            Rectangle t = TextPositions.translate(tp, cropBox);

                            boolean withinArea = area == null || area.contains(t);
                            boolean belowArea = below == null || below.getY() > t.getY();
                            return withinArea && belowArea;
                        })
                        .collect(Collectors.toList());

                Map<String, List<TextPosition>> textPositionsByFont = textPositions.stream()
                        .collect(Collectors.groupingBy(p -> p.getFont() + " " + p.getFontSizeInPt()));

                if (textPositionsByFont.size() <= 1) {
                    consumer.accept(text, textPositions);
                    return;
                }

                for (List<TextPosition> tps : textPositionsByFont.values()) {
                    tps.sort(Comparator.comparing(TextPosition::getY).thenComparing(TextPosition::getX));

                    if (!tps.isEmpty()) {
                        String t = tps.stream().map(TextPosition::getUnicode).collect(Collectors.joining());
                        consumer.accept(t, tps);
                    }
                }
            }
        };

        if (pageNumber != null) {
            stripper.setStartPage(pageNumber + 1);
            stripper.setEndPage(pageNumber + 1);
        }

        stripper.setSortByPosition(true);
        stripper.setAddMoreFormatting(true);
        stripper.getText(document);
    }

    public List<TextPosition> collect() throws IOException {
        List<TextPosition> textPositions = new ArrayList<>();
        iterate((text, tps) -> textPositions.addAll(tps));
        return textPositions;
    }

    public Stream<TextPosition> stream() throws IOException {
        List<TextPosition> textPositions = new ArrayList<>();
        iterate((text, tps) -> textPositions.addAll(tps));
        return textPositions.stream();
    }
}
