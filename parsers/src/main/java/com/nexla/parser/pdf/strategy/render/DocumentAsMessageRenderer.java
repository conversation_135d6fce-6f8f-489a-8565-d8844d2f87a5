package com.nexla.parser.pdf.strategy.render;

import com.nexla.common.NexlaMessage;
import com.nexla.parser.pdf.strategy.model.Block;
import com.nexla.parser.pdf.strategy.model.TableBlock;
import com.nexla.parser.pdf.strategy.model.TextBlock;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DocumentAsMessageRenderer extends TableAsMessageRenderer {

    private final boolean includeEntireFileContent;

    public DocumentAsMessageRenderer(String emptyValue, boolean includeEntireFileContent) {
        super(emptyValue);

        this.includeEntireFileContent = includeEntireFileContent;
    }

    @Override
    public Stream<NexlaMessage> render(Stream<Block> blockStream) {
        List<Block> blocks = blockStream.collect(Collectors.toList());

        List<Map<String, Object>> tables = blocks.stream()
                .filter(b -> b instanceof TableBlock)
                .map(b -> (TableBlock) b)
                .map(this::tableToMap)
                .collect(Collectors.toList());

        List<TextBlock> orderedTextBlocks = blocks.stream()
                .filter(b -> b instanceof TextBlock)
                .map(b -> (TextBlock) b)
                .sorted((a, b) -> {
                    if (a.getPageNumber() == null && b.getPageNumber() == null) {
                        return 0;
                    }

                    if (a.getPageNumber() == null) {
                        return -1;
                    }

                    if (b.getPageNumber() == null) {
                        return 1;
                    }

                    return a.getPageNumber().compareTo(b.getPageNumber());
                })
                .collect(Collectors.toList());

        List<Map<String, Object>> text = orderedTextBlocks.stream()
                .map(this::textToMap)
                .collect(Collectors.toList());

        LinkedHashMap<String, Object> rawMessage = new LinkedHashMap<>();
        if (!tables.isEmpty()) {
            rawMessage.put("tables", tables);
        }

        if (!text.isEmpty()) {
            rawMessage.put("pages", text);

            if (this.includeEntireFileContent) {
                rawMessage.put("content", orderedTextBlocks.stream().map(TextBlock::getText).collect(Collectors.joining("\n")));
            }
        }

        return Stream.of(new NexlaMessage(rawMessage));
    }

    @Override
    protected LinkedHashMap<String, Object> textToMap(TextBlock text) {
        LinkedHashMap<String, Object> map = super.textToMap(text);

        if (text.getPageNumber() != null && map != null) {
            map.put("pageNumber", text.getPageNumber());
        }

        return map;
    }
}
