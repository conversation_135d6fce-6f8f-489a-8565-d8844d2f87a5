package com.nexla.connect.common.offsets;

import com.nexla.common.sink.TopicPartition;
import com.nexla.control.coordination.SinkOffset;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.listing.client.ListingCoordinationClient;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class OffsetsTrackerTest {
	public static final int SINK_ID = 0;
	public static final String TOPIC_1 = "topic1";
	public static final TopicPartition TP_0 = new TopicPartition(TOPIC_1, 0);
	public static final int PARTITION_0 = 0;

	// T - trace message, M - common message

	//		T
	@Test
	public void shouldCommitOffsetsForSingleTraceMessage() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, true);

		offsetsTracker.flushTraceOffsets(SINK_ID, Set.of(TP_0));

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 1)));
		assertNoOffsetsInMemory(offsetsTracker);
	}

	//		T
	@Test
	public void shouldNotCommitOffsetsForSingleTraceMessageWhenTopicPartitionMissed() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, true);

		offsetsTracker.flushTraceOffsets(SINK_ID, Set.of());		// Topic partition not provided

		verifyNoInteractions(coordinationClient);

		// Should keep trace offset in memory
		assertEquals(1, offsetsTracker.getSinkTraceOffsets(SINK_ID).size());
	}

	//		T
	@Test
	public void shouldNotCommitOffsetsForSingleTraceMessageWhenFlushNotCalled() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, true);

		offsetsTracker.updateSinkOffsets(SINK_ID, Map.of());		// Commit no offsets

		verifyNoMoreInteractions(coordinationClient);

		// Should keep trace offset in memory
		assertEquals(1, offsetsTracker.getSinkTraceOffsets(SINK_ID).size());
	}

	//		TTT
	@Test
	public void shouldCommitOffsetsForThreeTraceMessages() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 2, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 3, true);

		offsetsTracker.flushTraceOffsets(SINK_ID, Set.of(TP_0));

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 3)));
		assertNoOffsetsInMemory(offsetsTracker);
	}

	//		MMMTT
	@Test
	public void shouldCommitOffsetsFor3M2TMessages() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 2, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 3, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 4, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 5, true);

		offsetsTracker.updateSinkOffsets(SINK_ID, Map.of(TP_0, 3L));		// Offset from the last common message. Should commit the last trace message

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 5)));
		assertNoOffsetsInMemory(offsetsTracker);
	}

	//		MMMTTMM
	@Test
	public void shouldCommitOffsetsFor3M2T2MMessages() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 2, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 3, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 4, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 5, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 6, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 7, false);

		offsetsTracker.updateSinkOffsets(SINK_ID, Map.of(TP_0, 7L));		// Offset from the last common message. Should commit the last trace message

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 7)));
		assertNoOffsetsInMemory(offsetsTracker);
	}

	//		TTMMM
	@Test
	public void shouldCommitOffsetsFor2T2MMessages() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 4, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 5, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 6, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 7, false);

		offsetsTracker.updateSinkOffsets(SINK_ID, Map.of(TP_0, 7L));		// Offset from the last common message. Should commit the last trace message

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 7)));
		assertNoOffsetsInMemory(offsetsTracker);
	}

	//		MMMTTMM
	@Test
	public void shouldNotCommitOffsetsFor3M2T2MMessagesWhenProcessingNotFinished() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 2, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 3, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 4, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 5, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 6, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 7, false);

		offsetsTracker.updateSinkOffsets(SINK_ID, Map.of(TP_0, 3L));		// Offset from the intermediate common message

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 3)));

		// Check tracked offsets in memory
		assertEquals(5L, (long) offsetsTracker.getSinkTraceOffsets(SINK_ID).get(TP_0));
		assertEquals(7L, (long) offsetsTracker.getSinkTrackedOffsets(SINK_ID).get(TP_0));
	}

	//		MMMTTMM
	@Test
	public void shouldNotCommitOffsetsFor3M2T2MMessagesWhenProcessingNotFinished2() {
		KafkaMessageTransport kafkaMessageTransport = mock(KafkaMessageTransport.class);

		ListingCoordinationClient coordinationClient = mock(ListingCoordinationClient.class);
		doNothing().when(coordinationClient).updateSinkOffsets(anyInt(), anyList());
		ArgumentCaptor<List<SinkOffset>> committedOffsetsCapture = ArgumentCaptor.forClass(List.class);

		OffsetsTracker offsetsTracker = new OffsetsTracker(coordinationClient);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 1, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 2, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 3, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 4, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 5, true);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 6, false);
		offsetsTracker.trackMessageOffset(SINK_ID, TP_0, 7, false);

		offsetsTracker.updateSinkOffsets(SINK_ID, Map.of(TP_0, 6L));		// Offset from the intermediate common message

		verify(coordinationClient, times(1)).updateSinkOffsets(eq(SINK_ID), committedOffsetsCapture.capture());
		verifyNoMoreInteractions(coordinationClient);

		assertTrue(committedOffsetsCapture.getValue().contains(new SinkOffset(SINK_ID, TOPIC_1, String.valueOf(PARTITION_0), 6)));

		// Check tracked offsets in memory
		assertNull(offsetsTracker.getSinkTraceOffsets(SINK_ID).get(TP_0));	// trace messages discarded
		assertEquals(7L, (long) offsetsTracker.getSinkTrackedOffsets(SINK_ID).get(TP_0));
	}

	private static void assertNoOffsetsInMemory(final OffsetsTracker offsetsTracker) {
		assertTrue(offsetsTracker.getSinkTraceOffsets(SINK_ID).isEmpty());
		assertTrue(offsetsTracker.getSinkTrackedOffsets(SINK_ID).isEmpty());
	}
}