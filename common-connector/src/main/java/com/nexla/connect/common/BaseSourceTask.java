package com.nexla.connect.common;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.nexla.admin.client.*;
import com.nexla.admin.client.config.SourceConfigUtils;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.common.*;
import com.nexla.common.datetime.NexlaBackoff;
import com.nexla.common.datetime.NexlaDelayer;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogMetricsProducer;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.transport.DataMessageProducer;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.ssh.tunnel.SshTunnel;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vault.CredentialsStore;
import com.nexla.control.coordination.HeartbeatConnectorCoordination;
import com.nexla.control.coordination.HeartbeatType;
import com.nexla.control.message.ControlEventType;
import com.nexla.control.message.SourceControlMessage;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.kafka.service.TopicMetaService;
import com.nexla.listing.client.ListingClient;
import com.nexla.telemetry.TelemetryContext$;
import com.nexla.transform.schema.FormatDetector;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.TopicConfig;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.record.AbstractRecords;
import org.apache.kafka.common.record.CompressionType;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.runtime.GetTaskProperties;
import org.apache.kafka.connect.runtime.SourceFlushStatus;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTask;
import org.apache.kafka.connect.source.SourceTaskContext;
import org.javatuples.Pair;
import org.jetbrains.annotations.NotNull;
import org.quartz.CronExpression;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.time.Duration;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials;
import static com.nexla.common.ConverterUtils.*;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NexlaNamingUtils.sourceConnectorServiceName;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.NotificationUtils.notifyErrorMessage;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.common.metrics.NexlaRawMetric.RUN_ID;
import static com.nexla.connect.common.connector.schema.SchemaDetectionUtils.createSchemaDetection;
import static com.nexla.connector.config.vault.VaultUtils.createNexlaCredentialsStore;
import static com.nexla.connect.common.DetailedFlowInsightsSender.RequestResponseDetailedMessages;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Objects.isNull;
import static java.util.Optional.*;
import static java.util.UUID.randomUUID;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;

public abstract class BaseSourceTask<C extends SourceConnectorConfig> extends SourceTask
	implements SshTunnelSupport {

	public static final String FEATURE_FLUSH_CONTROL = "feature.flush.control";

	private static final String ONE_MIN_CRON = "0 0/1 * 1/1 * ? *";
	public static final String ADMIN_API_PREFIX = "source";
	public static final byte MAX_USABLE_PRODUCE_MAGIC = 2;
	public static final CompressionType COMPRESSION_TYPE = CompressionType.forName("snappy");

	public static final ScheduledExecutorService SCHEDULED_POOL =
		Executors.newScheduledThreadPool(1, new ThreadFactoryBuilder().setNameFormat("source-scheduled-pool-%d").setDaemon(true).build());
	public NexlaLogger logger;

	private AtomicBoolean stop;

	private final AtomicBoolean stoppingConditionReached = new AtomicBoolean(false);

	public ControlMessageProducer controlMessageProducer;
	public DataMessageProducer dataMessageProducer;

	private NexlaBackoff backoff;
	private Optional<NexlaDelayer> pollDelayer = empty();

	@VisibleForTesting
	public C config;

	protected String dataSetTopic;

	private Integer dataSetId;
	protected Map<String, Integer> datasetMapping = new HashMap<>();

	protected Long runId;
	protected volatile boolean hasDataIngested = false;

	@VisibleForTesting
	public boolean rethrowRetryExceptions = false;

	public AdminApiClient adminApiClient;
	private Optional<SshTunnel> tunnel;

	static {
		FormatDetector.initDefault();
	}

	protected CredentialsStore nexlaCredentialsStore;
	protected ListingClient listingClient;
	protected Optional<JavaJobSchedulerClient> ctrlClient;
	protected Date lastCronRun;
	protected Optional<CronExpression> cronExpression;
	protected DataSource dataSource;

	private boolean firstBatch = true;

	private long totalRecords;
	protected SchemaDetectionUtils schemaDetection;
	protected TopicMetaService dataTopicMetaService;
	protected LoadingCache<Resource, Boolean> quarantineTopicExistsCache;
	protected int emptyPollCounter = 0;
	protected String podName;
	protected String taskId;
	protected Optional<Supplier<SourceFlushStatus>> sourceFlushStatusCalculator;

	protected Optional<ConnectorTelemetryReporter> sourceTelemetryReporter = Optional.empty();

	private long startTs = 0;
	protected RestTemplate restTemplate;

	private final AtomicInteger successDetailedFlowInsightsSentCount = new AtomicInteger(0);
	private final AtomicInteger errorDetailedFlowInsightsSentCount = new AtomicInteger(0);
	private final AtomicLong lastSuccessDetailedFlowInsightsResetTime = new AtomicLong(0);
	private final AtomicLong lastErrorDetailedFlowInsightsResetTime = new AtomicLong(0);
	public static final long DETAILED_FLOW_INSIGHTS_WINDOW_MS = 60000L;

	@Override
	public String version() {
		return CONNECTOR_VERSION;
	}

	public void start(Map<String, String> props) {
		start(props, ADMIN_API_PREFIX, empty());
	}

	public void start(Map<String, String> props, String adminApiPrefix, Optional<AdminApiClient> adminApiOpt) {
		Integer sourceId = toInteger(props.get(SOURCE_ID));
		this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SOURCE, sourceId, Optional.empty()));

		try {
			initSourceTask(props, adminApiPrefix, adminApiOpt);
			publishMonitoringLog("Task initialization is done", NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO);
			doStart(props);
			//TODO this message will occur 1 per each task, we probably want 1 per connector run
			publishMonitoringLog("Start processing data", NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO);
			this.startTs = System.currentTimeMillis();
		} catch (Exception e) {
			// Exception could be thrown before logger initialization
			if (logger != null) {
				logger.error("initSourceTask failed", e);
			}

			// Exception could be thrown before ControlMessageProducer initialization
			if (controlMessageProducer != null) {
				String fileName = isNull(dataSource) ? "UNKNOWN" : dataSource.getName();
				NexlaConnectorUtils.sendNexlaNotificationEvent(
					controlMessageProducer, ERROR, runId, SOURCE, config.sourceId, fileName, 0L,
					notifyErrorMessage(getErrorDetails(e, empty())));
				publishMonitoringLog("Error running source task. Error: " + e.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
			}

			reportTelemetryStartError();

			throw e;
		}
	}

	public void initialize(SourceTaskContext context) {
		super.initialize(context);
		try {
			GetTaskProperties taskProperties = new GetTaskProperties();
			this.taskId = taskProperties.getTaskId(context) + "";
			this.sourceFlushStatusCalculator = taskProperties.getSourceFlushStatusCalculator(context);
		} catch (Exception e) {
			this.taskId = randomUUID().toString();
			this.sourceFlushStatusCalculator = Optional.empty();
			LoggerFactory.getLogger(this.getClass()).error("", e);
		}
	}
	abstract public ConfigDef configDef();

	abstract public void doStart(Map<String, String> props);

	@SneakyThrows
	public void initSourceTask(Map<String, String> props,
	                           String adminApiPrefix,
	                           Optional<AdminApiClient> adminApiOpt) {
		this.stop = new AtomicBoolean(false);
		this.podName = Optional.ofNullable(System.getenv("POD_NAME"))
			.orElse(System.getenv("HOSTNAME"));

		ConnectorAdminConfig adminConfig = new ConnectorAdminConfig(props);

		Integer sourceId = toInteger(props.get(SOURCE_ID));

		if (this.logger == null) {
			this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SOURCE, sourceId, Optional.empty()));
		}

		this.adminApiClient = adminApiOpt.orElseGet(() -> getAdminApiClient(adminApiPrefix + "-" + sourceId, adminConfig));

		this.dataSource = queryDataSource(sourceId);
		Map<String, String> configMap = SourceConfigUtils.baseSourceConfig(this.dataSource);

		enrichWithDataCredentials(adminApiClient, configMap);
		enrichSourceParams(props, configMap);
		enrichWithCredentialsStore(configMap, configDef());
		configMap.putAll(customConnectorProperties(sourceId));

		try {
			// this is temp. solution, will be reworked
			configMap.putAll(inlineCredentialsReferences(dataSource, adminConfig));
		} catch (Throwable e) {
			logger.error("Error occurred while inline credentials references", e);
		}

		this.nexlaCredentialsStore = createNexlaCredentialsStore(configMap);

		this.config = parseConfig(configMap);
		this.controlMessageProducer = createControlMessageProducer(adminConfig.controlKafkaConfig);
		this.dataMessageProducer = createDataMessageProducer(adminConfig.dataKafkaConfig);

		this.tunnel = createTunnel(logger);

		this.backoff = new NexlaBackoff(logger, TimeUnit.MINUTES.toMillis(1));
		this.pollDelayer = of(config.pollMs).filter(a -> a > 0).map(pollMs -> new NexlaDelayer(config.pollMs));

		Boolean overrideRunId = Optional.ofNullable(toBoolean(props.get(OVERRIDE_RUN_ID))).orElse(false);
		Long confRunId = toLong(props.get(RUN_ID));
		if (overrideRunId) {
			this.runId = confRunId;
		} else {
			try {
				List<DataSourceRunId> runIds = Optional.of(
						queryDataSource(config.sourceId).getRunIds()).orElse(Collections.emptyList()
				);
				Long dataSourceRunId = StreamEx.of(runIds)
						.map(DataSourceRunId::getId)
						.append(confRunId)
						.reverseSorted()
						.limit(1)
						.toList()
						.get(0);

				this.runId = dataSourceRunId;
			} catch (Exception e) {
				logger.error("", e);
				this.runId = confRunId;
			}
		}
		if (this.runId == null) {
			throw new RuntimeException("runId cannot be null");
		}

		this.restTemplate = new RestTemplateBuilder()
				.withSSL(config.nexlaSslConfig)
				.withLogVerbose(config.logVerbose)
				.build();

		this.ctrlClient = config
			.ctrlHttpUrl
			.filter(x -> !config.fastMode && !config.listingMode)
			.map(c ->
				// passing null to val staticKafkaProperties: KafkaClusterPropertiesDefault, because connector does not use methods that request Kafka properties from Control
				new JavaJobSchedulerClient(c, config.nexlaUsername, config.nexlaPassword, null, restTemplate));

		this.listingClient = new ListingClient(config.listingAppServer, config.nexlaUsername, config.nexlaPassword, restTemplate);
		this.cronExpression = getCronExpression();
		this.dataTopicMetaService = new TopicMetaService(config.dataKafkaContext);
		this.quarantineTopicExistsCache = CacheBuilder.newBuilder()
			.build(CacheLoader.from(resource -> {
				Map<String, String> quarantineTopicConfig = new HashMap<>(Map.of(
					TopicConfig.RETENTION_MS_CONFIG, String.valueOf(TopicMetaService.DEFAULT_RETENTION_MS),
					TopicConfig.RETENTION_BYTES_CONFIG, String.valueOf(TopicMetaService.DEFAULT_RETENTION_BYTES)
				));

				if (config.maxRecordSize != null) {
					quarantineTopicConfig.put(TopicConfig.MAX_MESSAGE_BYTES_CONFIG, config.maxRecordSize.toString());
				}

				return dataTopicMetaService.createQuarantineTopicIfNotExist(
					resource,
					config.datasetPartitions,
					TopicMetaService.DEFAULT_TOPIC_REPLICATION_FACTOR,
					quarantineTopicConfig
				);
			}));

		initSchemaDetection();

		handleSchemaDetectionOnce();
	}

	@NotNull
	protected DataMessageProducer createDataMessageProducer(NexlaKafkaConfig dataKafkaConfig) {
		return new DataMessageProducer(new KafkaMessageTransport(dataKafkaConfig));
	}

	@NotNull
	protected ControlMessageProducer createControlMessageProducer(NexlaKafkaConfig controlKafkaConfig) {
		return new ControlMessageProducer(new KafkaMessageTransport(controlKafkaConfig));
	}

	protected void handleSchemaDetectionOnce() {
		if (this.config.schemaDetectionOnce) {
			this.schemaDetection.setTryCombineSingleSchema();
		}
	}

	protected void initSchemaDetection() {
		// Check for null to keep ability provide mock schemaDetection in parent class for unit testing
		if (this.schemaDetection == null) {
			this.schemaDetection = createSchemaDetection(config, adminApiClient, controlMessageProducer, empty());
		}
	}

	protected DataSource queryDataSource(Integer sourceId) {
		return adminApiClient.getDataSource(sourceId).get();
	}

	private Map<String, String> inlineCredentialsReferences(DataSource dataSource, ConnectorAdminConfig adminConfig) {
		// TODO REVISIT: check ReferencedResourceIds
		// XXX
		Object runtimeDataCredentialsId = dataSource.getSourceConfig()
				.get(NexlaConstants.RUNTIME_DATA_CREDENTIALS_ID);

		if (runtimeDataCredentialsId == null) {
			return Map.of();
		}

		Optional<DataCredentials> optCredentials = adminApiClient.getDataCredentials(Integer.parseInt(runtimeDataCredentialsId.toString()));

		if (optCredentials.isEmpty()) {
            this.logger.warn("Referenced credentials not found: id: {}", runtimeDataCredentialsId);
			return Map.of();
		}

		DataCredentials cs = optCredentials.get();

		return EntryStream.of(NexlaDataCredentials.getCreds(adminConfig.decryptKey, cs.getCredentialsEnc(), cs.getCredentialsEncIv()))
				.filterKeys(x -> !x.equals(NexlaConstants.CREDENTIALS_TYPE))
				.toMap();
	}

	public Map<String, String> customConnectorProperties(int sourceId) {
		return emptyMap();
	}

	public static void enrichSourceParams(Map<String, String> oldConfig, Map<String, String> newConfig) {
		oldConfig.forEach((k, v) -> {
			if (!newConfig.containsKey(k)) {
				newConfig.put(k, v);
			}
		});
	}

	@SneakyThrows
	private Optional<CronExpression> getCronExpression() {
		int srcId = -1;
		if (dataSource != null) {
			srcId = dataSource.getId();
		} else {
			srcId = config.sourceId;
		}

		try {
			if (config.startCron.isPresent()) {
				CronExpression result = new CronExpression(config.startCron.get());
				result.setTimeZone(TimeZone.getTimeZone(ZoneId.of("UTC")));
				return of(result);
			}
		} catch (ParseException e) {
			logger.warn("Error parsing cron expression for source [{}] - returning empty", srcId, e);
			return empty();
		}
		return empty();
	}

	@VisibleForTesting
	protected AdminApiClient getAdminApiClient(String adminApiPrefix, ConnectorAdminConfig adminConfig) {
		RestTemplate restTemplate = new RestTemplateBuilder().withSSL(adminConfig.nexlaSslConfig).build();

		return new AdminApiClientBuilder()
				.setAppName(adminApiPrefix)
				.setDataPlaneUid(adminConfig.dataplaneUid)
				.setEnrichmentUrl(adminConfig.enrichmentUrl)
				.setNoCache(true)
				.setTelemetry(Optional.of(TelemetryContext$.MODULE$.get()))
				.create(adminConfig.apiCredentialsServer, adminConfig.apiAccessKey, restTemplate);
	}

	protected abstract C parseConfig(Map<String, String> props);

	public CredentialsStore credentialsStore() {
		return this.nexlaCredentialsStore;
	}

	private boolean sourceFinishTracesSent = false;

	private final LoadingCache<Pair<Integer, HeartbeatType>, Object> heartbeatSender = CacheBuilder
		.newBuilder()
		.expireAfterWrite(1, MINUTES)
		.build(new CacheLoader<>() {
			@Override
			@SneakyThrows
			public Object load(Pair<Integer, HeartbeatType> key) {
				ctrlClient
					.filter(x -> !config.fastMode)
					.ifPresent(jpc -> {
						HeartbeatType heartbeatType = key.getValue1();
						HeartbeatConnectorCoordination heartbeat = new HeartbeatConnectorCoordination(
							randomUUID().toString(), SOURCE, config.sourceId, BaseSourceTask.this.runId, heartbeatType, System.currentTimeMillis());

						controlMessageProducer.sendHeartbeat(heartbeat);
					});
				return new Object();
			}
		});

	protected boolean shouldSendFinishTracesAndStop() {
		return !config.fastMode
						&& emptyPollCounter >= config.stopOnEmptyPollCount
						&& (sourceFlushStatusCalculator.map(x -> x.get().allFlushed()).orElse(false));
	}

	@Override
	public List<SourceRecord> poll() {

		//  temporary solution to avoid re-balancing issues at sources startup phase.
		if (!config.fastMode && config.tasksMax > 1  && System.currentTimeMillis() < startTs + config.sourcePollDelayMs){
			logger.info("Skip poll operation for scaled source during the start");
			heartbeat();
			Uninterruptibles.sleepUninterruptibly(Duration.ofSeconds(5));
			return null;
		}

		while (true) {
			try {
				if (isStopped()) {
					return null;
				}

				resetBackoff();

				backoff.sleepIfNecessary();

				if (isStopped()) {
					return null;
				}

				StopWatch stopWatch = new StopWatch();
				stopWatch.start();

				pollDelayer.ifPresent(NexlaDelayer::sleepIfNecessary);

				CollectRecordsResult collectRecords = collectRecords();
				List<SourceRecord> records = collectRecords.getRecords();

				Integer recordsCount = records != null ? records.size(): null;
				logger.info(String.format("RECORDS=%d POLL TIME=%s" , recordsCount, stopWatch));
				reportTelemetryPollDuration(stopWatch);

				if (recordsCount != null && recordsCount > 0) {
					this.totalRecords += recordsCount;
					reportTelemetryPollRawRecords(recordsCount);
				}

				this.hasDataIngested |= CollectionUtils.isNotEmpty(records);
				boolean hasMoreData = collectRecords.hasMoreData();

				if (hasMoreData){
					heartbeat();
				}

				if (hasMoreData && CollectionUtils.isNotEmpty(records)) {
					backoff.decreaseDelay();
				} else {
					backoff.increaseDelay();
				}

				boolean fireTrace = firstBatch && !config.fastMode;
				this.firstBatch = false;

				List<SourceRecord> resultRecords = fireTrace ? traceRecord() : new ArrayList<>();
				if (!isEmpty(records)) {
					resultRecords.addAll(filterMessagesBySize(records));
				}

				if (hasMoreData) {
					emptyPollCounter = 0;
				} else {
					emptyPollCounter += 1;
				}

				if (config.fastMode && emptyPollCounter >= config.stopOnEmptyPollCount) {
					logger.info("Stopping condition based on empty polls reached. Nr of empty polls {}", emptyPollCounter);
					stoppingConditionReached.set(true);
				}


				if (shouldSendFinishTracesAndStop()) {
					this.sourceFinishTracesSent = true;
					resultRecords.addAll(traceRecordFinish());
					sendPauseEvent();
				}

				reportTelemetryPollKafkaRecords(resultRecords.size());
				return resultRecords;
			} catch (ProbeRetriableException retryException) {
				if (rethrowRetryExceptions) {
					throw retryException;
				} else {
					String errorMessage = notifyErrorMessage(getErrorDetails(retryException, empty()));
					NexlaConnectorUtils.sendNexlaNotificationEvent(
						controlMessageProducer, ERROR, runId, SOURCE, config.sourceId,
							retryException.getMessage(), 0L, errorMessage);

					backoff.increaseDelay();

					double seconds = backoff.getCurrDelayMs() / 1000.0;
					logger.error("Retrying in {} sec after exception", seconds, retryException);
					publishMonitoringLog(
							"Failed to read records. Retrying in " + seconds + " sec after exception: " + errorMessage,
							NexlaMonitoringLogType.LOG,
							NexlaMonitoringLogSeverity.ERROR);
					reportTelemetryRetriableError();
				}

			} catch (Exception e) {
				NexlaConnectorUtils.sendNexlaNotificationEvent(
					controlMessageProducer, ERROR, runId, SOURCE, config.sourceId, e.getMessage(), 0L,
					notifyErrorMessage(getErrorDetails(e, empty())));
				publishMonitoringLog(
						"Error reading records. Error: " + e.getMessage(),
						NexlaMonitoringLogType.EVENT,
						NexlaMonitoringLogSeverity.ERROR);
				logger.error("", e);
				reportTelemetryPullError();
			} finally {
				this.lastCronRun = new Date();
				backoff.markCurrentTime();
			}
		}
	}

	@SneakyThrows
	public void heartbeat() {
		if (!config.fastMode && !config.listingMode) {
			heartbeatSender.get(new Pair<>(config.sourceId, BaseSourceTask.this.hasDataIngested ? HeartbeatType.DATA : HeartbeatType.TRACE));
		}
	}

	private void sendPauseEvent() {
		if (config.tasksMax > 1) {
			// Do nothing. Source will be paused by ctrl-jobscheduler after idle timeout
		} else {
			SCHEDULED_POOL.schedule(() -> {
				String origin = sourceConnectorServiceName(dataSource.getId(), dataSource.getConnectionType());

				Map<String, String> context = Maps.newHashMap();
				context.put(SOURCE_HAS_DATA, this.hasDataIngested + "");
				context.put(RUN_ID, String.valueOf(runId));
				context.put(FEATURE_FLUSH_CONTROL, String.valueOf(sourceFlushStatusCalculator.isPresent()));

				// Do not attach resourceJson to Control message, because adminApi client operates with expanded resource versions. Expanded dataSource version may not fit into Kafka message
				SourceControlMessage message = new SourceControlMessage(
					randomUUID(), dataSource.getId(), ControlEventType.PAUSE, dataSource.getConnectionType(),
					origin, context, Optional.empty(), Optional.empty());
				logger.info("Finished ingesting. Sending PAUSE event");

				controlMessageProducer.sendControlMessage(message);
			}, 1, TimeUnit.SECONDS);
		}
	}

	private List<SourceRecord> traceRecord() {

		Boolean emptyTraceDisable = ofNullable(System.getenv("EMPTY_TRACE_MESSAGE_DISABLE"))
			.map(Boolean::valueOf)
			.orElse(false);

		if (!emptyTraceDisable) {
			List<DataSet> dataSets = ofNullable(adminApiClient)
				 .flatMap(x -> x.getDataSource(config.sourceId).map(DataSource::getDatasets))
				 .orElse(emptyList());

			return StreamEx.of(dataSets)
					   .map(ds -> {
						   String topic = NexlaNamingUtils.nameDataSetTopic(ds.getId());
						   return new SourceRecord(emptyMap(), emptyMap(), topic, null, null, null, STRING_SCHEMA, toJsonString(new TraceMessage(runId, false)));
					   })
					   .toList();
		} else {
			return new ArrayList<>();
		}
	}

	private List<SourceRecord> traceRecordFinish() {
		if (config.fastMode || config.listingMode || !hasDataIngested) {
			return emptyList();
		}

		List<DataSet> dataSets = ofNullable(adminApiClient)
			.flatMap(x -> x.getDataSource(config.sourceId)
				.map(DataSource::getDatasets))
			.orElse(emptyList());

		List<String> topics = StreamEx.of(dataSets)
			.map(ds -> NexlaNamingUtils.nameDataSetTopic(ds.getId()))
			.collect(toList());

		Map<String, Integer> partitions = this.dataTopicMetaService.getTopicPartitionsCount(topics);

		return StreamEx.of(topics)
			.flatMap(topic -> {
				List<SourceRecord> res = new ArrayList<>();
				Integer partitionCnt = partitions.get(topic);
				for (int partition = 0; partition < partitionCnt; partition++) {

					SourceRecord sourceRecord = new SourceRecord(
						emptyMap(), emptyMap(), topic, partition,
						null, null, STRING_SCHEMA,
						toJsonString(new TraceMessage(runId, true)));

					res.add(sourceRecord);
				}
				return StreamEx.of(res);
			})
			.toList();
	}

	private List<SourceRecord> filterMessagesBySize(List<SourceRecord> records) {
		if (isEmpty(records) || config.fastMode) {
			return records;
		}

		return StreamEx.of(records)
			.filter(sourceRecord -> {
				var headers = StreamEx.of(sourceRecord.headers().iterator()).toArray(Header.class);
				byte[] keyBytes = ofNullable(sourceRecord.key())
					.map(x -> x.toString().getBytes(StandardCharsets.UTF_8))
					.orElse(new byte[0]);
				byte[] valueBytes = sourceRecord.value().toString().getBytes(StandardCharsets.UTF_8);

				var size = AbstractRecords.estimateSizeInBytesUpperBound(MAX_USABLE_PRODUCE_MAGIC, COMPRESSION_TYPE, keyBytes, valueBytes, headers);
				if (size >= config.maxRecordSize) {
					String errorMsg = "[" + sourceRecord.topic().toUpperCase() + "] Filtering out message of size " + size + " Max size = " + config.maxRecordSize;
					NexlaConnectorUtils.sendNexlaNotificationEvent(controlMessageProducer, ERROR, runId, SOURCE, config.sourceId, "", 0L, errorMsg);
					publishMonitoringLog(errorMsg, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
					logger.error(errorMsg);
				}
				return size < config.maxRecordSize;
			})
			.toList();
	}

	private void resetBackoff() {
		if (lastCronRun != null) {
			cronExpression.ifPresent(ce -> {
				Date nextCronDate = ce.getNextValidTimeAfter(lastCronRun);
				if (nextCronDate != null && new Date().after(nextCronDate)) {
					backoff.resetDelay();
				}
			});
		}
	}

	protected List<SourceRecord> detectSchemaIfNecessary(boolean multipleSchemas, List<SourceRecordCreator> records, Optional<NexlaSchema> resolveSchema) {
		return detectSchemaIfNecessary(multipleSchemas, records, resolveSchema, empty());
	}

	protected List<SourceRecord> detectSchemaIfNecessary(boolean multipleSchemas, List<SourceRecordCreator> records,
	                                                     Optional<NexlaSchema> resolveSchema, @NotNull Optional<String> name) {
		// delay schema detection until there are non-empty record set
		if (records.isEmpty()) {
			return Collections.emptyList();
		}

		if (this.dataSetTopic == null || multipleSchemas) {
			if (config.schemaDetectionTopic != null) {
				this.dataSetTopic = config.schemaDetectionTopic;
			} else {
				// detecting schema to calculate topic name
				if (!multipleSchemas) {
					if (this.schemaDetection.getNumSchemas() > 1) {
						throw new ConnectException("There are multiple data sets configured for this source, can't set single schema detection");
					}
					this.schemaDetection.setTryCombineSingleSchema();
				}

				List<LinkedHashMap<String, Object>> schemaDetectMessages = records.stream()
					.limit(config.schemaDetectionMaxMessages)
					.map(SourceRecordCreator::getDataMap)
					.collect(toList());

				SchemaDetectionResult detectionResult = this.schemaDetection.updateOrCreateDataSet(schemaDetectMessages, name.orElse(null), resolveSchema, multipleSchemas);
				this.dataSetTopic = detectionResult.topic;
				this.dataSetId = detectionResult.dataSetId;

				List<NexlaMessage> nexlaMessages = records.stream()
					.map(x -> x.nexlaMessage(dataSetId, dataSetTopic))
					.collect(toList());

				detectionResult.updateSamplesCallback.accept(nexlaMessages);
			}
		}

		if (!config.fastMode) {
			logger.info("Dataset topic={}", dataSetTopic);
		}

		name.ifPresent(n -> this.datasetMapping.put(n, this.dataSetId));

		// recreating records using topic name
		return records.stream()
			.map(x -> x.sourceRecord(dataSetId, dataSetTopic))
			.collect(toList());
	}

	public static NexlaMessage toNexlaMessage(SourceRecord r) {
		return StreamUtils.jsonUtil().stringToType(r.value().toString(), NexlaMessage.class);
	}

	private boolean isStopped() {
		return stop.get() || sourceFinishTracesSent;
	}

	/**
	 * Collects records from source
	 *
	 * @return null in order to continue silently
	 */
	public abstract CollectRecordsResult collectRecords();

	public BaseConnectorConfig getConfig() {
		return config;
	}

	@Override
	public void stop() {
		if (this.logger != null) {
			logger.info("Stopping task...");
		} else {
			System.out.println("Stopping task...");
		}

		if (controlMessageProducer != null) {
			publishMonitoringLog("Stopping task...", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO);
			if (totalRecords > 0) {
				publishMonitoringLog("Total of " + totalRecords + " records processed", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO);
			}
			controlMessageProducer.close();
		}

		if (backoff != null) {
			backoff.interrupt();
		}

		tunnel.ifPresent(SshTunnel::close);
		stop.set(true);
	}

	public ControlMessageProducer getControlMessageProducer() {
		return controlMessageProducer;
	}

	public Integer getDataSetId() {
		return this.dataSetId;
	}

	protected void sendMetrics(String metricName, long records, long size, long errors, long timestamp) {
		sendMetrics(metricName, records, size, errors, timestamp, getDataSetId());
	}

	protected void sendMetrics(String metricName, long records, long size, long errors, long timestamp, Integer dataSetId) {
		// adaptive flow tasks:
		// we need to pass the metric name from the outside (e.g. IMC or another runtime) if it's present in the basic source configuration.
		String resultingMetricName = ofNullable(config.externallySetMetricName).orElse(metricName);
		logger.info("metric name in BaseSourceTask: {}", resultingMetricName);
		NexlaConnectorUtils.publishMetrics(
			  controlMessageProducer,
				SOURCE,
				config.sourceId,
				resultingMetricName,
				records,
				size,
				errors,
				timestamp,
				Optional.of(runId),
				Optional.empty(),
				Optional.empty(),
				Optional.empty(),
				Optional.ofNullable(dataSetId),
				Optional.empty(),
				Optional.empty(),
				Optional.empty(),
				Optional.empty(),
				FlowType.STREAMING,
				dataSource.getOrgId(),
				dataSource.getOwnerId());
		publishMonitoringLogMetrics(records, size, errors);
	}

	protected void publishQuarantineMessage(List<SourceRecordCreator> records, Exception e) {
		if (records.isEmpty()) {
			return;
		}

		Resource source = Resource.source(config.sourceId);
		if (quarantineTopicExistsCache.getUnchecked(source)) {
			StreamEx.of(records)
					.map(x -> x.sourceRecord(getDataSetId(), dataSetTopic))
					.map(BaseSourceTask::toNexlaMessage)
					.forEach(m -> dataMessageProducer.sendQuarantineMessage(source, m, e));
		}
	}

	protected void publishMonitoringLog(
			String log,
			NexlaMonitoringLogType type,
			NexlaMonitoringLogSeverity severity) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				Optional.ofNullable(dataSource.getOrg())
						.map(Org::getId)
						.orElse(0),
				this.runId,
				config.sourceId,
				SOURCE,
				log,
				type,
				severity,
				System.currentTimeMillis()
		);
		controlMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

	protected void publishMonitoringLog(
			String log,
			NexlaMonitoringLogType type,
			NexlaMonitoringLogSeverity severity,
			Long eventTime) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				Optional.ofNullable(dataSource.getOrg())
						.map(Org::getId)
						.orElse(0),
				this.runId,
				config.sourceId,
				SOURCE,
				log,
				type,
				severity,
				eventTime
		);
		controlMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

	protected void publishMonitoringLogMetrics(long records, long size, long errors) {
		publishMonitoringLogMetrics(new MetricWithErrors(records, size, errors));
	}

	protected void publishMonitoringLogMetrics(MetricWithErrors metricWithErrors) {
		NexlaMonitoringLogMetricsProducer.publishMonitoringLogMetrics(
			controlMessageProducer,
				Optional.ofNullable(dataSource.getOrg())
						.map(Org::getId)
						.orElse(0),
				this.runId,
				config.sourceId,
				SOURCE,
				metricWithErrors
		);
	}

	protected void reportTelemetryStartError() {
		this.sourceTelemetryReporter.ifPresent(x -> x.reportStartError());
	}

	protected void reportTelemetryPullError() {
		this.sourceTelemetryReporter.ifPresent(x -> x.reportPollError());
	}

	protected void reportTelemetryRetriableError() {
		this.sourceTelemetryReporter.ifPresent(x -> x.reportRetriableError());
	}

	protected void reportTelemetryPollDuration(StopWatch stopWatch) {
		try {
			this.sourceTelemetryReporter.ifPresent(x -> x.reportPollDurationMillis(Duration.ofMillis(stopWatch.getTime())));
		} catch (Exception e) {
			logger.info("Can't report telemetry pull duration {}" , stopWatch, e);
		}
	}

	protected void reportTelemetryPollRawRecords(Integer records) {
		this.sourceTelemetryReporter.ifPresent(x -> x.reportPollRawRecords(records));
	}

	protected void reportTelemetryPollKafkaRecords(Integer records) {
		this.sourceTelemetryReporter.ifPresent(x -> x.reportPollKafkaRecords(records));
	}

	private Consumer<RequestResponseDetailedMessages> createRateLimitedFlowInsightsSender(boolean logVerbose, int rateLimitPerMinute,
																								  AtomicLong lastDetailedFlowInsightsResetTime, AtomicInteger detailedFlowInsightsSentCount) {
		return (detailedMessages) -> {
			long now = System.currentTimeMillis();
			long lastResetTime = lastDetailedFlowInsightsResetTime.get();
			if (now - lastResetTime >= DETAILED_FLOW_INSIGHTS_WINDOW_MS) {
				if (lastDetailedFlowInsightsResetTime.compareAndSet(lastResetTime, now)) {
					detailedFlowInsightsSentCount.set(0);
				}
			}
			long lastCount = detailedFlowInsightsSentCount.get();
			if (lastCount < rateLimitPerMinute) {
				detailedFlowInsightsSentCount.incrementAndGet();
				publishMonitoringLog(detailedMessages.getRequest(), NexlaMonitoringLogType.LOG, detailedMessages.getRequestSeverity(), detailedMessages.getRequestTime());
				publishMonitoringLog(detailedMessages.getResponse(), NexlaMonitoringLogType.LOG, detailedMessages.getResponseSeverity(), detailedMessages.getRequestTime());
			}
		};
	}

	protected Consumer<RequestResponseDetailedMessages> getSuccessFlowInsightsSender() {
		return createRateLimitedFlowInsightsSender(
				config.logVerbose,
				config.detailedFlowInsightsSuccessesPerMinute,
				lastSuccessDetailedFlowInsightsResetTime,
				successDetailedFlowInsightsSentCount
		);
	}

	protected Consumer<RequestResponseDetailedMessages> getErrorFlowInsightsSender() {
		return createRateLimitedFlowInsightsSender(
				config.logVerbose,
				config.detailedFlowInsightsErrorsPerMinute,
				lastErrorDetailedFlowInsightsResetTime,
				errorDetailedFlowInsightsSentCount
		);
	}

	public boolean isStoppingConditionReached() {
		return stoppingConditionReached.get();
	}

}