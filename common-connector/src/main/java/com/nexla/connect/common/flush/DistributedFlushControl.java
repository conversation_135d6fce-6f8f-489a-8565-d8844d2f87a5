package com.nexla.connect.common.flush;

import com.nexla.common.Resource;
import com.nexla.common.ResourceType;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.listing.client.ListingCoordinationClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;
import java.util.concurrent.*;

/**
 * Class designed in purpose to synchronise distributed sink tasks flush operations that take long time (> 1 minute)
 */
public class DistributedFlushControl implements AutoCloseable {
	private static final Logger logger = LoggerFactory.getLogger(DistributedFlushControl.class);
	private static final CompletableFuture<Object> NO_HEARTBEAT = CompletableFuture.completedFuture(null);

	private final ListingCoordinationClient coordinationClient;
	private final ScheduledExecutorService executorService;

	private final Resource resource;
	private final UUID flushId;
	private final boolean shouldSync;
	private final Future<?> heartbeat;

	public DistributedFlushControl(ListingCoordinationClient coordinationClient, SinkConnectorConfig config, ScheduledExecutorService executorService) {
		this.coordinationClient = coordinationClient;
		this.executorService = executorService;
		this.resource = new Resource(config.sinkId, ResourceType.SINK);
		this.flushId = UUID.randomUUID();
		this.shouldSync = config.tasksMax > 1;
		this.heartbeat = shouldSync ? startDelayedHeartbeat(config) : NO_HEARTBEAT;
	}

	private Future<?> startDelayedHeartbeat(SinkConnectorConfig config) {
		long ts = System.currentTimeMillis();
		return executorService.scheduleWithFixedDelay(() -> {
			if (System.currentTimeMillis() - ts > config.maxHeartbeatPeriodMs) {
				logger.warn("Max flush operation heartbeat period ({}) exceeded for resource {} flush {}", config.maxHeartbeatPeriodMs, resource, flushId);
				return;
			}
			try {
				this.coordinationClient.notifyFlush(flushId, resource);
			} catch (Exception e) {
				logger.warn("Failed to notify flush operation to coordination app for resource {} flush {}", resource, flushId, e);
			}
		}, 1, 1, TimeUnit.MINUTES);
	}

	public void sync() {
		if (!shouldSync) {
			return;
		}
		int counter = 0;
		while (!flushFinished()) {
			logger.debug("Awaiting for flush synchronization for resource {} flush {} (times: {})", resource, flushId, ++counter);
			try {
				Thread.sleep(TimeUnit.SECONDS.toMillis(45));
			} catch (InterruptedException ignored) {
			}
		}
	}

	@Override
	public void close() {
		heartbeat.cancel(true);
	}

	private boolean flushFinished() {
		try {
			return coordinationClient.flushFinished(flushId, resource);
		} catch (Exception e) {
			logger.warn("Failed to check flush finished status from coordination app for resource {} flush {}", resource, flushId, e);
			return false;
		}
	}
}