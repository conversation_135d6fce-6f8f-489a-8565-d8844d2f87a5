package com.nexla.connect.common;

import com.google.common.annotations.VisibleForTesting;

import java.util.function.Supplier;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * Not thread safe alternative to guava Suppliers.memoize
 */
@VisibleForTesting
class NonThreadSafeLazy<T> implements Supplier<T> {
    private Supplier<T> delegate;
    private boolean initialized;
    private T value;

    public NonThreadSafeLazy(Supplier<T> delegate) {
        this.delegate = checkNotNull(delegate);
    }

    @Override
    public T get() {
        if (!initialized) {
            value = delegate.get();
            initialized = true;
            delegate = null;    // Release the delegate to GC.
        }
        return value;
    }

    @Override
    public String toString() {
        return initialized ? "" + value : "<not computed>";
    }

    @VisibleForTesting
    static <T> Supplier<T> memoize(Supplier<T> delegate) {
        return new NonThreadSafeLazy<>(delegate);
    }
}