package com.nexla.sinkagent

import com.google.common.collect.Maps
import com.nexla.admin.client.AdminApiClient
import com.nexla.common.NexlaNamingUtils
import com.nexla.common.datetime.DateTimeUtils.{nowUTC, nowUtc}
import com.nexla.connect.common.BaseSinkTask
import com.nexla.connector.config.{PipelineTaskType, SinkInstanceConfig}
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum.{Finished, PipelineTaskState, Running}
import com.nexla.sc.client.job_scheduler._
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging, WithTryLock}
import edu.emory.mathcs.backport.java.util.concurrent.atomic.AtomicInteger
import one.util.streamex.StreamEx
import org.quartz.CronExpression

import java.time.{Duration, LocalDateTime, ZoneId}
import java.util.TimeZone
import java.util.concurrent.ConcurrentLinkedQueue
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.{ExecutionContext, Future}

class TaskReceiver(nodeId: String,
                   ip: String,
                   podName: Option[String],
                   dedicatedNode: Boolean,
                   taskType: PipelineTaskType,
                   sinkClient: SinkConnectorClient,
                   client: JavaNodeTaskManagerClient,
                   tags: Seq[String],
                   serviceName: Option[String],
                   adminApiClient: AdminApiClient,
                   props: SinkInstanceConfig)
                  (implicit val ec: ExecutionContext)
  extends WithTryLock
    with StrictNexlaLogging
    with WithLogging {

  val forceRestartTopics = new ConcurrentLinkedQueue[String]()
  val lastDataTsMap: java.util.Map[Int, LocalDateTime] = Maps.newConcurrentMap()
  val heartbeatTsMap: java.util.Map[Int, LocalDateTime] = Maps.newConcurrentMap()
  val pipelineStatuses: java.util.Map[Int, PipelineTaskState] = Maps.newConcurrentMap()

  // Map that tracks the number of started tasks per sink connector. Consider possibilities
  // - Sink task is scaled - normal case that COULD happen
  // - Sink task is not scaled, but assigned to the same node for the second time before the previous task thread has stopped - SHOULD NOT happen
  // - Sink task task thread got stuck at some operation - SHOULD NOT happen
  val tasksCounterMap: java.util.Map[Int, AtomicInteger] = Maps.newConcurrentMap()

  val intervalDurationMs = {
    val cron = new CronExpression(props.receiveTasksCron)
    cron.setTimeZone(TimeZone.getTimeZone(ZoneId.of("UTC")))

    val nextCron = cron.getNextValidTimeAfter(nowUTC().toDate)
    val nextAfterNext = cron.getNextValidTimeAfter(nextCron)
    (nextAfterNext.getTime - nextCron.getTime).toInt
  }

  def receiveAndProcessTasks(): Future[Unit] = {
    val currentSinksTry = sinkClient.getCurrentSinks()
    if (currentSinksTry.isSuccess) {
      withTryLock {
        receiveAndProcessTasks(currentSinksTry.get)
      } {
        logger.info(s"receiveAndProcessTasks is still busy, notify current state")
        notifyCurrentTasksState(currentSinksTry.get)
      }
    } else {
      logger.warn("Connector is not ready, heartbeating")
      Future(client.nodeTasksHeartbeat(NodeIdWrapped(nodeId)))
    }
  }

  private def receiveAndProcessTasks(currentSinks: Set[Int]): Future[Unit] = {
    for {
      _ <- {
        val statuses = pipelineStatuses(currentSinks ++ lastDataTsMap.asScala.keySet ++ heartbeatTsMap.asScala.keySet)
        val request = PipelineNodeDto(nodeId, ip, dedicatedNode, podName, Set(taskType),
          statuses, Some(tags), version = props.appVersion.asScala, serviceName)

        for {
          nodeTaskResponse: NodeTaskResponse <- Future(client.nodeTasksReceive(request))
          _ <- {
            if (!nodeTaskResponse.ready) {
              logger.info("NodeTaskManager is not ready")
              Future.successful(())
            } else {
              val tasks = nodeTaskResponse.tasks.values.flatten.toSeq
              logger.info(s"receiveAndProcessTasks received ${tasks.size}")

              val forceRestart = StreamEx.generate(() => forceRestartTopics.poll())
                .takeWhile(x => x != null)
                .toList
                .asScala

              val stopIds = nodeTaskResponse.stopIds.getOrElse(Map())
              val restartIds = nodeTaskResponse.restartIds.getOrElse(Map())

              Future(setCurrentActive(currentSinks, tasks, stopIds.values.flatten, restartIds.values.flatten, forceRestart))
            }
          }
        } yield {
        }
      }
    } yield {
    }
  }

  private def notifyCurrentTasksState(currentSinks: Set[Int]): Future[Unit] = {
    val statuses = pipelineStatuses(currentSinks ++ lastDataTsMap.asScala.keySet ++ heartbeatTsMap.asScala.keySet)
    val request = PipelineNodeDto(nodeId, ip, dedicatedNode, podName, Set(taskType),
      statuses, Some(tags), version = props.appVersion.asScala, serviceName)
    Future(client.nodeTasksNotify(request))
  }

  def notifyPipeline(sinkId: Int, data: Boolean): LocalDateTime = {
    if (data) {
      lastDataTsMap.put(sinkId, nowUtc())
    }
    heartbeatTsMap.put(sinkId, nowUtc())
  }

  def notifyTaskStarted(sinkId: Int): Unit = {
    pipelineStatuses.put(sinkId, Running)
    val tasksCount = tasksCounterMap.computeIfAbsent(sinkId, _ => new AtomicInteger()).incrementAndGet()
    logger.info(s"Task $sinkId has started: tasks count: $tasksCount")
  }

  def notifyTaskStopped(sinkId: Int): Unit = {
    val tasksCount = tasksCounterMap.computeIfAbsent(sinkId, _ => new AtomicInteger(1)).decrementAndGet()
    if (tasksCount == 0) {
      pipelineStatuses.put(sinkId, Finished)
    }
    if (tasksCount < 0) {
      logger.error(s"CRITICAL. Logic flaw. On TaskStopped, the number of tasks for sink #$sinkId is $tasksCount")
    }
    logger.info(s"Task $sinkId has stopped: tasks count: $tasksCount")
  }

  def pipelineStatuses(currentSinks: Set[Int]): List[NodeTaskStatus] = {
    currentSinks
      .toList
      .map { sinkId =>
        val lastDataTs = lastDataTsMap.asScala.get(sinkId)
        val heartbeatTs = heartbeatTsMap.asScala.get(sinkId)
        val status = pipelineStatuses.asScala.getOrElse(sinkId, PipelineTaskStateEnum.Running)
        NodeTaskStatus(TaskRequest.taskId(sinkId, taskType), None, status,
          None, None, None, None, None, None, lastDataTs, heartbeatTs)
      }
  }

  def startSink(index: String,
                pipelines: Set[Int],
                x: NodeTaskResponseElem,
                restartSinks: Set[Int]) = {
    x.meta
      .flatMap(_.sinkId)
      .filter(sinkId => pipelines.contains(sinkId))
      .filter(sinkId => x.restart || restartSinks.contains(sinkId))
      .foreach(sinkId => sinkClient.restartSink(index, sinkId))

    x.meta
      .flatMap(_.sinkId)
      .filterNot(pipelines.contains)
      .map(sinkId => {
        val existingTasksCount = tasksCounterMap.computeIfAbsent(sinkId, _ => new AtomicInteger()).getAndSet(0)
        if (existingTasksCount > 0) {
          // SHOULD NOT happen. The previous sink task thread got stuck and ctrl assigns it back to the same node.
          logger.error(s"Starting already running sink #$sinkId, tasks count: $existingTasksCount")
        }
        if (existingTasksCount < 0) {
          logger.error(s"CRITICAL. Logic flaw. The number of tasks for sink #$sinkId is $existingTasksCount")
        }
        sinkId
      })
      .foreach(sinkId => sinkClient.startSink(index, adminApiClient, sinkId, x.meta))
  }

  def setCurrentActive(currentConnectIds: Set[Int],
                       tasks: Iterable[NodeTaskResponseElem],
                       pauseSinks: Iterable[Int],
                       restartSinks: Iterable[Int],
                       forceRestart: Seq[String]) = {
    val tsThreshold = nowUtc().minus(Duration.ofMinutes(5))

    lastDataTsMap
      .asScala
      .filter(entry => entry._2.isBefore(tsThreshold))
      .keySet
      .diff(currentConnectIds)
      .foreach(lastDataTsMap.remove)

    heartbeatTsMap
      .asScala
      .filter(entry => entry._2.isBefore(tsThreshold))
      .keySet
      .diff(currentConnectIds)
      .foreach(heartbeatTsMap.remove)

    val tasksToSet = tasks.map(_.taskId.split("-")(1).toInt).toSet

    val topicsToTaskId = tasks.flatMap(x =>
        x.meta.flatMap(_.dataSetFrom)
          .map(xx => NexlaNamingUtils.nameDataSetTopic(xx) -> x.taskId.split("-")(1).toInt))
      .toMap

    val restartsForced = forceRestart.flatMap(topicsToTaskId.get)


    val tasksStop = currentConnectIds.diff(tasksToSet) ++ pauseSinks ++ restartsForced
    tasksStop
      .intersect(currentConnectIds)
      .toList
      .foreach(sinkId => {
        notifyPipeline(sinkId, data = false) // heartbeat pipeline for the last time
        sinkClient.stopSink(sinkId)
      })
    tasks
      .zipWithIndex
      .toList
      .foreach { case (task, index) =>
        startSink(s"${index + 1} / ${tasks.size}", currentConnectIds, task, restartSinks.toSet)
      }
  }

}
