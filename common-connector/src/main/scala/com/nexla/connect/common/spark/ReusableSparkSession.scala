package com.nexla.connect.common.spark

import com.nexla.sc.util.StrictNexlaLogging
import org.apache.spark.sql.SparkSession

case class ReusableSparkSession(session: SparkSession, key: AnyRef) extends StrictNexlaLogging {

  def tryReusingElseClose(newKey: AnyRef): Option[SparkSession] = {
    if (session.sparkContext.isStopped) {
      None
    } else {
      if (key == newKey) {
        Some(session)
      } else {
        logger.info(s"Stopping Spark session ${session.sparkContext.applicationId}")
        session.stop()
        None
      }
    }
  }
}
