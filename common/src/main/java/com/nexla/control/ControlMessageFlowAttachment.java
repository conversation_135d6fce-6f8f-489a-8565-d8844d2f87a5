package com.nexla.control;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import java.util.Optional;
import lombok.Data;

/**
 * Use class to map flow objects attached to control messages retrieved from AdminApi. Add other
 * properties as needed
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ControlMessageFlowAttachment {

  @JsonProperty("id")
  private final Optional<Integer> id;

  @JsonProperty("flow_type")
  private final FlowType flowType;

  @JsonProperty("ingestion_mode")
  private final IngestionMode ingestionMode;
}
