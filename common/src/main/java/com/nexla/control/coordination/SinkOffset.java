package com.nexla.control.coordination;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@EqualsAndHashCode
@ToString
@Getter
public class SinkOffset {

  public final int sinkId;
  public final String topic;
  public final String partition;
  public final long offset;

  @JsonCreator
  public SinkOffset(
      @JsonProperty("sinkId") int sinkId,
      @JsonProperty("topic") String topic,
      @JsonProperty("partition") String partition,
      @JsonProperty("offset") long offset) {
    this.sinkId = sinkId;
    this.topic = topic;
    this.partition = partition;
    this.offset = offset;
  }
}
