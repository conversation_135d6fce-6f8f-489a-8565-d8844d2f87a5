package com.nexla.control.coordination;

import lombok.Data;

@Data
public class DataSetTraceCoordination implements CoordinationMessage {

  public final String messageId;
  public final int datasetId;
  public final long runId;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.DATASET_TRACE;
  }
}
