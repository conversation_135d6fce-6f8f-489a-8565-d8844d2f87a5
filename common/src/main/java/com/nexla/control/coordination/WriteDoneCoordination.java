package com.nexla.control.coordination;

import lombok.Data;

@Data
public class WriteDoneCoordination implements CoordinationMessage {

  public final String messageId;
  public final int sinkId;
  public final long runId;
  public final int orgId;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.WRITE_DONE;
  }
}
