package com.nexla.control.coordination;

import lombok.Data;

@Data
@Deprecated
public class RequestSinkOffsetCoordination implements CoordinationMessage {

  private final String messageId;
  private final int sinkId;
  private final long ts;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.REQUEST_SINK_OFFSET;
  }
}
