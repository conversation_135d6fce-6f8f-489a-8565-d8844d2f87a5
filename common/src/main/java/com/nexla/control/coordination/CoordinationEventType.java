package com.nexla.control.coordination;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum CoordinationEventType {
  COMMIT_SINK_OFFSET,
  FLUSHED_SINK,
  HEARTBEAT_CONNECTOR,
  HEARTBEAT_CONNECTOR_STATE,
  REQUEST_SINK_OFFSET,
  SET_FILE_STATUS,
  @Deprecated
  LISTING_IN_PROGRESS, // Kept for backward compatibility with 2.15 version
  WRITE_DONE,
  READ_DONE,
  DATASET_TRACE,
  TX_DONE,
  CONNECTOR_SYNCHRONIZATION,
  RESOURCE_METRICS_DONE,
  NTM_PIPELINE_NODE_ASSIGNMENT;

  @SuppressWarnings("unused")
  @JsonCreator
  public static CoordinationEventType fromString(String string) {
    return CoordinationEventType.valueOf(string.toUpperCase());
  }
}
