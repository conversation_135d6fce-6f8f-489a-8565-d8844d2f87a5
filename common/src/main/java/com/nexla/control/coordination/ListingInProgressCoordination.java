package com.nexla.control.coordination;

import lombok.Data;

@Data
public class ListingInProgressCoordination implements CoordinationMessage {

  public final String messageId;
  public final int resourceId;
  public final Long timeMs;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.LISTING_IN_PROGRESS;
  }
}
