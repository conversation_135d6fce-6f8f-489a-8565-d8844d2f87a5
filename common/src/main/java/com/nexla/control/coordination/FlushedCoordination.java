package com.nexla.control.coordination;

import com.nexla.common.ResourceType;
import java.util.Set;
import lombok.Data;

@Data
public class FlushedCoordination implements CoordinationMessage {

  private final String messageId;
  private final ResourceType resourceType;
  private final Integer resourceId;
  private final Set<Long> runIds;
  private final Long timeMs;
  private final Long createdAt = System.currentTimeMillis();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.FLUSHED_SINK;
  }
}
