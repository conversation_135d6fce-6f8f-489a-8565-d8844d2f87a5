package com.nexla.control.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import com.nexla.control.ControlMessageFlowAttachment;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.*;

@Data
@AllArgsConstructor
public class SourceControlMessage implements ControlMessage {

  private UUID messageId;

  @JsonProperty("resource_id")
  private Integer resourceId;

  @JsonProperty("event_type")
  private ControlEventType eventType;

  @JsonProperty("connection_type")
  public ConnectionType connectionType; // todo check usage

  @Setter private String origin;

  private Map<String, String> context;

  @JsonProperty("resource_json")
  @Setter
  private Optional<Map<String, Object>> resourceJson = Optional.empty();

  @JsonProperty("flow")
  private Optional<ControlMessageFlowAttachment> flow;

  private final Long createdAt = System.currentTimeMillis();

  @Override
  public ControlResourceType getResourceType() {
    return ControlResourceType.SOURCE;
  }

  @Override
  public ControlEventType getEventType() {
    return eventType;
  }

  @Override
  public Integer getResourceId() {
    return resourceId;
  }
}
