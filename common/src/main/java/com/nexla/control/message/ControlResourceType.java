package com.nexla.control.message;

import com.nexla.common.ResourceType;

public enum ControlResourceType {
  SOURCE,
  SINK,
  CONNECTOR,
  DATASET,
  DATA_CREDENTIALS,
  FLOW_NODE,
  NOTIFICATION_SETTING,
  FLOW_RUNTIME_CONFIG,
  ORG;

  public boolean isFlowResource() {
    switch (this) {
      case SOURCE:
      case SINK:
      case DATASET:
      case DATA_CREDENTIALS:
        return true;
      default:
        return false;
    }
  }

  public ResourceType toResourceType() {
    switch (this) {
      case SOURCE:
        return ResourceType.SOURCE;
      case SINK:
        return ResourceType.SINK;
      case DATASET:
        return ResourceType.DATASET;
      case DATA_CREDENTIALS:
        return ResourceType.CREDENTIALS;
      case NOTIFICATION_SETTING:
        return ResourceType.NOTIFICATION_SETTING;
      case FLOW_NODE:
        return ResourceType.FLOW;
      case ORG:
        return ResourceType.ORG;
      default:
        throw new IllegalArgumentException("Not supported conversion for " + this);
    }
  }

  public static ControlResourceType fromResourceType(ResourceType rt) {
    switch (rt) {
      case SOURCE:
        return ControlResourceType.SOURCE;
      case SINK:
        return ControlResourceType.SINK;
      case DATASET:
        return ControlResourceType.DATASET;
      case CREDENTIALS:
        return ControlResourceType.DATA_CREDENTIALS;
      case FLOW:
        return ControlResourceType.FLOW_NODE;
      default:
        throw new IllegalArgumentException("Not supported conversion for " + rt);
    }
  }
}
