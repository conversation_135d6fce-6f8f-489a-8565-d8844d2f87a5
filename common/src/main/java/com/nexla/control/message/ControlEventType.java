package com.nexla.control.message;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum ControlEventType {
  CREATE,
  ACTIVATE,
  DELETE,
  PAUSE,
  UPDATE,
  RUN_NOW,
  SCHEMA_UPDATE,
  DETECT_SCHEMA, // left for backward compatibility
  STATE_CHANGE, // left for backward compatibility,
  SERVICE_ON_COMPLETION;

  @SuppressWarnings("unused")
  @JsonCreator
  public static ControlEventType fromString(String string) {
    return ControlEventType.valueOf(string.toUpperCase());
  }
}
