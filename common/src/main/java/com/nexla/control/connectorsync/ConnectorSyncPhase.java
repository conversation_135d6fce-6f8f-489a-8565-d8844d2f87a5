package com.nexla.control.connectorsync;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

@Getter
public enum ConnectorSyncPhase {

  /**
   * Use order to specify logical sequence between phases Do not modify the order of already created
   * phases
   */
  UNKNOWN,

  IN<PERSON><PERSON><PERSON>IZED,

  READ_STARTED,
  READ_FINISHED,

  <PERSON>IT<PERSON>_STARTED,
  WRITE_FINISHED,

  FLUSH_STARTED,
  FLUSH_FINISHED,

  FINALIZED;

  @JsonCreator
  public static ConnectorSyncPhase fromString(String key) {
    return ConnectorSyncPhase.valueOf(key.toUpperCase());
  }
}
