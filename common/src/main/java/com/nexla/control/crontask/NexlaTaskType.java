package com.nexla.control.crontask;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum NexlaTaskType {
  LISTING_LIST_SOURCE(false),
  LISTING_DELETE_LISTING(true),
  LISTING_DETECT_SCHEMA(true),
  LISTING_REFRESH_TOKENS(false),
  LISTING_FIX_ABANDONED(false),
  DUMMY(false),
  SCRIPT_RUNNER_RUN(false),
  SCRIPT_RUNNER_STOP(true),
  NODE_TASK_MANAGER_RUN(false),
  CTRL_TASK(true),
  ;

  public final Boolean
      important; // true for messages which should be processed in sync manner, keeping kafka

  // offsets order

  NexlaTaskType(Boolean important) {
    this.important = important;
  }

  @SuppressWarnings("unused")
  @JsonCreator
  public static NexlaTaskType fromString(String string) {
    return NexlaTaskType.valueOf(string.toUpperCase());
  }
}
