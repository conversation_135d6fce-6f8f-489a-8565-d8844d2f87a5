package com.nexla.control.crontask;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.nexla.control.crontask.messages.NodeTaskManagerTask;
import com.nexla.control.crontask.messages.ScriptRunRequestTask;
import com.nexla.control.crontask.messages.ScriptRunStopTask;
import com.nexla.control.crontask.messages.listing.*;
import com.nexla.control.message.MessageCreatedAt;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "taskType")
@JsonSubTypes({
  @JsonSubTypes.Type(value = ListSourceTask.class, name = "LISTING_LIST_SOURCE"),
  @JsonSubTypes.Type(value = DeleteListingTask.class, name = "LISTING_DELETE_LISTING"),
  @JsonSubTypes.Type(value = DetectSchemaTask.class, name = "LISTING_DETECT_SCHEMA"),
  @JsonSubTypes.Type(value = FixAbandonedTask.class, name = "LISTING_FIX_ABANDONED"),
  @JsonSubTypes.Type(value = DummyTask.class, name = "DUMMY"),
  @JsonSubTypes.Type(value = ScriptRunRequestTask.class, name = "SCRIPT_RUNNER_RUN"),
  @JsonSubTypes.Type(value = ScriptRunStopTask.class, name = "SCRIPT_RUNNER_STOP"),
  @JsonSubTypes.Type(value = CtrlEventTask.class, name = "CTRL_TASK"),
  @JsonSubTypes.Type(value = NodeTaskManagerTask.class, name = "NODE_TASK_MANAGER_RUN"),
  @JsonSubTypes.Type(value = RefreshTokensTask.class, name = "LISTING_REFRESH_TOKENS")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public interface TaskRequestMessage extends MessageCreatedAt {

  String getMessageId();

  @JsonIgnore
  NexlaTaskType getTaskType();

  Long getTimestamp();

  default String key() {
    return null;
  }
}
