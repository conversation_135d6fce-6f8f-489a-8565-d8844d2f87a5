package com.nexla.control.crontask;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Getter
@NoArgsConstructor
public class TaskCompleteMessage {

  private String taskId;
  private NexlaTaskType taskType;
  private Long timestamp;
  private Long processingTime;
  private String message;
  private TaskRequestMessage request;
  private NexlaTaskStatus status;
  private String nodeId;

  public TaskCompleteMessage(
      Long startedTime,
      String message,
      TaskRequestMessage request,
      NexlaTaskStatus status,
      String nodeId) {
    this.taskType = request.getTaskType();
    this.taskId = request.getMessageId();
    this.timestamp = System.currentTimeMillis();
    this.processingTime = System.currentTimeMillis() - startedTime;
    this.message = message;
    this.request = request;
    this.status = status;
    this.nodeId = nodeId;
  }
}
