package com.nexla.control.crontask.messages;

import com.nexla.control.crontask.NexlaTaskType;
import com.nexla.control.crontask.TaskRequestMessage;
import lombok.Data;

@Data
public class ScriptRunStopTask implements TaskRequestMessage {

  @Override
  public NexlaTaskType getTaskType() {
    return NexlaTaskType.SCRIPT_RUNNER_STOP;
  }

  private final String messageId;
  private final Long timestamp;
  private final Integer dataSourceId;
  private final Long createdAt = System.currentTimeMillis();
}
