package com.nexla.control.crontask.messages;

import com.nexla.control.crontask.NexlaTaskType;
import com.nexla.control.crontask.TaskRequestMessage;
import lombok.Data;

@Data
public class NodeTaskManagerTask implements TaskRequestMessage {

  @Override
  public NexlaTaskType getTaskType() {
    return NexlaTaskType.NODE_TASK_MANAGER_RUN;
  }

  private final String messageId;
  private final Long timestamp;
  private final Long createdAt = System.currentTimeMillis();
}
