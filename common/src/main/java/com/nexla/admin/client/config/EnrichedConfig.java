package com.nexla.admin.client.config;

import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;

public interface EnrichedConfig {

  @AllArgsConstructor
  class EnrichSinkParams {
    final String credentialsSource;

    final Optional<String> vaultHost;
    final Optional<String> vaultToken;

    final Optional<String> secretManagerRegion;
    final Optional<String> secretManagerAccessKey;
    final Optional<String> secretManagerSecretKey;
    final Optional<String> secretNames;
  }

  @Data
  class EnrichSourceParams {
    final Optional<String> vaultHost;
    final Optional<String> vaultToken;

    final int datasetPartitions;
    final int datasetReplication;

    final String credentialsSource;
    final Optional<String> secretManagerRegion;
    final Optional<String> secretManagerAccessKey;
    final Optional<String> secretManagerSecretKey;
    final Optional<String> secretManagerRoleArn;
    final Optional<String> secretManagerIdentityTokenFile;
    final Optional<String> secretNames;
  }
}
