package com.nexla.common.time;

import static java.util.Arrays.stream;
import static java.util.stream.Collectors.toMap;
import static org.joda.time.DateTimeFieldType.dayOfMonth;
import static org.joda.time.DateTimeFieldType.hourOfDay;
import static org.joda.time.DateTimeFieldType.minuteOfHour;
import static org.joda.time.DateTimeFieldType.monthOfYear;
import static org.joda.time.DateTimeFieldType.secondOfMinute;
import static org.joda.time.DateTimeFieldType.year;

import java.util.Map;
import java.util.function.BiFunction;
import org.joda.time.DateTime;
import org.joda.time.DateTimeFieldType;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

public enum NexlaTimeUnit {
  YYYY(VarUtils.YYYY, year(), DateTime::plusYears),

  MON(VarUtils.MON, monthOfYear(), DateTime::plusMonths),

  DD(VarUtils.DD, dayOfMonth(), DateTime::plusDays),

  HH(VarUtils.HH, hourOfDay(), DateTime::plusHours),

  MIN(VarUtils.MIN, minuteOfHour(), DateTime::plusMinutes),

  SEC(VarUtils.SEC, secondOfMinute(), DateTime::plusSeconds),
  ;

  public static NexlaTimeUnit findByPattern(String pattern) {
    return TIME_UNIT_BY_PATTERN.get(pattern);
  }

  public static final Map<String, NexlaTimeUnit> TIME_UNIT_BY_PATTERN =
      stream(values()).collect(toMap(e -> e.pattern, e -> e));

  public final String pattern;
  public final BiFunction<DateTime, Integer, DateTime> incrementFunction;

  private final DateTimeFieldType fieldType;
  private final DateTimeFormatter formatter;

  NexlaTimeUnit(
      String pattern,
      DateTimeFieldType fieldType,
      BiFunction<DateTime, Integer, DateTime> incrementFunction) {
    this.pattern = pattern;
    this.fieldType = fieldType;
    this.incrementFunction = incrementFunction;
    this.formatter = DateTimeFormat.forPattern(pattern);
  }

  public String format(DateTime dateTime) {
    return formatter.withZone(dateTime.getZone()).print(dateTime);
  }

  public Integer extractUnit(DateTime dateTime) {
    return dateTime.get(fieldType);
  }
}
