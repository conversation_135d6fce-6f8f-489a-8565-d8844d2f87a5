package com.nexla.common;

import static java.util.Optional.empty;

import java.io.File;
import java.util.Optional;
import lombok.Getter;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Getter
public class NexlaSslContext {
  private static final Logger LOGGER = LoggerFactory.getLogger(NexlaSslContext.class);

  public static NexlaSslContext NOSSL_CONTEXT =
      new NexlaSslContext(
          false, false, empty(), empty(), empty(), empty(), empty(), empty(), empty(), empty(),
          empty(), empty(), empty());
  private final Optional<SSLCertificateStore> serverKeystoreStore;
  private final Optional<SSLCertificateStore> serverTruststoreStore;
  private final Optional<SSLCertificateStore> clientKeystoreStore;
  private final Optional<SSLCertificateStore> clientTruststoreStore;
  private final Optional<String> clientPemCert;

  @SneakyThrows
  protected NexlaSslContext(
      boolean requireSsl,
      boolean requireClientSsl,
      Optional<String> serverP12,
      Optional<String> serverP12Path,
      Optional<String> serverTruststoreP12,
      Optional<String> serverTruststoreP12Path,
      Optional<String> serverPassword,
      Optional<String> clientCert,
      Optional<String> clientKeystoreP12,
      Optional<String> clientKeystoreP12Path,
      Optional<String> clientTruststoreP12,
      Optional<String> clientTruststoreP12Path,
      Optional<String> clientPassword) {
    this.serverKeystoreStore = keyStore(requireSsl, serverP12, serverP12Path, serverPassword);
    this.serverTruststoreStore =
        keyStore(requireSsl, serverTruststoreP12, serverTruststoreP12Path, serverPassword);

    this.clientKeystoreStore =
        keyStore(requireClientSsl, clientKeystoreP12, clientKeystoreP12Path, clientPassword);
    this.clientTruststoreStore =
        keyStore(requireClientSsl, clientTruststoreP12, clientTruststoreP12Path, clientPassword);

    this.clientPemCert =
        clientCert.map(
            x ->
                x.replaceAll("-----BEGIN CERTIFICATE-----", "-----BEGINCERTIFICATE-----")
                    .replaceAll("-----END CERTIFICATE-----", "-----ENDCERTIFICATE-----")
                    .replaceAll(" ", "\n")
                    .replaceAll("-----BEGINCERTIFICATE-----", "-----BEGIN CERTIFICATE-----")
                    .replaceAll("-----ENDCERTIFICATE-----", "-----END CERTIFICATE-----"));
  }

  private Optional<SSLCertificateStore> keyStore(
      boolean enabled, Optional<String> inmem, Optional<String> path, Optional<String> optPwd) {
    Optional<SSLCertificateStore> keyAsFile =
        path.flatMap(p -> optPwd.map(pwd -> new SSLCertificateStore(new File(p), pwd)));

    Optional<SSLCertificateStore> result =
        inmem
            .flatMap(
                p12 -> optPwd.map(pwd -> new SSLCertificateStore(p12.replaceAll(" ", ""), pwd)))
            .or(() -> keyAsFile)
            .filter(it -> enabled);
    if (enabled && result.isEmpty()) {
      LOGGER.error(
          "Potentially configuration issue: key store was enabled, but not built",
          new Exception("Stack trace"));
    }
    return result;
  }

  @Override
  public String toString() {
    return "NexlaSslContext{"
        + "serverKeystoreStore="
        + serverKeystoreStore
        + ", serverTruststoreStore="
        + serverTruststoreStore
        + ", clientKeystoreStore="
        + clientKeystoreStore
        + ", clientTruststoreStore="
        + clientTruststoreStore
        + ", clientPemCert="
        + clientPemCert
        + '}';
  }

  public static Builder newBuilder() {
    return new Builder();
  }

  public static class Builder {
    private boolean requireClientSsl = false;
    private boolean requireServerSsl = false;
    private Optional<String> serverP12 = Optional.empty();
    private Optional<String> serverP12Path = Optional.empty();
    private Optional<String> serverTruststoreP12 = Optional.empty();
    private Optional<String> serverTruststoreP12Path = Optional.empty();
    private Optional<String> serverPassword = Optional.empty();
    private Optional<String> clientCert = Optional.empty();
    private Optional<String> clientKeystoreP12 = Optional.empty();
    private Optional<String> clientKeystoreP12Path = Optional.empty();
    private Optional<String> clientTruststoreP12 = Optional.empty();
    private Optional<String> clientTruststoreP12Path = Optional.empty();
    private Optional<String> clientPassword = Optional.empty();

    public Builder withServerTLS(
        boolean requireServerSsl,
        Optional<String> serverP12,
        Optional<String> serverP12Path,
        Optional<String> serverTruststoreP12,
        Optional<String> serverTruststoreP12Path,
        Optional<String> serverPassword) {
      this.requireServerSsl = requireServerSsl;
      this.serverP12 = serverP12;
      this.serverP12Path = serverP12Path;
      this.serverTruststoreP12 = serverTruststoreP12;
      this.serverTruststoreP12Path = serverTruststoreP12Path;
      this.serverPassword = serverPassword;

      return this;
    }

    @Deprecated
    public Builder withClientCert(Optional<String> clientCert) {
      this.clientCert = clientCert;
      return this;
    }

    public Builder withClientSsl(
        boolean requireClientSsl,
        Optional<String> clientKeystoreP12,
        Optional<String> clientKeystoreP12Path,
        Optional<String> clientTruststoreP12,
        Optional<String> clientTruststoreP12Path,
        Optional<String> clientPassword) {
      this.requireClientSsl = requireClientSsl;
      this.clientKeystoreP12 = clientKeystoreP12;
      this.clientKeystoreP12Path = clientKeystoreP12Path;
      this.clientTruststoreP12 = clientTruststoreP12;
      this.clientTruststoreP12Path = clientTruststoreP12Path;
      this.clientPassword = clientPassword;

      return this;
    }

    public NexlaSslContext build() {
      return new NexlaSslContext(
          requireServerSsl,
          requireClientSsl,
          serverP12,
          serverP12Path,
          serverTruststoreP12,
          serverTruststoreP12Path,
          serverPassword,
          clientCert,
          clientKeystoreP12,
          clientKeystoreP12Path,
          clientTruststoreP12,
          clientTruststoreP12Path,
          clientPassword);
    }
  }
}
