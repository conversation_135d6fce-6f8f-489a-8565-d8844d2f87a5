package com.nexla.common.license;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@AllArgsConstructor
public class NexlaLicense {

  @JsonProperty("domain")
  private String domain;

  @JsonProperty("expires_timestamp")
  private Long expiresTimestamp;

  @JsonProperty("nexla_contact_email")
  private String nexlaContactEmail;

  @JsonProperty("client_contact_email")
  private String clientContactEmail;

  @JsonProperty("created_timestamp")
  private Long createdTimestamp;
}
