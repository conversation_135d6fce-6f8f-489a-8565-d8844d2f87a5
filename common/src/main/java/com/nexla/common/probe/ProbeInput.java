package com.nexla.common.probe;

import static com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY;
import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.CREDS_ID;
import static com.nexla.common.NexlaConstants.NEXLA_CREDS_ENC;
import static com.nexla.common.NexlaConstants.NEXLA_CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaConstants.SINK_TYPE;
import static com.nexla.common.NexlaConstants.SOURCE_ID;
import static com.nexla.common.NexlaConstants.SOURCE_TYPE;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.ConnectionType;
import java.util.HashMap;
import java.util.Map;
import one.util.streamex.EntryStream;
import org.apache.commons.lang3.builder.ToStringBuilder;

public class ProbeInput {

  private int credsId;
  private String credsEnc;
  private String credsEncIv;

  private Map<String, String> params = new HashMap<>();

  public ProbeInput(int credsId, String credsEnc, String credsEncIv, Map<String, String> params) {
    this.credsId = credsId;
    this.credsEnc = credsEnc;
    this.credsEncIv = credsEncIv;
    this.params = params;
  }

  // todo workaround not to change API
  public Map<String, String> getAllParams(
      String decryptKey,
      ConnectionType connectionType,
      String nexlaCredsEnc,
      String nexlaCredsEncIv) {
    HashMap<String, String> result = new HashMap<>(params);
    result.put(CREDENTIALS_DECRYPT_KEY, decryptKey);
    result.put(CREDS_ENC, credsEnc);
    result.put(CREDS_ENC_IV, credsEncIv);
    result.put(CREDS_ID, String.valueOf(credsId));

    result.put(SOURCE_TYPE, connectionType.name());
    result.putIfAbsent(SOURCE_ID, "-1");
    result.put(SINK_TYPE, connectionType.name());
    result.putIfAbsent(SINK_ID, "-1");
    result.put(CREDENTIALS_TYPE, connectionType.name());
    result.put(NEXLA_CREDS_ENC, nexlaCredsEnc);
    result.put(NEXLA_CREDS_ENC_IV, nexlaCredsEncIv);

    return result;
  }

  public ProbeInput(ProbeInput other) {
    this(
        other.credsId,
        other.getCredsEnc(),
        other.getCredsEncIv(),
        new HashMap<>(other.getParams()));
  }

  public ProbeInput() {}

  public String getCredsEnc() {
    return credsEnc;
  }

  public void setCredsEnc(String credsEnc) {
    this.credsEnc = credsEnc;
  }

  public String getCredsEncIv() {
    return credsEncIv;
  }

  public void setCredsEncIv(String credsEncIv) {
    this.credsEncIv = credsEncIv;
  }

  public Map<String, String> getParams() {
    return params;
  }

  public void setParams(Map<String, Object> params) {
    Map<String, String> stringifyParams =
        EntryStream.of(params)
            .mapValues(
                val -> {
                  if (val instanceof Iterable || val instanceof Map) {
                    return JsonUtils.toJsonString(val);
                  }
                  return val.toString();
                })
            .toMap();
    this.params = stringifyParams;
  }

  public int getCredsId() {
    return credsId;
  }

  public void setCredsId(int credsId) {
    this.credsId = credsId;
  }

  @Override
  public String toString() {
    return ToStringBuilder.reflectionToString(this);
  }
}
