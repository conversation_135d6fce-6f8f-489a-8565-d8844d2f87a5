package com.nexla.common.probe;

import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import one.util.streamex.StreamEx;

public interface ProbeSampleResult<U> {

  ProbeSampleResult EMPTY_SAMPLE =
      new ProbeSampleResult<Object>() {

        @Override
        public boolean isEmpty() {
          return true;
        }

        @Override
        public boolean isBinary() {
          return false;
        }

        @Override
        public ProbeSampleResult limitOutput(
            Optional<Integer> skipLines, Optional<Integer> countLines) {
          return this;
        }

        @Override
        public Map<String, Object> httpResultMap() {
          return emptyMap();
        }

        @Override
        public List<ProbeSampleResultEntry<Object>> getData() {
          return emptyList();
        }
      };

  ProbeSampleResult BINARY_SAMPLE =
      new ProbeSampleResult<Object>() {

        @Override
        public boolean isEmpty() {
          return false;
        }

        @Override
        public boolean isBinary() {
          return true;
        }

        @Override
        public ProbeSampleResult limitOutput(
            Optional<Integer> skipLines, Optional<Integer> countLines) {
          return this;
        }

        @Override
        public Map<String, Object> httpResultMap() {
          return emptyMap();
        }

        @Override
        public List<ProbeSampleResultEntry<Object>> getData() {
          return emptyList();
        }
      };

  default boolean isBinary() {
    List<ProbeSampleResultEntry<U>> resultList = getData();
    return resultList.stream().anyMatch(t -> t.isBinary);
  }

  default List<U> getResult() {
    return StreamEx.of(getData()).map(t -> t.data).collect(Collectors.toList());
  }

  default boolean isEmpty() {
    return getData().isEmpty();
  }

  ProbeSampleResult<U> limitOutput(Optional<Integer> skipLines, Optional<Integer> countLines);

  Map<String, Object> httpResultMap();

  List<ProbeSampleResultEntry<U>> getData();
}
