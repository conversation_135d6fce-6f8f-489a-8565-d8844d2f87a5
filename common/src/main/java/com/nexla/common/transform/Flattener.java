package com.nexla.common.transform;

import java.util.Deque;
import java.util.Iterator;
import java.util.LinkedHashMap;

public class Flattener {

  public static final String DEFAULT_DELIMITER = ".";
  public static Flattener INSTANCE = new Flattener(DEFAULT_DELIMITER);

  private final String delimiter;

  public Flattener(String delimiter) {
    this.delimiter = delimiter;
  }

  /**
   * Converts the possibly-nested input JSON into a flattened representation with nested keys
   * represented in dot notation.
   *
   * @param input the JSON object to flatten, in plain Jackson LinkedHashMap<String, Object> style.
   */
  public LinkedHashMap<String, Object> flatten(Object input) {
    LinkedHashMap<String, Object> flattened = new LinkedHashMap<>();

    new ObjectWalker()
        .walk(
            input,
            (path, o) -> {
              flattened.put(keyFromPathStack(path), o);
            });

    return flattened;
  }

  /**
   * Converts the possibly-nested input JSON into an expanded representation with all keys
   * represented in dot notation, regardless of nesting level.
   *
   * @param input the JSON object to expand, in plain Jackson LinkedHashMap<String, Object> style.
   */
  public LinkedHashMap<String, Object> expand(Object input) {
    LinkedHashMap<String, Object> expanded = new LinkedHashMap<>();

    new ObjectWalker()
        .expandWalk(
            input,
            (path, o) -> {
              expanded.put(keyFromPathStack(path), o);
            });

    return expanded;
  }

  private String keyFromPathStack(Deque<String> pathStack) {
    Iterator<String> it = pathStack.descendingIterator();
    StringBuilder key = new StringBuilder();
    while (it.hasNext()) {
      key.append(key.length() > 0 ? delimiter : "").append(it.next());
    }
    return key.toString();
  }
}
