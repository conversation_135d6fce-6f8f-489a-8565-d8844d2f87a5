package com.nexla.common;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.LinkedHashMap;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
public class NexlaMessage {

  private LinkedHashMap<String, Object> rawMessage;

  private NexlaMetaData nexlaMetaData = new NexlaMetaData();

  public NexlaMessage(LinkedHashMap<String, Object> rawMessage, NexlaMetaData nexlaMetaData) {
    this.rawMessage = rawMessage;
    this.nexlaMetaData = nexlaMetaData;
  }

  public NexlaMessage(LinkedHashMap<String, Object> rawMessage) {
    this.rawMessage = rawMessage;
  }

  public NexlaMessage() {}

  public LinkedHashMap<String, Object> getRawMessage() {
    return rawMessage;
  }

  public void setRawMessage(LinkedHashMap<String, Object> rawMessage) {
    this.rawMessage = rawMessage;
  }

  public NexlaMetaData getNexlaMetaData() {
    return nexlaMetaData;
  }

  public void setNexlaMetaData(NexlaMetaData nexlaMetaData) {
    this.nexlaMetaData = nexlaMetaData;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    NexlaMessage that = (NexlaMessage) o;
    return Objects.equals(rawMessage, that.rawMessage)
        && Objects.equals(nexlaMetaData, that.nexlaMetaData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rawMessage, nexlaMetaData);
  }

  @Override
  public String toString() {
    return "NexlaMessage{" + "rawMessage=" + rawMessage + ", nexlaMetaData=" + nexlaMetaData + '}';
  }

  public String toJsonString() {
    return JsonUtils.toJsonString(this);
  }
}
