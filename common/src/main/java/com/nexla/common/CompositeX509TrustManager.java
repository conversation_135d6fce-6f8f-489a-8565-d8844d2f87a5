package com.nexla.common;

import com.google.common.collect.Lists;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import javax.net.ssl.X509TrustManager;
import lombok.Data;
import one.util.streamex.StreamEx;

@Data
public class CompositeX509TrustManager implements X509TrustManager {

  private final List<X509TrustManager> trustManagers;

  @Override
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s)
      throws CertificateException {
    boolean trusted =
        StreamEx.of(trustManagers).anyMatch(x -> checkClientTrustManager(x509Certificates, s, x));

    if (!trusted) {
      throw new CertificateException("None of the TrustManagers trust this certificate chain");
    }
  }

  @Override
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s)
      throws CertificateException {
    boolean trusted =
        StreamEx.of(trustManagers).anyMatch(x -> checkServerTrustManager(x509Certificates, s, x));

    if (!trusted) {
      throw new CertificateException("None of the TrustManagers trust this certificate chain");
    }
  }

  @Override
  public X509Certificate[] getAcceptedIssuers() {

    LinkedList<X509Certificate> certificates = Lists.newLinkedList();
    for (X509TrustManager trustManager : trustManagers) {
      ArrayList<X509Certificate> acceptedIssuers =
          Lists.newArrayList(trustManager.getAcceptedIssuers());
      for (X509Certificate acceptedIssuer : acceptedIssuers) {
        certificates.add(acceptedIssuer);
      }
    }
    return certificates.toArray(new X509Certificate[certificates.size()]);
  }

  private boolean checkClientTrustManager(
      X509Certificate[] x509Certificates, String s, X509TrustManager x) {
    try {
      x.checkClientTrusted(x509Certificates, s);
    } catch (Exception e) {
      return false;
    }
    return true;
  }

  private boolean checkServerTrustManager(
      X509Certificate[] x509Certificates, String s, X509TrustManager x) {
    try {
      x.checkServerTrusted(x509Certificates, s);
    } catch (Exception e) {
      return false;
    }
    return true;
  }
}
