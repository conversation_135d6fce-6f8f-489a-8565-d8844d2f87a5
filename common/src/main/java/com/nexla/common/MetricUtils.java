package com.nexla.common;

import java.util.Map;

public class MetricUtils {
  /** Calculates approximate byte count of map content */
  public static long calcBytes(Map<String, Object> map) {
    return map.entrySet().stream()
        .mapToLong(e -> calcBytes(e.getKey()) + calcBytes(e.getValue()))
        .sum();
  }

  public static int calcBytes(Object value) {
    return value != null ? value.toString().getBytes().length : 0;
  }
}
