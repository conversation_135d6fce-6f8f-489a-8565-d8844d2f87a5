package com.nexla.common.datetime;

import static org.joda.time.DateTimeZone.UTC;

import com.bazaarvoice.jolt.common.Optional;
import com.joestelmach.natty.DateGroup;
import com.joestelmach.natty.Parser;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.function.Supplier;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.pojava.datetime.DateTimeConfig;
import org.pojava.datetime.DateTimeConfigBuilder;
import org.pojava.datetime.IDateTimeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DateTimeUtils {

  private static final Logger logger = LoggerFactory.getLogger(DateTimeUtils.class);

  public static final String ADMIN_API_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

  public static final ZoneId UTC_ZONE_ID = ZoneId.of("UTC");

  public static final TimeZone UTC_TIME_ZONE = TimeZone.getTimeZone("UTC");

  private static DateTimeFormatter PF_1 =
      new DateTimeFormatterBuilder()
          .appendPattern("MM/dd/yyyy[ [HH][:mm][:ss][.SSS]]")
          .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
          .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
          .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
          .toFormatter();
  private static DateTimeFormatter PF_2 =
      new DateTimeFormatterBuilder()
          .appendPattern("MM-dd-yyyy[ [HH][:mm][:ss][.SSS]]")
          .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
          .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
          .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
          .toFormatter();
  private static DateTimeFormatter PF_3 =
      new DateTimeFormatterBuilder()
          .appendPattern("dd/MM/yyyy[ [HH][:mm][:ss][.SSS]]")
          .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
          .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
          .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
          .toFormatter();
  private static DateTimeFormatter PF_4 =
      new DateTimeFormatterBuilder()
          .appendPattern("yyyy/MM/dd[ [HH][:mm][:ss][.SSS]]")
          .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
          .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
          .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
          .toFormatter();
  private static DateTimeFormatter PF_5 =
      new DateTimeFormatterBuilder()
          .appendPattern("yyyy-MM-dd[ [HH][:mm][:ss][.SSS]]")
          .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
          .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
          .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
          .toFormatter();
  private static DateTimeFormatter PF_6 =
      new DateTimeFormatterBuilder()
          .appendPattern("yyyyMMdd")
          .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
          .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
          .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
          .toFormatter();

  private static final int CUTOFF_LENGTH = 8;

  static IDateTimeConfig DATE_TIME_CONFIG;

  static {
    DateTimeConfigBuilder builder = DateTimeConfigBuilder.newInstance();
    builder.setDmyOrder(false);
    DATE_TIME_CONFIG = DateTimeConfig.fromBuilder(builder);
  }

  private static Optional<Long> parseEpochMillisecondsFromString(final String epochStr) {
    if (epochStr.contains(".")) {
      Double epochD = null;
      try {
        epochD = Double.parseDouble(epochStr);
      } catch (Exception ignored) {
      }
      if (epochD == null) {
        return Optional.empty();
      }
      return Optional.of((long) (epochD.doubleValue() * 1000));
    }

    Long epochL = null;
    try {
      epochL = Long.parseUnsignedLong(epochStr);
    } catch (Exception ignored) {
    }

    if (epochL == null) {
      return Optional.empty();
    }

    // epochL could be seconds, milliseconds, microseconds
    // or nanoseconds. One of the first two is much more likely
    // than the latter two, so let's try to distinguish
    // between seconds and milliseconds...
    //
    // January 1, 1980 is 315561600000 milliseconds from
    // the start of epoch. Interpreted as seconds from epoch,
    // 315561600000 would be October 1, 11969, which is
    // reasonably far in the future.
    //
    // So, if our parsed long is > 315561600000, it's fairly
    // safe to treat it as milliseconds. Otherwise, intrepret
    // as seconds.
    Long cutoff = new Long("315561600000");
    epochL = (epochL > cutoff) ? epochL : epochL * 1000;
    return Optional.of(epochL);
  }

  public static Optional<String> iso8601FromEpoch(final Object arg) {
    Long epoch = 0L;
    if (arg instanceof String) {
      Optional<Long> tmp = parseEpochMillisecondsFromString((String) arg);
      if (!tmp.isPresent()) {
        return Optional.empty();
      }
      epoch = tmp.get();
    } else if (arg instanceof Double) {
      epoch = (long) (((Double) arg).doubleValue() * 1000);
    } else if (arg instanceof Integer) {
      epoch = Long.valueOf(((Integer) arg).longValue() * 1000);
    } else if (arg instanceof Long) {
      epoch = (Long) arg;
    } else {
      return Optional.empty();
    }
    Instant instant = Instant.ofEpochMilli(epoch);
    ZonedDateTime zdt = ZonedDateTime.ofInstant(instant, ZoneId.of("UTC"));
    return Optional.of(zdt.format(DateTimeFormatter.ISO_INSTANT));
  }

  private static org.pojava.datetime.DateTime getDateTime(String timestamp) {
    org.pojava.datetime.DateTime dt = new org.pojava.datetime.DateTime(timestamp, DATE_TIME_CONFIG);
    return dt;
  }

  public static Long getEpochMillis(Object argObj) {
    if (argObj instanceof Long) {
      return parseEpochMillisecondsFromString(Long.toString((Long) argObj)).get();
    } else if (argObj instanceof Integer) {
      return parseEpochMillisecondsFromString(Integer.toString((Integer) argObj)).get();
    } else {
      return getDateTime((String) argObj).getSeconds() * 1000;
    }
  }

  /**
   * Calculates the difference between two timestamps Uses parseEpochMillisecondsFromString to
   * handle epoch based timestamps which POJava time parser does not handle properly Options for
   * time unit - second, minute, hour, day, week
   */
  public static Optional<Long> timestampDifference(String argTime, Object argObj1, Object argObj2) {
    Long arg1 = getEpochMillis(argObj1);
    Long arg2 = getEpochMillis(argObj2);

    Instant firstInstant = Instant.ofEpochMilli(arg1);
    Instant secondInstant = Instant.ofEpochMilli(arg2);

    long diff = getChronoUnit(argTime, firstInstant, secondInstant);
    return Optional.of(Math.abs(diff));
  }

  private static long getChronoUnit(String argTime, Instant i1, Instant i2) {
    switch (argTime.toUpperCase()) {
      case "W":
        return ChronoUnit.DAYS.between(i1, i2) / 7;
      case "D":
        return ChronoUnit.DAYS.between(i1, i2);
      case "H":
        return ChronoUnit.HOURS.between(i1, i2);
      case "M":
        return ChronoUnit.MINUTES.between(i1, i2);
      case "S":
        return ChronoUnit.SECONDS.between(i1, i2);
      case "SSS":
        return ChronoUnit.MILLIS.between(i1, i2);
      default:
        throw new IllegalArgumentException();
    }
  }

  public static Optional<Long> epochFromString(final Object arg) {
    Long tmpL = getEpochMillis(arg);
    return Optional.of(tmpL);
  }

  public static Optional<String> iso8601FromString(final Object arg) {
    String input = arg.toString();
    if (!(arg instanceof String)) {
      return Optional.of(input);
    }

    LocalDateTime date = null;

    try {
      date = LocalDateTime.parse(input, PF_1);
    } catch (Exception e) {
      date = null;
    }

    if (date == null) {
      try {
        date = LocalDateTime.parse(input, PF_2);
      } catch (Exception e) {
        date = null;
      }
    }

    if (date == null) {
      try {
        date = LocalDateTime.parse(input, PF_3);
      } catch (Exception e) {
        date = null;
      }
    }

    if (date == null) {
      try {
        date = LocalDateTime.parse(input, PF_4);
      } catch (Exception e) {
        date = null;
      }
    }

    if (date == null) {
      try {
        date = LocalDateTime.parse(input, PF_5);
      } catch (Exception e) {
        date = null;
      }
    }

    if (date == null) {
      try {
        date = LocalDateTime.parse(input, PF_6);
      } catch (Exception e) {
        date = null;
      }
    }

    if (date == null) {
      Parser parser = new Parser();
      List<DateGroup> groups = parser.parse(input);

      for (DateGroup group : groups) {
        List<Date> dates = group.getDates();
        for (Date d : dates) {
          Optional<String> dstr = DateTimeUtils.iso8601FromEpoch(d.getTime());
          if (dstr.isPresent()) {
            return dstr;
          }
        }
      }

      return Optional.empty();
    }

    try {
      String output = date.format(DateTimeFormatter.ISO_DATE_TIME);
      return Optional.of(output);
    } catch (Exception e) {
      return Optional.empty();
    }
  }

  public static Optional<String> extractDatePart(String dateFormat, Object arg) {
    org.pojava.datetime.DateTime dt2 = getDateTime(String.valueOf(arg));
    return Optional.of("" + dt2.toString(dateFormat));
  }

  /**
   * Returns the input date-time string in ISO8601 format, converted from the source timezone to the
   * destination timezone, where the source TZ is determined by input date-time itself, or the
   * srcTzStr param if no TZ information is encoded in the input date-time.
   *
   * @param srcTzStr String specifier for source timezone
   * @param dstTzStr String specifier for destination timezone
   * @param arg Object containing string representation of a date-time. Format is not necessarily
   *     iso8601.
   * @return ISO8601 string representation of input date-time converted to the destination timezone
   *     from either 1) the srcTzStr timezone, if the input date-time is expressed in local time, or
   *     2) the timezone specified by the input date-time.
   */
  public static Optional<String> convertTimeZone(String srcTzStr, String dstTzStr, Object arg) {
    // ZoneId.of() returns a ZoneId that contains the canonical id
    // for the one passed in. The canonical id works with both the
    // TimeZone and ZonedDateTime classes used below.
    // NOTE: ZoneId.of() will throw an exception if the input id
    // cannot be mapped to a known one.
    ZoneId srcZid;
    ZoneId dstZid;

    try {
      srcZid = ZoneId.of(srcTzStr, ZoneId.SHORT_IDS);
      dstZid = ZoneId.of(dstTzStr, ZoneId.SHORT_IDS);
    } catch (Exception e) {
      return Optional.empty();
    }

    TimeZone srcTz = TimeZone.getTimeZone(srcZid.getId());
    TimeZone dstTz = TimeZone.getTimeZone(dstZid.getId());

    // We use Pojava DateTime here because it can parse a variety of input
    // string formats with a format specifier. However, DateTimeUtils output
    // formatting does require an explicit format pattern to get the iso8601
    // format we've standardized on. Instead, create a ZonedDateTime from
    // the extracted Pojava DateTime and use the native formatter.
    org.pojava.datetime.DateTime dt =
        new org.pojava.datetime.DateTime(String.valueOf(arg), srcTz, dstTz);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime ldt = LocalDateTime.parse(dt.toString(), formatter);
    ZonedDateTime zdt;

    try {
      zdt = ZonedDateTime.of(ldt, dstZid.normalized());
    } catch (Exception e) {
      return Optional.empty();
    }

    String result = zdt.format(DateTimeFormatter.ISO_DATE_TIME);
    // We remove the timezone text description, if present, e.g.
    // "2017-01-23T07:52:00-08:00[America/Los_Angeles]" becomes
    // "2017-01-23T07:52:00-08:00"
    int idx = result.indexOf("[");
    if (idx > 0) {
      result = result.substring(0, idx);
    }

    return Optional.of(result);
  }

  public static Optional<Long> epochFromTimeStamp(final String timestamp) {
    try {
      // If input is a number of length greater than 8,
      // we can safely assume that it is not a date and
      // treat it as a millisecond input instead
      // Ex - 20130330 will be parsed as a Date but
      // 201303300 will be parsed as millisecond time
      // In case Date can not be parsed from a string of
      // length less than 8, it will be parsed as milliseconds
      if (timestamp.length() > CUTOFF_LENGTH) {
        return Optional.of(Long.parseLong(timestamp));
      } else {
        return epochFromString(timestamp);
      }
    } catch (Exception e) {
      return epochFromString(timestamp);
    }
  }

  public static DateTime validateTZ(DateTime dayFrom) {
    if (!UTC.equals(dayFrom.getZone())) {
      throw new IllegalStateException("Timezone is not UTC: " + dayFrom.getZone());
    }
    return dayFrom;
  }

  public static DateTime fromMillis(long utcMillis) {
    return new DateTime(utcMillis, UTC);
  }

  public static DateTime nowUTC() {
    return DateTime.now(UTC);
  }

  public static org.joda.time.LocalDateTime jodaNowUtc() {
    return org.joda.time.LocalDateTime.now(UTC);
  }

  public static LocalDateTime nowUtc() {
    return LocalDateTime.now(UTC_ZONE_ID);
  }

  public static long toMillis(LocalDateTime localDateTime) {
    return toMillis(localDateTime, UTC_ZONE_ID);
  }

  public static long toMillis(LocalDateTime localDateTime, ZoneId zoneId) {
    return localDateTime.atZone(zoneId).toInstant().toEpochMilli();
  }

  public static LocalDateTime toLocalDateTime(long epochMillis, ZoneId zoneId) {
    return LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMillis), zoneId);
  }

  public static LocalDateTime truncateToHour(LocalDateTime localDateTime) {
    return localDateTime.truncatedTo(ChronoUnit.HOURS);
  }

  public static LocalDateTime truncateToDay(LocalDateTime localDateTime) {
    return localDateTime.truncatedTo(ChronoUnit.DAYS);
  }

  public static DateTime fromDate(Date date) {
    return new DateTime(date.getTime(), UTC);
  }

  public static java.util.Optional<DateTime> formatEndDate(String dateTo) {
    // Append a day to dateTo if dateTo field has only date value and doesn't has hour/min/sec value
    if (dateTo == null) {
      return java.util.Optional.empty();
    } else if (dateTo.contains(":")) {
      return java.util.Optional.of(new DateTime(dateTo, UTC));
    } else {
      return java.util.Optional.of(new DateTime(dateTo, UTC).plusDays(1));
    }
  }

  public static DateTime truncHour(DateTime dateTime) {
    return dateTime.hourOfDay().roundFloorCopy();
  }

  public static DateTime truncDay(DateTime dateTime) {
    return dateTime.dayOfMonth().roundFloorCopy();
  }

  public static <T> Pair<T, StopWatch> timed(Supplier<T> supplier) {
    StopWatch stopWatch = new StopWatch();
    stopWatch.start();
    T returnValue = supplier.get();
    stopWatch.stop();
    return ImmutablePair.of(returnValue, stopWatch);
  }

  public static <T> T timed(Supplier<T> supplier, String action) {
    Pair<T, StopWatch> result = timed(supplier);
    logger.info("{}, TIME={}", action, result.getRight().toString());
    return result.getLeft();
  }

  public static void timed(Runnable runnable, String action) {
    timed(
        () -> {
          runnable.run();
          return null;
        },
        action);
  }

  public static LocalDateTime toLocalDateTime(org.joda.time.LocalDateTime d) {
    return LocalDateTime.of(
        d.getYear(),
        d.getMonthOfYear(),
        d.getDayOfMonth(),
        d.getHourOfDay(),
        d.getMinuteOfHour(),
        d.getSecondOfMinute(),
        d.getMillisOfSecond() * 1000);
  }

  public static LocalDateTime toLocalDateTime(org.joda.time.DateTime d) {
    return toLocalDateTime(d.toLocalDateTime());
  }

  public static DateTime toJodaDT(LocalDateTime lastModified) {
    Instant javaInstant = lastModified.toInstant(ZoneOffset.UTC);
    org.joda.time.Instant jodaInstant = new org.joda.time.Instant(javaInstant.toEpochMilli());
    return new org.joda.time.DateTime(jodaInstant);
  }
}
