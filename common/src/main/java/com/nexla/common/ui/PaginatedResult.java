package com.nexla.common.ui;

public class PaginatedResult<T> {
  T data;
  Meta meta;

  public PaginatedResult() {}

  public PaginatedResult(T data) {
    this(data, null);
  }

  public PaginatedResult(T data, Meta meta) {
    this.data = data;
    this.meta = meta;
  }

  public PaginatedResult setData(T data) {
    this.data = data;
    return this;
  }

  public PaginatedResult setMeta(int currentPage, int totalCount, int pageSize) {
    Meta meta = new Meta(currentPage, totalCount, pageSize);
    this.meta = meta;
    return this;
  }

  public PaginatedResult setMeta(Meta meta) {
    this.meta = meta;
    return this;
  }

  public T getData() {
    return data;
  }

  public Meta getMeta() {
    return meta;
  }
}
