package com.nexla.common.pool;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;
import static java.lang.System.currentTimeMillis;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.nexla.telemetry.dto.QuietAutoClosable;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ResizableNexlaPool<P> implements NexlaPool<P>, AutoCloseable {
  private static final Logger logger = LoggerFactory.getLogger(ResizableNexlaPool.class);

  public static final ScheduledThreadPoolExecutor REPORTING_EXECUTOR =
      new ScheduledThreadPoolExecutor(
          1,
          new ThreadFactoryBuilder()
              .setNameFormat("resizable-nexla-pool-report-%d")
              .setDaemon(true)
              .build());

  public static final int DEFAULT_EVICT_DELAY = 5 * 60 * 1000;

  private final ConcurrentLinkedDeque<CacheInfo> pooledObjects;
  private final AtomicBoolean isClosed = new AtomicBoolean(false);
  @Getter private final AtomicInteger totalObjects = new AtomicInteger(0);

  private final Supplier<P> elementCreator;
  private final int minimalSize;
  private final int evictDelayMs;
  private final Function<P, Boolean> healthCheck;
  private final Consumer<P> elementFinalizer;

  @Getter private final String poolName;
  private final long createdTs = System.currentTimeMillis();
  private long lastAccessTs = System.currentTimeMillis();
  private final ScheduledFuture<?> reportFuture;
  private List<ScheduledFuture<?>> reportFutures = new ArrayList<>();
  private final ExecutionMetricSet currentPoolMetrics;

  public ResizableNexlaPool(Supplier<P> elementCreator, int minimalSize, int evictDelayMs) {
    this(elementCreator, p -> true, minimalSize, evictDelayMs);
  }

  public ResizableNexlaPool(
      Supplier<P> elementCreator,
      Function<P, Boolean> healthCheck,
      int minimalSize,
      int evictDelayMs) {
    this(elementCreator, healthCheck, minimalSize, evictDelayMs, p -> {});
  }

  public ResizableNexlaPool(
      Supplier<P> elementCreator,
      Function<P, Boolean> healthCheck,
      int minimalSize,
      int evictDelayMs,
      Consumer<P> elementFinalizer) {
    this("pool", elementCreator, healthCheck, minimalSize, evictDelayMs, elementFinalizer);
  }

  public ResizableNexlaPool(
      final String poolNamePrefix,
      Supplier<P> elementCreator,
      Function<P, Boolean> healthCheck,
      int minimalSize,
      int evictDelayMs,
      Consumer<P> elementFinalizer) {
    this.poolName = poolNamePrefix + "#" + System.identityHashCode(this);
    logger.info("Initialize new ResizableNexlaPool {} ({})", this.poolName, minimalSize);
    // Do not set poolName with System.identityHashCode(this) as instanceName in metricSet.
    // It will cause cardinality explosion in Graphana. as due to identityHashCode being used, it
    // will
    // change on each restart.
    this.currentPoolMetrics = metricSet(ResizableNexlaPool.class, "default", poolNamePrefix);
    this.pooledObjects = new ConcurrentLinkedDeque<>();
    this.minimalSize = minimalSize;
    this.elementCreator = elementCreator;
    this.evictDelayMs = evictDelayMs;
    this.healthCheck = healthCheck;
    this.elementFinalizer = elementFinalizer;
    for (int i = 0; i < minimalSize; i++) {
      CacheInfo element = newElement(elementCreator);
      add(element);
    }
    this.reportFuture =
        REPORTING_EXECUTOR.scheduleAtFixedRate(
            () -> {
              logger.info("ResizableNexlaPool {}", this);
              currentPoolMetrics.setGauge("pooledObjects", pooledObjects.size());
              currentPoolMetrics.setGauge("totalObjects", totalObjects.get());
            },
            5,
            5,
            TimeUnit.MINUTES);
    this.addReportFuture(this.reportFuture);
  }

  public void addReportFuture(ScheduledFuture<?> reportFuture) {
    this.reportFutures.add(reportFuture);
  }

  /**
   * Provide client code with pooled object. Client code should explicitly close the pooled object
   * in order to return it back to the pool
   */
  public PooledObject providePooledObject() {
    return new PooledObject();
  }

  /**
   * Execute function with provided pooled object and return the result. Not suitable for
   * asynchronous computations
   */
  @Override
  public <T> T withPooledObject(Function<P, T> fn) {
    try (PooledObject pooledObject = providePooledObject()) {
      return fn.apply(pooledObject.get());
    }
  }

  /** Execute function with provided pooled object. Not suitable for asynchronous computations */
  @Override
  public void withPooledObjectConsumer(Consumer<P> fn) {
    try (PooledObject pooledObject = providePooledObject()) {
      fn.accept(pooledObject.get());
    }
  }

  @SneakyThrows
  private void add(CacheInfo object) {
    if (isClosed.get() || pooledObjects.size() >= minimalSize) {
      closeElement(object);
      return;
    }
    pooledObjects.add(object);
  }

  private CacheInfo take() {
    return currentPoolMetrics.track(
        "take",
        () -> {
          if (isClosed.get()) {
            logger.warn(
                "Taking pooling object from closed ResizableNexlaPool {}",
                this.poolName,
                new Exception("Stack trace"));
          }
          CacheInfo taken;
          do {
            taken = pooledObjects.pollFirst();
            if (taken == null) {
              return newElement(elementCreator);
            }

            if (!this.healthCheck.apply(taken.element)) {
              closeElement(taken);
              taken = null;
            }
          } while (taken == null);

          long ts = currentTimeMillis();
          taken.lastMs = ts;
          this.lastAccessTs = ts;
          removeOldObjects();
          return taken;
        });
  }

  private void removeOldObjects() {
    CacheInfo last;
    long evictThresholdMs = currentTimeMillis() - evictDelayMs;
    while (pooledObjects.size() > minimalSize
        && (last = pooledObjects.peekLast()) != null
        && evictThresholdMs > last.lastMs) {
      final var cacheInfo = pooledObjects.pollLast();
      if (cacheInfo != null
          && last
              == cacheInfo) { // perform extra check just in case the other thread have taken that
        // element
        closeElement(cacheInfo);
      }
    }
  }

  private static final ExecutionMetricSet closeMetrics =
      metricSet(ResizableNexlaPool.class, "close");

  public void close() {
    logger.info("Close ResizableNexlaPool {}", this.poolName);
    this.isClosed.set(true);
    while (!pooledObjects.isEmpty()) {
      CacheInfo cacheInfo = pooledObjects.poll();
      if (cacheInfo != null) {
        closeElement(cacheInfo);
      }
    }
    if (totalObjects.get() != 0) {
      // Not critical, but requires attention. Possibly ResizableNexlaPool object is closed too
      // early
      logger.warn(
          "Some objects of closed ResizableNexlaPool {} are still kept by client code: {}",
          this.poolName,
          totalObjects.get());
      closeMetrics.incCounter("totalObjectsStillKept");
    }
    reportFutures.forEach(scheduledFuture -> scheduledFuture.cancel(false));
    closeSilently(currentPoolMetrics);
  }

  private ResizableNexlaPool<P>.CacheInfo newElement(final Supplier<P> elementCreator) {
    return currentPoolMetrics.track(
        "newElement",
        () -> {
          CacheInfo cacheInfo = new CacheInfo(elementCreator.get(), currentTimeMillis());
          totalObjects.incrementAndGet();
          return cacheInfo;
        });
  }

  private void closeElement(CacheInfo cacheInfo) {
    closeSilently(() -> elementFinalizer.accept(cacheInfo.element));
    totalObjects.decrementAndGet();
  }

  @Override
  public String toString() {
    return "ResizableNexlaPool "
        + this.poolName
        + " (closed="
        + isClosed
        + ", pooledObjects="
        + pooledObjects.size()
        + ", totalObjects="
        + totalObjects
        + ", minimalSize="
        + minimalSize
        + ", evictDelayMs="
        + evictDelayMs
        + ", lastAccessTs="
        + lastAccessTs
        + "("
        + new Date(lastAccessTs)
        + ")"
        + ", createdTs="
        + createdTs
        + "("
        + new Date(createdTs)
        + ")"
        + ')';
  }

  public int getNumberOfPooledObjects() {
    return pooledObjects.size();
  }

  public boolean isIdle(Duration duration) {
    return isIdle(currentTimeMillis() - duration.toMillis());
  }

  public boolean isIdle(long tsThreshold) {
    boolean tsThresholdReached = tsThreshold > lastAccessTs;
    boolean allObjectsReturned = totalObjects.get() <= minimalSize;
    if (tsThresholdReached && !allObjectsReturned) {
      logger.warn(
          "Some objects of idle ResizableNexlaPool {} are still kept by client code: {}",
          this.poolName,
          this);
    }
    return allObjectsReturned && tsThresholdReached;
  }

  @AllArgsConstructor
  class CacheInfo {
    P element;
    long lastMs;
  }

  public class PooledObject implements AutoCloseable {
    private final CacheInfo cacheInfo;
    private final QuietAutoClosable timeMetric;

    PooledObject() {
      this.cacheInfo = take();
      this.timeMetric = currentPoolMetrics.time("providedTime");
    }

    public P get() {
      return cacheInfo.element;
    }

    @Override
    public void close() {
      add(cacheInfo);
      timeMetric.close();
    }
  }
}
