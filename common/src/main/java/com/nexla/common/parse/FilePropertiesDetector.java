package com.nexla.common.parse;

import static com.nexla.common.parse.ParserConfigs.DEFAULT_CHARSET_DETECT_THRESHOLD;
import static java.util.Arrays.asList;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.regex.Pattern.compile;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.io.ByteOrderMark.UTF_16BE;
import static org.apache.commons.io.ByteOrderMark.UTF_16LE;
import static org.apache.commons.io.ByteOrderMark.UTF_32BE;
import static org.apache.commons.io.ByteOrderMark.UTF_32LE;
import static org.apache.commons.io.ByteOrderMark.UTF_8;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.Maps;
import com.ibm.icu.text.CharsetDetector;
import com.ibm.icu.text.CharsetMatch;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Stack;
import java.util.regex.Pattern;
import javax.xml.parsers.DocumentBuilderFactory;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.ByteOrderMark;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.detect.TextDetector;
import org.apache.tika.mime.MediaType;
import org.javatuples.Pair;

public class FilePropertiesDetector {

  private static final int MAX_LENGTH = 131072;
  private static final int MIN_LENGTH = 128;
  private static final double VARIANCE_WEIGHT = 3.0;
  private static final double MATCH_WEIGHT = 1 / 1.5;

  private static final DocumentBuilderFactory DOCUMENT_BUILDER_FACTORY =
      DocumentBuilderFactory.newInstance();
  private static final List<Character> COLUMN_SEPARATORS = asList('|', '^', '\t', ';', ',', ':');
  private static final List<Character> QUOTE_CHARS = asList('~', '\'', '\"', null);
  private static final ByteOrderMark[] BOM_CHARS = {UTF_8, UTF_16BE, UTF_16LE, UTF_32BE, UTF_32LE};

  private static final Pattern TIMESTAMP_REGEX =
      compile(".*(2[0-3]|[01][0-9]):[0-5][0-9]:[0-5][0-9].*");

  private static final Map<Pair<Character, Character>, Pattern> SPLIT_LINE_REGEX =
      StreamEx.of(QUOTE_CHARS)
          .cross(COLUMN_SEPARATORS)
          .mapKeyValue(Pair::with)
          .toMap(pair -> compile(generateRegex(pair.getValue0(), pair.getValue1())));

  private static final Map<Character, Pattern> CHECK_TS_SPLIT_PATTERNS =
      StreamEx.of(QUOTE_CHARS)
          .filter(Objects::nonNull)
          .toMap(
              q -> q,
              q ->
                  compile(
                      "\\s*" + q + "[^" + q + "\\\\]*(?:\\\\.[^" + q + "\\\\]*)*" + q + "\\s*"));

  public static final int MAX_LINES = 50;
  public static final int MAX_DETECTION_LINE_LENGTH = 8 * 1024;

  private final byte[] bomData;
  private final int maxLines;

  @Getter private Charset detectedCharset;

  @SneakyThrows
  public FilePropertiesDetector(byte[] bytes, int maxLines) {
    // Remove BOM's from the stream
    BOMInputStream bomInputStream = new BOMInputStream(new ByteArrayInputStream(bytes), BOM_CHARS);
    this.bomData = readSample(bomInputStream);
    this.maxLines = maxLines;
  }

  @SneakyThrows
  public static byte[] readSample(InputStream inputStream) {
    return readSample(inputStream, MAX_LENGTH);
  }

  @SneakyThrows
  public static byte[] readSample(InputStream inputStream, int maxLength) {
    byte[] buffer = new byte[maxLength];
    int actualLength = IOUtils.read(inputStream, buffer);
    return Arrays.copyOf(buffer, actualLength);
  }

  public static Optional<SampleContentType> getContentTypeByExtension(String extension) {
    switch (extension.toLowerCase()) {
      case "xml":
        return of(SampleContentType.XML);
      case "json":
        return of(SampleContentType.JSON);
      default:
        return of(SampleContentType.TXT);
    }
  }

  public static Optional<SampleContentType> detectContentType(String content, boolean singleLine) {
    if (singleLine) {
      // For most of cases Rest response gives data in single line
      return detectContentType(content);
    } else {
      if (isEmpty(content)) {
        return empty();
      }
      Pair<Optional<String>, Optional<String>> fistLastLine = getFistLastLine(content);
      Optional<SampleContentType> firstLineContentType =
          fistLastLine.getValue0().flatMap(FilePropertiesDetector::detectContentType);
      Optional<SampleContentType> lastLineContentType =
          fistLastLine.getValue1().flatMap(FilePropertiesDetector::detectContentType);
      return firstLineContentType.equals(lastLineContentType) ? firstLineContentType : empty();
    }
  }

  private static Pair<Optional<String>, Optional<String>> getFistLastLine(String content) {
    String[] firstLastLine = new String[2];
    //noinspection ResultOfMethodCallIgnored
    new BufferedReader(new StringReader(content))
        .lines()
        .filter(StringUtils::isNotEmpty)
        .peek(
            line -> {
              if (firstLastLine[0] == null) {
                firstLastLine[0] = line;
              }
              firstLastLine[1] = line;
            })
        .count();

    return Pair.with(ofNullable(firstLastLine[0]), ofNullable(firstLastLine[1]));
  }

  @SneakyThrows
  public Optional<SampleContentType> detectContentType() {
    MediaType detectedType = new TextDetector().detect(new ByteArrayInputStream(bomData), null);
    if (detectedType == MediaType.OCTET_STREAM) {
      return Optional.of(SampleContentType.BINARY);
    } else {
      Charset charset = detectCharset("UTF-8", DEFAULT_CHARSET_DETECT_THRESHOLD);
      BufferedReader bufferedReader =
          new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bomData), charset));
      String content = bufferedReader.lines().collect(joining("\n"));
      return detectContentType(content);
    }
  }

  private static Optional<SampleContentType> detectContentType(String content) {
    if (StringUtils.isEmpty(content)) {
      return empty();
    } else {
      char firstChar = content.charAt(0);
      if (firstChar == '{' || firstChar == '[') {
        return validateJSONString(content);
      } else if (firstChar == '<') {
        return validateXMLString(content);
      } else if (isValidCsvFile(content)) {
        return Optional.of(SampleContentType.TXT);
      } else {
        return empty();
      }
    }
  }

  /**
   * Heuristic algorithm to determine by reading some lines, if this can be a csv file It basically
   * counts the occurrence of the delimiters in each line, if all lines contain same number of
   * delimiters, it's considered to be a csv file.
   */
  private static boolean isValidCsvFile(String content) {
    var lines = content.split("\n");
    var delimiters = List.of(",", ";", "|");
    var notAValidDelimiter = -2;
    var initialDelimiterValue = -1;
    Map<String, Integer> delimiterCounter = Maps.newHashMap();

    for (String line : lines) {

      delimiters.forEach(
          delimiter -> {
            var previousCounter = delimiterCounter.getOrDefault(delimiter, initialDelimiterValue);
            if (previousCounter != notAValidDelimiter) {
              if (line.contains(delimiter)) {
                var count = line.split(delimiter).length;

                if (previousCounter == initialDelimiterValue) {
                  delimiterCounter.put(delimiter, count);
                } else {
                  if (count != previousCounter) {
                    delimiterCounter.put(delimiter, notAValidDelimiter);
                  }
                }
              } else {
                delimiterCounter.put(delimiter, notAValidDelimiter);
              }
            }
          });
    }

    return delimiterCounter.values().stream().anyMatch(it -> it > 0);
  }

  private static Optional<SampleContentType> validateJSONString(String content) {
    try {
      JsonUtils.jsonToObject(content);
      return of(SampleContentType.JSON);
    } catch (Exception exp) {
      return empty();
    }
  }

  private static Optional<SampleContentType> validateXMLString(String content) {
    try {
      InputStream stream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
      DOCUMENT_BUILDER_FACTORY.newDocumentBuilder().parse(stream);
      return of(SampleContentType.XML);
    } catch (Exception exp) {
      return empty();
    }
  }

  /** Converts the read InputStream into a list of Strings */
  private List<String> convertToList() {
    ByteArrayInputStream bais = new ByteArrayInputStream(bomData);
    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(bais));
    List<String> readList = bufferedReader.lines().limit(maxLines).collect(toList());

    // Ignoring the last row - as it might be partial
    if (!readList.isEmpty() && (bomData.length == MAX_LENGTH)) {
      readList.remove(readList.size() - 1);
    }
    return readList;
  }

  /**
   * Creates an InputStreamReader from the InputStream to set the correct charset and read the
   * stream properly. The charset detection will not work for a small stream, because of that the
   * default charset will be used for streams with size less then 128 bytes
   *
   * @param defaultCharset the default charset to be used for small streams or in the detection
   *     failure.
   * @param confidenceThreshold Detected charset will be applied only if confidence is greater than
   *     the threshold.
   * @return the instance of InputStreamReader with the properly charset.
   */
  @SneakyThrows
  public Charset detectCharset(String defaultCharset, int confidenceThreshold) {
    this.detectedCharset = Charset.forName(doDetectCharset(defaultCharset, confidenceThreshold));
    return detectedCharset;
  }

  private String doDetectCharset(String defaultCharset, int confidenceThreshold) {
    final String charset;
    if (bomData.length > MIN_LENGTH && confidenceThreshold > 0) {
      CharsetDetector detector = new CharsetDetector();
      detector.setText(bomData);
      CharsetMatch charsetMatch = detector.detect();
      if ((charsetMatch != null) && (charsetMatch.getConfidence() > confidenceThreshold)) {
        charset = charsetMatch.getName();
      } else {
        charset = defaultCharset;
      }
    } else {
      charset = defaultCharset;
    }
    return charset;
  }

  public CsvCharacteristics detectTextDelimiter() {

    CsvCharacteristics characteristics = new CsvCharacteristics(null, empty());
    double maxMean = 0.0;

    for (Character quoteChar : QUOTE_CHARS) {
      boolean timestampDetected = false;
      for (Character columnSeparator : COLUMN_SEPARATORS) {
        List<Integer> splitLenList = new ArrayList<>();
        double mean = 0.0;
        double variance = 0.0;
        int totMatch = 0;
        boolean possibleMatch = true;

        if (columnSeparator == ':' && timestampDetected) {
          continue;
        }

        Pattern regex = SPLIT_LINE_REGEX.get(Pair.with(quoteChar, columnSeparator));

        Iterator<String> lineIter =
            convertToList().stream().filter(StringUtils::isNotBlank).iterator();

        int linesSize = 0;
        while (lineIter.hasNext()) {
          String line = lineIter.next();
          line = line.substring(0, Math.min(line.length(), MAX_DETECTION_LINE_LENGTH));
          linesSize++;

          int match = 0;
          String[] splitLine = regex.split(line);
          int splitLength = splitLine.length;
          splitLenList.add(splitLength);
          mean += splitLength;
          if (quoteChar != null) {
            if (checkForTimestamp(line, quoteChar)) {
              timestampDetected = true;
            }

            for (String split : splitLine) {
              int numMatches = StringUtils.countMatches(split, quoteChar);
              if (numMatches == 0
                  || (numMatches == 2
                      && split.charAt(0) == quoteChar
                      && split.charAt(split.length() - 1) == quoteChar)) {
                match += numMatches;
              } else {
                possibleMatch = false;
                break;
              }
            }
          } else {
            for (String split : splitLine) {
              if (compareTimestampRegex(split)) {
                timestampDetected = true;
                break;
              }
            }
          }

          int curMatch = match % 2 == 0 && possibleMatch ? match : 0;
          totMatch += curMatch;
        }

        if (quoteChar != null && totMatch == 0) {
          continue;
        }

        mean /= linesSize;

        for (int splitLen : splitLenList) {
          variance += Math.pow((splitLen - mean), 2);
        }

        variance /= linesSize;
        double adjustedMean =
            mean - (VARIANCE_WEIGHT * Math.sqrt(variance)) + (totMatch * MATCH_WEIGHT / linesSize);

        if (adjustedMean >= maxMean) {
          Optional<Character> optQuote = ofNullable(quoteChar);
          characteristics = new CsvCharacteristics(columnSeparator, optQuote);
          maxMean = adjustedMean;
        }
      }
    }
    return characteristics;
  }

  private boolean checkForTimestamp(String s, Character q) {
    String[] stringOutsideQuoteDelimiter = CHECK_TS_SPLIT_PATTERNS.get(q).split(s);
    for (String string : stringOutsideQuoteDelimiter) {
      if (compareTimestampRegex(string)) {
        return true;
      }
    }

    return false;
  }

  private boolean compareTimestampRegex(String s) {
    return TIMESTAMP_REGEX.matcher(s).matches();
  }

  private static String generateRegex(Character q, Character col) {
    if (q == null) {
      return "\\" + col;
    } else {
      return "\\" + col + "(?=(?:[^" + q + "]*" + q + "[^" + q + "]*" + q + ")*[^" + q + "]*$)";
    }
  }

  public Optional<Boolean> isSingleLineJson() {
    BufferedReader bufferedReader =
        new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bomData)));
    return bufferedReader
        .lines()
        .filter(FilePropertiesDetector::isFirstCharBraceOrBracket)
        .findFirst()
        .map(
            line -> {
              try {
                JsonUtils.jsonToMap(line);
                return true;
              } catch (Exception e) {
                return false;
              }
            });
  }

  public static boolean isFirstCharBraceOrBracket(String line) {
    String trimmedLine = line.trim();
    return trimmedLine.startsWith("{") || trimmedLine.startsWith("[");
  }

  /**
   * Returns true if XML mode is entire file Returns false if XML mode is single line
   *
   * @return XML mode
   */
  public boolean isEntireFileXml() {
    List<String> list = convertToList();

    Stack<String> stack = new Stack<>();
    StringBuilder sb = new StringBuilder();

    for (String str : list) {
      sb.setLength(0);
      // getFlag: flag to decide whether to add current character to string or not
      boolean getFlag = false, definitionFlag = false;
      for (int i = 0; i < str.length(); i++) {
        char ch = str.charAt(i);
        // If beginning of tag definition detected, start adding character to string
        // If end of tag definition detected, stop adding character to string
        if (ch == '<') {
          getFlag = true;
          continue;
        } else if (ch == '>') {
          getFlag = false;
          if (sb.toString().isEmpty()) {
            return true;
          }
          // If this is the end tag, check if there is a corresponding start tag in stack
          // If exists, pop stack. Else, tag this as an instance of entire file mode
          // If this is the start tag, add tag element to stack excluding attributes
          // If this is an XML document definition line, ignore and skip to next line
          if (sb.charAt(0) == '/') {
            sb.deleteCharAt(0);
            if (!stack.isEmpty() && stack.peek().equals(sb.toString())) {
              stack.pop();
            }
          } else if (sb.charAt(0) != '?' && sb.charAt(0) != '!') {
            stack.push(sb.toString().split(" ")[0]);
          } else {
            definitionFlag = true;
            break;
          }
          sb.setLength(0);
          continue;
        }
        if (getFlag) {
          sb.append(ch);
        }
      }
      if (definitionFlag) {
        continue;
      }
      return !stack.isEmpty();
    }
    return false;
  }

  @AllArgsConstructor
  public static class CsvCharacteristics {
    public Character delimiter;
    public Optional<Character> quote;
  }
}
