package com.nexla.common.parse;

import com.nexla.common.NexlaMessage;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import one.util.streamex.StreamEx;

public interface BatchParser {
  @Data
  @AllArgsConstructor
  class NexlaStreamDescriptor {
    public final String id;
    public final InputStream is;

    public static NexlaStreamDescriptor from(InputStream is) {
      return new NexlaStreamDescriptor(null, is);
    }
  }

  @Data
  @AllArgsConstructor
  class NexlaMessageWithContext {
    public final NexlaMessage message;
    public final NexlaStreamDescriptor context;
  }

  StreamEx<Optional<NexlaMessageWithContext>> parseMessages(List<NexlaStreamDescriptor> iss);
}
