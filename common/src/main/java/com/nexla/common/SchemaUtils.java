package com.nexla.common;

import com.bazaarvoice.jolt.Sortr;
import com.google.common.base.Charsets;
import com.google.common.collect.Sets;
import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class SchemaUtils {

  private static final Set<String> OMIT_KEYS = Sets.newHashSet("format");

  public static LinkedHashMap<String, Object> addSchemaMetadata(Map<String, Object> schema) {
    // sort for consistent hashing
    LinkedHashMap<String, Object> sortedSchema =
        (LinkedHashMap<String, Object>) Sortr.sortJson(schema);
    sortedSchema.put("$schema", "http://json-schema.org/draft-04/schema#");
    sortedSchema.put("$schema-id", schemaToIntegerHash(sortedSchema));
    return sortedSchema;
  }

  private static Integer schemaToIntegerHash(Map<String, Object> schema) {
    HashFunction hf = Hashing.sha256();
    String schemaStr = schemaToHashableString(schema);
    HashCode hc = hf.newHasher().putString(schemaStr, Charsets.UTF_8).hash();
    return Math.abs(hc.asInt());
  }

  private static String schemaToHashableString(Map<String, Object> schema) {
    return mapToHashableString(schema);
  }

  private static String mapToHashableString(Map<String, Object> map) {
    StringBuilder sb = new StringBuilder();
    sb.append("{");

    boolean first = true;

    for (String key : map.keySet()) {
      if (OMIT_KEYS.contains(key)) {
        continue;
      }
      if (!first) {
        sb.append(";");
      }
      sb.append(key);
      sb.append(":");
      Object value = map.get(key);
      if (value instanceof Map) {
        sb.append(mapToHashableString((Map<String, Object>) value));
      } else if (value instanceof List) {
        sb.append(listToHashableString((List<Object>) value));
      } else if (value instanceof String) {
        sb.append(value.toString());
      }
      first = false;
    }

    sb.append("}");
    return sb.toString();
  }

  private static String listToHashableString(List<Object> list) {
    StringBuilder sb = new StringBuilder();
    sb.append("[");
    for (Object thing : list) {
      if (thing instanceof Map) {
        sb.append(mapToHashableString((Map<String, Object>) thing));
      } else if (thing instanceof List) {
        sb.append(listToHashableString((List<Object>) thing));
      } else if (thing instanceof String) {
        sb.append(thing.toString());
      }
    }
    sb.append("]");
    return sb.toString();
  }
}
