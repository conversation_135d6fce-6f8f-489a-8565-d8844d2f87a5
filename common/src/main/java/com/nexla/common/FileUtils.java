package com.nexla.common;

import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

import com.nexla.common.io.SequenceInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.AntPathMatcher;

public class FileUtils {

  private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class);
  private static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();
  private static final String PATH_SEPARATOR = "/";

  public static void closeSilently(AutoCloseable... objectsToClose) {
    for (AutoCloseable closeable : objectsToClose) {
      try {
        if (closeable != null) {
          closeable.close();
        }
      } catch (Exception e) {
        LOGGER.warn("Error while closing resources: " + e.getMessage());
      }
    }
  }

  public static InputStream joinStream(byte[] prefix, InputStream inputStream) {
    return new SequenceInputStream(new ByteArrayInputStream(prefix), inputStream);
  }

  public static boolean checkWhiteBlackLists(
      Set<String> whiteList, Set<String> blackList, String path) {
    if (isNotEmpty(whiteList)) {
      return whiteList.stream()
          .flatMap(absoluteAndRelative())
          .anyMatch(whitePath -> ANT_PATH_MATCHER.match(whitePath, path));
    } else if (isNotEmpty(blackList)) {
      return !blackList.stream()
          .flatMap(absoluteAndRelative())
          .anyMatch(blackPath -> ANT_PATH_MATCHER.match(blackPath, path));
    } else {
      return true;
    }
  }

  public static boolean checkWhiteBlackListsFirstLayer(
      Set<String> whiteList, Set<String> blackList, String currentPath) {
    if (containsPrefixWithWildcard(whiteList) || containsPrefixWithWildcard(blackList)) {
      return true;
    }
    if (isNotEmpty(whiteList)) {
      return whiteList.stream()
          .anyMatch(
              whitePath ->
                  ANT_PATH_MATCHER.match(getPrefixPath(currentPath, whitePath), currentPath));
    } else if (isNotEmpty(blackList)) {
      return !blackList.stream()
          .anyMatch(
              blackPath ->
                  ANT_PATH_MATCHER.match(getPrefixPath(currentPath, blackPath), currentPath));
    } else {
      return true;
    }
  }

  private static String getPrefixPath(String currentPath, String scannedPath) {
    long scannedPathSlashes = scannedPath.chars().filter(x -> x == '/').count();
    long currentPathSlashes = currentPath.chars().filter(x -> x == '/').count();
    if (scannedPathSlashes > currentPathSlashes) {
      int i = 0;
      int lastIndex = 0;
      while (i < currentPathSlashes) {
        lastIndex = scannedPath.indexOf(PATH_SEPARATOR, lastIndex + 1);
        i++;
      }
      scannedPath = scannedPath.substring(0, lastIndex);
    }
    return scannedPath;
  }

  private static boolean containsPrefixWithWildcard(Set<String> patternList) {
    return patternList.stream().filter(x -> x.startsWith("/**")).findAny().isPresent();
  }

  public static boolean checkWhiteBlackLists(
      Set<String> whiteList, Set<String> blackList, String path, String namespace) {
    String namespacePrefix = namespace + PATH_SEPARATOR;
    Function<String, Stream<String>> considerFilepathWithoutNamespacePrefix =
        filePath -> {
          if (filePath.startsWith(namespacePrefix)) {
            String filePathWithoutNamespacePrefix = filePath.substring(namespacePrefix.length());
            return Stream.of(filePath, filePathWithoutNamespacePrefix);
          } else {
            return Stream.of(filePath);
          }
        };

    if (isNotEmpty(whiteList)) {
      boolean whiteListed =
          whiteList.stream()
              .flatMap(considerFilepathWithoutNamespacePrefix)
              .flatMap(absoluteAndRelative())
              .anyMatch(whitePath -> ANT_PATH_MATCHER.match(whitePath, path));
      return whiteListed;
    } else if (isNotEmpty(blackList)) {
      boolean blackListed =
          blackList.stream()
              .flatMap(considerFilepathWithoutNamespacePrefix)
              .flatMap(absoluteAndRelative())
              .anyMatch(blackPath -> ANT_PATH_MATCHER.match(blackPath, path));
      return !blackListed;
    } else {
      return true;
    }
  }

  public static Function<String, Stream<String>> absoluteAndRelative() {
    return pattern ->
        Stream.of(
            pattern,
            pattern.startsWith(PATH_SEPARATOR)
                ? pattern.substring(PATH_SEPARATOR.length())
                : PATH_SEPARATOR + pattern);
  }

  public static String removeSlashes(String s) {
    return s.replaceAll(PATH_SEPARATOR, "");
  }

  public static boolean filterIfDeltaFile(ConnectionType connectionType, String fileKey) {
    var extension = ".snappy.parquet";
    var deltaTypes =
        Set.of(
            ConnectionType.DELTA_LAKE_S3,
            ConnectionType.DELTA_LAKE_AZURE_DATA_LAKE,
            ConnectionType.DELTA_LAKE_AZURE_BLB);
    return !deltaTypes.contains(connectionType) || fileKey.endsWith(extension);
  }

  @SneakyThrows
  public static String skipFirstLines(
      BufferedReader reader, Integer linesToSkip, boolean doIncludeSkippedLines) {
    IntStream skipLinesIntStream = IntStream.rangeClosed(1, linesToSkip);
    if (doIncludeSkippedLines) {
      return skipLinesIntStream
          .mapToObj(e -> readLineSneaky(reader))
          .collect(Collectors.joining(System.lineSeparator()));
    } else {
      skipLinesIntStream.forEach(e -> readLineSneaky(reader));
      return StringUtils.EMPTY;
    }
  }

  @SneakyThrows
  private static String readLineSneaky(BufferedReader reader) {
    return reader.readLine();
  }
}
