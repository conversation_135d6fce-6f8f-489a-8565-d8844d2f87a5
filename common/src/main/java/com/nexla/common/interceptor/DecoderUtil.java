package com.nexla.common.interceptor;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;

@Slf4j
public class DecoderUtil {

  /**
   * Trying to decode unicode escape sequence like u0440 to Support Russian language with %
   *
   * @param body string in bytes
   * @return decode string
   */
  public static String tryDecode(byte[] body) {
    String bodyString = new String(body);

    String content = StringEscapeUtils.unescapeJava(bodyString);
    content = StringEscapeUtils.unescapeJson(content);
    content = content.replaceAll("%(?![0-9a-fA-F]{2})", "%25");

    try {
      content = URLDecoder.decode(content, StandardCharsets.UTF_8.name());
    } catch (Exception e) {
      log.info("There is an error while BODY DECODE:", e);
    }

    return content;
  }
}
