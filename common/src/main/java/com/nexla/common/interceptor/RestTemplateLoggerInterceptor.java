package com.nexla.common.interceptor;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

public class RestTemplateLoggerInterceptor implements ClientHttpRequestInterceptor {

  private final Logger log = LoggerFactory.getLogger(this.getClass());
  private final boolean logVerbose;
  private final int MAX_LOG_LENGTH = 255;

  public RestTemplateLoggerInterceptor(boolean logVerbose) {
    this.logVerbose = logVerbose;
  }

  @Override
  public ClientHttpResponse intercept(
      HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

    logRequest(request, body);
    ClientHttpResponse response = execution.execute(request, body);
    logResponse(response);
    return response;
  }

  private void logRequest(HttpRequest request, byte[] body) {

    if (logVerbose) {
      log.info(
          "===========================RestTemplate request"
              + " begin============================================");
      log.info("URI         : {}", request.getURI());
      log.info("Method      : {}", request.getMethod());
      log.info("Headers     : {}", request.getHeaders());
      try {
        // to print Russian and encoded Strings
        String decodedBody = DecoderUtil.tryDecode(body);
        log.info("Request body: {}", decodedBody.replace("\n", "\\n"));
      } catch (IllegalArgumentException e) {
        log.warn("Error while decoding request body: {}", e.getMessage());
      }
      log.info(
          "===========================RestTemplate request"
              + " end==============================================");
    }
  }

  private void logResponse(ClientHttpResponse response) throws IOException {

    if (logVerbose) {
      log.info(
          "===========================RestTemplate response"
              + " begin===========================================");
      log.info("Status code  : {}", response.getStatusCode());
      log.info("Status text  : {}", response.getStatusText());
      String content = DecoderUtil.tryDecode(response.getBody().readNBytes(MAX_LOG_LENGTH));
      content = content.replace("\n", "\\n");
      log.info("Response body: {}", content);

      log.info(
          "===========================RestTemplate response"
              + " end=============================================");
    }
  }
}
