package com.nexla.common;

import java.util.ArrayList;
import java.util.Deque;
import java.util.LinkedList;
import java.util.List;

public class FixedSizeQueue<E> {
  private Deque<E> deque;
  private int maxSize;

  public FixedSizeQueue(int maxSize) {
    this.deque = new LinkedList<E>();
    this.maxSize = maxSize;
  }

  public void add(E element) {
    if (deque.size() == maxSize) {
      deque.removeFirst();
    }
    deque.addLast(element);
  }

  public List<E> toList() {
    return new ArrayList<>(deque);
  }

  public int size() {
    return deque.size();
  }
}
