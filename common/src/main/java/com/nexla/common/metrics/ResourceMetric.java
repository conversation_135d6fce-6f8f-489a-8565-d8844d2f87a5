package com.nexla.common.metrics;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ResourceMetric {
  private int id;
  private MetricWithErrors metrics;

  @JsonCreator
  public ResourceMetric(
      @JsonProperty("id") int id, @JsonProperty("metrics") MetricWithErrors metrics) {
    this.id = id;
    this.metrics = metrics;
  }
}
