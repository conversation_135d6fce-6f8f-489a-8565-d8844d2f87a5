package com.nexla.common.metrics;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NexlaSummaryMetric {

  @JsonProperty("total_record_count")
  private Long totalRecordCount;

  @JsonProperty("total_volume")
  private Double totalVolume;

  @JsonProperty("total_file_count")
  private Long totalFileCount;

  @JsonProperty("daily_avg_volume")
  private Double dailyAvgFileSize;

  @JsonProperty("daily_avg_record_count")
  private Double dailyAvgRecordCount;
}
