package com.nexla.common.metrics;

import static org.joda.time.DateTimeZone.UTC;

import lombok.*;
import org.joda.time.DateTime;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@ToString
@Deprecated(forRemoval = true)
public class Metric {
  private long records;
  private long size;
  private long errors;
  private long startInMillis = DateTime.now(UTC).getMillis();

  public Metric(long record, long size, long errors) {
    this.records = record;
    this.size = size;
    this.errors = errors;
  }

  public Metric accumulate(Metric m2) {
    this.records += m2.getRecords();
    this.size += m2.getSize();
    this.errors += m2.getErrors();
    this.startInMillis = Math.min(this.startInMillis, m2.startInMillis);
    return this;
  }

  public void addSize(long size) {
    this.size += size;
  }
}
