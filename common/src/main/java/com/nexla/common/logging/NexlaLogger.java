package com.nexla.common.logging;

import one.util.streamex.StreamEx;
import org.slf4j.Logger;
import org.slf4j.Marker;

public class NexlaLogger implements Logger {

  private final Logger logger;

  private String prefix;

  public NexlaLogger(Logger logger, Object... prefixParts) {
    if (logger instanceof NexlaLogger) {
      NexlaLogger nexlaLogger = (NexlaLogger) logger;
      this.logger = nexlaLogger.logger;
    } else {
      this.logger = logger;
    }
    this.prefix = normalizePrefix(prefixParts);
  }

  public void setPrefix(Object... prefixParts) {
    this.prefix = normalizePrefix(prefixParts);
  }

  public String getPrefix() {
    return prefix;
  }

  private String normalizePrefix(Object... prefixParts) {
    return StreamEx.of(prefixParts).map(a -> a.toString().trim()).joining(", ", "[", "]" + " ");
  }

  @Override
  public String getName() {
    return logger.getName();
  }

  @Override
  public boolean isTraceEnabled() {
    return logger.isTraceEnabled();
  }

  @Override
  public void trace(String msg) {
    if (isTraceEnabled()) {
      logger.trace(prefix + msg);
    }
  }

  @Override
  public void trace(String format, Object arg) {
    if (isTraceEnabled()) {
      logger.trace(prefix + format, arg);
    }
  }

  @Override
  public void trace(String format, Object arg1, Object arg2) {
    if (isTraceEnabled()) {
      logger.trace(prefix + format, arg1, arg2);
    }
  }

  @Override
  public void trace(String format, Object... arguments) {
    if (isTraceEnabled()) {
      logger.trace(prefix + format, arguments);
    }
  }

  @Override
  public void trace(String msg, Throwable t) {
    if (isTraceEnabled()) {
      logger.trace(prefix + msg, t);
    }
  }

  @Override
  public boolean isTraceEnabled(Marker marker) {
    return logger.isTraceEnabled(marker);
  }

  @Override
  public void trace(Marker marker, String msg) {
    if (isTraceEnabled()) {
      logger.trace(marker, prefix + msg);
    }
  }

  @Override
  public void trace(Marker marker, String format, Object arg) {
    if (isTraceEnabled()) {
      logger.trace(marker, prefix + format, arg);
    }
  }

  @Override
  public void trace(Marker marker, String format, Object arg1, Object arg2) {
    if (isTraceEnabled()) {
      logger.trace(marker, prefix + format, arg1, arg2);
    }
  }

  @Override
  public void trace(Marker marker, String format, Object... argArray) {
    if (isTraceEnabled()) {
      logger.trace(marker, prefix + format, argArray);
    }
  }

  @Override
  public void trace(Marker marker, String msg, Throwable t) {
    if (isTraceEnabled()) {
      logger.trace(marker, prefix + msg, t);
    }
  }

  @Override
  public boolean isDebugEnabled() {
    return logger.isDebugEnabled();
  }

  @Override
  public void debug(String msg) {
    if (isDebugEnabled()) {
      logger.debug(prefix + msg);
    }
  }

  @Override
  public void debug(String format, Object arg) {
    if (isDebugEnabled()) {
      logger.debug(prefix + format, arg);
    }
  }

  @Override
  public void debug(String format, Object arg1, Object arg2) {
    if (isDebugEnabled()) {
      logger.debug(prefix + format, arg1, arg2);
    }
  }

  @Override
  public void debug(String format, Object... arguments) {
    if (isDebugEnabled()) {
      logger.debug(prefix + format, arguments);
    }
  }

  @Override
  public void debug(String msg, Throwable t) {
    if (isDebugEnabled()) {
      logger.debug(prefix + msg, t);
    }
  }

  @Override
  public boolean isDebugEnabled(Marker marker) {
    return logger.isDebugEnabled(marker);
  }

  @Override
  public void debug(Marker marker, String msg) {
    if (isDebugEnabled()) {
      logger.debug(marker, prefix + msg);
    }
  }

  @Override
  public void debug(Marker marker, String format, Object arg) {
    if (isDebugEnabled()) {
      logger.debug(marker, prefix + format, arg);
    }
  }

  @Override
  public void debug(Marker marker, String format, Object arg1, Object arg2) {
    if (isDebugEnabled()) {
      logger.debug(prefix + format, arg1, arg2);
    }
  }

  @Override
  public void debug(Marker marker, String format, Object... arguments) {
    if (isDebugEnabled()) {
      logger.debug(marker, prefix + format, arguments);
    }
  }

  @Override
  public void debug(Marker marker, String msg, Throwable t) {
    if (isDebugEnabled()) {
      logger.debug(marker, prefix + msg, t);
    }
  }

  @Override
  public boolean isInfoEnabled() {
    return logger.isInfoEnabled();
  }

  @Override
  public void info(String msg) {
    if (isInfoEnabled()) {
      logger.info(prefix + msg);
    }
  }

  @Override
  public void info(String format, Object arg) {
    if (isInfoEnabled()) {
      logger.info(prefix + format, arg);
    }
  }

  @Override
  public void info(String format, Object arg1, Object arg2) {
    if (isInfoEnabled()) {
      logger.info(prefix + format, arg1, arg2);
    }
  }

  @Override
  public void info(String format, Object... arguments) {
    if (isInfoEnabled()) {
      logger.info(prefix + format, arguments);
    }
  }

  @Override
  public void info(String msg, Throwable t) {
    if (isInfoEnabled()) {
      logger.info(prefix + msg, t);
    }
  }

  @Override
  public boolean isInfoEnabled(Marker marker) {
    return logger.isInfoEnabled(marker);
  }

  @Override
  public void info(Marker marker, String msg) {
    if (isInfoEnabled()) {
      logger.info(marker, prefix + msg);
    }
  }

  @Override
  public void info(Marker marker, String format, Object arg) {
    if (isInfoEnabled()) {
      logger.info(marker, prefix + format, arg);
    }
  }

  @Override
  public void info(Marker marker, String format, Object arg1, Object arg2) {
    if (isInfoEnabled()) {
      logger.info(marker, prefix + format, arg1, arg2);
    }
  }

  @Override
  public void info(Marker marker, String format, Object... arguments) {
    if (isInfoEnabled()) {
      logger.info(marker, prefix + format, arguments);
    }
  }

  @Override
  public void info(Marker marker, String msg, Throwable t) {
    if (isInfoEnabled()) {
      logger.info(marker, prefix + msg, t);
    }
  }

  @Override
  public boolean isWarnEnabled() {
    return logger.isWarnEnabled();
  }

  @Override
  public void warn(String msg) {
    if (isWarnEnabled()) {
      logger.warn(prefix + msg);
    }
  }

  @Override
  public void warn(String format, Object arg) {
    if (isWarnEnabled()) {
      logger.warn(prefix + format, arg);
    }
  }

  @Override
  public void warn(String format, Object... arguments) {
    if (isWarnEnabled()) {
      logger.warn(prefix + format, arguments);
    }
  }

  @Override
  public void warn(String format, Object arg1, Object arg2) {
    if (isWarnEnabled()) {
      logger.warn(prefix + format, arg1, arg2);
    }
  }

  @Override
  public void warn(String msg, Throwable t) {
    if (isWarnEnabled()) {
      logger.warn(prefix + msg, t);
    }
  }

  @Override
  public boolean isWarnEnabled(Marker marker) {
    return logger.isWarnEnabled(marker);
  }

  @Override
  public void warn(Marker marker, String msg) {
    if (isWarnEnabled()) {
      logger.warn(marker, prefix + msg);
    }
  }

  @Override
  public void warn(Marker marker, String format, Object arg) {
    if (isWarnEnabled()) {
      logger.warn(marker, prefix + format, arg);
    }
  }

  @Override
  public void warn(Marker marker, String format, Object arg1, Object arg2) {
    if (isWarnEnabled()) {
      logger.warn(marker, prefix + format, arg1, arg2);
    }
  }

  @Override
  public void warn(Marker marker, String format, Object... arguments) {
    if (isWarnEnabled()) {
      logger.warn(marker, prefix + format, arguments);
    }
  }

  @Override
  public void warn(Marker marker, String msg, Throwable t) {
    if (isWarnEnabled()) {
      logger.warn(marker, prefix + msg, t);
    }
  }

  @Override
  public boolean isErrorEnabled() {
    return logger.isErrorEnabled();
  }

  @Override
  public void error(String msg) {
    if (isErrorEnabled()) {
      logger.error(prefix + msg);
    }
  }

  @Override
  public void error(String format, Object arg) {
    if (isErrorEnabled()) {
      logger.error(prefix + format, arg);
    }
  }

  @Override
  public void error(String format, Object arg1, Object arg2) {
    if (isErrorEnabled()) {
      logger.error(prefix + format, arg1, arg2);
    }
  }

  @Override
  public void error(String format, Object... arguments) {
    if (isErrorEnabled()) {
      logger.error(prefix + format, arguments);
    }
  }

  @Override
  public void error(String msg, Throwable t) {
    if (isErrorEnabled()) {
      logger.error(prefix + msg, t);
    }
  }

  @Override
  public boolean isErrorEnabled(Marker marker) {
    return logger.isErrorEnabled(marker);
  }

  @Override
  public void error(Marker marker, String msg) {
    if (isErrorEnabled()) {
      logger.error(marker, prefix + msg);
    }
  }

  @Override
  public void error(Marker marker, String format, Object arg) {
    if (isErrorEnabled()) {
      logger.error(marker, prefix + format, arg);
    }
  }

  @Override
  public void error(Marker marker, String format, Object arg1, Object arg2) {
    if (isErrorEnabled()) {
      logger.error(marker, prefix + format, arg1, arg2);
    }
  }

  @Override
  public void error(Marker marker, String format, Object... arguments) {
    if (isErrorEnabled()) {
      logger.error(marker, prefix + format, arguments);
    }
  }

  @Override
  public void error(Marker marker, String msg, Throwable t) {
    if (isErrorEnabled()) {
      logger.error(marker, prefix + msg, t);
    }
  }
}
