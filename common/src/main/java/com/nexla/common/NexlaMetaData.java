package com.nexla.common;

import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static java.util.Optional.ofNullable;
import static org.apache.commons.io.FilenameUtils.getName;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NexlaMetaData {

  public static final String METADATA_TRACKER_ID = "trackerId";
  public static final String METADATA_ARCHIVE_SOURCE_FILENAME = "archiveSourceFilename";
  public static final String NX_RESPONSE_HEADERS = "nx.response.headers";
  public static final String NX_REQUEST_HEADERS = "nx.request.headers";
  public static final String NX_REQUEST_URL = "nx.request.url";
  public static final String FILE_SIZE = "file_size";
  public static final String FILE_ID = "file_id";

  private ConnectionType sourceType;
  private Long ingestTime;
  private Long sourceOffset;
  private String sourceKey;
  private String topic;
  private Integer datasetId;
  private ResourceType resourceType;
  private Integer resourceId;
  private Tracker trackerId;
  private boolean eof;
  private Long lastModified;
  private Long runId;
  private Map<String, Object> tags;

  @JsonIgnore private boolean skip = false;
  @JsonIgnore private boolean error = false;

  public NexlaMetaData(
      ConnectionType sourceType,
      Long ingestTime,
      Long sourceOffset,
      String sourceKey,
      String topic,
      ResourceType resourceType,
      Integer id,
      boolean eof,
      Tracker trackerId,
      Long runId) {
    this.sourceType = sourceType;
    this.ingestTime = ingestTime;
    this.sourceOffset = sourceOffset;
    this.sourceKey = sourceKey;
    this.topic = topic;
    try {
      this.datasetId = trackerId.getSource().getDataSetId();
    } catch (Exception e) {
      this.datasetId = -1;
    }
    this.resourceType = resourceType;
    this.resourceId = id;
    this.eof = eof;
    this.trackerId = trackerId;
    this.runId = runId;
  }

  public NexlaMetaData(
      ConnectionType sourceType,
      Long ingestTime,
      Long sourceOffset,
      String sourceKey,
      String topic,
      Integer datasetId,
      ResourceType resourceType,
      Integer id,
      boolean eof,
      Tracker trackerId,
      Long runId) {
    this.sourceType = sourceType;
    this.ingestTime = ingestTime;
    this.sourceOffset = sourceOffset;
    this.sourceKey = sourceKey;
    this.topic = topic;
    this.datasetId = datasetId;
    this.resourceType = resourceType;
    this.resourceId = id;
    this.eof = eof;
    this.trackerId = trackerId;
    this.runId = runId;
  }

  public static Map<String, Object> extractMetaDataMap(NexlaMetaData nexlaMetaData) {
    Map<String, Object> metadataContext = new LinkedHashMap<>();
    metadataContext.put("sourceKey", nexlaMetaData.getSourceKey());
    metadataContext.put("ingestTime", nexlaMetaData.getIngestTime());
    metadataContext.put("resourceId", nexlaMetaData.getResourceId());
    metadataContext.put("resourceType", nexlaMetaData.getResourceType());
    metadataContext.put("sourceOffset", nexlaMetaData.getSourceOffset());
    metadataContext.put("sourceType", nexlaMetaData.getSourceType());
    if (nexlaMetaData.getTrackerId() != null) {
      metadataContext.put(
          METADATA_TRACKER_ID, nexlaMetaData.getTrackerId().toString(Tracker.TrackerMode.FULL));
    }
    metadataContext.put("eof", nexlaMetaData.isEof());
    metadataContext.put("runId", nexlaMetaData.runId);
    metadataContext.put("tags", nexlaMetaData.tags);
    return metadataContext;
  }

  public static Map<String, Object> extractMetaDataMapNoTracker(NexlaMetaData nexlaMetaData) {
    Map<String, Object> metadataContext = new LinkedHashMap<>();
    metadataContext.put("sourceKey", nexlaMetaData.getSourceKey());
    metadataContext.put("ingestTime", nexlaMetaData.getIngestTime());
    metadataContext.put("resourceId", nexlaMetaData.getResourceId());
    metadataContext.put("resourceType", nexlaMetaData.getResourceType());
    metadataContext.put("sourceOffset", nexlaMetaData.getSourceOffset());
    metadataContext.put("sourceType", nexlaMetaData.getSourceType());
    metadataContext.put("eof", nexlaMetaData.isEof());
    metadataContext.put("runId", nexlaMetaData.runId);
    metadataContext.put("tags", nexlaMetaData.tags);
    return metadataContext;
  }

  public byte[] partitioningKey(boolean partitionByOffset) {
    String partitioningKey = partitionByOffset ? sourceKey + "-" + sourceOffset : sourceKey;
    return partitioningKey.getBytes();
  }

  public static NexlaMetaData createSourceMetadata(
      ConnectionType connectionType,
      Integer sourceId,
      Integer version,
      NexlaFile nexlaFile,
      boolean eof,
      Long offset,
      String topic,
      Long runId,
      NexlaMetaData original,
      Integer dataSetId) {
    // Create tracker ID
    String fullPath = nexlaFile.getFullPath();
    long now = nowUTC().getMillis();

    Long recordNumber = ofNullable(offset).map(x -> x + 1).orElse(null);
    SourceItem sourceItem =
        SourceItem.fullTracker(sourceId, dataSetId, getName(fullPath), recordNumber, version, now);
    Tracker trackerId = new Tracker(Tracker.TrackerMode.FULL, sourceItem);
    NexlaMetaData metaData =
        new NexlaMetaData(
            connectionType,
            now,
            offset,
            fullPath,
            topic,
            dataSetId,
            SOURCE,
            sourceId,
            eof,
            trackerId,
            runId);

    if (original != null) {
      metaData.setTags(original.getTags());
    }
    metaData.setLastModified(nexlaFile.getLastModified());

    nexlaFile
        .getMetadata()
        .ifPresent(
            fileTags -> {
              Map<String, Object> resultTags = new LinkedHashMap<>();

              // https://nexla1.atlassian.net/browse/NEX-9169
              Optional.ofNullable(metaData.getTags()).ifPresent(resultTags::putAll);
              resultTags.putAll(fileTags);

              metaData.setTags(resultTags);
            });

    if (metaData.getTags() == null) {
      metaData.setTags(new LinkedHashMap<>());
    }
    metaData.getTags().put(FILE_SIZE, nexlaFile.getSize());
    metaData.getTags().put(FILE_ID, nexlaFile.getId());

    metaData.setLastModified(nexlaFile.getLastModified());

    return metaData;
  }

  public NexlaMetaData deepCopyMetadata() {
    NexlaMetaData nm = new NexlaMetaData();
    nm.setSourceType(this.sourceType);
    nm.setIngestTime(Long.valueOf(this.ingestTime));
    nm.setSourceOffset(Long.valueOf(this.sourceOffset));
    nm.setSourceKey(new String(this.sourceKey));
    nm.setTopic(new String(this.topic));
    nm.setDatasetId(Integer.valueOf(this.datasetId));
    nm.setResourceType(this.resourceType);
    nm.setResourceId(Integer.valueOf(this.resourceId));

    Tracker tracker = this.trackerId.deepCopy();
    nm.setTrackerId(tracker);

    nm.setEof(Boolean.valueOf(this.eof));
    nm.setLastModified(Long.valueOf(this.lastModified));
    nm.setRunId(Long.valueOf(this.runId));

    Map<String, Object> newTags = new HashMap<>(Map.copyOf(this.tags));
    nm.setTags(newTags);

    nm.setSkip(Boolean.valueOf(this.skip));
    nm.setError(Boolean.valueOf(this.error));
    return nm;
  }
}
