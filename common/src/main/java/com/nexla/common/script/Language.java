package com.nexla.common.script;

import java.util.Arrays;
import java.util.NoSuchElementException;

public enum Language {
  JAVASCRIPT("javascript"),
  JAVASCRIPT_ES6("javascript-es6"),
  <PERSON><PERSON><PERSON><PERSON>("python"),
  PYTHON3("python3");

  /*name identified by ScriptEngine*/
  private String name;

  Language(String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }

  public static Language valueOfIgnoreCase(String value) {
    return Arrays.stream(Language.values())
        .filter(l -> l.getName().equalsIgnoreCase(value))
        .findFirst()
        .orElseThrow(NoSuchElementException::new);
  }
}
