package com.nexla.common.connectiontype;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import lombok.Data;

@Data
public class ConnectionTypeBuilder {

  public final String name;
  public final NexlaConstants.ConnectionTypeCategory category;
  public String connectionStringPrefix;
  public boolean isWarehouseSource = false;
  public boolean isWarehouseSink = false;
  public boolean canBeSourceContainer = true;
  public boolean isRefreshable = false;

  public ConnectionTypeBuilder withConnectionPrefix(String connectionStringPrefix) {
    this.connectionStringPrefix = connectionStringPrefix;
    return this;
  }

  // types supporting copy operation with source connectors
  public ConnectionTypeBuilder warehouseSource() {
    this.isWarehouseSource = true;
    return this;
  }

  public ConnectionTypeBuilder warehouseSink() {
    this.isWarehouseSink = true;
    return this;
  }

  public ConnectionTypeBuilder noSourceContainer() {
    this.canBeSourceContainer = false;
    return this;
  }

  public ConnectionTypeBuilder isRefreshable() {
    this.isRefreshable = true;
    return this;
  }

  public ConnectionType build() {
    ConnectionType value =
        new ConnectionType(
            name,
            category,
            connectionStringPrefix,
            isWarehouseSource,
            isWarehouseSink,
            canBeSourceContainer,
            isRefreshable);

    ConnectionType.CONNECTION_TYPE_MAP.put(name, value);
    return value;
  }
}
