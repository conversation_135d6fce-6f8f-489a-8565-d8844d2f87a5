package com.nexla.common.connectiontype;

import static com.nexla.common.AppUtils.createClosableHttpClient;
import static com.nexla.common.ScheduledThreadPool.CACHED_EXECUTOR;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.nexla.common.*;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoadConnectorServiceClient {
  private static final Logger LOGGER = LoggerFactory.getLogger(LoadConnectorServiceClient.class);
  public static String USERNAME;
  public static String PASSWORD;
  public static String SERVER_URL;
  private static NexlaSslContext SSL_CONTEXT;
  private static final ListeningExecutorService listeningExecutorService =
      MoreExecutors.listeningDecorator(CACHED_EXECUTOR);

  public static synchronized void init(
      String serverUrl, String userName, String password, NexlaSslContext sslContext) {
    if (SERVER_URL == null) {
      LOGGER.info("Initializing LoadConnectorServiceClient with server url {}", serverUrl);
      SERVER_URL = serverUrl;
      USERNAME = userName;
      PASSWORD = password;
      SSL_CONTEXT = sslContext;

      try {
        CONNECTION_TYPE_CACHE.get(KEY);
        CONNECTOR_SPECS_CACHE.get(KEY);
        CONNECTION_TYPE_CACHE.refresh(KEY);
        CONNECTOR_SPECS_CACHE.refresh(KEY);
      } catch (ExecutionException e) {
        LOGGER.error("", e);
      }
    }
    LOGGER.info("LoadConnectorServiceClient already initialized with server url {}", SERVER_URL);
  }

  private static LoadingCache<String, Map<String, ConnectionType>> CONNECTION_TYPE_CACHE =
      CacheBuilder.newBuilder()
          .refreshAfterWrite(5, TimeUnit.MINUTES)
          .build(
              new CacheLoader<>() {
                @Override
                public ListenableFuture<Map<String, ConnectionType>> reload(
                    String key, Map<String, ConnectionType> oldValue) throws Exception {
                  return listeningExecutorService.submit(
                      () -> {
                        try {
                          Map<String, ConnectionType> fetchedData = doFetchConnectionTypes();
                          LOGGER.info("Successfully fetched ConnectionTypes");
                          return fetchedData;
                        } catch (Exception e) {
                          LOGGER.error("Error fetching ConnectionTypes");
                          throw e;
                        }
                      });
                }

                @Override
                public Map<String, ConnectionType> load(String key) {
                  return Collections.emptyMap();
                }
              });

  private static LoadingCache<String, List<ConnectorSpecDto>> CONNECTOR_SPECS_CACHE =
      CacheBuilder.newBuilder()
          .refreshAfterWrite(5, TimeUnit.MINUTES)
          .build(
              new CacheLoader<>() {

                @Override
                public ListenableFuture<List<ConnectorSpecDto>> reload(
                    String key, List<ConnectorSpecDto> oldValue) throws Exception {
                  return listeningExecutorService.submit(
                      () -> {
                        try {
                          List<ConnectorSpecDto> fetchedData = doFetchConnectorSpecs();
                          LOGGER.info("Successfully fetched connector specs.");
                          return fetchedData;
                        } catch (Exception e) {
                          LOGGER.error("Error fetching connector specs");
                          throw e;
                        }
                      });
                }

                @Override
                public List<ConnectorSpecDto> load(String key) {
                  return Collections.emptyList();
                }
              });

  public static final String KEY = "";

  @SneakyThrows
  public static Map<String, ConnectionType> getRemoteCredentialTypes() {
    return CONNECTION_TYPE_CACHE.get(KEY);
  }

  @SneakyThrows
  public static List<ConnectorSpecDto> getConnectorSpecs() {
    return CONNECTOR_SPECS_CACHE.get(KEY);
  }

  public static RemoteClassLoader remoteClassLoader() {
    return new RemoteClassLoader(
        SERVER_URL, USERNAME, PASSWORD, Thread.currentThread().getContextClassLoader());
  }

  @SneakyThrows
  private static Map<String, ConnectionType> doFetchConnectionTypes() {
    String url = SERVER_URL + "/connectionTypes";
    HttpGet request = new HttpGet(url);
    String encodedAuth = Base64.getEncoder().encodeToString((USERNAME + ":" + PASSWORD).getBytes());
    request.setHeader("Authorization", "Basic " + encodedAuth);

    if (SSL_CONTEXT.getClientKeystoreStore().isEmpty()) {
      LOGGER.error("SSL context wasn't set. Unable to fetch connection types");
      return Collections.emptyMap();
    }

    SSLCertificateStore clientKeystoreStore = SSL_CONTEXT.getClientKeystoreStore().get();
    SSLCertificateStore clientTruststoreStore = SSL_CONTEXT.getClientTruststoreStore().orElse(null);

    try (CloseableHttpClient httpClient =
            createClosableHttpClient(
                AppUtils.sslContext(clientKeystoreStore, clientTruststoreStore));
        CloseableHttpResponse response = httpClient.execute(request)) {
      String json = EntityUtils.toString(response.getEntity());
      Map<String, ConnectionType> result = Maps.newHashMap();
      List<ConnectionTypeDto> ctDtos =
          StreamUtils.jsonUtil().stringToType(json, new TypeReference<>() {});

      ctDtos.forEach(
          x -> {
            NexlaConstants.ConnectionTypeCategory category =
                x.category == null
                    ? null
                    : NexlaConstants.ConnectionTypeCategory.fromString(x.category);
            ConnectionType ct =
                new ConnectionType(
                    x.name,
                    category,
                    x.connectionStringPrefix,
                    x.isWarehouseSource,
                    x.isWarehouseSink,
                    x.isSourceConnectorType,
                    x.isRefreshable);
            result.put(x.name, ct);
          });
      return result;
    }
  }

  @SneakyThrows
  private static List<ConnectorSpecDto> doFetchConnectorSpecs() {
    String url = SERVER_URL + "/connectorSpecs";
    HttpGet request = new HttpGet(url);
    String encodedAuth = Base64.getEncoder().encodeToString((USERNAME + ":" + PASSWORD).getBytes());
    request.setHeader("Authorization", "Basic " + encodedAuth);

    if (SSL_CONTEXT.getClientKeystoreStore().isEmpty()) {
      LOGGER.error("SSL context wasn't set. Unable to fetch connector specs");
      return Collections.emptyList();
    }

    SSLCertificateStore clientKeystoreStore = SSL_CONTEXT.getClientKeystoreStore().get();
    SSLCertificateStore clientTruststoreStore = SSL_CONTEXT.getClientTruststoreStore().orElse(null);

    try (CloseableHttpClient httpClient =
            createClosableHttpClient(
                AppUtils.sslContext(clientKeystoreStore, clientTruststoreStore));
        CloseableHttpResponse response = httpClient.execute(request)) {
      String json = EntityUtils.toString(response.getEntity());
      return StreamUtils.jsonUtil().stringToType(json, new TypeReference<>() {});
    }
  }
}
