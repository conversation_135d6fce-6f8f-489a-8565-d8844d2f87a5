package com.nexla.common;

import lombok.Data;

public class VersionComparator {

  public static final String DELIMITER_VERSION = "\\.";

  @Data
  static class Version {
    private final int v1;
    private final int v2;
    private final int v3;
  }

  public static boolean isVersionGE(String current, String threshold) {
    var currVersionSplit = current.split("-")[0].split("_")[0].split(DELIMITER_VERSION);
    var currVersion =
        new Version(
            Integer.parseInt(currVersionSplit[0]),
            Integer.parseInt(currVersionSplit[1]),
            Integer.parseInt(currVersionSplit[2]));

    var threshVersionSplit = threshold.split(DELIMITER_VERSION);
    var threshVersion =
        new Version(
            Integer.parseInt(threshVersionSplit[0]),
            Integer.parseInt(threshVersionSplit[1]),
            Integer.parseInt(threshVersionSplit[2]));

    if (currVersion.v1 > threshVersion.v1) {
      return true;
    } else if (currVersion.v1 < threshVersion.v1) {
      return false;
    } else {
      if (currVersion.v2 > threshVersion.v2) {
        return true;
      } else if (currVersion.v2 < threshVersion.v2) {
        return false;
      } else {
        return currVersion.v3 >= threshVersion.v3;
      }
    }
  }
}
