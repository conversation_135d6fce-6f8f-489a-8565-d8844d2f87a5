package com.nexla.common.compute;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum ComputeMetricSource {
  SOURCE("SOURCE"),
  DATASET("DATASET"),
  SINK("SINK");

  private static final Map<String, ComputeMetricSource> valuesMap;

  static {
    valuesMap =
        Arrays.stream(ComputeMetricSource.values())
            .collect(Collectors.toUnmodifiableMap(element -> element.value, Function.identity()));
  }

  @Getter @JsonValue private final String value;

  @JsonCreator
  public static ComputeMetricSource fromValue(JsonNode jsonNode) {
    final String value = jsonNode.asText();
    return fromValue(value);
  }

  public static ComputeMetricSource fromValue(String value) {
    return Optional.ofNullable(valuesMap.get(value))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format(
                        "Enum value not found: %s for class %s",
                        value, ComputeMetricSource.class)));
  }
}
