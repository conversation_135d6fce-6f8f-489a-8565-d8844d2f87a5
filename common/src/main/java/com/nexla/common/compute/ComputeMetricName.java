package com.nexla.common.compute;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.JsonNode;
import com.nexla.common.compute.reporting.ComputeMetricAggregationType;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum ComputeMetricName {
  PROCESSING_TIME("PROCESSING_TIME", ComputeMetricAggregationType.SUM),

  CPU_UTILIZATION_AVR("CPU_UTILIZATION_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  CPU_UTILIZATION_MAX("CPU_UTILIZATION_MAX", ComputeMetricAggregationType.MAX),

  MEMORY_AVR("MEMORY_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  MEMORY_MAX("MEMORY_MAX", ComputeMetricAggregationType.MAX),

  SWAP_AVR("SWAP_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  SWAP_MAX("SWAP_MAX", ComputeMetricAggregationType.MAX),

  DISK_USAGE_AVR("DISK_USAGE_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  DISK_IO_READS("DISK_IO_READS", ComputeMetricAggregationType.SUM),
  DISK_IO_WRITES("DISK_IO_WRITES", ComputeMetricAggregationType.SUM),

  NETWORK_IN("NETWORK_IN", ComputeMetricAggregationType.SUM),
  NETWORK_OUT("NETWORK_OUT", ComputeMetricAggregationType.SUM),
  NETWORK_TCP_CONNECTIONS("NETWORK_TCP_CONNECTIONS", ComputeMetricAggregationType.MAX),
  NETWORK_SOCKETS("NETWORK_SOCKETS", ComputeMetricAggregationType.MAX),

  DB_CONNECTIONS("DB_CONNECTIONS", ComputeMetricAggregationType.MAX),
  DB_TRANSACTIONS("DB_TRANSACTIONS", ComputeMetricAggregationType.SUM),
  DB_ROW_READS("DB_ROW_READS", ComputeMetricAggregationType.SUM),
  DB_ROW_WRITES("DB_ROW_WRITES", ComputeMetricAggregationType.SUM),
  DB_NETWORK_IN("DB_NETWORK_IN", ComputeMetricAggregationType.SUM),
  DB_NETWORK_OUT("DB_NETWORK_OUT", ComputeMetricAggregationType.SUM),

  REDIS_COMMANDS("REDIS_COMMANDS", ComputeMetricAggregationType.SUM),
  REDIS_MEMORY_USAGE_AVR("REDIS_MEMORY_USAGE_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  REDIS_MEMORY_USAGE_MAX("REDIS_MEMORY_USAGE_MAX", ComputeMetricAggregationType.MAX),
  REDIS_CPU_USAGE_AVR("REDIS_CPU_USAGE_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  REDIS_CPU_USAGE_MAX("REDIS_CPU_USAGE_MAX", ComputeMetricAggregationType.MAX),
  REDIS_NETWORK_IN("REDIS_NETWORK_IN", ComputeMetricAggregationType.SUM),
  REDIS_NETWORK_OUT("REDIS_NETWORK_OUT", ComputeMetricAggregationType.SUM),

  GPU_UTILIZATION_AVR("GPU_UTILIZATION_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  GPU_UTILIZATION_MAX("GPU_UTILIZATION_MAX", ComputeMetricAggregationType.MAX),
  GPU_MEMORY_USAGE_AVR("GPU_MEMORY_USAGE_AVR", ComputeMetricAggregationType.WEIGHTED_AVERAGE),
  GPU_MEMORY_USAGE_MAX("GPU_MEMORY_USAGE_MAX", ComputeMetricAggregationType.MAX);

  private static final Map<String, ComputeMetricName> valuesMap;

  static {
    valuesMap =
        Arrays.stream(ComputeMetricName.values())
            .collect(Collectors.toUnmodifiableMap(element -> element.value, Function.identity()));
  }

  @Getter @JsonValue private final String value;

  @Getter private final ComputeMetricAggregationType aggregationType;

  @JsonCreator
  public static ComputeMetricName fromValue(JsonNode jsonNode) {
    final String value = jsonNode.asText();
    return fromValue(value);
  }

  public static ComputeMetricName fromValue(String value) {
    return Optional.ofNullable(valuesMap.get(value))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    String.format(
                        "Enum value not found: %s for class %s", value, ComputeMetricName.class)));
  }
}
