package com.nexla.common.schema;

public enum SchemaType {
  ARRAY("array"),
  BOOLEAN("boolean"),
  INTEGER("integer"),
  NULL("null"),
  NUMBER("number"),
  OBJECT("object"),
  STRING("string"),
  UNKNOWN("unknown");

  final String type;

  SchemaType(String type) {
    this.type = type;
  }

  public static SchemaType fromString(String type) {
    for (SchemaType schemaType : SchemaType.values()) {
      if (schemaType.type.equalsIgnoreCase(type)) {
        return schemaType;
      }
    }
    throw new IllegalArgumentException("No constant with type " + type + " found");
  }

  @Override
  public String toString() {
    return this.type;
  }
}
