package com.nexla.common.exception;

public class ProbeRetriableException extends RuntimeException {

  public ProbeRetriableException() {
    super();
  }

  public ProbeRetriableException(Throwable cause) {
    super(cause);
  }

  public ProbeRetriableException(String message) {
    super(message);
  }

  public ProbeRetriableException(String message, Throwable cause) {
    super(message, cause);
  }

  protected ProbeRetriableException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
