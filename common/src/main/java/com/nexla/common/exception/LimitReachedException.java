package com.nexla.common.exception;

public class LimitReachedException extends NexlaException {
  public LimitReachedException() {
    super();
  }

  public LimitReachedException(String message) {
    super(message);
  }

  public LimitReachedException(String message, Throwable cause) {
    super(message, cause);
  }

  public LimitReachedException(Throwable cause) {
    super(cause);
  }

  protected LimitReachedException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
