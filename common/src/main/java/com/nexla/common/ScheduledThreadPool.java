package com.nexla.common;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledThreadPoolExecutor;

public class ScheduledThreadPool {

  public static final ScheduledThreadPoolExecutor SCHEDULED_EXECUTOR =
      new ScheduledThreadPoolExecutor(
          Math.max(10, Runtime.getRuntime().availableProcessors() * 2 + 1),
          new ThreadFactoryBuilder().setNameFormat("scheduled-pool-%d").setDaemon(true).build());

  public static final ExecutorService CACHED_EXECUTOR =
      Executors.newCachedThreadPool(
          new ThreadFactoryBuilder().setNameFormat("cached-pool-%d").setDaemon(true).build());
}
