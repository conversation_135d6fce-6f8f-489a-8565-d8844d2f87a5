package com.nexla.common.notify.monitoring;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ResourceType;
import com.nexla.control.message.MessageCreatedAt;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class NexlaMonitoringLogEvent implements MessageCreatedAt {

  @JsonProperty("org_id")
  private Integer orgId;

  @JsonProperty("run_id")
  private Long runId;

  @JsonProperty("resource_id")
  private Integer resourceId;

  @JsonProperty("resource_type")
  private ResourceType resourceType;

  @JsonProperty("log")
  private String log;

  @JsonProperty("log_type")
  private NexlaMonitoringLogType logType;

  @JsonProperty("severity")
  private NexlaMonitoringLogSeverity severity;

  @JsonProperty("event_time_millis")
  private Long eventTimeMillis;

  @JsonProperty("@timestamp")
  private String indexTimestamp;

  public static NexlaMonitoringLogEvent of(
      Integer orgId,
      Long runId,
      Integer resourceId,
      ResourceType resourceType,
      String log,
      NexlaMonitoringLogType logType,
      NexlaMonitoringLogSeverity severity,
      Long eventTimeMillis) {
    NexlaMonitoringLogEvent res = new NexlaMonitoringLogEvent();
    res.setOrgId(orgId);
    res.setRunId(runId);
    res.setResourceId(resourceId);
    res.setResourceType(resourceType);
    res.setLog(log);
    res.setLogType(logType);
    res.setSeverity(severity);
    res.setEventTimeMillis(eventTimeMillis);
    return res;
  }

  @Override
  @JsonIgnore
  public Long getCreatedAt() {
    return eventTimeMillis;
  }
}
