package com.nexla.common.notify.transport;

import com.nexla.control.health.ExpectedHealthEvent;
import com.nexla.control.health.HealthMessage;
import com.nexla.control.health.metrics.MetricEvent;

public interface HealthMessageSender {
  void sendHealthMessage(HealthMessage message);

  void sendExpectedHealthEvent(ExpectedHealthEvent expectedHealthEvent, String podName);

  void sendHealthMetricEvent(MetricEvent metricEvent);
}
