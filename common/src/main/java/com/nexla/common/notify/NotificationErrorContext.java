package com.nexla.common.notify;

public class NotificationErrorContext {

  private long id;
  private long timestamp;
  private String details;

  public NotificationErrorContext() {}

  public NotificationErrorContext(long id, long timestamp, String details) {
    this.id = id;
    this.timestamp = timestamp;
    this.details = details;
  }

  public long getId() {
    return id;
  }

  public void setId(long id) {
    this.id = id;
  }

  public long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(long timestamp) {
    this.timestamp = timestamp;
  }

  public String getDetails() {
    return details;
  }

  public void setDetails(String details) {
    this.details = details;
  }
}
