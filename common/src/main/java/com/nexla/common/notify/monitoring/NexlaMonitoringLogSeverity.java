package com.nexla.common.notify.monitoring;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum NexlaMonitoringLogSeverity {
  ERROR("error"),
  WARNING("warning"),
  INFO("info");

  private String key;

  NexlaMonitoringLogSeverity(String key) {
    this.key = key;
  }

  @JsonCreator
  public static NexlaMonitoringLogSeverity fromString(String key) {
    return NexlaMonitoringLogSeverity.valueOf(key.toUpperCase());
  }

  @JsonValue
  public String getKey() {
    return key;
  }
}
