package com.nexla.common.notify.transport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nexla.control.message.MessageCreatedAt;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DelayedMessage implements MessageCreatedAt {

  public final String topic;
  public final String key;
  public final String value;
  public final Long timeToFire;
  private final Long createdAt = System.currentTimeMillis();
}
