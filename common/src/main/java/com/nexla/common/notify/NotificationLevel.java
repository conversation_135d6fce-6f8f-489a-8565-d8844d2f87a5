package com.nexla.common.notify;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum NotificationLevel {
  DEBUG("debug"),
  INFO("info"),
  WARN("warn"),
  ERROR("error");

  private String key;

  NotificationLevel(String key) {
    this.key = key;
  }

  @JsonCreator
  public static NotificationLevel fromString(String key) {
    return NotificationLevel.valueOf(key.toUpperCase());
  }

  @JsonValue
  public String getKey() {
    return key;
  }
}
