package com.nexla.common.notify.transport;

import com.nexla.common.exception.NexlaErrorNotificationEvent;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.error.NexlaInternalNotificationEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;

public interface NexlaMessageTransport extends AutoCloseable {

  void publish(String topic, String json, String key);

  void publish(String topic, int partition, String key, String value);

  void publishMetrics(NexlaRawMetric nexlaRawMetric);

  void publishMetrics(NexlaRawMetric nexlaRawMetric, String key);

  void publishNotification(NexlaNotificationEvent notificationEvent);

  void publishMonitoringLog(NexlaMonitoringLogEvent monitoringLogEvent);

  void publishInternalNotification(NexlaInternalNotificationEvent notificationEvent);

  void publishQuarantineMessage(NexlaQuarantineMessage message, String topicName);

  void publishTransformIOMessage(
      String quarantineWriteTopic, NexlaQuarantineMessage quarantineMessage);

  void publishErrorMessage(String topic, NexlaErrorNotificationEvent notificationEvent);

  void flush();

  @Override
  default void close() throws Exception {}
}
