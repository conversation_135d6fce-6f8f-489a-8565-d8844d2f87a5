package com.nexla.common.notify.context;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Optional;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class NexlaNotificationEventContextResourceTypeOrg
    implements NexlaNotificationEventContextResourceType {

  @JsonProperty("id")
  private Long id;

  @JsonProperty("type")
  private ResourceType type;

  @JsonProperty("org_id")
  private Long orgId;

  @JsonProperty("access_role")
  private String accessRole;

  @JsonProperty("org")
  private NexlaNotificationEventContextOrg org;

  @Override
  public String getDisplayName() {
    return Optional.ofNullable(org).map(NexlaNotificationEventContextOrg::getName).orElse(null);
  }
}
