package com.nexla.common.notify.context;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.notify.NotificationLevel;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NexlaNotificationEventContext {

  @JsonProperty("id")
  private Long id;

  @JsonProperty("type")
  private String type;

  @JsonProperty("file_location")
  private String fileLocation;

  @JsonProperty("record_count")
  private Long recordCount;

  @JsonProperty("error_message")
  private String errorMessage;

  @JsonProperty("error_detail")
  private String errorDetail;

  @JsonProperty("run_id")
  private Long runId;

  @JsonProperty("errors")
  private String errors;

  @JsonProperty("resource_link")
  private String resourceLink;

  @JsonProperty("resource_name")
  private String resourceName;

  @JsonProperty("resource_id")
  private Integer resourceId;

  @JsonProperty("resource_type")
  private String resourceType;

  @JsonProperty("owner_name")
  private String ownerName;

  @JsonProperty("notification_level")
  private NotificationLevel notificationLevel;

  @JsonProperty("data_source_name")
  private String dataSourceName;

  @JsonProperty("data_source_link")
  private String dataSourceLink;

  @JsonProperty("schema_update_message")
  private String schemaUpdateMessage;

  @JsonProperty("nexla_ui_url")
  private String nexlaUiUrl;

  @JsonProperty("nexla_quick_start_guide_url")
  private String nexlaQuickStartGuideUrl;

  @JsonProperty("nexla_documentation_url")
  private String nexlaDocumentationUrl;

  @JsonProperty("nexla_logo_url")
  private String nexlaLogoUrl;

  @JsonProperty("nexla_support_videos")
  private String nexlaSupportVideos;

  @JsonProperty("dataset_link")
  private String datasetLink;

  @JsonProperty("dataset_id")
  private Integer datasetId;

  @JsonProperty("dataset_name")
  private String datasetName;

  @JsonProperty("sharer_name")
  private String sharerName;

  @JsonProperty("name")
  private String name;

  @JsonProperty("owner_id")
  private Integer ownerId;

  @JsonProperty("owner_full_name")
  private String ownerFullName;

  @JsonProperty("owner_email")
  private String ownerEmail;

  @JsonProperty("org_id")
  private Integer orgId;

  @JsonProperty("message")
  private String message;

  @JsonProperty("metric_name")
  private String metricName;

  @JsonProperty("metric_value")
  private Long metricValue;

  @JsonProperty("tags")
  private List<String> tags;

  @JsonProperty("email")
  private String email;

  @JsonProperty("entriesDiffering")
  private Map<String, Object> entriesDiffering;

  @JsonProperty("entriesOnlyOnRight")
  private Map<String, Object> entriesOnlyOnRight;

  @JsonProperty("added")
  private List<NexlaNotificationEventContextResourceType> added;

  @JsonProperty("removed")
  private List<NexlaNotificationEventContextResourceType> removed;

  @JsonProperty("reset_token")
  private String resetToken;

  @JsonProperty("reset_url")
  private String resetUrl;
}
