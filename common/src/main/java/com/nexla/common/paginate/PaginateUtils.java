package com.nexla.common.paginate;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class PaginateUtils {
  private static final Logger logger = LoggerFactory.getLogger(PaginateUtils.class);

  public static List paginate(List source, int page, int size, Comparator comparator) {
    if (!validate(page, size)) {
      return Collections.emptyList();
    }
    int selectFrom = (page - 1) * size;
    if (source == null || source.size() < selectFrom) {
      return Collections.emptyList();
    }
    int selectTo = Math.min(source.size(), selectFrom + size);
    Collections.sort(source, comparator);
    return source.subList(selectFrom, selectTo);
  }

  private static boolean validate(int page, int size) {
    if (size <= 0 || page <= 0) {
      logger.error("Page and size should be positive integers");
      return false;
    }
    return true;
  }
}
