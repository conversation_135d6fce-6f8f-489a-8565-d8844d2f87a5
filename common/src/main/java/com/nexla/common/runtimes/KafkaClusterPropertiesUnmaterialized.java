package com.nexla.common.runtimes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nexla.common.ConfigUtils;
import java.util.Optional;
import lombok.Data;
import org.apache.kafka.common.security.auth.SecurityProtocol;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KafkaClusterPropertiesUnmaterialized {

  private static final Logger LOGGER =
      LoggerFactory.getLogger(KafkaClusterPropertiesUnmaterialized.class);

  public final String id;
  public final String zookeeperConnect;
  public final String bootstrapServer;
  public final SecurityProtocol securityProtocol;
  public final Optional<String> truststoreLocation;
  public final Optional<String> truststoreSecret;
  public final Optional<String> truststoreKey;
  public final Optional<String> truststorePasswordEnv;
  public final Optional<String> keystoreLocation;
  public final Optional<String> keystoreSecret;
  public final Optional<String> keystoreKey;
  public final Optional<String> keystorePasswordEnv;
  public final Optional<String> saslMechanism;
  public final Optional<String> saslJaasConfigTemplate;
  public final Optional<String> saslUsername;
  public final Optional<String> saslPassword;

  public KafkaClusterProperties materializeWithEnv() {
    Optional<String> truststoreKey = truststorePasswordEnv.map(ConfigUtils::env);
    Optional<String> keystoreKey = keystorePasswordEnv.map(ConfigUtils::env);
    return new KafkaClusterProperties(
        id,
        zookeeperConnect,
        bootstrapServer,
        securityProtocol,
        truststoreLocation,
        truststoreKey,
        keystoreLocation,
        keystoreKey,
        saslMechanism,
        saslJaasConfigTemplate,
        saslUsername,
        saslPassword);
  }
}
