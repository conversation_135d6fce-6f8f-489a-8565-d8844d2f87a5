package com.nexla.common.runtimes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Optional;
import lombok.Data;
import org.apache.kafka.common.security.auth.SecurityProtocol;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KafkaClusterProperties {

  public final String id;
  public final String zookeeperConnect;
  public final String bootstrapServer;
  public final SecurityProtocol securityProtocol;
  public final Optional<String> truststoreLocation;
  public final Optional<String> truststorePassword;
  public final Optional<String> keystoreLocation;
  public final Optional<String> keystorePassword;
  public final Optional<String> saslMechanism;
  public final Optional<String> saslJaasConfigTemplate;
  public final Optional<String> saslUsername;
  public final Optional<String> saslPassword;
}
