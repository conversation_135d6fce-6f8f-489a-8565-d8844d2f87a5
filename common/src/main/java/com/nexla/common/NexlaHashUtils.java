package com.nexla.common;

import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.SneakyThrows;

public class NexlaHashUtils {

  @SneakyThrows
  public static String hashAndBase64Encode(
      String data, String key, String charset, String algorithm) {
    Mac hasher = Mac.getInstance(algorithm);
    SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(charset), algorithm);
    hasher.init(secretKey);
    return Base64.getEncoder().encodeToString(hasher.doFinal(data.getBytes(charset)));
  }
}
