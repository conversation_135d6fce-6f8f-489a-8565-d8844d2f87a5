package com.nexla.connector.config;

import org.apache.kafka.common.config.ConfigDef;

public class NexlaConfigKeyBuilder {

  private final String name;
  private final ConfigDef.Type type;
  private Object defaultValue;
  private ConfigDef.Validator validator;
  private ConfigDef.Importance importance = ConfigDef.Importance.HIGH;
  private String documentation = "";
  private String group = "";
  private String displayName = "";
  private NexlaConfigKey.DependsOn dependsOn;
  private NexlaConfigKey.Nullability nullability = NexlaConfigKey.Nullability.NULLABLE;
  private boolean maskValue = false;

  public static NexlaConfigKeyBuilder nexlaKey(
      String name, ConfigDef.Type type, Object defaultValue) {
    return new NexlaConfigKeyBuilder(name, type, defaultValue);
  }

  public NexlaConfigKeyBuilder(String name, ConfigDef.Type type, Object defaultValue) {
    this.name = name;
    this.type = type;
    this.defaultValue = defaultValue;
  }

  public NexlaConfigKey build(int orderInGroup) {
    return new NexlaConfigKey(
        name,
        type,
        defaultValue,
        validator,
        importance,
        documentation,
        group,
        orderInGroup,
        ConfigDef.Width.LONG,
        displayName,
        dependsOn,
        nullability,
        maskValue);
  }

  public NexlaConfigKeyBuilder validator(ConfigDef.Validator validator) {
    this.validator = validator;
    return this;
  }

  public NexlaConfigKeyBuilder importance(ConfigDef.Importance importance) {
    this.importance = importance;
    return this;
  }

  public NexlaConfigKeyBuilder documentation(String documentation) {
    this.documentation = documentation;
    return this;
  }

  public NexlaConfigKeyBuilder group(String group) {
    this.group = group;
    return this;
  }

  public NexlaConfigKeyBuilder displayName(String displayName) {
    this.displayName = displayName;
    return this;
  }

  public NexlaConfigKeyBuilder dependsOn(NexlaConfigKey.DependsOn dependsOn) {
    this.dependsOn = dependsOn;
    return this;
  }

  public NexlaConfigKeyBuilder nullable() {
    this.nullability = NexlaConfigKey.Nullability.NULLABLE;
    return this;
  }

  public NexlaConfigKeyBuilder notNullable() {
    this.nullability = NexlaConfigKey.Nullability.NO_NULLS;
    return this;
  }

  public NexlaConfigKeyBuilder notEmpty() {
    this.nullability = NexlaConfigKey.Nullability.NOT_EMPTY;
    return this;
  }

  public NexlaConfigKeyBuilder maskValue() {
    this.maskValue = true;
    return this;
  }
}
