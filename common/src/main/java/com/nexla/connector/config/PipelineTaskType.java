package com.nexla.connector.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.google.common.base.Functions;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public enum PipelineTaskType {
  KAFKA_TX("tx", false),
  SQL_TX("sql_tx", false),
  SINK_FILE("sinkfile", true),
  SINK_JDBC("sinkjdbc", true),
  SINK_BIGQ("sinkbigq", true),
  SINK_REST("sinkrest", true),
  SINK_SOAP("sinksoap", true),
  SINK_REDIS("sinkredis", true),
  SINK_DOCUMENT("sinkdocument", true),
  SINK_SHEET("sinksheet", true),
  SINK_DELTA_LAKE("sinkdelta", true),
  SINK_ICEBERG("sinkiceberg", true),
  SINK_VECTOR("sinkvector", true),
  STREAMING_SINK("sinkstream", false),
  STREAMING_SOURCE("sourcestream", false),
  @Deprecated
  IN_MEMORY("memory", false), // going to be removed in favor of fast_flow - 2.16.1 / NEX-9070
  IN_MEMORY_FLOW("inmemory", false),
  REPLICATION_FLOW("replication", false),
  SPARK("spark", false);

  public final String shortName;
  public final Boolean isSink;

  private static final Map<String, PipelineTaskType> nameMap =
      Arrays.stream(PipelineTaskType.values())
          .collect(Collectors.toMap(t -> t.shortName, Functions.identity()));

  PipelineTaskType(String name, boolean isSink) {
    this.shortName = name;
    this.isSink = isSink;
  }

  @JsonCreator
  public static PipelineTaskType fromString(String key) {
    return PipelineTaskType.valueOf(key.toUpperCase());
  }

  public static PipelineTaskType fromName(String name) {
    return Optional.ofNullable(nameMap.get(name.toLowerCase()))
        .orElseThrow(() -> new IllegalArgumentException("Unknown PipelineTaskType name: " + name));
  }

  public boolean isRunId() {
    return EnumSet.of(IN_MEMORY, IN_MEMORY_FLOW, REPLICATION_FLOW, SPARK).contains(this);
  }

  // TODO Pay attention, method does not distinguish stateful sinks.
  public boolean isStatefulTask() {
    return EnumSet.of(IN_MEMORY, IN_MEMORY_FLOW, REPLICATION_FLOW, SPARK).contains(this);
  }
}
