package com.nexla.connector.config.file;

import static com.nexla.common.StreamUtils.lhm;

import java.net.URI;
import java.util.Map;
import one.util.streamex.EntryStream;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.rds.RdsClient;
import software.amazon.awssdk.services.rds.model.GenerateAuthenticationTokenRequest;

public class IamAuth {

  public static String iamAuthToken(
      AWSAuthConfig awsAuthConfig, String hostname, String port, String username) {
    AwsCredentialsProvider provider =
        NexlaAWSCredentialsProvider.getCredentialsProvider(awsAuthConfig);

    RdsClient rdsClient =
        RdsClient.builder()
            .credentialsProvider(provider)
            .region(Region.of(awsAuthConfig.region))
            .build();

    GenerateAuthenticationTokenRequest authTokenRequest =
        GenerateAuthenticationTokenRequest.builder()
            .hostname(hostname)
            .port(Integer.parseInt(port))
            .username(username)
            .build();

    return rdsClient.utilities().generateAuthenticationToken(authTokenRequest);
  }

  public static Pair<String, String> getIamPasswordUrl(
      String jdbcUrl, String jdbcUser, AWSAuthConfig awsAuthConfig) {
    String clearUrl = jdbcUrl.substring(5);
    URI uri = URI.create(clearUrl);

    String iAmToken =
        iamAuthToken(awsAuthConfig, uri.getHost(), String.valueOf(uri.getPort()), jdbcUser);

    Map<String, String> iamProperties =
        lhm(
            "verifyServerCertificate",
            "true",
            "useSSL",
            "true",
            "requireSSL",
            "true",
            "user",
            jdbcUser,
            "awsRegion",
            awsAuthConfig.region);

    String props = EntryStream.of(iamProperties).mapKeyValue((k, v) -> k + "=" + v).joining("&");

    String joiningSymbol = (StringUtils.isEmpty(uri.getQuery())) ? "?" : "&";
    return Pair.with(iAmToken, jdbcUrl + joiningSymbol + props);
  }
}
