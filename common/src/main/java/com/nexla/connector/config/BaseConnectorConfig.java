package com.nexla.connector.config;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static java.util.Optional.ofNullable;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Importance.MEDIUM;
import static org.apache.kafka.common.config.ConfigDef.NO_DEFAULT_VALUE;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

import com.google.common.collect.Maps;
import com.nexla.common.ConfigUtils;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.time.EpochDateTimeFormatter;
import com.nexla.common.time.NexlaTimeUnit;
import com.nexla.common.time.VarUtils;
import com.nexla.common.time.VarUtils.VarInfo;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;

public class BaseConnectorConfig extends AbstractNoLoggingConfig {

  public static final String VAULT_HOST = "vault.host";
  public static final String VAULT_TOKEN = "vault.token";

  public static final String MAX_HEARTBEAT_PERIOD_MS = "heartbeat.period.ms";
  public static final String TUNNEL_BASTION_HOST = "tunnel.bastion.host";
  public static final String TUNNEL_BASTION_PORT = "tunnel.bastion.port";
  public static final String TUNNEL_BASTION_USER = "tunnel.bastion.user";
  public static final String DATE_TIME_UNIT = "date.time.unit";
  public static final String DATE_FORMAT = "date.format";

  public static final String LOG_VERBOSE = "log.verbose";

  public static final String NOTIFY_TOPIC = "topic.notify";
  public static final String METRICS_TOPIC = "topic.metrics";

  public static final String FAST_MODE = "fast.mode";
  public static final String CTRL_SERVER_URL = "ctrl.server.url";
  public static final String PROBE_APP_URL = "probe.app.url";
  public static final String CTRL_NODETASKMANAGER_URL = "ctrl.nodetaskmanager.url";
  public static final String SKIP_CTRL_NODE_CHECK = "skip.ctrl.node.check";

  public static final String TASKS_MAX = "tasks.max";
  public static final String UNIT_TEST = "unit.test";

  public final boolean logVerbose;

  public final boolean unitTest;

  public final NexlaKafkaConfig controlKafkaContext;
  public final NexlaKafkaConfig dataKafkaContext;
  public final NexlaSslContext nexlaSslConfig;
  public final String decryptKey;
  public final String apiAccessKey;

  public final String dataplaneUid;
  public final String apiCredentialsServer;

  public final String listingAppServer;
  public final String coordinationServer;
  public final boolean listingEnabled;

  public final String nexlaUsername;
  public final String nexlaPassword;

  public final String topicMetrics;
  public final String topicNotify;

  public final boolean fastMode;
  public final String credentialsSource;
  public final Optional<String> secretManagerRegion;
  public final Optional<String> secretManagerAccessKey;
  public final Optional<String> secretManagerSecretKey;
  public final Optional<String> secretManagerRoleArn;
  public final Optional<String> secretManagerIdentityTokenFile;
  public final Optional<String> secretNames;
  public final Boolean listingMode;
  public final Optional<String> vaultHost;
  public final Optional<String> vaultToken;
  public final EpochDateTimeFormatter dateFormat;
  public final NexlaTimeUnit dateTimeUnit;
  public final String fileVaultServer;
  public final Long maxHeartbeatPeriodMs;
  public final Optional<String> ctrlHttpUrl;
  public final String probeAppUrl;
  public final String enrichmentUrl;
  public final String ctrlNodeTaskManagerUrl;
  public final boolean skipCtrlNodeCheck;
  public final int tasksMax;

  public BaseConnectorConfig(NexlaConfigDef definition, Map<String, ?> originals) {
    super(definition, originals);
    ConnectorAdminConfig adminConfig = new ConnectorAdminConfig(originals);
    this.decryptKey = adminConfig.decryptKey;
    this.apiAccessKey = adminConfig.apiAccessKey;
    this.dataplaneUid = adminConfig.dataplaneUid;
    this.apiCredentialsServer = adminConfig.apiCredentialsServer;
    this.enrichmentUrl = adminConfig.enrichmentUrl;
    this.nexlaSslConfig = adminConfig.nexlaSslConfig;
    this.listingMode = getBoolean(LISTING_MODE);

    this.logVerbose = getBoolean(LOG_VERBOSE);
    this.unitTest = getBoolean(UNIT_TEST);

    this.listingAppServer = getString(LISTING_APP_SERVER_URL);
    this.coordinationServer = getString(COORDINATION_SERVER_URL);
    this.listingEnabled = getBoolean(LISTING_ENABLED);
    this.secretNames = ConfigUtils.opt(getString(SECRET_NAMES));

    if (unitTest) {
      this.nexlaUsername = getString(NEXLA_USERNAME);
      this.nexlaPassword = getString(NEXLA_PASSWORD);
    } else {
      Map<String, String> creds =
          getCreds(
              adminConfig.decryptKey, getString(NEXLA_CREDS_ENC), getString(NEXLA_CREDS_ENC_IV));
      this.nexlaUsername = creds.get(NEXLA_USERNAME);
      this.nexlaPassword = creds.get(NEXLA_PASSWORD);
    }

    this.topicMetrics =
        ConfigUtils.opt(getString(METRICS_TOPIC)).orElse(NexlaConstants.TOPIC_METRICS);
    this.topicNotify = ConfigUtils.opt(getString(NOTIFY_TOPIC)).orElse(NexlaConstants.TOPIC_NOTIFY);
    this.controlKafkaContext = adminConfig.controlKafkaConfig;
    this.dataKafkaContext = adminConfig.dataKafkaConfig;
    this.fastMode = getBoolean(FAST_MODE);
    this.credentialsSource = getString(CREDENTIALS_SOURCE);
    this.secretManagerRegion = ConfigUtils.opt(getString(AWS_SECRET_MANAGER_REGION));
    this.secretManagerAccessKey = ConfigUtils.opt(getString(AWS_SECRET_MANAGER_ACCESS_KEY));
    this.secretManagerSecretKey = ConfigUtils.opt(getString(AWS_SECRET_MANAGER_SECRET_KEY));
    this.secretManagerRoleArn = ConfigUtils.opt(getString(AWS_SECRET_MANAGER_ROLE_ARN));
    this.secretManagerIdentityTokenFile =
        ConfigUtils.opt(getString(AWS_SECRET_MANAGER_IDENTITY_TOKEN_FILE));
    this.vaultHost = ConfigUtils.opt(getString(VAULT_HOST));
    this.vaultToken = ConfigUtils.opt(getString(VAULT_TOKEN));
    this.dateFormat = new EpochDateTimeFormatter(getString(DATE_FORMAT));
    this.dateTimeUnit = NexlaTimeUnit.findByPattern(getString(DATE_TIME_UNIT));
    this.fileVaultServer = getString(FILE_VAULT_SERVER);
    this.maxHeartbeatPeriodMs = getLong(MAX_HEARTBEAT_PERIOD_MS);
    this.ctrlHttpUrl = ConfigUtils.opt(getString(CTRL_SERVER_URL));
    this.ctrlNodeTaskManagerUrl = getString(CTRL_NODETASKMANAGER_URL);
    this.probeAppUrl = getString(PROBE_APP_URL);
    this.skipCtrlNodeCheck = this.unitTest || getBoolean(SKIP_CTRL_NODE_CHECK);
    this.tasksMax = getInt(TASKS_MAX);
  }

  protected String replaceNowVars(String p) {
    VarInfo strVar = VarUtils.processStringWithVars(p);
    var replacementMap = getSubstitutionDatesMap(strVar);
    return VarUtils.replaceVars(strVar, replacementMap);
  }

  protected String withMacro(String value) {
    return ConfigUtils.withMacro(value, this::replaceNowVars);
  }

  protected Optional<String> optWithMacro(String value) {
    return ConfigUtils.optWithMacro(value, this::replaceNowVars);
  }

  protected Optional<Integer> optIntWithMacro(String value) {
    return ConfigUtils.optIntWithMacro(value, this::replaceNowVars);
  }

  /**
   * Enrich original default values in VarInfo with {@link VarUtils#DATE_VAR} variable if according
   * value was determined by {@link VarUtils#processStringWithVars(String)} and exists in the
   * VarInfo variables set
   *
   * @param varInfo - original {@link VarInfo} object to be enriched
   * @return - new {@link VarInfo} object with enriched default values map
   */
  protected VarInfo enrichWithNowVars(VarInfo varInfo) {
    Map<String, String> resultMap = Maps.newHashMap();
    resultMap.putAll(varInfo.defaults);
    if (VarUtils.hasDateVariableForSubstitute(varInfo.variables))
      resultMap.putAll(getSubstitutionDatesMap(varInfo));
    return new VarInfo(varInfo.template, varInfo.variables, resultMap);
  }

  protected Map<String, String> getSubstitutionDatesMap(VarInfo varInfo) {
    return VarUtils.getSubstitutionDates(
        Optional.of(dateTimeUnit), Optional.of(dateFormat), varInfo.variables, nowUTC());
  }

  public static NexlaConfigDef baseConfigDef() {

    return ConnectorAdminConfig.adminConfigDef()
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(LISTING_APP_SERVER_URL, ConfigDef.Type.STRING, null)
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(COORDINATION_SERVER_URL, ConfigDef.Type.STRING, null)
                .importance(LOW))
        .withKey(NexlaConfigKeyBuilder.nexlaKey(LISTING_ENABLED, BOOLEAN, true).importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(NEXLA_CREDS_ENC, ConfigDef.Type.STRING, null)
                .maskValue()
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(NEXLA_CREDS_ENC_IV, ConfigDef.Type.STRING, null)
                .maskValue()
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(NEXLA_USERNAME, ConfigDef.Type.STRING, null)
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(NEXLA_PASSWORD, ConfigDef.Type.STRING, null)
                .maskValue()
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(UNIT_TEST, BOOLEAN, false)
                .importance(LOW)
                .documentation("Configure for unit testing")
                .displayName("Configure for unit testing"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(LOG_VERBOSE, BOOLEAN, false)
                .importance(LOW)
                .documentation("Log verbose")
                .displayName("Log verbose"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(
                    METRICS_TOPIC, ConfigDef.Type.STRING, NexlaConstants.TOPIC_METRICS)
                .importance(LOW)
                .documentation("Topic for metrics")
                .displayName("Topic for metrics"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(
                    NOTIFY_TOPIC, ConfigDef.Type.STRING, NexlaConstants.TOPIC_NOTIFY)
                .importance(LOW)
                .documentation("Topic for notifications")
                .displayName("Topic for notifications"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(
                    CONNECT_BOOTSTRAP_SERVERS,
                    ConfigDef.Type.STRING,
                    System.getenv(CONNECT_BOOTSTRAP_SERVERS))
                .importance(LOW)
                .documentation("Connect bootstrap servers (use ssl)")
                .displayName("Connect bootstrap servers"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(FAST_MODE, BOOLEAN, false)
                .importance(LOW)
                .documentation("Fast mode: passed only internally to disable some legacy handlers")
                .displayName("Fast mode: passed only internally to disable some legacy handlers"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(CREDENTIALS_SOURCE, STRING, NO_DEFAULT_VALUE)
                .importance(LOW)
                .documentation("Credentials source")
                .displayName("Credentials source"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(AWS_SECRET_MANAGER_REGION, STRING, null)
                .importance(LOW)
                .documentation("Secret manager region")
                .displayName("Secret manager region"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(AWS_SECRET_MANAGER_ACCESS_KEY, STRING, null)
                .maskValue()
                .importance(LOW)
                .documentation("Secret manager access key")
                .displayName("Secret manager access key"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(AWS_SECRET_MANAGER_SECRET_KEY, STRING, null)
                .maskValue()
                .importance(LOW)
                .documentation("Secret manager secret key")
                .displayName("Secret manager secret key"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(AWS_SECRET_MANAGER_ROLE_ARN, STRING, null)
                .maskValue()
                .importance(LOW)
                .documentation("Secret manager role arn")
                .displayName("Secret manager role arn"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(AWS_SECRET_MANAGER_IDENTITY_TOKEN_FILE, STRING, null)
                .maskValue()
                .importance(LOW)
                .documentation("Secret manager identity token file")
                .displayName("Secret manager identity token file"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(SECRET_NAMES, ConfigDef.Type.STRING, null)
                .maskValue()
                .documentation("Secret names to read credentials")
                .displayName("Secret names to read credentials"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(LISTING_MODE, BOOLEAN, false)
                .importance(LOW)
                .documentation("Listing mode: passed only internally")
                .displayName("Listing mode: passed only internally"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(VAULT_HOST, STRING, null)
                .importance(LOW)
                .documentation("Vault host")
                .displayName("Vault host"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(VAULT_TOKEN, STRING, null)
                .maskValue()
                .importance(LOW)
                .documentation("Vault token")
                .displayName("Vault token"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(DATE_FORMAT, ConfigDef.Type.STRING, "yyyy-MM-dd")
                .importance(LOW)
                .documentation("DateTime var format")
                .displayName("DateTime var format"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(DATE_TIME_UNIT, ConfigDef.Type.STRING, "dd")
                .importance(LOW)
                .documentation("DateTime add/sub time unit")
                .displayName("DateTime add/sub time unit"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(FILE_VAULT_SERVER, ConfigDef.Type.STRING, null)
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(
                    MAX_HEARTBEAT_PERIOD_MS, LONG, TimeUnit.HOURS.toMillis(12))
                .importance(LOW)
                .documentation("Connector bulk tasks heartbeat period")
                .displayName("Connector bulk tasks heartbeat period"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(CTRL_SERVER_URL, ConfigDef.Type.STRING, null)
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(PROBE_APP_URL, ConfigDef.Type.STRING, null)
                .importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(CTRL_NODETASKMANAGER_URL, STRING, null).importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(SKIP_CTRL_NODE_CHECK, BOOLEAN, false).importance(LOW))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(TASKS_MAX, INT, 1)
                .importance(MEDIUM)
                .documentation("The maximum number of connector tasks")
                .displayName("The maximum number of connector tasks"));
  }

  public void ifLogVerbose(Runnable logFn) {
    if (logVerbose) {
      logFn.run();
    }
  }

  public static void require(String paramName, Optional<?> value) {
    value.orElseThrow(() -> new ConfigException(paramName + " should be set"));
  }

  public static void require(String paramName, Object value) {
    ofNullable(value).orElseThrow(() -> new ConfigException(paramName + " should be set"));
  }

  public static void requireOneOf(
      String paramName1, Optional<?> value1, String paramName2, Optional<?> value2) {
    if ((value1.isPresent() && value2.isPresent()) || (value1.isEmpty() && value2.isEmpty())) {
      throw new ConfigException(
          "Only one of parameters should be set: [" + paramName1 + ", " + paramName2 + "]");
    }

    if (value1.isEmpty()) {
      value2.orElseThrow(() -> new ConfigException(paramName2 + " should be set"));
    } else {
      value1.orElseThrow(() -> new ConfigException(paramName1 + " should be set"));
    }
  }
}
