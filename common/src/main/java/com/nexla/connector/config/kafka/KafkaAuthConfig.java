package com.nexla.connector.config.kafka;

import static com.nexla.common.ConfigUtils.*;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.clients.admin.AdminClientConfig.*;
import static org.apache.kafka.common.config.ConfigDef.Type.*;
import static org.apache.kafka.common.config.SaslConfigs.SASL_JAAS_CONFIG;
import static org.apache.kafka.common.config.SaslConfigs.SASL_MECHANISM;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.BaseAuthConfig;
import java.util.Map;
import java.util.Optional;

public class KafkaAuthConfig extends BaseAuthConfig {

  private static final String AUTH_GROUP = "";
  private static final NexlaConfigDef CONFIG_DEF = authConfigDef();

  public static final String TARGET_BOOTSTRAP_SERVERS = "target.bootstrap.servers";
  public static final String SCHEMA_REGISTRY_URL = "schema.registry.url";
  public static final String SCHEMA_REGISTRY_API_KEY = "schema.registry.api.key";
  public static final String SCHEMA_REGISTRY_API_SECRET = "schema.registry.api.secret";
  public static final String GROUP_ID_PREFIX = "group.id.prefix";
  public static final String GROUP_ID_STRICT_PREF = "group.id.strict";
  public static final String REQUEST_TIMEOUT_MS = "request.timeout.ms";

  public static final String SASL_JAAS_USERNAME = "sasl.jaas.username";
  public static final String SASL_JAAS_PASSWORD = "sasl.jaas.password";

  public final String targetBootstrapServers;
  public final Optional<String> saslJaasUsername;
  public final Optional<String> saslJaasPassword;
  public final Optional<String> saslJaasConfig;
  public final String saslMechanism;
  public final String securityProtocol;
  public final String schemaRegistryUrl;
  public final String schemaRegistryApiKey;
  public final String schemaRegistryApiSecret;
  public final Optional<String> groupIdPrefix;
  public final boolean strictGroupIdPref;
  public final Optional<Integer> requestTimeoutMs;

  public KafkaAuthConfig(Map<String, String> parsedConfig, Integer credsId) {
    super(CONFIG_DEF, parsedConfig, credsId);

    this.targetBootstrapServers = getString(TARGET_BOOTSTRAP_SERVERS);
    this.schemaRegistryUrl = getString(SCHEMA_REGISTRY_URL);
    this.schemaRegistryApiKey = getString(SCHEMA_REGISTRY_API_KEY);
    this.schemaRegistryApiSecret = getString(SCHEMA_REGISTRY_API_SECRET);
    this.saslJaasUsername = opt(getString(SASL_JAAS_USERNAME));
    this.saslJaasPassword = opt(getString(SASL_JAAS_PASSWORD));
    this.saslJaasConfig =
        saslJaasUsername
            .map(
                username ->
                    saslJaasPassword.map(password -> generateSaslJaasConfig(username, password)))
            .orElse(opt(getString(SASL_JAAS_CONFIG)));
    this.saslMechanism = getString(SASL_MECHANISM);
    this.securityProtocol = getString(SECURITY_PROTOCOL_CONFIG);
    this.groupIdPrefix = opt(getString(GROUP_ID_PREFIX));
    this.strictGroupIdPref = optBoolean(getBoolean(GROUP_ID_STRICT_PREF)).orElse(false);
    this.requestTimeoutMs = optInt(getInt(REQUEST_TIMEOUT_MS));
  }

  public static NexlaConfigDef authConfigDef() {

    return baseAuthConfigDef()
        .withKey(
            nexlaKey(TARGET_BOOTSTRAP_SERVERS, STRING, "localhost:9092")
                .documentation("Kafka bootstrap servers, generally the VIP used to access Kafka")
                .group(AUTH_GROUP)
                .displayName("Kafka bootstrap servers"))
        .withKey(
            nexlaKey(SCHEMA_REGISTRY_URL, STRING, null)
                .documentation("Schema Registry URL")
                .group(AUTH_GROUP)
                .displayName("Schema Registry URL"))
        .withKey(
            nexlaKey(SCHEMA_REGISTRY_API_KEY, STRING, null)
                .documentation("Schema Registry API Key")
                .group(AUTH_GROUP)
                .displayName("Schema Registry API Key"))
        .withKey(
            nexlaKey(SCHEMA_REGISTRY_API_SECRET, STRING, null)
                .documentation("Schema Registry API Secret")
                .group(AUTH_GROUP)
                .displayName("Schema Registry API Secret"))
        .withKey(
            nexlaKey(SASL_JAAS_CONFIG, STRING, null)
                .documentation("SASL JAAS config")
                .group(AUTH_GROUP)
                .displayName("SASL JAAS config"))
        .withKey(
            nexlaKey(SASL_MECHANISM, STRING, "PLAIN")
                .documentation("SASL mechanism [default: PLAIN]")
                .group(AUTH_GROUP)
                .displayName("SASL mechanism"))
        .withKey(
            nexlaKey(SECURITY_PROTOCOL_CONFIG, STRING, DEFAULT_SECURITY_PROTOCOL)
                .documentation("Security protocol [default: PLAINTEXT]")
                .group(AUTH_GROUP)
                .displayName("Security protocol"))
        .withKey(
            nexlaKey(GROUP_ID_PREFIX, STRING, null)
                .documentation("Group id prefix")
                .group(AUTH_GROUP)
                .displayName("Group id prefix"))
        .withKey(
            nexlaKey(GROUP_ID_STRICT_PREF, BOOLEAN, null)
                .documentation("Strict group id")
                .group(AUTH_GROUP)
                .displayName("Strict group id"))
        .withKey(
            nexlaKey(REQUEST_TIMEOUT_MS, INT, null)
                .documentation("Request timeout, in milliseconds")
                .group(AUTH_GROUP)
                .displayName("Request timeout"))
        .withKey(
            nexlaKey(SASL_JAAS_USERNAME, STRING, null)
                .documentation("SASL JAAS username")
                .group(AUTH_GROUP)
                .displayName("SASL JAAS username"))
        .withKey(
            nexlaKey(SASL_JAAS_PASSWORD, STRING, null)
                .documentation("SASL JAAS password")
                .group(AUTH_GROUP)
                .displayName("SASL JAAS password"));
  }

  private static String generateSaslJaasConfig(String username, String password) {
    return String.format(
        "org.apache.kafka.common.security.plain.PlainLoginModule required username='%s'"
            + " password='%s';",
        username, password);
  }
}
