package com.nexla.connector.config.file;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.ConnectionType.*;
import static com.nexla.connector.config.file.S3Constants.*;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.removeEnd;
import static org.apache.commons.lang3.StringUtils.removeStart;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;

import com.google.common.collect.Sets;
import com.nexla.common.ConfigUtils;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.NexlaConfigKeyBuilder;
import com.nexla.connector.config.rest.BaseAuthConfig;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.apache.kafka.common.config.ConfigDef;

public class AWSAuthConfig extends BaseAuthConfig {

  public static final String IAM_ROLE = "iam.role";
  public static final String TEST_PATH = "test.path";
  public static final String SERVICE_ENDPOINT = "service.endpoint";

  public final String accessKeyId;
  public final String secretKey;
  public final String region;
  public final String arn;
  public final String identityFile;
  public final String externalId;
  public final Optional<String> cryptoMode;
  public final Optional<String> kmsKey;
  public final boolean sseEnabled;
  public final Optional<String> sseKmsKey;
  public final String iamRole;
  public Optional<String> testPath;
  public final Boolean bucketOwnerFullControl;
  public final Optional<String> serviceEndpoint;

  public AWSAuthConfig(Map<String, ?> parsedConfig, Integer credsId) {
    super(authConfigDef(), parsedConfig, credsId);

    this.accessKeyId = getString(ACCESS_KEY_ID);
    this.secretKey = getString(SECRET_KEY);
    this.region = getString(REGION);
    this.arn = getString(ARN);
    this.externalId = getString(EXTERNAL_ID);
    this.iamRole = getString(IAM_ROLE);
    this.cryptoMode = ConfigUtils.opt(getString(ENCRYPTION_MODE));
    this.kmsKey = ConfigUtils.opt(getString(KMS_KEY));
    this.sseEnabled = getBoolean(SSE_ENABLED);
    this.sseKmsKey = ConfigUtils.opt(getString(SSE_KMS_KEY));
    this.identityFile = getString(IDENTITY_TOKEN_FILE);
    this.bucketOwnerFullControl = getBoolean(BUCKET_OWNER_FULL_CONTROL);

    if (parsedConfig.containsKey(TEST_PATH)) {
      this.testPath = ConfigUtils.opt(normalizePath(getString(TEST_PATH)));
    } else if (parsedConfig.containsKey("test.bucket")) {
      this.testPath =
          ConfigUtils.opt(
              normalizePath(
                  ConfigUtils.opt(getString("test.bucket")).orElse("")
                      + "/"
                      + ConfigUtils.opt(getString("test.prefix")).orElse("")));
    } else {
      this.testPath = empty();
    }
    this.serviceEndpoint = ConfigUtils.opt(getString(SERVICE_ENDPOINT));
  }

  public static NexlaConfigDef authConfigDef() {

    return BaseAuthConfig.baseAuthConfigDef()
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(ACCESS_KEY_ID, ConfigDef.Type.STRING, "")
                .documentation("AWS access key ID")
                .displayName("AWS access key ID"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(SECRET_KEY, ConfigDef.Type.STRING, "")
                .documentation("AWS Secret Key")
                .displayName("AWS secret key"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(TEST_PATH, ConfigDef.Type.STRING, null)
                .documentation("Test path")
                .displayName("Test path"))

        // for backward compatibility
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey("test.bucket", ConfigDef.Type.STRING, null)
                .documentation("Test bucket")
                .displayName("Test bucket"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey("test.prefix", ConfigDef.Type.STRING, null)
                .documentation("Test bucket")
                .displayName("Test prefix"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(REGION, ConfigDef.Type.STRING, "us-east-1")
                .validator(
                    ConfigDef.ValidString.in(
                        "us-east-2",
                        "us-east-1",
                        "us-west-1",
                        "us-west-2",
                        "ap-south-1",
                        "ap-northeast-2",
                        "ap-southeast-1",
                        "ap-southeast-2",
                        "ap-northeast-1",
                        "ca-central-1",
                        "eu-central-1",
                        "eu-west-1",
                        "eu-west-2",
                        "sa-east-1"))
                .documentation("AWS Region for bucket, defaults to us-east-1")
                .displayName(REGION))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(ARN, ConfigDef.Type.STRING, null)
                .documentation("Amazon Resource Name, only valid for ARN based access")
                .displayName("Amazon Resource Name"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(IDENTITY_TOKEN_FILE, ConfigDef.Type.STRING, null)
                .documentation("Amazon Identity Token File")
                .displayName("Amazon Identity Token File"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(EXTERNAL_ID, ConfigDef.Type.STRING, null)
                .documentation("AWS External Id, only valid for ARN based access")
                .displayName("AWS External Id, only valid for ARN based access"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(IAM_ROLE, ConfigDef.Type.STRING, null)
                .documentation("IAM Role")
                .displayName("IAM Role"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(KMS_KEY, ConfigDef.Type.STRING, null)
                .documentation("KMS key")
                .displayName("KMS key"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(SSE_KMS_KEY, ConfigDef.Type.STRING, null)
                .documentation("SSE key")
                .displayName("SSE key"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(SSE_ENABLED, BOOLEAN, false)
                .documentation("Is SSE enabled")
                .displayName("Is SSE enabled"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(ENCRYPTION_MODE, ConfigDef.Type.STRING, null)
                .documentation(
                    "Encryption mode: EncryptionOnly, AuthenticatedEncryption,"
                        + " StrictAuthenticatedEncryption")
                .documentation("Encryption mode"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(BUCKET_OWNER_FULL_CONTROL, ConfigDef.Type.BOOLEAN, false)
                .documentation(
                    "New objects uploaded to this bucket will be owned by the bucket owner.")
                .displayName("Grant Full Control to Bucket Owner"))
        .withKey(
            NexlaConfigKeyBuilder.nexlaKey(SERVICE_ENDPOINT, ConfigDef.Type.STRING, null)
                .documentation("Service endpoint")
                .displayName("Service endpoint"));
  }

  @AllArgsConstructor
  @ToString
  public static class BucketPrefix {
    public final String bucket;
    public final String prefix;
  }

  public static BucketPrefix toBucketPrefix(String path, boolean normalize) {
    int firstSlashIndex = opt(path).map(p -> p.indexOf("/")).orElse(-1);
    final String bucket;
    final String prefix;
    if (firstSlashIndex != -1) {
      bucket = path.substring(0, firstSlashIndex);
      prefix = path.substring(firstSlashIndex + 1);
    } else {
      bucket = path;
      prefix = null;
    }
    return new BucketPrefix(bucket, normalize ? normalizePath(prefix) : prefix);
  }

  public static String getDirectoryPath(ConnectionType connectionType, String configPath) {
    return isCloudObjectStore(connectionType)
        ? toBucketPrefix(configPath, true).prefix
        : configPath;
  }

  public static String getFilePath(ConnectionType connectionType, String configPath) {
    String path =
        isCloudObjectStore(connectionType)
            ? normalizeFilePath(connectionType, toBucketPrefix(configPath, false).prefix)
            : configPath;

    return removeEnd(path, "/");
  }

  public static String normalizeFilePath(ConnectionType connectionType, String filePath) {
    return isCloudObjectStore(connectionType) ? removeStart(filePath, "/") : filePath;
  }

  public static boolean isCloudObjectStore(ConnectionType connectionType) {
    return Sets.newHashSet(
            S3,
            GCS,
            AZURE_BLB,
            AZURE_DATA_LAKE,
            MERCURY_S3,
            NEPTUNE_S3,
            MIN_IO_S3,
            DELTA_LAKE_S3,
            DELTA_LAKE_AZURE_BLB,
            DELTA_LAKE_AZURE_DATA_LAKE,
            S3_ICEBERG)
        .contains(connectionType);
  }

  public static String normalizePath(String prefix) {
    return opt(prefix)
        .filter("/"::equals)
        .orElseGet(() -> ofNullable(prefix).map(p -> Paths.get(p).toString() + "/").orElse("/"));
  }
}
