package com.nexla.connector.config.vault;

import static com.nexla.common.StreamUtils.stringifyMap;

import com.bazaarvoice.jolt.JsonUtils;
import java.io.File;
import java.io.FileReader;
import java.util.Map;
import java.util.Optional;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

public class LocalCredentialsStore implements CredentialsStore {

  public static final String TYPE = "LOCAL";

  private final File configPath;

  public LocalCredentialsStore() {
    String envPath = System.getenv().get("NEXLA_LOCAL_VAULT");
    if (envPath == null) {
      this.configPath = new File("/etc/nexla/prod");
    } else {
      this.configPath = new File(envPath);
    }
  }

  @SneakyThrows
  @Override
  public Optional<Map<String, String>> getValuesMap(String path) {
    File valuePath = new File(configPath, path.replaceAll("/", "."));
    if (valuePath.exists()) {
      return Optional.of(
          stringifyMap(JsonUtils.jsonToMap(IOUtils.toString(new FileReader(valuePath)))));
    } else {
      return Optional.empty();
    }
  }

  @Override
  public String getType() {
    return TYPE;
  }
}
