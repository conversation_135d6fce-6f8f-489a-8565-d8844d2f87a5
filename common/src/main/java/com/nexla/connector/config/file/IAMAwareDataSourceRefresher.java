package com.nexla.connector.config.file;

import static com.nexla.common.StreamUtils.map;
import static com.nexla.connector.config.file.S3Constants.REGION;

import com.mchange.v2.c3p0.ComboPooledDataSource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.javatuples.Pair;

public class IAMAwareDataSourceRefresher {

  private static final ScheduledExecutorService executorService =
      Executors.newScheduledThreadPool(1);

  public static void addC3P0DataSourceRefresh(
      ComboPooledDataSource dataSource, String jdbcUrl, String region, String jdbcUser) {
    addC3P0DataSourceRefresh(
        dataSource, jdbcUrl, jdbcUser, new AWSAuthConfig(map(REGION, region), null));
  }

  public static void addC3P0DataSourceRefresh(
      ComboPooledDataSource dataSource,
      String jdbcUrl,
      String jdbcUser,
      AWSAuthConfig awsAuthConfig) {
    executorService.scheduleAtFixedRate(
        () -> {
          Pair<String, String> pwdUrl = IamAuth.getIamPasswordUrl(jdbcUrl, jdbcUser, awsAuthConfig);
          dataSource.setPassword(pwdUrl.getValue0());
          dataSource.setJdbcUrl(pwdUrl.getValue1());
        },
        10,
        10,
        TimeUnit.MINUTES);
  }
}
