package com.nexla.connector.config;

import static org.apache.kafka.common.config.ConfigDef.ConfigKey;
import static org.apache.kafka.common.config.ConfigDef.Importance;
import static org.apache.kafka.common.config.ConfigDef.Type;
import static org.apache.kafka.common.config.ConfigDef.Validator;
import static org.apache.kafka.common.config.ConfigDef.Width;

import java.util.Collections;

public class NexlaConfigKey extends ConfigKey {

  public static class DependsOn {
    public final String name;
    public final Object value;

    public DependsOn(String name, Object value) {
      this.name = name;
      this.value = value;
    }
  }

  public enum Nullability {
    NULLABLE,
    NOT_EMPTY,
    NO_NULLS
  }

  public final DependsOn dependsOn;
  public final Nullability nullability;
  public final boolean maskValue;

  public NexlaConfigKey(
      String name,
      Type type,
      Object defaultValue,
      Validator validator,
      Importance importance,
      String documentation,
      String group,
      int orderInGroup,
      Width width,
      String displayName,
      DependsOn dependsOn,
      Nullability nullability,
      boolean maskValue) {
    super(
        name,
        type,
        defaultValue,
        validator,
        importance,
        documentation,
        group,
        orderInGroup,
        width,
        displayName,
        Collections.emptyList(),
        null,
        false);
    this.dependsOn = dependsOn;
    this.nullability = nullability;
    this.maskValue = maskValue;
  }
}
