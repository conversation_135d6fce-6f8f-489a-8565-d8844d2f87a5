package com.nexla.connector.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.common.config.ConfigValue;
import org.apache.kafka.common.config.types.Password;
import org.apache.kafka.common.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NexlaConfigDef extends ConfigDef {

  private static final Logger LOGGER = LoggerFactory.getLogger(NexlaConfigDef.class);

  private final Map<String, NexlaConfigKey> configKeys;
  private Set<String> configsWithNoParent;
  private int order;

  public NexlaConfigDef() {
    this.configsWithNoParent = null;
    this.configKeys = new LinkedHashMap<>();
  }

  public NexlaConfigDef(NexlaConfigDef base) {
    super(base);
    this.configKeys = new LinkedHashMap<>(base.configKeys);
    this.configsWithNoParent = null;
  }

  public Map<String, ConfigKey> configKeys() {
    return (Map) configKeys;
  }

  /**
   * Returns unmodifiable set of properties names defined in this {@linkplain NexlaConfigDef}
   *
   * @return new unmodifiable {@link Set} instance containing the keys
   */
  public Set<String> names() {
    return Collections.unmodifiableSet(configKeys.keySet());
  }

  public Map<String, Object> defaultValues() {
    Map<String, Object> defaultValues = new HashMap<>();
    for (ConfigKey key : configKeys.values()) {
      if (key.defaultValue != NO_DEFAULT_VALUE) defaultValues.put(key.name, key.defaultValue);
    }
    return defaultValues;
  }

  public NexlaConfigDef withKey(NexlaConfigKeyBuilder builder) {
    NexlaConfigKey key = builder.build(this.order++);
    if (configKeys.containsKey(key.name)) {
      throw new ConfigException("Configuration " + key.name + " is defined twice.");
    }
    if (key.group != null && !groups().contains(key.group)) {
      groups().add(key.group);
    }
    configKeys.put(key.name, key);
    return this;
  }

  public NexlaConfigDef withKeyOverrideExisting(NexlaConfigKeyBuilder builder) {
    NexlaConfigKey key = builder.build(this.order++);
    if (configKeys.containsKey(key.name)) {
      final int existingOrder = configKeys.get(key.name).orderInGroup;
      NexlaConfigKey overrideKey = builder.build(existingOrder);
      configKeys.put(key.name, overrideKey);
      return this;
    }
    if (key.group != null && !groups().contains(key.group)) {
      groups().add(key.group);
    }
    configKeys.put(key.name, key);
    return this;
  }

  /**
   * Parse and validate configs against this configuration definition. The input is a map of
   * configs. It is expected that the keys of the map are strings, but the values can either be
   * strings or they may already be of the appropriate type (int, string, etc). This will work
   * equally well with either java.util.Properties instances or a programmatically constructed map.
   *
   * @param props The configs to parse and validate.
   * @return Parsed and validated configs. The key will be the config name and the value will be the
   *     value parsed into the appropriate type (int, string, etc).
   */
  public Map<String, Object> parse(Map<?, ?> props) {
    // Check all configurations are defined
    List<String> undefinedConfigKeys = undefinedDependentConfigs();
    if (!undefinedConfigKeys.isEmpty()) {
      String joined = Utils.join(undefinedConfigKeys, ",");
      throw new ConfigException(
          "Some configurations in are referred in the dependents, but not defined: " + joined);
    }
    // parse all known keys
    Map<String, Object> values = new HashMap<>();
    for (NexlaConfigKey key : configKeys.values()) {
      Object value = props.get(key.name);
      boolean isSet = props.containsKey(key.name);
      try {
        values.put(key.name, parseValue(key, value, isSet));
      } catch (Exception e) {
        LOGGER.error("Error parsing key={} value={} isSet={}", key.name, value, isSet, e);
      }
    }
    return values;
  }

  Object parseValue(NexlaConfigKey key, Object value, boolean isSet) {
    if (!isSet && key.defaultValue != NO_DEFAULT_VALUE) {
      return key.defaultValue;
    }
    Object parsedValue = parseType(key, value);
    if (key.validator != null) {
      key.validator.ensureValid(key.name, parsedValue);
    }
    return parsedValue;
  }

  /**
   * Validate the current configuration values with the configuration definition.
   *
   * @param props the current configuration values
   * @return List of Config, each Config contains the updated configuration information given the
   *     current configuration values.
   */
  public List<ConfigValue> validate(Map<String, String> props) {
    return new ArrayList<>(validateAll(props).values());
  }

  public Map<String, ConfigValue> validateAll(Map<String, String> props) {
    Map<String, ConfigValue> configValues = new HashMap<>();
    for (String name : configKeys.keySet()) {
      configValues.put(name, new ConfigValue(name));
    }

    List<String> undefinedConfigKeys = undefinedDependentConfigs();
    for (String undefinedConfigKey : undefinedConfigKeys) {
      ConfigValue undefinedConfigValue = new ConfigValue(undefinedConfigKey);
      undefinedConfigValue.addErrorMessage(
          undefinedConfigKey + " is referred in the dependents, but not defined.");
      undefinedConfigValue.visible(false);
      configValues.put(undefinedConfigKey, undefinedConfigValue);
    }

    Map<String, Object> parsed = parseForValidate(props, configValues);
    return validate(parsed, configValues);
  }

  // package accessible for testing
  Map<String, Object> parseForValidate(
      Map<String, String> props, Map<String, ConfigValue> configValues) {
    Map<String, Object> parsed = new HashMap<>();
    Set<String> configsWithNoParent = getConfigsWithNoParent();
    for (String name : configsWithNoParent) {
      parseForValidate(name, props, parsed, configValues);
    }
    return parsed;
  }

  private Map<String, ConfigValue> validate(
      Map<String, Object> parsed, Map<String, ConfigValue> configValues) {
    Set<String> configsWithNoParent = getConfigsWithNoParent();
    for (String name : configsWithNoParent) {
      validate(name, parsed, configValues);
    }
    return configValues;
  }

  private List<String> undefinedDependentConfigs() {
    Set<String> undefinedConfigKeys = new HashSet<>();
    for (ConfigKey configKey : configKeys.values()) {
      for (String dependent : configKey.dependents) {
        if (!configKeys.containsKey(dependent)) {
          undefinedConfigKeys.add(dependent);
        }
      }
    }
    return new ArrayList<>(undefinedConfigKeys);
  }

  // package accessible for testing
  Set<String> getConfigsWithNoParent() {
    if (this.configsWithNoParent != null) {
      return this.configsWithNoParent;
    }
    Set<String> configsWithParent = new HashSet<>();

    for (ConfigKey configKey : configKeys.values()) {
      List<String> dependents = configKey.dependents;
      configsWithParent.addAll(dependents);
    }

    Set<String> configs = new HashSet<>(configKeys.keySet());
    configs.removeAll(configsWithParent);
    this.configsWithNoParent = configs;
    return configs;
  }

  private void parseForValidate(
      String name,
      Map<String, String> props,
      Map<String, Object> parsed,
      Map<String, ConfigValue> configs) {
    if (!configKeys.containsKey(name)) {
      return;
    }
    NexlaConfigKey key = configKeys.get(name);
    ConfigValue config = configs.get(name);

    Object value = null;
    if (props.containsKey(key.name)) {
      try {
        value = parseType(key, props.get(key.name));
      } catch (ConfigException e) {
        config.addErrorMessage(e.getMessage());
      }
    } else if (NO_DEFAULT_VALUE.equals(key.defaultValue)) {
      config.addErrorMessage(
          "Missing required configuration \"" + key.name + "\" which has no default value.");
    } else {
      value = key.defaultValue;
    }

    if (key.validator != null) {
      try {
        key.validator.ensureValid(key.name, value);
      } catch (ConfigException e) {
        config.addErrorMessage(e.getMessage());
      }
    }
    config.value(value);
    parsed.put(name, value);
    for (String dependent : key.dependents) {
      parseForValidate(dependent, props, parsed, configs);
    }
  }

  private void validate(String name, Map<String, Object> parsed, Map<String, ConfigValue> configs) {
    if (!configKeys.containsKey(name)) {
      return;
    }

    ConfigKey key = configKeys.get(name);
    ConfigValue value = configs.get(name);

    if (key.recommender != null) {
      try {
        List<Object> recommendedValues = key.recommender.validValues(name, parsed);
        List<Object> originalRecommendedValues = value.recommendedValues();
        if (!originalRecommendedValues.isEmpty()) {
          Set<Object> originalRecommendedValueSet = new HashSet<>(originalRecommendedValues);
          Iterator<Object> it = recommendedValues.iterator();
          while (it.hasNext()) {
            Object o = it.next();
            if (!originalRecommendedValueSet.contains(o)) {
              it.remove();
            }
          }
        }
        value.recommendedValues(recommendedValues);
        value.visible(key.recommender.visible(name, parsed));
      } catch (ConfigException e) {
        value.addErrorMessage(e.getMessage());
      }
    }

    configs.put(name, value);
    for (String dependent : key.dependents) {
      validate(dependent, parsed, configs);
    }
  }

  /**
   * Parse a value according to its expected type.
   *
   * @param value The config value
   * @return The parsed object
   */
  public static Object parseType(NexlaConfigKey key, Object value) {
    String name = key.name;
    Type type = key.type;
    Object defaultValue = key.defaultValue;

    try {
      if (value == null) {
        if (key.nullability == NexlaConfigKey.Nullability.NULLABLE) {
          return null;
        } else {
          if (defaultValue != NO_DEFAULT_VALUE) {
            return defaultValue;
          } else {
            throw new ConfigException(name, value, "Expected not-null value for " + key.name);
          }
        }
      }
      String trimmed = value.toString().trim();

      switch (type) {
        case BOOLEAN:
          if (Boolean.TRUE.toString().equalsIgnoreCase(trimmed)) {
            return true;
          } else if (Boolean.FALSE.toString().equalsIgnoreCase(trimmed)) {
            return false;
          } else if (defaultValue != NO_DEFAULT_VALUE) {
            return defaultValue;
          } else {
            throw new ConfigException(name, value, "Expected value to be either true or false");
          }
        case PASSWORD:
          if (value instanceof Password) {
            return value;
          } else {
            if (trimmed.length() > 0) {
              return new Password(trimmed);
            } else {
              if (defaultValue != NO_DEFAULT_VALUE) {
                if (defaultValue instanceof Password) {
                  return defaultValue;
                } else {
                  return new Password(defaultValue.toString());
                }
              } else {
                return new Password("");
              }
            }
          }
        case STRING:
          String candidate = value.toString();
          if (candidate.isEmpty() && key.nullability == NexlaConfigKey.Nullability.NOT_EMPTY) {
            if (defaultValue != NO_DEFAULT_VALUE) {
              return defaultValue;
            } else {
              throw new ConfigException(name, value, "Expected some value for " + key.name);
            }
          }
          return candidate;
        case INT:
          try {
            if (value instanceof Number) {
              return ((Number) value).intValue();
            }
            return Integer.parseInt(trimmed);
          } catch (Exception e) {
            if (defaultValue != NO_DEFAULT_VALUE) {
              return Integer.parseInt(defaultValue.toString());
            }
            throw new ConfigException(
                name, value, "Expected value to be a Integer, but it was a " + value);
          }
        case SHORT:
          try {
            if (value instanceof Number) {
              return ((Number) value).shortValue();
            }
            return Short.parseShort(trimmed);
          } catch (Exception e) {
            if (defaultValue != NO_DEFAULT_VALUE) {
              return Short.parseShort(defaultValue.toString());
            }
            throw new ConfigException(
                name, value, "Expected value to be a Short, but it was a " + value);
          }
        case LONG:
          try {
            if (value instanceof Number) {
              return ((Number) value).longValue();
            }
            return Long.parseLong(trimmed);
          } catch (Exception e) {
            if (defaultValue != NO_DEFAULT_VALUE) {
              return Long.parseLong(defaultValue.toString());
            }
            throw new ConfigException(
                name, value, "Expected value to be a Long, but it was a " + value);
          }
        case DOUBLE:
          try {
            if (value instanceof Number) {
              return ((Number) value).doubleValue();
            }
            return Double.parseDouble(trimmed);
          } catch (Exception e) {
            if (defaultValue != NO_DEFAULT_VALUE) {
              return Long.parseLong(defaultValue.toString());
            }
            throw new ConfigException(
                name, value, "Expected value to be a Double, but it was a " + value);
          }
        case LIST:
          if (value instanceof List) return value;
          else if (value instanceof String)
            if (trimmed.isEmpty()) return Collections.emptyList();
            else return Arrays.asList(trimmed.split("\\s*,\\s*", -1));
          else throw new ConfigException(name, value, "Expected a comma separated list.");
        case CLASS:
          if (value instanceof Class) return value;
          else if (value instanceof String)
            return Class.forName(trimmed, true, Utils.getContextOrKafkaClassLoader());
          else throw new ConfigException(name, value, "Expected a Class instance or class name.");
        default:
          throw new IllegalStateException("Unknown type.");
      }
    } catch (NumberFormatException e) {
      throw new ConfigException(name, value, "Not a number of type " + type);
    } catch (ClassNotFoundException e) {
      throw new ConfigException(name, value, "Class " + value + " could not be found.");
    }
  }

  public static String convertToString(Object parsedValue, Type type) {
    if (parsedValue == null) {
      return null;
    }

    if (type == null) {
      return parsedValue.toString();
    }

    switch (type) {
      case BOOLEAN:
      case SHORT:
      case INT:
      case LONG:
      case DOUBLE:
      case STRING:
      case PASSWORD:
        return parsedValue.toString();
      case LIST:
        List<?> valueList = (List<?>) parsedValue;
        return Utils.join(valueList, ",");
      case CLASS:
        Class<?> clazz = (Class<?>) parsedValue;
        return clazz.getName();
      default:
        throw new IllegalStateException("Unknown type.");
    }
  }

  /**
   * Converts a map of config (key, value) pairs to a map of strings where each value is converted
   * to a string. This method should be used with care since it stores actual password values to
   * String. Values from this map should never be used in log entries.
   */
  public static Map<String, String> convertToStringMapWithPasswordValues(Map<String, ?> configs) {
    Map<String, String> result = new HashMap<>();
    for (Map.Entry<String, ?> entry : configs.entrySet()) {
      Object value = entry.getValue();
      String strValue;
      if (value instanceof Password) strValue = ((Password) value).value();
      else if (value instanceof List) strValue = convertToString(value, Type.LIST);
      else if (value instanceof Class) strValue = convertToString(value, Type.CLASS);
      else strValue = convertToString(value, null);
      if (strValue != null) result.put(entry.getKey(), strValue);
    }
    return result;
  }

  protected List<String> headers() {
    return Arrays.asList("Name", "Description", "Type", "Default", "Valid Values", "Importance");
  }

  protected String getConfigValue(ConfigKey key, String headerName) {
    switch (headerName) {
      case "Name":
        return key.name;
      case "Description":
        return key.documentation;
      case "Type":
        return key.type.toString().toLowerCase(Locale.ROOT);
      case "Default":
        if (key.hasDefault()) {
          if (key.defaultValue == null) return "null";
          String defaultValueStr = convertToString(key.defaultValue, key.type);
          if (defaultValueStr.isEmpty()) return "\"\"";
          else return defaultValueStr;
        } else return "";
      case "Valid Values":
        return key.validator != null ? key.validator.toString() : "";
      case "Importance":
        return key.importance.toString().toLowerCase(Locale.ROOT);
      default:
        throw new RuntimeException(
            "Can't find value for header '" + headerName + "' in " + key.name);
    }
  }

  public String toHtmlTable() {
    return toHtmlTable(Collections.<String, String>emptyMap());
  }

  private void addHeader(StringBuilder builder, String headerName) {
    builder.append("<th>");
    builder.append(headerName);
    builder.append("</th>\n");
  }

  private void addColumnValue(StringBuilder builder, String value) {
    builder.append("<td>");
    builder.append(value);
    builder.append("</td>");
  }

  /**
   * Converts this config into an HTML table that can be embedded into docs. If <code>
   * dynamicUpdateModes</code> is non-empty, a "Dynamic Update Mode" column will be included n the
   * table with the value of the update mode. Default mode is "read-only".
   *
   * @param dynamicUpdateModes Config name -> update mode mapping
   */
  public String toHtmlTable(Map<String, String> dynamicUpdateModes) {
    boolean hasUpdateModes = !dynamicUpdateModes.isEmpty();
    List<ConfigKey> configs = sortedConfigs();
    StringBuilder b = new StringBuilder();
    b.append("<table class=\"data-table\"><tbody>\n");
    b.append("<tr>\n");
    // print column headers
    for (String headerName : headers()) {
      addHeader(b, headerName);
    }
    if (hasUpdateModes) addHeader(b, "Dynamic Update Mode");
    b.append("</tr>\n");
    for (ConfigKey key : configs) {
      if (key.internalConfig) {
        continue;
      }
      b.append("<tr>\n");
      // print column values
      for (String headerName : headers()) {
        addColumnValue(b, getConfigValue(key, headerName));
        b.append("</td>");
      }
      if (hasUpdateModes) {
        String updateMode = dynamicUpdateModes.get(key.name);
        if (updateMode == null) updateMode = "read-only";
        addColumnValue(b, updateMode);
      }
      b.append("</tr>\n");
    }
    b.append("</tbody></table>");
    return b.toString();
  }

  /**
   * Get the configs formatted with reStructuredText, suitable for embedding in Sphinx
   * documentation.
   */
  public String toRst() {
    StringBuilder b = new StringBuilder();
    for (ConfigKey key : sortedConfigs()) {
      if (key.internalConfig) {
        continue;
      }
      getConfigKeyRst(key, b);
      b.append("\n");
    }
    return b.toString();
  }

  /**
   * Configs with new metadata (group, orderInGroup, dependents) formatted with reStructuredText,
   * suitable for embedding in Sphinx documentation.
   */
  public String toEnrichedRst() {
    StringBuilder b = new StringBuilder();

    String lastKeyGroupName = "";
    for (ConfigKey key : sortedConfigs()) {
      if (key.internalConfig) {
        continue;
      }
      if (key.group != null) {
        if (!lastKeyGroupName.equalsIgnoreCase(key.group)) {
          b.append(key.group).append("\n");

          char[] underLine = new char[key.group.length()];
          Arrays.fill(underLine, '^');
          b.append(new String(underLine)).append("\n\n");
        }
        lastKeyGroupName = key.group;
      }

      getConfigKeyRst(key, b);

      if (key.dependents != null && key.dependents.size() > 0) {
        int j = 0;
        b.append("  * Dependents: ");
        for (String dependent : key.dependents) {
          b.append("``");
          b.append(dependent);
          if (++j == key.dependents.size()) b.append("``");
          else b.append("``, ");
        }
        b.append("\n");
      }
      b.append("\n");
    }
    return b.toString();
  }

  /** Shared content on Rst and Enriched Rst. */
  private void getConfigKeyRst(ConfigKey key, StringBuilder b) {
    b.append("``").append(key.name).append("``").append("\n");
    for (String docLine : key.documentation.split("\n")) {
      if (docLine.length() == 0) {
        continue;
      }
      b.append("  ").append(docLine).append("\n\n");
    }
    b.append("  * Type: ").append(getConfigValue(key, "Type")).append("\n");
    if (key.hasDefault()) {
      b.append("  * Default: ").append(getConfigValue(key, "Default")).append("\n");
    }
    if (key.validator != null) {
      b.append("  * Valid Values: ").append(getConfigValue(key, "Valid Values")).append("\n");
    }
    b.append("  * Importance: ").append(getConfigValue(key, "Importance")).append("\n");
  }

  /**
   * Get a list of configs sorted taking the 'group' and 'orderInGroup' into account.
   *
   * <p>If grouping is not specified, the result will reflect "natural" order: listing required
   * fields first, then ordering by importance, and finally by name.
   */
  private List<ConfigKey> sortedConfigs() {
    final Map<String, Integer> groupOrd = new HashMap<>(groups().size());
    int ord = 0;
    for (String group : groups()) {
      groupOrd.put(group, ord++);
    }

    List<ConfigKey> configs = new ArrayList<>(configKeys.values());
    Collections.sort(
        configs,
        new Comparator<ConfigKey>() {
          @Override
          public int compare(ConfigKey k1, ConfigKey k2) {
            int cmp =
                k1.group == null
                    ? (k2.group == null ? 0 : -1)
                    : (k2.group == null
                        ? 1
                        : Integer.compare(groupOrd.get(k1.group), groupOrd.get(k2.group)));
            if (cmp == 0) {
              cmp = Integer.compare(k1.orderInGroup, k2.orderInGroup);
              if (cmp == 0) {
                // first take anything with no default value
                if (!k1.hasDefault() && k2.hasDefault()) {
                  cmp = -1;
                } else if (!k2.hasDefault() && k1.hasDefault()) {
                  cmp = 1;
                } else {
                  cmp = k1.importance.compareTo(k2.importance);
                  if (cmp == 0) {
                    return k1.name.compareTo(k2.name);
                  }
                }
              }
            }
            return cmp;
          }
        });
    return configs;
  }
}
