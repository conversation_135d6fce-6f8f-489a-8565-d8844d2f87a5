package com.nexla.connector.config.file;

import lombok.Data;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

@Data
public class CustomS3ServiceCredentialProvider {

  public static final Logger LOGGER =
      LoggerFactory.getLogger(CustomS3ServiceCredentialProvider.class);
  public static final String MERCURY_AUTH_TYPE = "mercury_s3";
  public static final String NEPTUNE_AUTH_TYPE = "neptune_s3";
  public static final String MIN_IO_AUTH_TYPE = "min_io_s3";

  public static final String NX_EXTERNAL_MERCURY_HOST = "nx_external_mercury_host";
  public static final String NX_EXTERNAL_HOST = "nx_external_host";

  private final String secretKey;
  private final String accessKeyId;
  private final String url;

  public CustomS3ServiceCredentialProvider(AWSAuthConfig authConfig) {
    String externalHost = null;
    this.secretKey = authConfig.customAuthProps.get("secret_key");
    this.accessKeyId = authConfig.customAuthProps.get("access_key_id");

    if (authConfig.originals().containsKey(NX_EXTERNAL_MERCURY_HOST)) {
      externalHost = authConfig.originals().get(NX_EXTERNAL_MERCURY_HOST).toString();
    }
    // JPMC will change everything to nx_external_host .. will eventually decommission
    // nx_external_mercury_host
    if (externalHost == null) {
      externalHost = authConfig.originals().get(NX_EXTERNAL_HOST).toString();
    }
    this.url = externalHost;
  }

  @SneakyThrows
  public AwsCredentialsProvider getCredentialsProvider() {
    return StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKeyId, secretKey));
  }
}
