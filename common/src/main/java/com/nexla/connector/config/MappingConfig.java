package com.nexla.connector.config;

import static com.nexla.common.NexlaConstants.DEFAULT_TRACKER_NAME;
import static com.nexla.common.tracker.Tracker.TrackerMode;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MappingConfig {

  public static final String MODE_MANUAL = "manual";
  public static final String MODE_AUTO = "auto";

  private String mode;

  @JsonProperty("tracker_mode")
  private TrackerMode trackerMode = TrackerMode.NONE;

  @JsonProperty("tracker_name")
  private String trackerFieldName = DEFAULT_TRACKER_NAME;

  // source_field_name -> (target_field_name -> target_field_type), default type is "default"
  private LinkedHashMap<String, Map<String, String>> mapping = Maps.newLinkedHashMap();

  // source_field_name -> (option_name -> option_value)
  @JsonProperty("source_field_options")
  private LinkedHashMap<String, Map<String, String>> sourceFieldOptions = Maps.newLinkedHashMap();

  // target_field_name -> (option_name -> option_value)
  @JsonProperty("target_field_options")
  private LinkedHashMap<String, Map<String, String>> targetFieldOptions = Maps.newLinkedHashMap();

  private Set<String> excludes = Sets.newHashSet();

  @JsonProperty("fields_order")
  private List<String> fieldsOrder = Lists.newArrayList();

  @JsonProperty("header_template")
  private String headerTemplate;

  @JsonProperty("date_format")
  private String dateFormat;

  @JsonIgnore
  public boolean isManual() {
    return MODE_MANUAL.equals(this.mode);
  }

  // CDC PROPS
  @JsonProperty("replay_row_deletions")
  private boolean replayRowDeletions = true;

  @JsonProperty("table_name_prefix")
  private String tableNamePrefix = StringUtils.EMPTY;

  @JsonProperty("table_name_suffix")
  private String tableNameSuffix = StringUtils.EMPTY;

  @JsonProperty("exclude_tables")
  private Set<String> excludeTables = Sets.newHashSet();

  @JsonProperty("overridden_mappings")
  // source_table_name -> (target_table_name, source_field_name -> (target_field_name,
  // target_field_type))
  private LinkedHashMap<String, TableMapping> overriddenMappings = Maps.newLinkedHashMap();

  @JsonProperty("overridden_db_type_mappings")
  private OverriddenDbTypeMapping overriddenDbTypeMapping;

  @Getter
  @Setter
  @JsonInclude(JsonInclude.Include.NON_EMPTY)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TableMapping {
    private String name;
    private LinkedHashMap<String, ColumnMapping> columns = Maps.newLinkedHashMap();

    @JsonProperty("partial_enabled")
    private boolean partialEnabled;
  }

  @Getter
  @Setter
  @JsonInclude(JsonInclude.Include.NON_EMPTY)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ColumnMapping {
    private String name;
    private String type;

    @JsonProperty("enable_hashing")
    private Boolean enableHashing;
  }

  @Getter
  @Setter
  @JsonInclude(JsonInclude.Include.NON_EMPTY)
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class OverriddenDbTypeMapping {
    // if you see data type X in table Y map it to Z type
    private LinkedHashMap<String, LinkedHashMap<DataType, String>> tables = Maps.newLinkedHashMap();
    // if you see any column named X, map it to Z type
    private LinkedHashMap<String, String> columns = Maps.newLinkedHashMap();
    // for any table and column when you detect data type X, map it to Y data type
    private LinkedHashMap<DataType, String> general = Maps.newLinkedHashMap();

    // for all data types detected, map the columns to Z data type
    @JsonProperty("all_data_types")
    private String allDataTypes;
  }

  public enum DataType {
    STRING,
    INTEGER,
    FLOAT,
    JSON,
    BOOLEAN,
    NULL
  }
}
