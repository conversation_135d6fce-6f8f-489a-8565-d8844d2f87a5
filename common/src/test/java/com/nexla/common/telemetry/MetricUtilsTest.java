package com.nexla.common.telemetry;

import com.nexla.common.AppType;
import com.nexla.test.UnitTests;
import org.junit.Assert;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Test;

@Category(UnitTests.class)
class MetricUtilsTest {

  @Test
  void testMetricHttpName() {
    String metricName =
        MetricUtils.getMetricName(
            AppType.METRICS_HTTP, "DbMetricsHttpController", MetricUnit.DURATION);
    Assert.assertEquals("nexla_metrics_http_DbMetricsHttpController_duration", metricName);
  }

  @Test
  void testMetricCronName() {
    String metricName =
        MetricUtils.getMetricName(AppType.METRICS_CRON, "data_monitoring", MetricUnit.RECORDS);
    Assert.assertEquals("nexla_metrics_cron_data_monitoring_records", metricName);
  }

  @Test
  void testMetricAggregationName() {
    String metricName =
        MetricUtils.getMetricName(AppType.METRICS_AGGREGATION, "insert", MetricUnit.SIZE);
    Assert.assertEquals("nexla_metrics_aggregation_insert_size", metricName);
  }
}
