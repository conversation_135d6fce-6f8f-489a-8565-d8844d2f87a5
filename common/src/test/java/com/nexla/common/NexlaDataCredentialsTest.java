package com.nexla.common;

import com.nexla.test.UnitTests;
import java.util.Map;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.junit.experimental.categories.Category;

@Category(UnitTests.class)
public class NexlaDataCredentialsTest extends TestCase {

  public NexlaDataCredentialsTest(String testName) {
    super(testName);
  }

  public static Test suite() {
    return new TestSuite(NexlaDataCredentialsTest.class);
  }

  public void testBasic() {
    String credsEnc = "3SW44CFcdlff2Cp5okuzLyY4RvgnwCfsrwx/zcoE7iWKSPBnNGWw";
    String credsEncIv = "zMEZHwZeIVhARZJY";
    String key = "6aea52fdf667ad08ed9a7ab2c18de555";
    NexlaDataCredentials ndsc = new NexlaDataCredentials(key, credsEnc, credsEncIv);
    Map<String, String> credsMap = ndsc.decrypt();
    assertTrue("my amazon key".equals(credsMap.get("key")));
  }

  public void testS3() {
    String credsEnc =
        "ZHeo/xsK1X85/R/RJtb7ox9r/QhY81aAOevQSulRUNkdfF09DHXV5OCyHrz7q3i/2EBFHaNZ3bOA4zLearHXht2dsN14P+jDBySG6aWOuplVPA==";
    String credsEncIv = "dHboqAZ6hcw+cSLr";
    String key = "6aea52fdf667ad08ed9a7ab2c18de555";
    NexlaDataCredentials ndsc = new NexlaDataCredentials(key, credsEnc, credsEncIv);
    Map<String, String> credsMap = ndsc.decrypt();
    assertTrue("S3".equals(credsMap.get("credentials_type")));
    assertTrue("XYZ".equals(credsMap.get("access_key_id")));
    assertTrue("123".equals(credsMap.get("secret_key")));
  }

  public void testUnsupportedCredsVersion() {
    // Note, same encrypted payload as testS3(), but unsupported encryption version...
    String credsEnc =
        "ZHeo/xsK1X85/R/RJtb7ox9r/QhY81aAOevQSulRUNkdfF09DHXV5OCyHrz7q3i/2EBFHaNZ3bOA4zLearHXht2dsN14P+jDBySG6aWOuplVPA==";
    String credsEncIv = "dHboqAZ6hcw+cSLr";
    String credsVersion = "3";
    String key = "6aea52fdf667ad08ed9a7ab2c18de555";
    NexlaDataCredentials ndsc = new NexlaDataCredentials(key, credsEnc, credsEncIv, credsVersion);
    Map<String, String> credsMap = ndsc.decrypt();
    assertTrue(credsMap.isEmpty());
  }
}
