package com.nexla.common.notify.transport;

import com.nexla.common.exception.NexlaErrorNotificationEvent;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.error.NexlaInternalNotificationEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import java.util.ArrayList;
import java.util.List;

public class NexlaMessageTransportMock implements NexlaMessageTransport {

  public final List<NexlaMonitoringLogEvent> nexlaMonitoringLogEvents = new ArrayList<>();
  public final List<NexlaNotificationEvent> nexlaNotificationEvents = new ArrayList<>();

  @Override
  public void publish(String topic, String json, String key) {
    // TODO:
  }

  @Override
  public void publish(String topic, int partition, String key, String value) {}

  @Override
  public void publishMetrics(NexlaRawMetric nexlaRawMetric) {
    // TODO:
  }

  @Override
  public void publishMetrics(NexlaRawMetric nexlaRawMetric, String key) {}

  @Override
  public void publishNotification(NexlaNotificationEvent notificationEvent) {
    nexlaNotificationEvents.add(notificationEvent);
  }

  @Override
  public void publishMonitoringLog(NexlaMonitoringLogEvent monitoringLogEvent) {
    nexlaMonitoringLogEvents.add(monitoringLogEvent);
  }

  @Override
  public void publishInternalNotification(NexlaInternalNotificationEvent notificationEvent) {
    // TODO:
  }

  @Override
  public void publishQuarantineMessage(NexlaQuarantineMessage quarantineMessage, String topicName) {
    // TODO:
  }

  @Override
  public void publishTransformIOMessage(
      String quarantineWriteTopic, NexlaQuarantineMessage quarantineMessage) {
    // TODO:
  }

  @Override
  public void publishErrorMessage(String topic, NexlaErrorNotificationEvent notificationEvent) {
    // TODO:
  }

  @Override
  public void flush() {}
}
