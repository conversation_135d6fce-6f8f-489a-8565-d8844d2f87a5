package com.nexla.common;

import static com.nexla.common.ConnectionType.AZURE_BLB;
import static org.junit.Assert.*;

import com.nexla.test.UnitTests;
import java.util.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.experimental.categories.Category;

@Category(UnitTests.class)
public class NexlaNamingUtilsTest {

  @Test
  public void resourceBySourceConnectorName() {
    final Resource resource = NexlaNamingUtils.resourceByConnectorName("source-1");
    assertEquals(1, resource.id);
    assertEquals(ResourceType.SOURCE, resource.type);
  }

  @Test
  public void resourceBySinkConnectorName() {
    final Resource resource = NexlaNamingUtils.resourceByConnectorName("sink-1");
    assertEquals(1, resource.id);
    assertEquals(ResourceType.SINK, resource.type);
  }

  @Test
  public void resourceFromServiceName() {
    Map<String, Resource> serviceNameToResource = new HashMap<>();
    serviceNameToResource.put("azure-blb-source-1", new Resource(1, ResourceType.SOURCE));
    serviceNameToResource.put("azure-blb-source-28325", new Resource(28325, ResourceType.SOURCE));
    serviceNameToResource.put(
        "azure-data-lake-source-25734", new Resource(25734, ResourceType.SOURCE));
    serviceNameToResource.put("ftp-source-16418", new Resource(16418, ResourceType.SOURCE));
    serviceNameToResource.put("mysql-source-10322", new Resource(10322, ResourceType.SOURCE));
    serviceNameToResource.put(
        "oracle-autonomous-source-25650", new Resource(25650, ResourceType.SOURCE));
    serviceNameToResource.put("postgres-source-17436", new Resource(17436, ResourceType.SOURCE));
    serviceNameToResource.put("redshift-source-26523", new Resource(26523, ResourceType.SOURCE));
    serviceNameToResource.put("rest-source-9388", new Resource(9388, ResourceType.SOURCE));
    serviceNameToResource.put("s3-source-22699", new Resource(22699, ResourceType.SOURCE));
    serviceNameToResource.put("snowflake-source-23802", new Resource(23802, ResourceType.SOURCE));
    serviceNameToResource.put("sqlserver-source-23001", new Resource(23001, ResourceType.SOURCE));

    serviceNameToResource.put("azure-blb-sink-1", new Resource(1, ResourceType.SINK));
    serviceNameToResource.put("azure-blb-sink-28325", new Resource(28325, ResourceType.SINK));
    serviceNameToResource.put("azure-data-lake-sink-25734", new Resource(25734, ResourceType.SINK));
    serviceNameToResource.put("ftp-sink-16418", new Resource(16418, ResourceType.SINK));
    serviceNameToResource.put("mysql-sink-10322", new Resource(10322, ResourceType.SINK));
    serviceNameToResource.put(
        "oracle-autonomous-sink-25650", new Resource(25650, ResourceType.SINK));
    serviceNameToResource.put("postgres-sink-17436", new Resource(17436, ResourceType.SINK));
    serviceNameToResource.put("redshift-sink-26523", new Resource(26523, ResourceType.SINK));
    serviceNameToResource.put("rest-sink-9388", new Resource(9388, ResourceType.SINK));
    serviceNameToResource.put("s3-sink-22699", new Resource(22699, ResourceType.SINK));
    serviceNameToResource.put("snowflake-sink-23802", new Resource(23802, ResourceType.SINK));
    serviceNameToResource.put("sqlserver-sink-23001", new Resource(23001, ResourceType.SINK));

    serviceNameToResource.put("inmemory-123-456-789", new Resource(789, ResourceType.SOURCE));
    serviceNameToResource.put("replication-123-456-789", new Resource(789, ResourceType.SOURCE));

    serviceNameToResource.forEach(
        (service, resource) ->
            assertEquals(service, resource, NexlaNamingUtils.resourceFromServiceName(service)));
  }

  @Test
  public void resourceFromServiceNameException() {
    List<String> list = new ArrayList<>();

    list.addAll(
        Arrays.asList(
            "s3-source22699",
            "source-23802",
            "sqlserver-sink23001",
            "sink-23001",
            "inmemory-123-456-789-325",
            "inmemory-123-456",
            "replication-123-456-789-325",
            "replication-123-456",
            "abra-cadabra",
            ""));
    list.addAll(NOT_DEDICATED_SERVICE_DEPLOYMENT_NAMES);

    list.forEach(
        service ->
            assertThrows(
                service,
                IllegalArgumentException.class,
                () -> NexlaNamingUtils.resourceFromServiceName(service)));
  }

  @Test
  public void sourceConnectorServiceName() {
    final String connectorServiceName = NexlaNamingUtils.sourceConnectorServiceName(1, AZURE_BLB);
    assertEquals("azure-blb-source-1", connectorServiceName);
  }

  @Test
  public void sinkConnectorServiceName() {
    final String connectorServiceName = NexlaNamingUtils.sinkConnectorServiceName(1, AZURE_BLB);
    assertEquals("azure-blb-sink-1", connectorServiceName);
  }

  @Test
  public void inMemoryConnectorServiceName() {
    String connectorServiceName = NexlaNamingUtils.inMemoryConnectorServiceName(123, 456, 789);
    assertEquals("inmemory-123-456-789", connectorServiceName);
  }

  @Test
  public void replicationConnectorServiceName() {
    String connectorServiceName = NexlaNamingUtils.replicationConnectorServiceName(123, 456, 789);
    assertEquals("replication-123-456-789", connectorServiceName);
  }

  @Test
  public void isDedicatedInMemoryServiceNameTest() {
    Assert.assertTrue(NexlaNamingUtils.isDedicatedInMemoryServiceName("inmemory-123-456-789"));

    SOURCE_DEPLOYMENT_NAMES.forEach(
        name ->
            Assert.assertFalse(
                name + " is not inMemory", NexlaNamingUtils.isDedicatedInMemoryServiceName(name)));

    DEDICATED_SINK_DEPLOYMENT_NAMES.forEach(
        name ->
            Assert.assertFalse(
                name + " is not inMemory", NexlaNamingUtils.isDedicatedInMemoryServiceName(name)));

    NOT_DEDICATED_SERVICE_DEPLOYMENT_NAMES.forEach(
        name ->
            Assert.assertFalse(
                name + " is not inMemory", NexlaNamingUtils.isDedicatedInMemoryServiceName(name)));
  }

  @Test
  public void isDedicatedReplicationServiceNameTest() {
    Assert.assertTrue(
        NexlaNamingUtils.isDedicatedReplicationServiceName("replication-123-456-789"));

    SOURCE_DEPLOYMENT_NAMES.forEach(
        name ->
            Assert.assertFalse(
                name + " is not replication",
                NexlaNamingUtils.isDedicatedReplicationServiceName(name)));

    DEDICATED_SINK_DEPLOYMENT_NAMES.forEach(
        name ->
            Assert.assertFalse(
                name + " is not replication",
                NexlaNamingUtils.isDedicatedReplicationServiceName(name)));

    NOT_DEDICATED_SERVICE_DEPLOYMENT_NAMES.forEach(
        name ->
            Assert.assertFalse(
                name + " is not replication",
                NexlaNamingUtils.isDedicatedReplicationServiceName(name)));
  }

  private final List<String> SOURCE_DEPLOYMENT_NAMES =
      Arrays.asList(
          "azure-blb-source-28325",
          "azure-data-lake-source-25734",
          "ftp-source-16418",
          "mysql-source-10322",
          "oracle-autonomous-source-25650",
          "postgres-source-17436",
          "redshift-source-26523",
          "rest-source-9388",
          "s3-source-22699",
          "snowflake-source-23802",
          "sqlserver-source-23001");

  private final List<String> DEDICATED_SINK_DEPLOYMENT_NAMES =
      Arrays.asList(
          "azure-blb-sink-28325",
          "azure-data-lake-sink-25734",
          "ftp-sink-16418",
          "mysql-sink-10322",
          "oracle-autonomous-sink-25650",
          "postgres-sink-17436",
          "redshift-sink-26523",
          "rest-sink-9388",
          "s3-sink-22699",
          "snowflake-sink-23802",
          "sqlserver-sink-23001");

  private final List<String> NOT_DEDICATED_SERVICE_DEPLOYMENT_NAMES =
      Arrays.asList(
          "backdoor-app",
          "catalog-plugins",
          "catalog-plugins-celery",
          "coordination-app",
          "ctrl",
          "ctrl-background",
          "ctrl-container",
          "ctrl-cron",
          "ctrl-eventgenerator",
          "ctrl-heartbeats",
          "custom-python-tx",
          "elasticsearch-kb",
          "email-connector",
          "error-monitor",
          "fast-connector",
          "fast-connector-blue",
          "fast-connector-green",
          "file-vault",
          "flow-execution-monitoring",
          "http-gateway",
          "http-sink",
          "ingestion",
          "internal-admin-api",
          "internal-admin-api-cache",
          "internal-cli-admin-api",
          "kafka-cluster-cruise-control",
          "kafka-cluster-entity-operator",
          "kafka-cluster-kafka-exporter",
          "kafka-cluster-kafka-jmx-trans",
          "kafka-stream-connector-blue-1",
          "kafka-stream-connector-blue-2",
          "kafka-stream-connector-blue-3",
          "kafka-stream-connector-blue-4",
          "listing-app",
          "listing-app-cont",
          "metrics-aggregation",
          "metrics-cron",
          "metrics-http",
          "metrics-topic-listener",
          "monitors-api",
          "nexla-api",
          "nexla-internal-admin-api",
          "nginxproxy",
          "probe-http",
          "rds-connect",
          "script-runner",
          "search",
          "sink-bigquery-connector-blue-1",
          "sink-dd-file-connector-blue-1",
          "sink-dd-jdbc-connector-blue-1",
          "sink-documentdb-connector-blue-1",
          "sink-documentdb-connector-green-1",
          "sink-file-connector-blue-1",
          "sink-jdbc-connector-green-9",
          "sink-redis-connector-blue-1",
          "sink-redshift-connector-blue-3",
          "sink-rest-connector-blue-1",
          "sink-rest-connector-green-7",
          "sink-soap-connector-blue-1",
          "sink-spreadsheets-connector-blue-1",
          "sink-ssh-jdbc-connector-blue-1",
          "sshserver",
          "sync-api",
          "transform-http",
          "transform-stream-abs",
          "transform-stream-blue",
          "ui",
          "ui-alpha",
          "ui-demo");
}
