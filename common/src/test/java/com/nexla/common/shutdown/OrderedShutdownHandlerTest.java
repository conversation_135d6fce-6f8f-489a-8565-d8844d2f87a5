package com.nexla.common.shutdown;

import static com.nexla.common.shutdown.OrderedShutdownHandler.ShutdownPriority.*;
import static org.junit.Assert.*;

import com.nexla.test.UnitTests;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.experimental.categories.Category;

@Slf4j
@Category(UnitTests.class)
public class OrderedShutdownHandlerTest {

  @Test
  @SneakyThrows
  public void verifyExecutionOrder() {
    AtomicInteger value = new AtomicInteger(0);
    OrderedShutdownHandler.INSTANCE.addHook(
        REDIS,
        () -> {
          try {
            Thread.sleep(100);
          } catch (InterruptedException e) {
            throw new RuntimeException(e);
          }
          value.compareAndSet(2, 3);
          throw new RuntimeException();
        });
    OrderedShutdownHandler.INSTANCE.addHook(
        AKKA_HTTP,
        () -> {
          try {
            Thread.sleep(400);
          } catch (InterruptedException e) {
            throw new RuntimeException(e);
          }
          value.compareAndSet(1, 2);
        });
    OrderedShutdownHandler.INSTANCE.addHook(
        TASK_LIFECYCLE_MANAGER,
        () -> {
          try {
            Thread.sleep(700);
          } catch (InterruptedException e) {
            throw new RuntimeException(e);
          }
          value.compareAndSet(0, 1);
          throw new RuntimeException();
        });
    OrderedShutdownHandler.INSTANCE.createShutdownHook().run();
    assertEquals(3, value.get());
    assertTrue(OrderedShutdownHandler.INSTANCE.waitForCompletion().get(1, TimeUnit.SECONDS));
  }
}
