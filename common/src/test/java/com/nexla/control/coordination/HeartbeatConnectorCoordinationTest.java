package com.nexla.control.coordination;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.nexla.common.ResourceType;
import com.nexla.common.StreamUtils;
import com.nexla.connector.config.FlowType;
import com.nexla.control.message.MessageCreatedAt;
import com.nexla.test.UnitTests;
import lombok.Data;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.experimental.categories.Category;

@Category(UnitTests.class)
public class HeartbeatConnectorCoordinationTest {

  @Test
  public void testSerialization2_16_0() {
    final long now = 1707332679778L;
    final long runId = 1707332678642L;
    HeartbeatConnectorCoordination heartbeat =
        new HeartbeatConnectorCoordination(
            "mess_id", ResourceType.SOURCE, 5, runId, HeartbeatType.DATA, now);
    String heartbeatStr =
        "{"
            + "\"coordinationEventType\":\"HEARTBEAT_CONNECTOR\","
            + "\"messageId\":\"mess_id\","
            + "\"resourceType\":\"SOURCE\","
            + "\"resourceId\":5,"
            + "\"runId\":1707332678642,"
            + "\"type\":\"DATA\","
            + "\"timeMs\":1707332679778,"
            + "\"createdAt\":1707337340755}";

    String serialized = StreamUtils.jsonUtil().toJsonString(heartbeat);

    HeartbeatConnectorCoordination deserialized =
        (HeartbeatConnectorCoordination)
            StreamUtils.jsonUtil().stringToType(serialized, CoordinationMessage.class);

    HeartbeatConnectorCoordination deserialized2 =
        (HeartbeatConnectorCoordination)
            StreamUtils.jsonUtil().stringToType(heartbeatStr, CoordinationMessage.class);

    Assertions.assertThat(heartbeat).isEqualTo(deserialized);
    Assertions.assertThat(heartbeat).isEqualToIgnoringGivenFields(deserialized2, "createdAt");
    Assertions.assertThat(deserialized.getContext()).isNotNull();
    Assertions.assertThat(deserialized2.getContext()).isNotNull();
  }

  @Test
  public void testSerialization2_17_0() {
    final long now = 1707332679778L;
    final long runId = 1707332678642L;
    HeartbeatConnectorCoordination heartbeat =
        new HeartbeatConnectorCoordination(
            "mess_id", ResourceType.SOURCE, 5, runId, HeartbeatType.DATA, now);
    heartbeat.getContext().put("flowType", FlowType.IN_MEMORY.name());
    heartbeat.getContext().put("flowId", "1");
    heartbeat.getContext().put("sourceId", "2");
    heartbeat.getContext().put("state", "ERROR");

    String heartbeatStr =
        "{\"coordinationEventType\":\"HEARTBEAT_CONNECTOR\","
            + "\"messageId\":\"mess_id\","
            + "\"resourceType\":\"SOURCE\","
            + "\"resourceId\":5,"
            + "\"runId\":1707332678642,"
            + "\"type\":\"DATA\","
            + "\"timeMs\":1707332679778,"
            + "\"createdAt\":1707339689151,"
            + "\"context\":{\"sourceId\":\"2\","
            + "\"state\":\"ERROR\","
            + "\"flowId\":\"1\","
            + "\"flowType\":\"IN_MEMORY\"}}";

    String serialized = StreamUtils.jsonUtil().toJsonString(heartbeat);
    //		System.out.println(serialized);

    HeartbeatConnectorCoordination deserialized =
        (HeartbeatConnectorCoordination)
            StreamUtils.jsonUtil().stringToType(serialized, CoordinationMessage.class);

    HeartbeatConnectorCoordination deserialized2 =
        (HeartbeatConnectorCoordination)
            StreamUtils.jsonUtil().stringToType(heartbeatStr, CoordinationMessage.class);

    Assertions.assertThat(heartbeat).isEqualTo(deserialized);

    Assertions.assertThat(heartbeat).isEqualToIgnoringGivenFields(deserialized2, "createdAt");
  }

  @Test
  public void testSerialization2_16_0_forward_compatibility_to_2_17_0() {
    final long now = 1707332679778L;
    final long runId = 1707332678642L;
    HeartbeatConnectorCoordination_v2_16_0 heartbeat =
        new HeartbeatConnectorCoordination_v2_16_0(
            "mess_id", ResourceType.SOURCE, 5, runId, HeartbeatType.DATA, now);

    String heartbeatStr_2_17_0 =
        "{\"coordinationEventType\":\"HEARTBEAT_CONNECTOR\","
            + "\"messageId\":\"mess_id\","
            + "\"resourceType\":\"SOURCE\","
            + "\"resourceId\":5,"
            + "\"runId\":1707332678642,"
            + "\"type\":\"DATA\","
            + "\"timeMs\":1707332679778,"
            + "\"createdAt\":1707339689151,"
            + "\"context\":{\"sourceId\":\"2\","
            + "\"state\":\"ERROR\","
            + "\"flowId\":\"1\","
            + "\"flowType\":\"IN_MEMORY\"}}";

    String serialized = StreamUtils.jsonUtil().toJsonString(heartbeat);

    HeartbeatConnectorCoordination_v2_16_0 deserialized =
        (HeartbeatConnectorCoordination_v2_16_0)
            StreamUtils.jsonUtil().stringToType(serialized, CoordinationMessage_v2_16_0.class);
    HeartbeatConnectorCoordination_v2_16_0 deserialized2 =
        (HeartbeatConnectorCoordination_v2_16_0)
            StreamUtils.jsonUtil()
                .stringToType(heartbeatStr_2_17_0, CoordinationMessage_v2_16_0.class);

    Assertions.assertThat(heartbeat).isEqualTo(deserialized);

    Assertions.assertThat(heartbeat).isEqualToIgnoringGivenFields(deserialized2, "createdAt");
  }

  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "coordinationEventType")
  @JsonSubTypes({
    @JsonSubTypes.Type(
        value = HeartbeatConnectorCoordination_v2_16_0.class,
        name = "HEARTBEAT_CONNECTOR"),
  })
  @JsonIgnoreProperties(ignoreUnknown = true)
  public interface CoordinationMessage_v2_16_0 extends MessageCreatedAt {

    String getMessageId();

    @JsonIgnore
    CoordinationEventType getCoordinationEventType();
  }

  @Data
  static class HeartbeatConnectorCoordination_v2_16_0 implements CoordinationMessage_v2_16_0 {

    private final String messageId;
    private final ResourceType resourceType;
    private final Integer resourceId;
    private final Long runId;
    private final HeartbeatType type;
    private final Long timeMs;
    private final Long createdAt = System.currentTimeMillis();

    @Override
    public CoordinationEventType getCoordinationEventType() {
      return CoordinationEventType.HEARTBEAT_CONNECTOR;
    }
  }
}
