package com.nexla.inmemory_connector_common.listing

import akka.NotUsed
import akka.actor.ActorSystem
import akka.stream.scaladsl.Source
import com.nexla.inmemory_connector_common.listing.ListingStreamSource.FileIterator
import com.nexla.sc.client.listing.{ListedFile, ListingAppClient, ListingResult}
import com.nexla.sc.util.StrictNexlaLogging

import java.time.Instant
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}


class ListingStreamSource(listingAppClient: ListingAppClient,
                          listingCooldownPeriod: FiniteDuration,
                          maxRestartPeriod: FiniteDuration = 1.day)(implicit val ec: ExecutionContext, system: ActorSystem) extends StrictNexlaLogging {

  def listFiles(sourceId: Int, updateDataIngestionTs: () => Unit): Source[ListedFile, NotUsed] = {

    logger.info(s"List files for sourceId $sourceId STARTED")

    val fileIterator = new FileIterator(sourceId, listingAppClient, listingCooldownPeriod, maxRestartPeriod, updateDataIngestionTs)

    Source.unfoldAsync(None) { _ =>
      fileIterator.requestNewFile().map {
        case Some(file) => Some((None, file))
        case None => None
      }
    }
  }

}

object ListingStreamSource {
  private class FileIterator(sourceId: Int,
                             listingAppClient: ListingAppClient,
                             listingCooldownPeriod: FiniteDuration,
                             maxRestartPeriod: FiniteDuration,
                             updateDataTs: () => Unit)
                            (implicit val ec: ExecutionContext, system: ActorSystem) extends StrictNexlaLogging {
    private val globalTimeoutDate = Instant.now().plusSeconds(maxRestartPeriod.toSeconds)

    private sealed trait Result

    private case object ListingInProgress extends Result

    private case class FileFound(file: ListedFile) extends Result

    private case object ListingFinished extends Result

    private def innerFetchFile(): Future[Result] = {
      updateDataTs()
      listingAppClient.takeFile(sourceId).map {
        case ListingResult(Some(file), _) => FileFound(file)
        case ListingResult(None, true) => ListingInProgress
        case ListingResult(None, false) => ListingFinished
      }
    }

    def requestNewFile(): Future[Option[ListedFile]] = {
      if (Instant.now().isAfter(globalTimeoutDate)) {
        logger.info(s"Global waiting date exceeded: $maxRestartPeriod")
        Future.successful(None)
      } else {
        innerFetchFile().flatMap {
          case ListingInProgress =>
            logger.info(s"No files as of now, but listing is still listing something")
            akka.pattern.after(listingCooldownPeriod)(requestNewFile())
          case FileFound(file) =>
            logger.info(s"File found: id=${file.id}, path=${file.fullPath}")
            Future.successful(Some(file))
          case ListingFinished =>
            logger.info(s"Listing finished. No more files as of now")
            Future.successful(None)
        }.recoverWith {
          case ex =>
            logger.error(s"Exception while fetching file. Retrying", ex)
            akka.pattern.after(listingCooldownPeriod)(requestNewFile())

        }
      }
    }
  }
}
