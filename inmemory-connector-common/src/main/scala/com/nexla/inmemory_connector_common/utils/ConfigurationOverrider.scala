package com.nexla.inmemory_connector_common.utils

import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import com.nexla.common.ResourceType
import com.nexla.common.datetime.DateTimeUtils
import com.nexla.common.exception.{NexlaError, NexlaErrorMessage}
import com.nexla.common.notify.monitoring.{NexlaMonitoringLogEvent, NexlaMonitoringLogSeverity, NexlaMonitoringLogType}
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.connect.common.NexlaConnectorUtils
import com.nexla.connector.config.FlowType
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.sc.util.StrictNexlaLogging

import java.util.Optional
import java.util.Optional.empty
import scala.jdk.CollectionConverters.iterableAsScalaIterableConverter

class ConfigurationOverrider(adminApiClient: AdminApiClient, debugLogging: Boolean = false, controlMessageProducer: ControlMessageProducer, runId: Long) extends StrictNexlaLogging {

  private def printAll(dataSources: List[DataSource], dataSets: List[DataSet], dataSinks: List[DataSink], msg: String): Unit = {
      val allSourcesConfigString = dataSources.map(dataSource => s"[id=${dataSource.getId}, credId=${dataSource.getDataCredentials.getId}, cfg=(${dataSource.getSourceConfig.entrySet().toArray.mkString(", ")})]").mkString("\n")
      val allSetsCustomConfigString = dataSets.map(dataSet => s"[id=${dataSet.getId}, cfg=(${dataSet.getCustomConfig.entrySet().toArray.mkString(", ")})]").mkString("\n")
      val allSetsRuntimeConfigString = dataSets.map(dataSet => s"[id=${dataSet.getId}, cfg=(${dataSet.getRuntimeConfig.entrySet().toArray.mkString(", ")})]").mkString("\n")
      val allSinksConfigString = dataSinks.map(dataSink => s"[id=${dataSink.getId}, credId=${dataSink.getDataCredentials.getId}, cfg=(${dataSink.getSinkConfig.entrySet().toArray.mkString(", ")})]").mkString("\n")
      logger.info(s"source config $msg enrichment: $allSourcesConfigString")
      logger.info(s"datasets custom config $msg enrichment: $allSetsCustomConfigString")
      logger.info(s"datasets runtime config $msg enrichment: $allSetsRuntimeConfigString")
      logger.info(s"sinks config $msg enrichment: $allSinksConfigString")
  }

  private def prepareMonitoringLogEventSrc(source: DataSource, msg: String): NexlaMonitoringLogEvent = {
    val orgId: Int = Option(source.getOrg).map(_.getId).map(_.toInt).getOrElse(0)
    val monitoringLogEvent: NexlaMonitoringLogEvent = NexlaMonitoringLogEvent.of(
      orgId,
      runId,
      source.getId,
      ResourceType.SOURCE,
      msg,
      NexlaMonitoringLogType.LOG,
      NexlaMonitoringLogSeverity.ERROR,
      System.currentTimeMillis()
    )
    monitoringLogEvent
  }

  private def prepareMonitoringLogEventSink(sink: DataSink, msg: String): NexlaMonitoringLogEvent = {
    val orgId: Int = Option(sink.getOrg).map(_.getId).map(_.toInt).getOrElse(0)
    val monitoringLogEvent: NexlaMonitoringLogEvent = NexlaMonitoringLogEvent.of(
      orgId,
      runId,
      sink.getId,
      ResourceType.SINK,
      msg,
      NexlaMonitoringLogType.LOG,
      NexlaMonitoringLogSeverity.ERROR,
      System.currentTimeMillis()
    )
    monitoringLogEvent
  }

  private def getDatasetId(source: DataSource): Int = {
    if (source.getDatasets == null || source.getDatasets.isEmpty) {
      logger.warn(s"source ${source.getId} has no datasets")
      0
    } else {
      source.getDatasets.get(0).getId
    }
  }

  /**
   * Override the metrics' name, so that the actual source / sink tasks will pick this up when reporting the metrics
   *
   * @param dataSources
   * @param dataSets
   * @param dataSinks
   * @param adaptiveFlowTask
   */
  def overrideMetricName(dataSources: List[DataSource], dataSinks: List[DataSink], adaptiveFlowTask: AdaptiveFlowTask): Unit = {
    dataSources.foreach(source => { source.getSourceConfig.put("externally.set.metric.name", getName(adaptiveFlowTask)) })
    dataSinks.foreach(sink => { sink.getSinkConfig.put("externally.set.metric.name", getName(adaptiveFlowTask)) })
    logger.info("metric name overridden for sources and sinks")
  }

  /**
   * Override the configs of the data sources and sinks with the configs from the adaptive flow task.
   *
   * @param dataSources data sources of the flow
   * @param dataSinks data sinks of the flow
   * @param adaptiveFlowTask received adaptive flow task
   */
  def overrideConfigs(dataSources: List[DataSource], dataSets: List[DataSet], dataSinks: List[DataSink], adaptiveFlowTask: AdaptiveFlowTask): Unit = {
    if (debugLogging) logger.info(s"Replacing configs with adaptive flow task, task id [${Option(adaptiveFlowTask.getId).getOrElse("No ID")}]")
    val start = System.currentTimeMillis()
    if (debugLogging) printAll(dataSources, dataSets, dataSinks, "BEFORE")

    // now do the enrichment
    // priority: top to bottom. the deeper you have your variable, the more priority it has.

    // 1. take and process globals. first, root of this level (all props). parameters may be missing or empty
    Option(adaptiveFlowTask.getParameters).map {
      definedEntry =>
        // global is optional
        Option(definedEntry.get("global")).map {
          globalCfgsEntry =>
            val globalConfigsMap = globalCfgsEntry.asInstanceOf[java.util.Map[String, AnyRef]].entrySet().asScala
              .filter(kv => kv.getKey.toLowerCase.equals("config")) // take the global config entry and put it into all source configs
              .map(a => (a.getKey, a.getValue))
              .toList.toMap

            // and the config may be absent too
            Option(globalConfigsMap.get("config")).map {
              finalEntry =>
                val globalConfigs = finalEntry.get.asInstanceOf[java.util.Map[String, AnyRef]]
                // and put it everywhere
                putIntoAllConfigs(globalConfigs, dataSources.map(_.getSourceConfig))
                putIntoAllConfigs(globalConfigs, dataSets.map(_.getCustomConfig))
                putIntoAllConfigs(globalConfigs, dataSets.map(_.getRuntimeConfig.asInstanceOf[java.util.Map[String, AnyRef]]))
                putIntoAllConfigs(globalConfigs, dataSinks.map(_.getSinkConfig))
            }
        }
    }

    // TBD: properties and variables must be processed too.

    // 3. then, the same for each of the sources individually. params may still be missing
    Option(adaptiveFlowTask.getParameters).map {
      someParams =>
        // and source section may be missing too
        Option(someParams.get("sources")).map {
          sourceParams =>
            sourceParams.asInstanceOf[java.util.ArrayList[java.util.Map[String, AnyRef]]].asScala
              .foreach { singleSourceStructure =>
                val sourceId = singleSourceStructure.get("id").toString.toInt
                // properties and variables will be used later here - TBD
                val cfgValuesForThisSource = singleSourceStructure.get("config").asInstanceOf[java.util.Map[String, AnyRef]]

                dataSources.find(_.getId == sourceId).foreach { source =>
                  putIntoAllConfigs(cfgValuesForThisSource, List(source.getSourceConfig))
                  // and replace the creds in this exact data source with the ones from the adaptive flow task
                  Option(singleSourceStructure.get("data_credentials_id")).foreach { credsId =>
                    try {
                      val actualCredsId = credsId.toString.toInt
                      val actualCreds = adminApiClient.getDataCredentials(actualCredsId).get()
                      source.setCredentialsType(actualCreds.getCredentialsType.toString)
                      source.setCredentialsEncrypted(actualCreds.getCredentialsEnc)
                      source.setCredentialsEncryptedIv(actualCreds.getCredentialsEncIv)
                      source.setDataCredentials(actualCreds)
                      if (debugLogging) logger.info(s"credentials in source ${source.getId} replaced with individual source credentials ${actualCreds.getId}")
                    } catch {
                      case e: Exception =>
                        val msg = "Individual credentials id for the source - either empty or cannot be parsed or missing, potentially deleted."
                        val monitoringLogEvent = prepareMonitoringLogEventSrc(source, msg)
                        logger.error(msg, e)
                        controlMessageProducer.publishMonitoringLog(monitoringLogEvent)
                        publishErrorNotification(source.getId, ResourceType.SOURCE, source.getOrgId(), source.getOwnerId(), getDatasetId(source), runId, getName(adaptiveFlowTask), e)
                        throw new RuntimeException(msg)
                    }
                  }
                }
              }
        }
    }

    // 4. then, the same for each of the sinks individually
    Option(adaptiveFlowTask.getParameters).map {
      someParams =>
        Option(someParams.get("sinks")).map {
          sinkParams =>
            sinkParams.asInstanceOf[java.util.ArrayList[java.util.Map[String, AnyRef]]].asScala
              .foreach { singleSinkStructure =>
                val sinkId = singleSinkStructure.get("id").toString.toInt
                // properties and variables will be used later here - TBD
                val cfgValuesForThisSink = singleSinkStructure.get("config").asInstanceOf[java.util.Map[String, AnyRef]]

                dataSinks.find(_.getId == sinkId).foreach { sink =>
                  putIntoAllConfigs(cfgValuesForThisSink, List(sink.getSinkConfig))
                  // and replace the creds in this exact data source with the ones from the adaptive flow task
                  Option(singleSinkStructure.get("data_credentials_id")).foreach { credsId =>
                    try {
                      val actualCredsId = credsId.toString.toInt
                      val actualCreds = adminApiClient.getDataCredentials(actualCredsId).get()
                      sink.setDataCredentials(actualCreds)
                      if (debugLogging) logger.info(s"credentials in sink ${sink.getId} replaced with individual sink credentials ${actualCreds.getId}")
                    } catch {
                      case e: Exception =>
                        val msg = "Individual credentials id for the sink - either empty or cannot be parsed or missing, potentially deleted."
                        val monitoringLogEvent = prepareMonitoringLogEventSink(sink, msg)
                        logger.error(msg, e)
                        controlMessageProducer.publishMonitoringLog(monitoringLogEvent)
                        publishErrorNotification(sink.getId, ResourceType.SINK, sink.getOrgId(), sink.getOwnerId(), sink.getDataSetId, runId, getName(adaptiveFlowTask), e)
                        throw new RuntimeException(msg)
                    }
                  }
                }
              }
        }
    }

    val stop = System.currentTimeMillis()
    if (debugLogging) printAll(dataSources, dataSets, dataSinks, "AFTER")
    if (debugLogging) logger.info(s"configuration override done for the entire flow, taken ${stop-start} ms")
  }

  private def putIntoAllConfigs(maybeThingsYouWantToPut: java.util.Map[String, AnyRef], maybeTargetsForPutting: List[java.util.Map[String, AnyRef]]): Unit = {
    Option(maybeThingsYouWantToPut).foreach { thingsYouWantToPut =>
      Option(maybeTargetsForPutting).foreach {
        targetsForPutting =>
          targetsForPutting.foreach(target => {
            target.putAll(thingsYouWantToPut)
          })
      }
    }
  }

  private def publishErrorNotification(resourceId: Int, resourceType: ResourceType, orgId: Int, ownerId: Int, dataSetId: Int, runId: Long, taskName: String, err: Throwable): Unit = {
    val errorMessage = new NexlaErrorMessage(err, "", empty())
    NexlaConnectorUtils.publishMetrics(controlMessageProducer, resourceType, resourceId, taskName, 0L, 0L, 1,
      DateTimeUtils.nowUTC().getMillis, Optional.of(runId), Optional.of(true), empty(), empty(), Optional.of(dataSetId),
      empty(), empty(), Optional.of(errorMessage), empty(), FlowType.IN_MEMORY, orgId, ownerId)
    NexlaConnectorUtils.publishException(controlMessageProducer, runId, resourceType, resourceId, 0L, err.getMessage, NexlaError.getErrorDetails(err, Optional.of(java.lang.Long.valueOf(0L))))
  }

  private def getName(task: AdaptiveFlowTask): String = {
    String.valueOf(task.getId)
  }

}
