import com.amazonaws.services.kinesis.AmazonKinesis;
import com.amazonaws.services.kinesis.AmazonKinesisClientBuilder;
import com.amazonaws.services.kinesis.model.PutRecordRequest;
import com.amazonaws.services.kinesis.model.ScalingType;
import com.amazonaws.services.kinesis.model.UpdateShardCountRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connector.kinesis.source.KinesisSourceTask;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.kafka.connect.source.SourceTaskContext;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.junit.ClassRule;
import org.junit.Ignore;
import org.junit.experimental.categories.Category;
import org.testcontainers.containers.localstack.LocalStackContainer;

import java.nio.ByteBuffer;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.testcontainers.containers.localstack.LocalStackContainer.Service.KINESIS;

// for some reason Kinesis service doesnt work on LocalStackContainer 0.8.5
// i leave this stub for future
// this test should put some records to Kinesis, assert they are readed
// then change number of Shards, put some records to Kinesis, assert they are readed
// then put some records to Kinesis, do not read, change number of Shards, assert all of them are readed
// for now this is manually tested. (akonopko 27.07.2018)
@Ignore
@Category(IntegrationTests.class)
public class KinesisSourceTaskTest extends BaseKafkaTest {

	private static final String DATASET_TOPIC = "dataset-1-source-1";
	private static final String TEST_SOURCE_ID = "1";

	private static final int LOCAL_REDIS_PORT = 6379;
	public static final String STREAM_NAME = "stream";

	@ClassRule
	public static LocalStackContainer localstack = new LocalStackContainer()
		.withServices(KINESIS);

	public static final UpdateShardCountRequest TWO_SHARDS_REQUEST = new UpdateShardCountRequest()
		.withScalingType(ScalingType.UNIFORM_SCALING)
		.withStreamName(STREAM_NAME)
		.withTargetShardCount(2);

	public static final UpdateShardCountRequest ONE_SHARDS_REQUEST = new UpdateShardCountRequest()
		.withScalingType(ScalingType.UNIFORM_SCALING)
		.withStreamName(STREAM_NAME)
		.withTargetShardCount(1);

	private KinesisSourceTask task;
	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	private OffsetStorageReader offsetStorageReader;

	public void onBefore() {

		AmazonKinesis kinesis = AmazonKinesisClientBuilder
			.standard()
			.withEndpointConfiguration(localstack.getEndpointConfiguration(KINESIS))
			.withCredentials(localstack.getDefaultCredentialsProvider())
			.build();

		SourceTaskContext sourceTaskContext = mock(SourceTaskContext.class);
		offsetStorageReader = mock(OffsetStorageReader.class);
		when(sourceTaskContext.offsetStorageReader()).thenReturn(offsetStorageReader);
		when(offsetStorageReader.offset(anyMap())).thenReturn(null);

		kinesis.listStreams();

		kinesis.createStream(STREAM_NAME, 1);
		PutRecordRequest record1 = getPutRecordRequest("1", "1");
		PutRecordRequest record2 = getPutRecordRequest("2", "2");

		kinesis.putRecord(record1);
		kinesis.putRecord(record2);

	}

	@SneakyThrows
	private PutRecordRequest getPutRecordRequest(String value, String key) {
		String json = toJsonString(ImmutableMap.of(value, value));
		ByteBuffer data = ByteBuffer.wrap(json.getBytes("UTF-8"));
		return new PutRecordRequest()
			.withStreamName(STREAM_NAME)
			.withData(data)
			.withPartitionKey(key);
	}
}