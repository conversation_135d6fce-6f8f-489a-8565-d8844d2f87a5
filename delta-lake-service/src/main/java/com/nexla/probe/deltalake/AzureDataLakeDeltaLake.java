package com.nexla.probe.deltalake;

import com.nexla.connector.config.deltalake.DeltaLakeAuthConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.AzureAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.probe.azure.datalake.AzureDataLakeConnectorService;

import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.normalizePath;

public class AzureDataLakeDeltaLake implements DeltaLakeAware<AzureAuthConfig> {

    @Override
    public FileConnectorService<AzureAuthConfig> getFileConnectorService() {
        return new AzureDataLakeConnectorService();
    }

    @Override
    public BaseAuthConfig getDataLakeAuthConfig(DeltaLakeAuthConfig config) {
        return config.asAzure();
    }

    @Override
    public Map<String, String> getDataLakeConfig(DeltaLakeAuthConfig config) {
        Map<String, String> configs = new HashMap<>();
        final AzureAuthConfig azureAuthConfig = config.asAzure();
        configs.put(String.format("fs.azure.account.key.%s.dfs.core.windows.net", azureAuthConfig.storageAccountName)
                , azureAuthConfig.storageAccountKey);
        return configs;
    }

    @Override
    public String getDeltaLakePath(FileConnectorAuth connectorConfig, String path) {
        AWSAuthConfig.BucketPrefix bucketPrefix = toBucketPrefix(connectorConfig.getPath(), true);
        final AzureAuthConfig azureAuthConfig = (AzureAuthConfig) connectorConfig.getAuthConfig();
        String prefix = String.format("%s@%s.dfs.core.windows.net", bucketPrefix.bucket, azureAuthConfig.storageAccountName);
        return "abfss://" + normalizePath(Paths.get(prefix, path).toString());
    }

}
