package com.nexla.listing.client;

import static com.nexla.common.AppUtils.authorizationHeader;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

public class FileVaultClient {

  private final HttpHeaders authorizationHeader;
  private final String fileVaultServer;
  private final RestTemplate restTemplate;
  private static final Logger logger = LoggerFactory.getLogger(FileVaultClient.class);

  public FileVaultClient(
      String fileVaultServer,
      String nexlaUsername,
      String nexlaPassword,
      RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
    this.fileVaultServer = fileVaultServer;
    this.authorizationHeader = authorizationHeader(nexlaUsername, nexlaPassword);
  }

  @SneakyThrows
  public List<Entry> listForSource(int sourceId) {
    ParameterizedTypeReference<List<Entry>> ref = new ParameterizedTypeReference<>() {};

    return restTemplate
        .exchange(
            fromHttpUrl(fileVaultServer + "/file/source/" + sourceId).toUriString(),
            HttpMethod.GET,
            empty(),
            ref)
        .getBody();
  }

  @SneakyThrows
  public Optional<Entry> info(int sourceId, String fileId) {
    try {
      return Optional.of(
          restTemplate
              .exchange(
                  fromHttpUrl(fileVaultServer + "/file/source/" + sourceId + "/" + fileId)
                      .toUriString(),
                  HttpMethod.GET,
                  empty(),
                  Entry.class)
              .getBody());
    } catch (Exception e) {
      logger.error("Error during file info check", e);
      return Optional.empty();
    }
  }

  @SneakyThrows
  public InputStream streamFile(int sourceId, String fileId) {
    logger.info("Streaming File source={}, file={}", sourceId, fileId);

    HttpHeaders headers = new HttpHeaders();
    headers.addAll(this.authorizationHeader);
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));

    Path local = File.createTempFile(sourceId + "-" + fileId, null).toPath();
    restTemplate.execute(
        fromHttpUrl(fileVaultServer + "/file/source/" + sourceId + "/" + fileId + "/data")
            .toUriString(),
        HttpMethod.GET,
        r -> r.getHeaders().addAll(headers),
        response -> {
          Files.copy(response.getBody(), local, StandardCopyOption.REPLACE_EXISTING);
          return null;
        });

    return Files.newInputStream(local, StandardOpenOption.DELETE_ON_CLOSE);
  }

  public void deleteFile(int sourceId, String fileId) {
    logger.info("Deleting File source={}, file={}", sourceId, fileId);
    restTemplate.exchange(
        fromHttpUrl(fileVaultServer + "/file/source/" + sourceId + "/" + fileId).toUriString(),
        HttpMethod.DELETE,
        empty(),
        String.class);
  }

  @SneakyThrows
  public String putFile(int sourceId, File file) {
    logger.info("Put File source={}, file={}", sourceId, file.getAbsoluteFile());
    try (FileInputStream stream = new FileInputStream(file)) {
      MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
      parts.add("data", new FileResource(stream, file.getName(), file.length()));

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.MULTIPART_FORM_DATA);
      headers.addAll(this.authorizationHeader);

      FileId newFile =
          restTemplate
              .exchange(
                  fromHttpUrl(fileVaultServer + "/file/source/" + sourceId).toUriString(),
                  HttpMethod.POST,
                  new HttpEntity<>(parts, headers),
                  FileId.class)
              .getBody();

      return newFile.getFileId();
    }
  }

  private HttpEntity empty() {
    return new HttpEntity(authorizationHeader);
  }

  @Data
  private static class FileId {
    @JsonProperty("file.id")
    private final String fileId;
  }

  @Data
  public static class Entry {
    @JsonProperty("file.id")
    private final String fileId;

    @JsonProperty("file.name")
    private final String fileName;

    @JsonProperty("size")
    private final long size;

    @JsonProperty("created.at")
    private final long createdAt;
  }

  private static class FileResource extends InputStreamResource {
    private final String filename;
    private final long size;

    public FileResource(InputStream inputStream, String filename, long size) {
      super(inputStream);

      this.filename = filename;
      this.size = size;
    }

    @Override
    public String getFilename() {
      return this.filename;
    }

    @Override
    public long contentLength() throws IOException {
      return this.size;
    }
  }
}
