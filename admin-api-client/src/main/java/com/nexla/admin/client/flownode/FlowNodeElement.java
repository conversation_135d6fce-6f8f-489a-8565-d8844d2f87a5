package com.nexla.admin.client.flownode;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class FlowNodeElement {
  @JsonProperty("id")
  public final int id;

  @JsonProperty("parent_node_id")
  public final int parentNodeId;

  @JsonProperty("origin_node_id")
  public final int originNodeId;

  @JsonProperty("data_set_id")
  public final int datasetId;

  @JsonProperty("data_sink_id")
  public final int datasinkId;

  @JsonProperty("children")
  public final List<FlowNodeElement> children;

  public String toShortString() {
    if (datasetId == 0) {
      return String.format("[sink-%d]", this.datasinkId);
    } else if (datasinkId == 0) {
      return String.format("[dataset-%d]", this.datasetId);
    } else {
      return String.format("[dataset-%d|sink-%d]", this.datasetId, this.datasinkId);
    }
  }
}
