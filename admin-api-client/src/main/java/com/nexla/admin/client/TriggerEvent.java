package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.Resource;
import com.nexla.common.ResourceType;

public class TriggerEvent {

  public final ResourceType resourceType;
  public final int resourceId;

  public TriggerEvent(
      @JsonProperty("resource_type") ResourceType resourceType,
      @JsonProperty("resource_id") int resourceId) {
    this.resourceType = resourceType;
    this.resourceId = resourceId;
  }

  public Resource toResource() {
    return new Resource(resourceId, resourceType);
  }
}
