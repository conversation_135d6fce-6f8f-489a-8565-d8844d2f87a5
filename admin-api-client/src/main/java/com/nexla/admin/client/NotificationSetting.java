package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class NotificationSetting implements OwnerAndOrg {

  private Integer id;
  private NotificationChannel channel;

  private String status;

  @JsonProperty("notification_type_id")
  private Integer notificationTypeId;

  private Owner owner;

  private Org org;

  @JsonProperty("notification_type_default")
  private boolean notificationTypeDefault;

  @JsonProperty("notification_resource_type")
  private NotificationResourceType notificationResourceType;

  @JsonProperty("resource_id")
  private Integer resourceId;

  @JsonProperty("config")
  private Map<String, String> config;

  @JsonProperty("notification_channel_setting")
  private NotificationChannelSetting notificationChannelSetting;

  public enum NotificationChannel {
    APP,
    EMAIL,
    SMS,
    SLACK,
    WEBHOOKS
  }

  public enum NotificationResourceType {
    SOURCE,
    PUB,
    SUB,
    SINK,
    DATASET,
    USER,
    ORG,
    PIPELINE,
    UNKNOWN;

    @JsonCreator
    public static NotificationResourceType fromString(String key) {
      return NotificationResourceType.valueOf(key.toUpperCase());
    }
  }
}
