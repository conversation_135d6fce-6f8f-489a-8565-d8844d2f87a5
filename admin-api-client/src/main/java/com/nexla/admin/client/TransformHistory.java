package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class TransformHistory implements Serializable {

  private static final long serialVersionUID = 1238086925593592142L;

  private Integer version;

  @JsonProperty("transforms")
  private List<Object> transforms;

  @JsonProperty("data_maps")
  private List<Integer> dataMaps;
}
