package com.nexla.admin.client;

import static com.nexla.admin.client.AdminApiClient.retryIfException;
import static com.nexla.admin.client.WithClassVersion.OBJECT_VERSION_JSON_PROPERTY;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;
import static java.util.function.Function.identity;
import static org.springframework.http.HttpMethod.GET;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.flownode.AdminApiFlowCondensed;
import com.nexla.common.Resource;
import com.nexla.common.ResourceType;
import com.nexla.common.StreamUtils;
import com.nexla.control.message.ControlEventType;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.web.util.UriComponentsBuilder;

public class AdminApiClientCondensed {

  private static final Logger LOGGER = LoggerFactory.getLogger(AdminApiClientCondensed.class);
  private final AdminApiClient adminApiClient;
  private final boolean useOldCondensedMethods;

  protected AdminApiClientCondensed(AdminApiClient adminApiClient, boolean useOldCondensedMethods) {
    this.adminApiClient = adminApiClient;
    this.useOldCondensedMethods = useOldCondensedMethods;
  }

  /**
   * Method should be called from AdminApiClient for every resource type stored in
   * AdminApiClientCondensed
   */
  @SneakyThrows
  public void updateCache(
      Integer id,
      ResourceType resourceType,
      ControlEventType eventType,
      Optional<Map<String, Object>> resourceJson) {
    // Do Nothing
  }

  // ================================== Get all resources by status
  // ==================================
  private static final ExecutionMetricSet getAllResourcesByStatusMetrics =
      metricSet(AdminApiClientCondensed.class, "getAllResourcesByStatus").withTimeLogger(LOGGER);

  public Map<Resource, ResourceStatus> getAllResourcesByStatus() {
    return getAllResourcesByStatusMetrics.track(
        () -> {
          Map<Resource, ResourceStatus> allResources = new HashMap<>();

          // double get to not allow concurrency issues when status changes between two calls
          allResources.putAll(fetchAllResourcesByStatus(ResourceStatus.ACTIVE));
          allResources.putAll(fetchAllResourcesByStatus(ResourceStatus.PAUSED));
          allResources.putAll(fetchAllResourcesByStatus(ResourceStatus.ACTIVE));
          allResources.putAll(fetchAllResourcesByStatus(ResourceStatus.PAUSED));

          LOGGER.info("Retrieved resource statuses: {}", allResources.size());
          getAllResourcesByStatusMetrics.markHist("size", allResources.size());
          return allResources;
        });
  }

  private Map<Resource, ResourceStatus> fetchAllResourcesByStatus(ResourceStatus status) {
    Map<Resource, ResourceStatus> allResources = new HashMap<>();
    adminApiClient
        .dataSourcesIdsByStatus(java.util.Optional.of(status))
        .forEach(x -> allResources.put(new Resource(x, ResourceType.SOURCE), status));
    adminApiClient
        .dataSetsIdsByStatus(java.util.Optional.of(status))
        .forEach(x -> allResources.put(new Resource(x, ResourceType.DATASET), status));
    adminApiClient
        .dataSinksIdsByStatus(java.util.Optional.of(status))
        .forEach(x -> allResources.put(new Resource(x, ResourceType.SINK), status));
    return allResources;
  }

  // ================================== Get Data Credentials Raw ==================================

  protected List<Map<String, Object>> getAllDataCredentialsRaw() {
    return new ArrayList<>(doGetDataCredentialsRaw().values());
  }

  private static final ExecutionMetricSet doGetDataCredentialsRawMetrics =
      metricSet(AdminApiClientCondensed.class, "doGetDataCredentialsRaw").withTimeLogger(LOGGER);
  ;

  protected Map<Integer, Map<String, Object>> doGetDataCredentialsRaw() {
    return doGetDataCredentialsRawMetrics.track(
        () -> {
          Map<Integer, Map<String, Object>> result = new HashMap<>();
          String url = adminApiClient.apiCredentialsServer + "/data_credentials/all";
          List<Map<String, Object>> dataCredentials =
              retryIfException(
                  () ->
                      adminApiClient
                          .restTemplate
                          .exchange(
                              url,
                              GET,
                              adminApiClient.getHttpHeadersEntity(),
                              new ParameterizedTypeReference<List<Map<String, Object>>>() {})
                          .getBody());
          dataCredentials.forEach(
              x -> {
                int id = Integer.parseInt(x.get("id").toString());
                result.put(id, x);
              });
          return result;
        });
  }

  // ================================== Get Data Sets ==================================

  protected Map<Integer, Map<String, Object>> getDataSetsCondensedRaw() {
    if (useOldCondensedMethods) {
      return getDataSetsCondensedRawOld();
    }

    return getDataSetsCondensedRawNew();
  }

  private static final ExecutionMetricSet getDataSetsCondensedRawNewMetrics =
      metricSet(AdminApiClientCondensed.class, "getDataSetsCondensedRawNew").withTimeLogger(LOGGER);

  public Map<Integer, Map<String, Object>> getDataSetsCondensedRawNew() {
    return getDataSetsCondensedRawNewMetrics.track(
        () -> {
          List<Org> orgs = getOrgsList();

          List<Map<String, Object>> dataSets =
              orgs.stream()
                  .parallel()
                  .flatMap(org -> getByPage(org.getId(), "/data_sets/all/condensed"))
                  .collect(Collectors.toList());

          return StreamEx.of(dataSets)
              .peek(x -> x.put(OBJECT_VERSION_JSON_PROPERTY, DataSet.CLASS_VERSION))
              .mapToEntry(d -> Integer.valueOf(d.get("id").toString()), identity())
              .toMap();
        });
  }

  private static final ExecutionMetricSet getDataSetsCondensedRawOldMetrics =
      metricSet(AdminApiClientCondensed.class, "getDataSetsCondensedRawOld").withTimeLogger(LOGGER);

  public Map<Integer, Map<String, Object>> getDataSetsCondensedRawOld() {
    return getDataSetsCondensedRawOldMetrics.track(
        () -> {
          HttpEntity request = adminApiClient.getHttpHeadersEntity();
          URI uri =
              fromHttpUrl(adminApiClient.apiCredentialsServer + "/data_sets/all/condensed")
                  .build()
                  .encode()
                  .toUri();
          List<Map<String, Object>> dataSets =
              retryIfException(
                  () ->
                      adminApiClient
                          .restTemplate
                          .exchange(
                              uri,
                              GET,
                              request,
                              new ParameterizedTypeReference<List<Map<String, Object>>>() {})
                          .getBody());
          return StreamEx.of(dataSets)
              .peek(x -> x.put(OBJECT_VERSION_JSON_PROPERTY, DataSet.CLASS_VERSION))
              .mapToEntry(d -> Integer.valueOf(d.get("id").toString()), identity())
              .toMap();
        });
  }

  private static final ExecutionMetricSet getDataSetsCondensedMetrics =
      metricSet(AdminApiClientCondensed.class, "getDataSetsCondensed").withTimeLogger(LOGGER);

  public Map<Integer, DataSetCondensed> getDataSetsCondensed() {
    return getDataSetsCondensedMetrics.track(
        () -> {
          Map<Integer, DataSetCondensed> result = Maps.newHashMap();
          getDataSetsCondensedRaw()
              .forEach(
                  (k, v) -> {
                    try {
                      result.put(k, mapToResource(v, DataSetCondensed.class));
                    } catch (Exception e) {
                      LOGGER.error("Error while mapping data set: {}", v.get("id"), e);
                      getDataSetsCondensedMetrics.incError();
                    }
                  });
          getDataSetsCondensedMetrics.markHist("size", result.size());
          return result;
        });
  }

  // ================================== Get Data Sources ==================================
  public Map<Integer, DataSource> getDataSourcesCondensed() {
    return getDataSourcesCondensed(Optional.empty());
  }

  public Map<Integer, DataSource> getDataSourcesCondensed(Optional<ResourceStatus> resourceStatus) {
    Map<Integer, DataSource> result = Maps.newHashMap();
    getDataSourcesCondensedRaw(resourceStatus)
        .forEach(
            (k, v) -> {
              try {
                result.put(k, mapToResource(v, DataSource.class));
              } catch (Exception e) {
                LOGGER.error("Error while mapping data source: {}", v.get("id"), e);
                getDataSourcesCondensedRawMetrics.incError();
              }
            });
    return result;
  }

  private static final ExecutionMetricSet getDataSourcesCondensedRawMetrics =
      metricSet(AdminApiClientCondensed.class, "getDataSourcesCondensedRaw").withTimeLogger(LOGGER);

  public Map<Integer, Map<String, Object>> getDataSourcesCondensedRaw(
      Optional<ResourceStatus> resourceStatus) {
    return getDataSourcesCondensedRawMetrics.track(
        () -> {
          Map<Integer, Map<String, Object>> result = Maps.newHashMap();
          EntryStream.of(dataSourceCondensedMap())
              .filter(
                  x ->
                      resourceStatus
                          .map(
                              st -> {
                                try {
                                  return st
                                      == mapToResource(x.getValue(), DataSource.class).getStatus();
                                } catch (Exception e) {
                                  LOGGER.error(
                                      "Error while mapping data source: {}", x.getKey(), e);
                                  getDataSourcesCondensedRawMetrics.incError();
                                  return false;
                                }
                              })
                          .orElse(true))
              .forKeyValue(result::put);
          getDataSourcesCondensedRawMetrics.markHist(
              resourceStatus.map(Enum::toString).orElse("all") + ".size", result.size());
          return result;
        });
  }

  protected Map<Integer, Map<String, Object>> dataSourceCondensedMap() {
    if (useOldCondensedMethods) {
      return dataSourceCondensedMapOld();
    }

    return dataSourceCondensedMapNew();
  }

  private static final ExecutionMetricSet getOrgsListMetrics =
      metricSet(AdminApiClientCondensed.class, "getOrgsList").withTimeLogger(LOGGER);

  protected List<Org> getOrgsList() {
    return getOrgsListMetrics.track(
        () -> {
          HttpEntity request = adminApiClient.getHttpHeadersEntity();
          UriComponentsBuilder builderOrg =
              fromHttpUrl(adminApiClient.apiCredentialsServer + "/orgs/all");
          URI uriOrg = builderOrg.build().encode().toUri();
          return retryIfException(
              () ->
                  adminApiClient
                      .restTemplate
                      .exchange(
                          uriOrg, GET, request, new ParameterizedTypeReference<List<Org>>() {})
                      .getBody());
        });
  }

  private static final ExecutionMetricSet dataSourceCondensedMapNewMetrics =
      metricSet(AdminApiClientCondensed.class, "dataSourceCondensedMapNew").withTimeLogger(LOGGER);

  protected Map<Integer, Map<String, Object>> dataSourceCondensedMapNew() {
    return dataSourceCondensedMapNewMetrics.track(
        () -> {
          List<Org> orgs = getOrgsList();

          List<Map<String, Object>> dataSources =
              orgs.stream()
                  .parallel()
                  .flatMap(org -> getByPage(org.getId(), "/data_sources/all/condensed"))
                  .collect(Collectors.toList());

          return StreamEx.of(dataSources)
              .peek(x -> x.put(OBJECT_VERSION_JSON_PROPERTY, DataSource.CLASS_VERSION))
              .mapToEntry(x -> Integer.valueOf(x.get("id").toString()), identity())
              .toMap();
        });
  }

  private Stream<Map<String, Object>> getByPage(int orgId, String path) {
    HttpEntity request = adminApiClient.getHttpHeadersEntity();
    String lastResourceId = null;
    List<Map<String, Object>> result = new ArrayList<>();
    while (true) {
      UriComponentsBuilder builder = fromHttpUrl(adminApiClient.apiCredentialsServer + path);
      builder.queryParam("org_id", orgId);
      builder.queryParam("per_page", 100);
      builder.queryParam("sort_by", "id");
      builder.queryParam("sort_order", "asc");
      if (lastResourceId != null) {
        builder.queryParam("resource_id", lastResourceId);
      }

      URI uri = builder.build().encode().toUri();
      List<Map<String, Object>> maps =
          retryIfException(
              () ->
                  adminApiClient
                      .restTemplate
                      .exchange(
                          uri,
                          GET,
                          request,
                          new ParameterizedTypeReference<List<Map<String, Object>>>() {})
                      .getBody());

      if (maps.isEmpty()) {
        break;
      }
      result.addAll(maps);

      Map<String, Object> lastMap = maps.get(maps.size() - 1);
      if (!lastMap.containsKey("id") || lastMap.get("id") == null) {
        throw new IllegalStateException(
            String.format("Condensed API call: %s is missing id field for last element", uri));
      }
      lastResourceId = lastMap.get("id").toString();
    }

    return result.stream();
  }

  private static final ExecutionMetricSet dataSourceCondensedMapOldMetrics =
      metricSet(AdminApiClientCondensed.class, "dataSourceCondensedMapOld").withTimeLogger(LOGGER);

  protected Map<Integer, Map<String, Object>> dataSourceCondensedMapOld() {
    return dataSourceCondensedMapOldMetrics.track(
        () -> {
          HttpEntity request = adminApiClient.getHttpHeadersEntity();
          UriComponentsBuilder builder =
              fromHttpUrl(adminApiClient.apiCredentialsServer + "/data_sources/all/condensed");
          Optional.<ResourceStatus>empty()
              .ifPresent(status -> builder.queryParam("status", status));
          URI uri = builder.build().encode().toUri();
          List<Map<String, Object>> dataSources =
              retryIfException(
                  () ->
                      adminApiClient
                          .restTemplate
                          .exchange(
                              uri,
                              GET,
                              request,
                              new ParameterizedTypeReference<List<Map<String, Object>>>() {})
                          .getBody());

          return StreamEx.of(dataSources)
              .peek(x -> x.put(OBJECT_VERSION_JSON_PROPERTY, DataSource.CLASS_VERSION))
              .mapToEntry(x -> Integer.valueOf(x.get("id").toString()), identity())
              .toMap();
        });
  }

  // ================================== Get Data Sinks ==================================
  private static final ExecutionMetricSet getDataSetAndSinksCondensedMetrics =
      metricSet(AdminApiClientCondensed.class, "getDataSetAndSinksCondensed")
          .withTimeLogger(LOGGER);

  public Map<Integer, DataSinkCondensed> getDataSetAndSinksCondensed() {
    return getDataSetAndSinksCondensedMetrics.track(
        () -> {
          Map<Integer, DataSinkCondensed> result = Maps.newHashMap();
          dataSinkCondensedMap()
              .forEach(
                  (k, v) -> {
                    try {
                      result.put(k, mapToResource(v, DataSinkCondensed.class));
                    } catch (Exception e) {
                      LOGGER.error("Error while mapping data sink: {}", v.get("id"), e);
                      getDataSetAndSinksCondensedMetrics.incError();
                    }
                  });
          getDataSetAndSinksCondensedMetrics.markHist("size", result.size());
          return result;
        });
  }

  protected Map<Integer, Map<String, Object>> dataSinkCondensedMap() {
    if (useOldCondensedMethods) {
      return dataSinkCondensedMapOld();
    }

    return dataSinkCondensedMapNew();
  }

  private static final ExecutionMetricSet dataSinkCondensedMapNewMetrics =
      metricSet(AdminApiClientCondensed.class, "dataSinkCondensedMapNew").withTimeLogger(LOGGER);

  protected Map<Integer, Map<String, Object>> dataSinkCondensedMapNew() {
    return dataSinkCondensedMapNewMetrics.track(
        () -> {
          List<Org> orgs = getOrgsList();

          List<Map<String, Object>> entities =
              orgs.stream()
                  .parallel()
                  .flatMap(org -> getByPage(org.getId(), "/data_sinks/all/condensed"))
                  .collect(Collectors.toList());

          return StreamEx.of(entities)
              .peek(
                  x -> {
                    x.put(OBJECT_VERSION_JSON_PROPERTY, DataSink.CLASS_VERSION);
                    // fix for admin api discrepancy between Condensed and non-Condensed Sink format
                    Optional.ofNullable(x.remove("owner_id"))
                        .ifPresent(ownerId -> x.put("owner", lhm("id", ownerId)));
                    Optional.ofNullable(x.remove("org_id"))
                        .ifPresent(ownerId -> x.put("org", lhm("id", ownerId)));
                  })
              .mapToEntry(e -> Integer.valueOf(e.get("id").toString()), identity())
              .toMap();
        });
  }

  private static final ExecutionMetricSet dataSinkCondensedMapOldMetrics =
      metricSet(AdminApiClientCondensed.class, "dataSinkCondensedMapOld").withTimeLogger(LOGGER);

  protected Map<Integer, Map<String, Object>> dataSinkCondensedMapOld() {
    return dataSinkCondensedMapOldMetrics.track(
        () -> {
          HttpEntity request = adminApiClient.getHttpHeadersEntity();
          URI uri =
              fromHttpUrl(adminApiClient.apiCredentialsServer + "/data_sinks/all/condensed")
                  .build()
                  .encode()
                  .toUri();
          List<Map<String, Object>> entities =
              retryIfException(
                  () ->
                      adminApiClient
                          .restTemplate
                          .exchange(
                              uri,
                              GET,
                              request,
                              new ParameterizedTypeReference<List<Map<String, Object>>>() {})
                          .getBody());
          return StreamEx.of(entities)
              .peek(
                  x -> {
                    x.put(OBJECT_VERSION_JSON_PROPERTY, DataSink.CLASS_VERSION);
                    Optional.ofNullable(x.remove("owner_id"))
                        .ifPresent(ownerId -> x.put("owner", lhm("id", ownerId)));
                    Optional.ofNullable(x.remove("org_id"))
                        .ifPresent(ownerId -> x.put("org", lhm("id", ownerId)));
                  })
              .mapToEntry(e -> Integer.valueOf(e.get("id").toString()), identity())
              .toMap();
        });
  }

  public static <T> T mapToResource(Map<String, Object> x, Class<T> tClass) {
    return StreamUtils.jsonUtil().stringToType(StreamUtils.jsonUtil().toJsonString(x), tClass);
  }

  private static final ExecutionMetricSet getAllDataCredentialsMetrics =
      metricSet(AdminApiClientCondensed.class, "getAllDataCredentials").withTimeLogger(LOGGER);
  ;

  public List<DataCredentials> getAllDataCredentials() {
    return getAllDataCredentialsMetrics.track(
        () -> {
          List<DataCredentials> result = Lists.newArrayList();
          getAllDataCredentialsRaw()
              .forEach(
                  objectMap -> {
                    try {
                      result.add(mapToResource(objectMap, DataCredentials.class));
                    } catch (Exception e) {
                      LOGGER.error(
                          "Error while mapping data credentials: {}", objectMap.get("id"), e);
                      getAllDataCredentialsMetrics.incError();
                    }
                  });
          getAllDataCredentialsMetrics.markHist("size", result.size());
          return result;
        });
  }

  private static final ExecutionMetricSet getFlowsCondensedRawMetrics =
      metricSet(AdminApiClientCondensed.class, "getFlowsCondensedRaw").withTimeLogger(LOGGER);

  public Map<Integer, Map<String, Object>> getFlowsCondensedRaw() {
    return getFlowsCondensedRawMetrics.track(
        () -> {
          List<Org> orgs = getOrgsList();

          List<Map<String, Object>> flows =
              orgs.stream()
                  .parallel()
                  .flatMap(org -> getByPage(org.getId(), "/flows/all/condensed"))
                  .collect(Collectors.toList());

          return StreamEx.of(flows)
              .mapToEntry(d -> Integer.valueOf(d.get("id").toString()), identity())
              .toMap();
        });
  }

  public Map<Integer, AdminApiFlowCondensed> getFlowsCondensed() {
    Map<Integer, AdminApiFlowCondensed> map = new HashMap<>();

    getFlowsCondensedRaw()
        .forEach(
            (key, value) -> {
              try {
                AdminApiFlowCondensed flowCondensed =
                    mapToResource(value, AdminApiFlowCondensed.class);
                map.put(key, flowCondensed);
              } catch (Exception ex) {
                LOGGER.error("Error while mapping flow: {}", key, ex);
                getFlowsCondensedRawMetrics.incError();
              }
            });

    return map;
  }
}
