package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class RuntimeDataCredentials {
  private final Integer id;
  private final String name;
  private final String description;

  @JsonProperty("updated_at")
  private final String updatedAt;

  @JsonProperty("created_at")
  private final String createdAt;
}
