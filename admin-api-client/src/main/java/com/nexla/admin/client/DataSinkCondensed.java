package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataSinkCondensed implements FlowNodeResource, OwnerAndOrg {

  @JsonProperty("id")
  public Integer id;

  @JsonProperty("data_set_id")
  public Optional<Integer> dataSetId;

  @JsonProperty("status")
  public ResourceStatus dataSinkStatus;

  // aka the main flow id
  @JsonProperty("origin_node_id")
  public Integer originNodeId;

  @JsonProperty("sink_config")
  public Map<String, Object> sinkConfig;

  @JsonProperty("flow_type")
  public FlowType flowType;

  @JsonProperty("sink_type")
  private ConnectionType connectionType;

  @JsonProperty("ingestion_mode")
  public IngestionMode ingestionMode;

  public Owner owner;

  public Org org;
}
