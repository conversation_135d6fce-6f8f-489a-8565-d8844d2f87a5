package com.nexla.admin.client;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface WithClassVersion {
  String OBJECT_VERSION_JSON_PROPERTY = "objectVersion";

  Integer getObjectVersion();

  WithClassVersion setObjectVersion(Integer objectVersion);

  int getClassVersion();

  static int calculateVersion(Class<?> clazz) {
    return Stream.concat(
            Stream.of(clazz.getDeclaredFields()).map(Field::toString),
            Stream.of(clazz.getDeclaredMethods()).map(Method::toString))
        .sorted()
        .collect(Collectors.toList())
        .hashCode();
  }
}
