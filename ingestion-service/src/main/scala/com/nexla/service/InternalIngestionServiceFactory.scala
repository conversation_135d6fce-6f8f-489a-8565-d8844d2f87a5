package com.nexla.service

import com.nexla.admin.client.{AdminApiClient, DataSource, NexlaSchema}
import com.nexla.common.notify.transport.{ControlMessageProducer, NexlaMessageProducer}
import com.nexla.common.{NexlaKafkaConfig, NexlaNamingUtils, NexlaSslContext, ResourceType}
import com.nexla.http.AppProps
import com.nexla.http.api.{DelayedKafkaMetricsProducer, IngestionCommons, SchemaDetectionCache}
import com.nexla.schemasync.SchemaSyncProducerService
import com.nexla.service.InternalIngestionServiceFactory.{Context, SchemasCache}
import com.typesafe.scalalogging.StrictLogging

import java.util.Optional
import java.util.concurrent.ConcurrentHashMap
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.{ExecutionContext, Future}


class InternalIngestionServiceFactory(
                                       adminApiClient: AdminApiClient,
                                       controlMessageProducer: ControlMessageProducer,
                                       schemaDetectionCache: SchemaDetectionCache,
                                       appProps: AppProps,
                                       dataKafkaConfig: NexlaKafkaConfig,
                                       controlKafkaConfig: NexlaKafkaConfig
                                     )(implicit ec: ExecutionContext) extends StrictLogging {
  private val kafkaProducer = IngestionCommons.kafkaProducer(appProps, dataKafkaConfig)
  private val schemaSyncService = new SchemaSyncProducerService(appProps, controlKafkaConfig)
  private val metricsSender = new DelayedKafkaMetricsProducer(controlMessageProducer, appProps.metricsWindowMs)
  private val schemaCache = new SchemasCache()

  private def getOrCreateDatasetId(dataSource: DataSource): Future[Int] = {
    if (dataSource.getDatasets.isEmpty) {
      Future {
        logger.info(s"No dataset found for source ${dataSource.getId}. Trying to create an empty one")
        try {
          val schemaDetection = schemaDetectionCache.getSchemaDetection(dataSource.getId)
          schemaDetection.setTryCombineSingleSchema()
          val res = schemaDetection.createDatasetWithEmptySchema()
          logger.info(s"Created an empty dataset or found an existing one ${res.dataSetId} for source ${dataSource.getId}")
          res.dataSetId
        } finally {
          logger.info(s"Invalidating caches for source ${dataSource.getId}")
          schemaDetectionCache.invalidate(dataSource.getId)
          adminApiClient.invalidate(dataSource.getId, Optional.empty[Integer], ResourceType.SOURCE)
        }
      }
    } else {
      Future.successful(dataSource.getDatasets.get(0).getId)
    }
  }

  private def fetchDataSource(sourceId: Int): Future[DataSource] = {
    Future(adminApiClient.getDataSource(sourceId).asScala).map(_.getOrElse(throw new IllegalArgumentException(s"Source $sourceId not found")))
  }

  def fetchOrCreateDatasetWithRetry(sourceId: Int, runId: Long, forceSchemaDetection: Boolean): Future[Context] = {
    val eff = () => fetchDataSource(sourceId).flatMap(getOrCreateDatasetId)
    eff().recoverWith { case e =>
      logger.warn(s"First attempt of dataset creation failed for source $sourceId: ${e.getMessage}. Retrying once...", e)
      eff()
    }.map { datasetId =>
      val topicName = NexlaNamingUtils.nameDataSetTopic(datasetId)
      Context(sourceId, datasetId, topicName, runId, forceSchemaDetection)
    }
  }

  def forSource(ctx: Context): Future[InternalIngestionService] =
    fetchDataSource(ctx.sourceId).map { ds =>
      new InternalIngestionService(ds, ctx, schemaCache, schemaDetectionCache, kafkaProducer, metricsSender, schemaSyncService)
    }
}

object InternalIngestionServiceFactory {
  class SchemasCache {
    private val schemas: ConcurrentHashMap[Int, NexlaSchema] = new ConcurrentHashMap[Int, NexlaSchema]()

    def getForDataset(datasetId: Int): Option[NexlaSchema] = Option(schemas.get(datasetId))

    def putForDataset(datasetId: Int, schema: NexlaSchema): Unit = schemas.put(datasetId, schema)
  }

  case class Context(sourceId: Int, datasetId: Int, topicName: String, runId: Long, forceSchemaDetection: Boolean)
}
