package com.nexla.probe

import com.nexla.admin.client.config.EnrichedConfig.EnrichSinkParams
import com.nexla.common.ConnectionType
import com.nexla.connector.config.vault.NexlaAppConfig
import com.nexla.sc.config._
import lombok.ToString

import scala.compat.java8.OptionConverters._

@ToString
class AppProps(val config: NexlaAppConfig)
  extends Vault with NexlaCreds with NexlaCredsEncoded
    with KafkaProperties with NexlaClusterApplication
    with NexlaDecryptKey with NexlaSslConfig
    with NexlaAdminApi with NexlaEndpoints with AwsCredentials
    with TelemetryConfig with DataDog with SecretNames {

  val maxListBucketFiles = config.getInt("max.list.bucket.files")
  val featureFlagJsonIncludeNull = config.getOptBoolean("feature.flag.json.include.null")

  val consumerMaxPartitionFetchBytes: Option[Integer] = config.getOptInt("consumer.max.partition.fetch.bytes").map(Int.box)
  val consumerFetchMaxBytes: Option[Integer] = config.getOptInt("consumer.fetch.max.bytes").map(Int.box)

  // Use ctrl-listenres-jobscheduler because it is not scaled
  val ctrlListenersUrl = config.getOptString("ctrl.listeners.url").getOrElse("https://ctrl-listeners-jobscheduler.nexla.svc.cluster.local:8080")
  val imcMezzanineCredsId = config.getOptInt("mezzanine.creds.id")
  val imcDryRunDriverFunction = config.getOptString("dry.run.driver.function").getOrElse("process")
  val imcDryRunMezzanineSamplePath = config.getOptString("dry.run.mezzanine.sample.path").getOrElse("nexla-shared-s3/probe-run")
  val imcDryRunMezzanineConnectionType = config.getOptString("dry.run.mezzanine.connection.type").map(ConnectionType.fromString).getOrElse(ConnectionType.S3)
  val rayApiServer = config.getString("ray.api.server.url")
  val ffMockStatusFile = config.getOptBoolean("mock.status.file").getOrElse(false)
  val ffMockForceFlag = config.getOptBoolean("mock.force.flag").getOrElse(false)
}
