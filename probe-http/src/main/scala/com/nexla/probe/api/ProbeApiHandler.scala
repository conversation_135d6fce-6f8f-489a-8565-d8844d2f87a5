package com.nexla.probe.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.ContentTypes._
import akka.http.scaladsl.model.HttpEntity
import akka.http.scaladsl.model.StatusCodes._
import akka.http.scaladsl.server.{Directives, Route}
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.{DeleteObjectRequest, GetObjectRequest, ListObjectsV2Request, PutObjectRequest, S3Exception}
import com.fasterxml.jackson.databind.json.JsonMapper
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.joda.JodaModule
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.google.common.collect.{Lists, Maps}
import com.nexla.admin.client.AdminApiClient
import com.nexla.common.ConnectionType._
import com.nexla.common.NexlaConstants.ConnectionTypeCategory._
import com.nexla.common.StreamUtils._
import com.nexla.common.connectiontype.{ConfigSpecDto, ConnectorSpecDto}
import com.nexla.common.probe.ProbeControllerConstants.{ERROR_MESSAGE, RESPONSE, STATUS_CODE}
import com.nexla.common.probe.ProbeException
import com.nexla.common.{ConnectionType, StreamUtils, probe}
import com.nexla.connector.config.file.{AWSAuthConfig, AdvancedSettings, CustomRtMode, FileSourceConnectorConfig}
import com.nexla.probe.{AppProps, DryRunStartRequest, ProbeController}
import com.nexla.probe.api.Dto._
import com.nexla.probe.api.ray.RayDryRunner
import com.nexla.probe.sql.SqlConnectorService
import com.nexla.sc.api.common.ApiResponse
import com.nexla.sc.util._
import com.nexla.sc.util.StrictNexlaLogging
import com.nexla.spec.Connectors
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsDirectives._
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.apache.kafka.common.config.ConfigException
import org.joor.ReflectException
import org.springframework.web.client.HttpClientErrorException
import software.amazon.awssdk.core.sync.RequestBody
import spray.json.DefaultJsonProtocol

import java.io.File
import java.lang.reflect.InvocationTargetException
import java.nio.charset.Charset
import java.sql.SQLException
import java.text.SimpleDateFormat
import java.util
import java.util.Collections
import scala.collection.JavaConverters._
import scala.collection.SortedMap
import scala.compat.java8.OptionConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try, Using}

case class DryRunReadSampleContext(sourceConfig: util.HashMap[String, AnyRef],
                                   fileConfig: FileSourceConnectorConfig,
                                   dryRunRequest: DryRunStartRequest,
                                   isForced: Boolean,
                                   mode: CustomRtMode,
                                   readWithoutProcessing: Boolean)

class ProbeApiHandler(oldController: ProbeController,
                      probeService: ProbeService,
                      featureFlagJsonIncludeNull: Option[Boolean],
                      rayDryRunner: RayDryRunner,
                      adminApiClient: AdminApiClient,
                      mezzanineS3Client: Option[S3Client],
                      appProps: AppProps)
                     (implicit ec: ExecutionContext)
  extends Directives
    with SprayJsonSupport
    with DefaultJsonProtocol
    with StrictNexlaLogging
    with AppUtils {

  import com.nexla.probe.api.Marshalling._

  private val ConnectionTypeMatcher = Segment.flatMap(a => Try(ConnectionType.fromString(a.toUpperCase)).toOption)
  private val DbConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(x => x.category == DATABASE))
  private val SqlDbConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(x => x.category == DATABASE && !S3_ICEBERG.equals(x)))
  private val DocumentDbConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(x => x.isDocumentDB))
  private val VectorDbConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(x => x.isVectorDB))
  private val KafkaConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(ct => ct.isKafka))
  private val PubSubConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(ct => ct == GOOGLE_PUBSUB))
  private val SoapConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(_ == SOAP))
  private val DataMapConnectionTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(_ == DATA_MAP))
  private val ListTreeDirectTypeMatcher = ConnectionTypeMatcher.flatMap(Option(_).filter(_ == JMS))
  private val dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")

  private implicit val BucketFormat = jsonFormat1(Bucket)
  private implicit val ProbeFormat = ProbeInputFormat.Format

  // todo cleanup after using the new mapper for a while
  private val jsonMapper = if (featureFlagJsonIncludeNull.getOrElse(true))
    JsonMapper.builder()
      .addModule(DefaultScalaModule)
      .addModule(new JodaModule)
      .addModule(new Jdk8Module().configureAbsentsAsNulls(true))
      .defaultDateFormat(dateFormat)
      .build()
  else
    OBJECT_MAPPER

  private val listBuckets =
    (post & pathPrefixLabeled("listBuckets" / ConnectionTypeMatcher, "listBuckets/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        complete {
          Future {
            val buckets = oldController.list(connectionType, toProbeInput(p))
            buckets.iterator().asScala.toList.map(b => Bucket(b.getName))
          }
        }
    }

  private val authenticate =
    (post & pathPrefixLabeled("authenticate" / ConnectionTypeMatcher, "authenticate/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onSuccess {
          Future(oldController.authenticate(connectionType, toProbeInput(p)))
        } { result =>
          if (result.success) {
            complete(OK -> ApiResponse("Success"))
          } else {
            val detailedMessage = result.responseBody.asScala.orElse(result.message.asScala)
            // was it timeout or authentication error?
            result.throwable.asScala match {
              case Some(e) =>
                e match {
                  case _: java.net.SocketTimeoutException | _: java.sql.SQLTimeoutException=>
                    logger.error("Connection timeout", e)
                    complete(Unauthorized -> ApiResponse(makeErrorMessage("Connection timeout", e), detailedMessage))
                  case _ =>
                    // assuming http 4xx
                    complete(Forbidden -> ApiResponse(makeErrorMessage("Unable to authenticate", e), detailedMessage))
                }
              case _ =>
                logger.error("Authentication failed, but no throwable was saved")
                val message = "Unable to connect" + detailedMessage.fold("")(m =>s". $m")
                complete(Forbidden -> ApiResponse(message, detailedMessage))
            }
          }
        }
    }

  private val readDataMapSample =
    (post & pathPrefixLabeled("read" / "sample" / DataMapConnectionTypeMatcher, "read/sample/datamap") & end & entity(as[ProbeInput]) &
      parameters(
        "offset".as[Int].?,
        "pageSize".as[Int].?
      )) {
      case (connectionType, p, offset, pageSize) =>
        onComplete {
          Future {
            probeService.readDataMapSample(connectionType, toProbeInput(p), offset, pageSize)
          }
        } {
          case Success(result) => complete(result)
          case Failure(e) => complete(InternalServerError -> Map("error" -> e.getMessage))
        }
    }

  private val readSample =
    (post & pathPrefixLabeled("read" / "sample" / ConnectionTypeMatcher, "read/sample/:connectionType") & end & entity(as[ProbeInput]) &
      parameters(
        "raw".as[Boolean].?,
        "offset".as[Int].?,
        "pageSize".as[Int].?
      )) {
      case (connectionType, probeInput, raw, offset, pageSize) =>
        onComplete {
          Future {
            doReadSample(connectionType, probeInput, raw, offset, pageSize)
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"read/sample ($connectionType, $probeInput)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  val mezzanineBucketPrefix = AWSAuthConfig.toBucketPrefix(appProps.imcDryRunMezzanineSamplePath, true)

  private def markNewTestRequest(req: DryRunStartRequest): Unit = {
    if (mezzanineS3Client.isDefined) {
      logger.info(s"Creating init file in ${req.mezzBucket}: ${req.jobTrackFile}")
      mezzanineS3Client.get.putObject(PutObjectRequest.builder()
        .bucket(req.mezzBucket)
        .key(req.jobTrackFile)
        .build(), RequestBody.fromString(req.dryRunId.toString)
      )
    } else {
      logger.warn("Mezzanine S3 client not created. Please check configuration - IMC_MEZZANINE_CREDS_ID, IMC_DRY_RUN_MEZZANINE_SAMPLE_PATH and IMC_DRY_RUN_DRIVER_FUNCTION env properties should be present.")
    }
  }

  private def cleanupResultStatusFile(hash: String): Unit = {
    if (mezzanineS3Client.isDefined) {
      val bucket = mezzanineBucketPrefix.bucket
      val statusFilePath = Some(mezzanineBucketPrefix.prefix).getOrElse("") + s"$hash/status"
      logger.info(s"Cleanup result files in $bucket: $statusFilePath")
      val s3Client = mezzanineS3Client.get
      val deleteObjectRequest = DeleteObjectRequest.builder()
        .bucket(bucket)
        .key(statusFilePath)
        .build()

      try {
        s3Client.deleteObject(deleteObjectRequest)
      } catch {
        case e: S3Exception =>
          logger.error(s"Failed to cleanup result status file for $hash", e)
      }
    } else {
      logger.warn("Mezzanine S3 client not created. Please check configuration - IMC_MEZZANINE_CREDS_ID, IMC_DRY_RUN_MEZZANINE_SAMPLE_PATH and IMC_DRY_RUN_DRIVER_FUNCTION env properties should be present.")
    }
  }

  private def doReadSample(connectionType: ConnectionType,
                         probeInput: ProbeInput,
                         raw: Option[Boolean],
                         offset: Option[Int],
                         pageSize: Option[Int]): util.Map[String, AnyRef] = {
    val isCustom: Boolean = probeInput.params.flatMap(_.get("mode")).contains("custom")

    lazy val ctx: DryRunReadSampleContext = createProbeReadSampleContext(probeInput, connectionType)
    if (!isCustom || ctx.readWithoutProcessing) {
      oldController.readSample(
        connectionType,
        toProbeInput(probeInput),
        raw.map(Boolean.box).asJava,
        offset.map(Int.box).asJava, pageSize.map(Int.box).asJava
      )
    } else {
      logger.info("Read sample for {} with uuidHash: {}, probeInput: {}", connectionType, ctx.dryRunRequest.uuidHash, probeInput)
      val maybePrev = if (ctx.isForced) {
        Try {
          markNewTestRequest(ctx.dryRunRequest)
        }.failed.foreach {
          e => logger.error(s"Failed to cleanup result status file for ${ctx.dryRunRequest.uuidHash}", e)
        }
        None
      } else {
        readSampleResults(raw, offset, pageSize, ctx.dryRunRequest)
      }
      maybePrev
        .getOrElse {
          if (ctx.isForced) {
            rayDryRunner.scheduleDryRunStart(ctx, connectionType, ctx.fileConfig, pageSize)
          }
          val result: util.Map[String, AnyRef] = java.util.Map.of(
            "response", s"${ctx.dryRunRequest.uuidHash}",
            "statusCode", Integer.valueOf(202),
            "contentType", "text/plain")
          result
        }
    }
  }

  private def readSampleResults(raw: Option[Boolean],
                                offset: Option[Int],
                                pageSize: Option[Int],
                                req: DryRunStartRequest): Option[util.Map[String, AnyRef]] = {
    if (appProps.imcMezzanineCredsId.isDefined) {
      val mezCreds = adminApiClient.getDataCredentials(appProps.imcMezzanineCredsId.get).get()

      val bucket = req.mezzBucket

      val maxRunIdFile = req.mezzBucketPrefix + req.jobTrackFile

      val maxSuffix = IOUtils.toString(mezzanineS3Client.get.getObject(GetObjectRequest.builder()
          .bucket(bucket)
          .key(maxRunIdFile)
          .build()), Charset.defaultCharset)
      val statusFilePath = req.fullOutputPath(maxSuffix)

      val resultFile = mezzanineS3Client.get.listObjectsV2(ListObjectsV2Request.builder()
          .bucket(bucket)
          .prefix(req.mezzBucketPrefix + req.noBucketOutputPath(maxSuffix))
          .build)
        .contents()
        .asScala
        .filterNot(x => new File(x.key).getName == "status")
        .map(_.key())

      val statusFile = if (req.autoStatusFile) {
        if (resultFile.isEmpty) {
          Collections.emptyMap()
        } else {
          lhm(STATUS_CODE, java.lang.Integer.valueOf(200))
        }
      } else {
        oldController.readSample(
        appProps.imcDryRunMezzanineConnectionType,
          toProbeInput(ProbeInput(mezCreds.getId, mezCreds.getCredentialsEnc, mezCreds.getCredentialsEncIv, Some(Map("path" -> s"$statusFilePath/status")))),
        raw.map(Boolean.box).asJava,
        offset.map(Int.box).asJava,
        pageSize.map(Int.box).asJava)
      }

      Some(statusFile.get(STATUS_CODE))
        .filter(_ == 200) // Status file is present, the processing has finished
        .map { _ =>
          Option(statusFile.get("error")) match {
            case Some(error) => java.util.Map.of(STATUS_CODE, Integer.valueOf(500), ERROR_MESSAGE, error) // there was an error during processing. Return it to UI
            case None => // there was no error during processing. Return the result to UI
              val sampleResultFilePath = s"${req.mezzFullPath}/${resultFile.head}"
              logger.info("Read sample result for {} from {}", req.uuidHash, sampleResultFilePath)
              oldController.readSample(
                appProps.imcDryRunMezzanineConnectionType,
                toProbeInput(ProbeInput(mezCreds.getId, mezCreds.getCredentialsEnc, mezCreds.getCredentialsEncIv, Some(Map("path" -> sampleResultFilePath)))),
                raw.map(Boolean.box).asJava,
                offset.map(Int.box).asJava,
                pageSize.map(Int.box).asJava)
          }
        }
    } else {
      val errorMsg = "Please check configuration - IMC_MEZZANINE_CREDS_ID, IMC_DRY_RUN_MEZZANINE_SAMPLE_PATH and IMC_DRY_RUN_DRIVER_FUNCTION env properties should be present."
      logger.warn(errorMsg)
      Option(java.util.Map.of(STATUS_CODE, Integer.valueOf(500), ERROR_MESSAGE, errorMsg))
    }
  }

  private val listFiles =
    (post & pathPrefixLabeled("listFiles" / ConnectionTypeMatcher, "listFiles/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        complete {
          Future {
            oldController
              .listFiles(connectionType, toProbeInput(p))
              .asScala
              .map(f => toNexFile(f))
              .toList
          }
        }
    }

  private val listTreeKafka =
    (post & pathPrefixLabeled("listTree" / KafkaConnectionTypeMatcher, "listTree/kafka") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        complete {
          Future {
            toTopics(
              oldController
                .listFiles(
                  connectionType,
                  toProbeInput(p))
                .asScala
            )
          }
        }
    }

  private val listTreePubSub =
    (post & pathPrefixLabeled("listTree" / PubSubConnectionTypeMatcher, "listTree/googlePubSub") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        complete {
          Future {
            toTopicWithSubscriptions(
              oldController
                .listFiles(
                  connectionType,
                  toProbeInput(p))
                .asScala
            )
          }
        }
    }

  private val listTreeDb =
    (post & pathPrefixLabeled("listTree" / DbConnectionTypeMatcher, "listTree/database") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listTreeDb(
                connectionType,
                toProbeInput(p))
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"listTreeIterative($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val listTreeDbPaged =
    (post & pathPrefixLabeled("v2" / "list-tree" / DbConnectionTypeMatcher, "/v2/list-tree/databases") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listTreeDbPaged(
                connectionType,
                toProbeInput(p))
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"/v2/listTreeIterative($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val listTreeDocumentDb =
    (post & pathPrefixLabeled("listTree" / DocumentDbConnectionTypeMatcher, "listTree/documentDatabase") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listTreeDocumentDb(
                connectionType,
                toProbeInput(p))
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"listTreeIterative($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val listTreeVectorDb =
    (post & pathPrefixLabeled("listTree" / VectorDbConnectionTypeMatcher, "listTree/vectorDatabase") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listTreeVectorDb(
                connectionType,
                toProbeInput(p))
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"listTreeIterative($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val listTreeSoap =
    (post & pathPrefixLabeled("listTree" / SoapConnectionTypeMatcher, "listTree/soap") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        complete {
          Future {
            oldController
              .listSoap(
                connectionType,
                toProbeInput(p))
              .toWsdlDefinition
          }
        }
    }

  private val listTreeDirect =
    (post & pathPrefixLabeled("listTree" / ListTreeDirectTypeMatcher, "listTree/jms") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listTreeDirect(
                connectionType,
                toProbeInput(p)
              )
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"listTreeAll($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val listTreeAll =
    (post & pathPrefixLabeled("listTree" / ConnectionTypeMatcher, "listTree/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listTreeFiles(
                connectionType,
                toProbeInput(p)
              )
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"listTreeAll($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val read =
    (post & pathPrefixLabeled("read" / KafkaConnectionTypeMatcher, "read/kafka") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onSuccess(
          Future {
            oldController.readKafkaSamples(connectionType, toProbeInput(p))
          }
        ) { result => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result))) }
    }

  private val readSamplesFromKafka =
    (post & pathPrefixLabeled("read" / ConnectionTypeMatcher, "read/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onSuccess(
          Future {
            oldController.read(connectionType, toProbeInput(p))
          }
        ) { result => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result))) }
    }

  private val readQuarantine =
    (post & pathPrefixLabeled("read" / "quarantine") & end & entity(as[Map[String, String]])) { config =>
      onSuccess(
        Future {
          oldController.readQuarantine(new util.HashMap(config.asJava))
        }
      ) { result => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result))) }
    }

  private val listVolumes =
    (post & pathPrefixLabeled("listVolumes" / DbConnectionTypeMatcher, "listVolumes/database") & end & entity(as[ProbeInput])) {
      case (connectionType, p) =>
        onComplete {
          Future {
            oldController
              .listVolumes(
                connectionType,
                toProbeInput(p))
          }
        } {
          case Success(result) => complete(OK -> HttpEntity(`application/json`, jsonMapper.writeValueAsString(result)))
          case Failure(e) =>
            logger.error(s"listVolumes($connectionType, $p)", e)
            complete(InternalServerError -> ApiResponse(e.getMessage))
        }
    }

  private val listDataTypes =
    (get & pathPrefixLabeled("listDataTypes" / SqlDbConnectionTypeMatcher, "listDataTypes/database") & end) { connectionType =>
      onComplete {
        Future {
          val service = oldController.getProbeService(connectionType).asInstanceOf[SqlConnectorService]
          val dataTypesResult = service.listDataTypes(connectionType)
          jsonMapper.writeValueAsString(dataTypesResult)
        }
      } {
        case Success(result) => complete(OK -> HttpEntity(`application/json`, result))
        case Failure(e) => complete(InternalServerError -> Map("error" -> e.getMessage))
      }
    }

  private val checkWrite =
    (post & pathPrefixLabeled("checkWrite" / ConnectionTypeMatcher, "checkWrite/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, probeInput) =>
        onSuccess {
          Future(oldController.checkWrite(connectionType, toProbeInput(probeInput)))
        } {
          result => complete(ApiResponse(if (result) "Success" else "Failure"))
        }
    }

  private val createDestination =
    (post & pathPrefixLabeled("createDestination" / ConnectionTypeMatcher, "createDestination/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, probeInput) =>
        onComplete {
          Future(oldController.createDestination(connectionType, toProbeInput(probeInput)))
        } {
          case Success(_) => complete(ApiResponse("Success"))
          case Failure(e) => {
            logger.error(s"Could not create destination for: $connectionType $probeInput", e)
            complete(InternalServerError -> ApiResponse(s"Unable to create destination: ${e.getMessage}"))
          }
        }
    }

  private val alterTable =
    (post & pathPrefixLabeled("alterTable" / ConnectionTypeMatcher, "alterTable/:connectionType") & end & entity(as[ProbeInput])) {
      case (connectionType, probeInput) =>
        onComplete {
          Future(oldController.alterTable(connectionType, toProbeInput(probeInput)))
        } {
          case Success(_) => complete(ApiResponse("Success"))
          case Failure(e) => {
            logger.error(s"Could not alter table for: $connectionType $probeInput", e)
            complete(InternalServerError -> ApiResponse("Unable to alter table"))
          }
        }
    }

  val connectionTypes =
    (get & pathPrefixLabeled("connectionTypes") & end) {
      onComplete {
        Future {
          val result = Lists.newArrayList[java.util.Map[String, AnyRef]]()
          ConnectionType.CONNECTION_TYPE_MAP.values().asScala.foreach { ct =>
            val elem = new util.HashMap[String, AnyRef]()
            elem.put("name", ct.name)
            if (ct.category != null) {
              elem.put("category", ct.category.name())
            }
            if (ct.connectionStringPrefix != null) {
              elem.put("connectionStringPrefix", ct.connectionStringPrefix)
            }
            elem.put("isWarehouseSource", java.lang.Boolean.valueOf(ct.isWarehouseSource))
            elem.put("isWarehouseSink", java.lang.Boolean.valueOf(ct.isWarehouseSink))
            elem.put("isSourceConnectorType", java.lang.Boolean.valueOf(ct.isSourceConnectorType))
            elem.put("isRefreshable", java.lang.Boolean.valueOf(ct.isRefreshable))
            result.add(elem)
          }
          StreamUtils.jsonUtil().toJsonString(result)
        }
      } {
        case Success(result) => complete(OK -> HttpEntity(`application/json`, result))
        case Failure(e) => complete(InternalServerError -> Map("error" -> e.getMessage))
      }
    }

  val connectorSpecs =
    (get & pathPrefixLabeled("connectorSpecs") & end) {
      onComplete {
        Future {
          val configSpecs = Connectors
            .configs
            .filterNot(_.isSecondary)
            .flatMap { configSpc =>
              configSpc.authConfigClass.map { authCfg =>

                val sourceConfig = configSpc
                  .sourceConfig
                  .map(xx => new ConfigSpecDto(xx.connectorClass, xx.configClass.getName, xx.dockerImage))
                  .asJava

                val sinkConfig = configSpc
                  .sinkConfig
                  .map(xx => new ConfigSpecDto(xx.connectorClass, xx.configClass.getName, xx.dockerImage))
                  .asJava

                new ConnectorSpecDto(authCfg.getName, sourceConfig, sinkConfig, configSpc.connectionType, configSpc.connectorServiceClass.asJava)

              }
            }.toList.asJava
          StreamUtils.jsonUtil().toJsonString(configSpecs)
        }
      } {
        case Success(result) => complete(OK -> HttpEntity(`application/json`, result))
        case Failure(e) => complete(InternalServerError -> Map("error" -> e.getMessage))
      }
    }

  private def toProbeInput(p: ProbeInput) =
    new probe.ProbeInput(p.credsId, p.credsEnc, p.credsEncIv, new util.HashMap[String, String](p.params.getOrElse(Map()).asJava))

  private def makeErrorMessage(baseMessage: String, throwable: Throwable): String = {
    var current = throwable
    var betterCause: Option[String] = None

    // try to find a class exceptions that can contain a good message to show to users
    // (not all exceptions have messages useful to users).
    // We use the dippiest good exception as it probably the most specific one (JDBC pools may hide true cause)
    while (current != null) {
      current match {
        case _: ProbeException | _: SQLException | _: HttpClientErrorException | _: ConfigException =>
          if (current.getMessage() != null) {
            betterCause = Some(current.getMessage())
          }
        case _ => ()
      }
      current = current.getCause
    }

    // fallback to message in root cause
    val finalCause = betterCause.getOrElse(ExceptionUtils.getRootCause(throwable).getMessage())

    s"$baseMessage. $finalCause"
  }

  val route: Route = concat(
    listBuckets,
    authenticate,
    readDataMapSample,
    readSample,
    listTreeKafka,
    listTreePubSub,
    listFiles,
    listTreeDb,
    listTreeDbPaged,
    listTreeDocumentDb,
    listTreeVectorDb,
    listTreeSoap,
    listTreeDirect,
    listTreeAll,
    read,
    readSamplesFromKafka,
    readQuarantine,
    listVolumes,
    listDataTypes,
    checkWrite,
    createDestination,
    alterTable,
    connectorSpecs,
    connectionTypes
  )


  def createProbeReadSampleContext(probeInput: ProbeInput, connectionType: ConnectionType): DryRunReadSampleContext = {
    val sourceConfig: util.HashMap[String, AnyRef] = {
      val allParams = toProbeInput(probeInput).getAllParams(appProps.decryptKey, connectionType, appProps.nexlaCredsEncoded.enc, appProps.nexlaCredsEncoded.encIv)
      Maps.newHashMap[String, AnyRef](StreamUtils.jsonUtil().jsonToMap(allParams.get("source_config")))
    }
    val fileConfig: FileSourceConnectorConfig = {
      val fullSourceConfig = oldController.fullSourceConfig(connectionType, toProbeInput(probeInput))
      val resultConfig = Maps.newHashMap[String, String](fullSourceConfig.originalsStrings())
      resultConfig.putAll(sourceConfig.asScala
        .filterNot { case (_, v) => v == null }
        .mapValues(_.toString).asJava)

      new FileSourceConnectorConfig(resultConfig)
    }
    val req: DryRunStartRequest = rayDryRunner.probeInputDryStartRequest(probeInput)
    val isForced: Boolean = {
      if (appProps.ffMockForceFlag) {
        val content = IOUtils.toString(
          mezzanineS3Client.get.getObject(GetObjectRequest.builder()
            .bucket(req.mezzBucket)
            .key(req.jobParserTrackFile)
            .build()), Charset.defaultCharset
        )
        val forceTs = Option(content).map(_.toLong)
        val l = System.currentTimeMillis()
        val result = if (forceTs.isDefined) {
          if (l - forceTs.get > 30 * 1000) {
            true
          } else {
            false
          }
        } else {
          true
        }
        logger.info(s"Checking getForcedFlag file in ${req.mezzBucket} ${req.jobParserTrackFile} : $forceTs, force = $result")
        mezzanineS3Client.get.putObject(PutObjectRequest.builder()
          .bucket(req.mezzBucket)
          .key(req.jobParserTrackFile)
          .build, RequestBody.fromString(l.toString))
        result
      } else {
        probeInput.params.flatMap(_.get("force")).exists(_.toBoolean)
      }
    }

    val maybeCustomRuntimeFromSrcConfig: Option[CustomRtMode] = sourceConfig.asScala
      .get("custom_runtime.mode")
      .collect { case s: String => s }
      .flatMap(s => Try(CustomRtMode.valueOf(s.toUpperCase())).toOption)

    val mode: CustomRtMode = maybeCustomRuntimeFromSrcConfig match {
      case Some(m) => m
      case None =>
        if (AdvancedSettings.RAW_DATA_FILE.equalsIgnoreCase(fileConfig.advancedSettings)) {
          CustomRtMode.REPLICATION
        } else {
          CustomRtMode.PARSING
        }
    }

    val readWithoutProcessing: Boolean = {
      val isCustom: Boolean = probeInput.params.flatMap(_.get("mode")).contains("custom")
      val parserCall = req.codeBase64.isEmpty // there are 2 types of Call for RAY - based pipelines: Parser and Ray-processing
      !isCustom || (parserCall && Set(CustomRtMode.OFF, CustomRtMode.REPLICATION).contains(mode))
    }
    DryRunReadSampleContext(sourceConfig, fileConfig, req, isForced, mode, readWithoutProcessing)
  }
}
